import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 *  导出交互配置表
 */
export function exportExcel() {
  return request.get({
    url: `${homeServerPrefix}/SparePartsAssembly/export`,
    header: {
      headers: { "Content-Type": "application/x-download" },
    },
    responseType: "blob",
  })
}

/**
 *  导入交互配置表
 */
export function importExcel(data) {
  return request.post({
    url: `${homeServerPrefix}/VulnerablePartsLife/import`,
    headers: { "Content-Type": "multipart/form-data" },
    data,
  })
}

/**
 *  根据ID自动修改库存
 */
export function putInventory(data) {
  return request.put({
    url: `${homeServerPrefix}/SpareParts/Inventory?id=${data}`,
  })
}

/**
 *  根据ID修改易损件状态
 */
export function putStatus(data) {
  return request.put({
    url: `${homeServerPrefix}/VulnerablePartsLife/status`,
    data,
  })
}
