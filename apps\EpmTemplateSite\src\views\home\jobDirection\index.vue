<template>
  <div class="job p-z2">
    <div class="tab-head">
      <div v-for="(tab, index) in head" :key="tab.id" class="head-item" @click="select(tab, index)">
        <div :class="['m-item', activeHead == tab.id ? 't-active' : '']">
          {{ tab.text }}
        </div>
      </div>
    </div>
    <div class="tab-body">
      <el-form :inline="true" class="search form">
        <el-form-item :label="$t('jobDirection.fileName')">
          <el-input v-model="form.fileName" class="search-form-input"
            :placeholder="$t('jobDirection.fileNamePlaceholder')" clearable></el-input>
        </el-form-item>
        <el-form-item :label="$t('jobDirection.fileDescription')">
          <el-input v-model="form.fileDesc" class="search-form-input"
            :placeholder="$t('jobDirection.fileDescriptionPlaceholder')" clearable></el-input>
        </el-form-item>
        <el-form-item :label="$t('jobDirection.fileFormat')">
          <el-input v-model="form.fileContentType" class="search-form-input"
            :placeholder="$t('jobDirection.fileFormatPlaceholder')" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleCheck">{{
            $t("common.queryText")
          }}</el-button>
          <el-button type="primary" @click="handleReset">{{
            $t("common.resetText")
          }}</el-button>
          <el-button type="primary" @click="handleAddFile">
            {{ $t("jobDirection.addFile") }}
            <el-icon class="el-icon--right">
              <Plus />
            </el-icon>
          </el-button>
        </el-form-item>
      </el-form>
      <div class="cont">
        <div class="cont-box">
          <div class="view" v-if="isViewShow">
            <div class="body">
              <div v-for="(item, index) of tableData" :key="index" class="vidio-list">
                <div class="file-cont">
                  <div class="mask" @click="filePreview(item)">
                    <div class="preview">
                      {{ $t("jobDirection.clickPreview") }}
                    </div>
                  </div>
                  <img class="v-img" :src="videoBg" v-if="isVideo(item.fileContentType)" />
                  <img class="oth-img" :src="wordImg" v-if="isWdord(item.fileContentType)" />
                  <img class="oth-img" :src="pdfImg" v-if="isPdf(item.fileContentType)" />
                  <img class="oth-img" :src="excelImg" v-if="isExcel(item.fileContentType)" />
                  <img class="img-img" :src="item.fileUrl" v-if="isImage(item.fileContentType)" />
                </div>
                <div class="file-name" :title="item.fileName">
                  {{ item?.fileName }}
                </div>
              </div>
            </div>
            <div class="foot">
              <el-pagination background layout="total, sizes, prev, pager, next, jumper" :pageSizes="[10, 20, 50]"
                :total="total" @size-change="handleSizeChange" @current-change="handlerPageIndex"
                v-model:current-page="currentPageView" v-model:page-size="pageSizeView" />
            </div>
          </div>
          <div class="list" v-else>
            <div class="list-box">
              <hmx-table :table-data="tableData" :options="tableOptions" :columns="tableColumn" @command="handleAction"
                @size-change="handlerPageSize" @current-change="handlerPageIndex">
                <template #fileType="{ row }">
                  <span v-if="row.fileType == 1">
                    {{ $t("jobDirection.toolMaterials") }}
                  </span>
                  <span v-if="row.fileType == 2">
                    {{ $t("jobDirection.modelChangeMaterials") }}
                  </span>
                  <span v-if="row.fileType == 3">
                    {{ $t("jobDirection.maintenanceMaterials") }}
                  </span>
                </template>
                <template #fileName="{ row }">
                  <div class="fileBox">
                    <img class="t-img" :src="wordImg" v-if="isWdord(row.fileContentType)" />
                    <img class="t-img" :src="pdfImg" v-if="isPdf(row.fileContentType)" />
                    <img class="t-img" :src="excelImg" v-if="isExcel(row.fileContentType)" />
                    <img class="t-img" :src="videoImg" v-if="isVideo(row.fileContentType)" />
                    <img class="t-img" :src="row.fileUrl" alt="" v-if="isImage(row.fileContentType)" />
                    <span>
                      {{ row.fileName }}
                    </span>
                  </div>
                </template>
                <template #fileSize="{ row }">
                  <span>
                    {{ row.fileSize + "M" }}
                  </span>
                </template>
                <template #quickWearPart="{ row }">
                  <el-button type="primary" text @click="handleDetail(row)">{{
                    $t("common.detail")
                  }}</el-button>
                </template>
              </hmx-table>
            </div>
          </div>
        </div>

        <div class="l-v">
          <div class="l-btn" :class="isViewShow ? 'active' : ''" @click="handleView(0)">
            {{ $t("jobDirection.viewMode") }}
          </div>
          <div class="l-btn" :class="isViewShow ? '' : 'active'" @click="handleView(1)">
            {{ $t("jobDirection.listMode") }}
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 新增、编辑弹框 -->
  <spare-parts-dialog :options="options" @onCancel="closeDialog" @onSure="onSure" />
  <videoDialog :isVisable="isVideoDialogShow" :checkOpt="checkOpt" @close-dialog="closeVideo" />
  <previewFile :show="isPreviewFileShow" :curFile="curFile" @onClose="closePreviewFile" />
  <preview-img :options="previewImgOptions" @onCancel="closePreviewImgDialog" />
  <!-- 详情 -->
  <DetailDialog :isShow="isDetailShow" @onClose="handleCloseDetailDialog" :currentItem="currentDetailItem"
    @onSure="handleConfirmDetail" />
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue"
import useTable from "@/hooks/useTable"
import { getOPSFiles } from "@/api/front/jobDirection"
import videoDialog from "./components/videoDialog.vue"
import previewFile from "./components/preview-file.vue"
import HmxTable from "@/components/hmx-table/index.vue"
import pdfImg from "@/assets/jobDirection/pdf2.png"
import wordImg from "@/assets/jobDirection/word2.png"
import excelImg from "@/assets/jobDirection/excel2.png"
import videoImg from "@/assets/jobDirection/video.png"
import videoBg from "@/assets/jobDirection/video-bg.png"
import PreviewImg from "./components/preview-img.vue"
import SparePartsDialog from "./components/spare-parts-dialog.vue"
import DetailDialog from "./components/detail-dialog.vue"
import { useI18n } from "@xfe/locale"
import { config } from "@/config"
import { useMessage } from "@/hooks/web/useMessage"
import downloadFile from "@/utils/downloadFile"
import checkExcelHasImg from "@/utils/checkExcelHasImg"
const { createMessage } = useMessage()
const { t: $t } = useI18n()
const homeServerPrefix = config.base_url.homeServerPrefix

let head = [
  { id: 1, text: $t("jobDirection.toolMaterials") },
  { id: 2, text: $t("jobDirection.modelChangeMaterials") },
  { id: 3, text: $t("jobDirection.maintenanceMaterials") },
]
let activeHead = ref(1)

const { list, total, loading, getList, deleteFun } = useTable(
  `${homeServerPrefix}/OPSFiles`,
  true,
  false
)

let curFile = ref({ fileType: "", filePath: "" })

// 是否开启预览
let isPreviewFileShow = ref(false)

const currentPageView = ref(1)

const pageSizeView = ref(10)

let tableColumn = [
  {
    prop: "fileName",
    label: $t("jobDirection.fileName"),
    align: "center",
    slot: "fileName",
    showOverflowTooltip: true,
  },
  {
    prop: "creationTime",
    label: $t("jobDirection.uploadDate"),
    align: "center",
  },
  {
    prop: "fileType",
    label: $t("jobDirection.type"),
    align: "center",
    slot: "fileType",
  },
  {
    prop: "fileSize",
    label: $t("jobDirection.size"),
    align: "center",
    slot: "fileSize",
  },
  {
    prop: "fileDesc",
    label: $t("jobDirection.fileDescription"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "fileContentType",
    label: $t("jobDirection.fileFormat"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "quickWearPart",
    label: $t("jobDirection.relatedVulnerableParts"),
    align: "center",
    slot: "quickWearPart",
  },
  {
    width: "220",
    label: $t("common.operate"),
    align: "center",
    buttons: [
      {
        command: "preView",
        type: "text",
        name: $t("common.preview"),
      },
      {
        name: $t("common.download"),
        type: "text",
        command: "downLoad",
      },
      {
        name: $t("common.edit"),
        type: "text",
        command: "edit",
      },
      {
        name: $t("common.delText"),
        type: "text",
        command: "delete",
      },
    ],
  },
]

const data = reactive({
  // 绑定数据
  form: {
    fileName: "", // 文件名称
    fileDesc: "", // 文件描述
    fileContentType: "", // 文件格式
  },
  page: {
    pageSize: 10,
    pageIndex: 1,
  },
  options: {
    show: false,
    type: "", // 用于判断是编辑还是删除 add edit
    curFile: null,
  },
  curFile: {},
  previewImgOptions: {
    show: false,
    imgUrl: null,
  },
})

const tableData = computed(() => {
  let arr = []
  arr = list.value
  return arr
})

const tableOptions = computed(() => {
  return {
    loading: loading.value,
    showPagination: true,
    paginationPosition: "right",
    border: true,
    paginationConfig: {
      total: total.value,
      currentPage: data.page.pageIndex,
      pageSize: data.page.pageSize,
    },
  }
})

const params = computed(() => {
  let form = JSON.parse(JSON.stringify(data.form))
  Object.keys(form).forEach(key => {
    if (form[key] === null) {
      form[key] = ""
    }
  })
  return {
    fileType: activeHead.value,
    ...form,
    ...data.page,
  }
})

function handlerPageSize(pageSize) {
  data.page.pageSize = pageSize
  pageSizeView.value = pageSize
  getList(params.value)
}
// 表格页数改变
function handlerPageIndex(pageIndex) {
  data.page.pageIndex = pageIndex
  currentPageView.value = pageIndex
  getList(params.value)
}

const handlerDownLoad = row => {
  const { fileName, fileUrl } = row
  downloadFile(fileUrl, fileName, {
    errormsg: $t("sparePartsWearingParts.downloadFailure")
  }).catch(() => {
    console.error($t("sparePartsWearingParts.downloadFailure"))
    createMessage($t("sparePartsWearingParts.downloadFailure"), "error")
  })
}

// 操作事件
const handleAction = async (command, row) => {
  switch (command) {
    case "preView":
      filePreview(row)
      break
    case "edit":
      handlerEdit(row)
      break
    case "edit":
      handlerEdit(row)
      break
    case "downLoad":
      handlerDownLoad(row)
      break
    case "delete":
      handlerDel(row.id)
      break
    default:
      break
  }
}

function filePreview(file) {
  if (file.fileContentType == "mp4") {
    handleVidioPlay(file)
  } else if (
    file.fileContentType == "png" ||
    file.fileContentType == "jpg" ||
    file.fileContentType == "webp"
  ) {
    data.previewImgOptions.imgUrl = file.fileUrl
    data.previewImgOptions.show = true
  } else if (
    file.fileContentType == "xls" ||
    file.fileContentType == "xlsx"
  ) {
    //处理excel 内容含图片的提示  不含的给预览
    checkExcelHasImg(file?.fileUrl).then(check => {
      if (check) {
        // 提示用户文件含有图片，请下载查看
        createMessage($t("jobDirection.hasImgPlsDown"), "error")
      } else {
        isPreviewFileShow.value = true
        curFile.value.fileType = file?.fileContentType
        curFile.value.filePath = file?.fileUrl
      }
    }).catch((Error) => {
      createMessage(Error, "error")
    })

  } else {
    isPreviewFileShow.value = true
    curFile.value.fileType = file?.fileContentType
    curFile.value.filePath = file?.fileUrl
  }
}

// 视图还是列表展示
const isViewShow = ref(true)

// 是否开启视频弹框
let isVideoDialogShow = ref(false)

let checkOpt = ref({
  title: "",
  url: "",
})

function select(tab) {
  activeHead.value = tab.id
  getList(params.value)
}

const handleView = val => {
  if (val === 0) {
    isViewShow.value = true
  } else {
    isViewShow.value = false
  }
}

// 点击播放
const handleVidioPlay = item => {
  checkOpt.value.title = item?.fileName
  checkOpt.value.url = item?.fileUrl
  isVideoDialogShow.value = true
}

// 关闭播放
const closeVideo = () => {
  isVideoDialogShow.value = false
  checkOpt.value.title = ""
  checkOpt.value.url = ""
}

// 关闭预览
const closePreviewFile = () => {
  closeVideo()
  isPreviewFileShow.value = false
  curFile.value.filePath = ""
  curFile.value.fileType = ""
}

// 关闭图片预览
const closePreviewImgDialog = () => {
  data.previewImgOptions.imgUrl = ""
  data.previewImgOptions.show = false
}

// 查询
const handleCheck = async () => {
  data.page.pageIndex = 1
  getList(params.value)
}

const handleReset = () => {
  data.form = {
    fileName: "", // 文件名称
    fileDesc: "", // 文件描述
    fileContentType: "", // 文件格式
  }
  handleCheck()
}

const handleAddFile = () => {
  data.options = {
    show: true,
    type: "add", // 用于判断是编辑还是删除 add edit
    curFile: null,
  }
}

// 编辑
const handlerEdit = async item => {
  data.options = {
    show: true,
    type: "edit", // 用于判断是编辑还是删除 add edit
    curFile: item,
  }
}

// 删除
const handlerDel = async item => {
  deleteFun([item])
}

// 视图每页显示个数事件
const handleSizeChange = size => {
  data.page.pageSize = size

  getList(params.value)
}

// 视图页数事件
const handleCurrentChange = index => {
  data.page.pageIndex = index
  currentPageView.value = index
  getList(params.value)
}

function isVideo(file) {
  return ["mp4", "wav", "flv"].includes(file)
}
function isImage(file) {
  return ["png", "jpg", "webp"].includes(file)
}
function isWdord(file) {
  return ["docx", "txt", "doc"].includes(file)
}
function isExcel(file) {
  return ["xlsx", "xls"].includes(file)
}
function isPdf(file) {
  return ["pdf"].includes(file)
}

// 关联易损件详情
const isDetailShow = ref(false)
const currentDetailItem = ref({})
const handleDetail = item => {
  isDetailShow.value = true
  currentDetailItem.value = item
}
const handleCloseDetailDialog = () => {
  isDetailShow.value = false
}
const handleConfirmDetail = () => {
  getList(params.value)
}

// 保存弹框
const onSure = async options => {
  activeHead.value = options?.activeTab
  await handleCheck()
  closeDialog()
}
// 关闭弹框
const closeDialog = () => {
  data.options = {
    show: false,
    type: "", // 用于判断是编辑还是删除 add edit
    curFile: null,
  }
}

onMounted(() => {
  handleCheck()
})




const { form, options, previewImgOptions } = toRefs(data)
</script>
<style lang="scss" scoped>
.job {
  width: 100%;
  height: 100%;
  background-color: var(--front-layout-background);

  .tab-head {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-start;
    color: var(--g-font-color);
    font-size: 16px;
    align-items: center;
    border-bottom: 1px solid rgba(0, 132, 254, 0.75);
    padding: 0 10px;

    .head-item {
      .m-item {
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        min-width: 100px;
        padding: 0 20px;
        text-align: center;
        cursor: pointer;
      }

      .t-active {
        color: #fff;
        background-color: #0048ff;
      }
    }
  }

  .tab-body {
    width: 100%;
    height: calc(100% - 50px);

    .search {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 50px;
      padding: 0 10px;
      margin-top: 10px;

      .inp {
        width: 200px;
        margin-right: 10px;
      }
    }

    .cont {
      position: relative;
      height: calc(100% - 60px);

      .l-v {
        position: absolute;
        bottom: 4px;
        left: 10px;
        display: flex;
        height: 50px;
        justify-content: space-between;
        align-items: center;

        .l-btn {
          width: 84px;
          height: 32px;
          line-height: 32px;
          background: var(--g-background-color);
          border: 1px solid var(--tab-border-color);
          text-align: center;
          cursor: pointer;
        }

        .active {
          background: #0048ff;
          color: var(--g-font-color);
        }
      }

      .cont-box {
        height: 100%;

        .view {
          width: 100%;
          height: 100%;

          .body {
            height: calc(100% - 40px);
            padding: 10px;
            display: flex;
            // justify-content: space-between;
            flex-wrap: wrap;
            overflow: scroll;

            .vidio-list {
              margin-right: 20px;
              margin-bottom: 20px;
              width: 370px;

              .file-cont {
                position: relative;
                margin: 0 auto;

                .mask {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  background-color: rgba(0, 0, 0, 0.8);

                  font-size: 20px;
                  cursor: pointer;
                  display: none;

                  .preview {
                    color: #fff;
                  }
                }

                &:hover {
                  .mask {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  }
                }

                .v-img {
                  width: 370px;
                  height: 180px;
                }

                .oth-img {
                  width: 160px;
                  height: 180px;
                }

                .img-img {
                  width: 370px;
                  height: 180px;
                }
              }

              .file-name {
                height: 22px;
                line-height: 22px;
                font-size: 16px;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }

          .foot {
            padding: 0 10px;
            height: 40px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
          }
        }

        .list {
          height: 100%;
          padding: 0 10px;

          .list-box {
            display: flex;
            flex-direction: column;
            height: 100%;
          }
        }
      }
    }
  }
}

.fileBox {
  display: flex;
  align-items: center;

  img {
    width: 30px;
    height: 30px;
    margin-right: 10px;
  }
}
</style>
