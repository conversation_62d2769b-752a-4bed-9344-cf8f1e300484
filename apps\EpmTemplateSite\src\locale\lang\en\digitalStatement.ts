export default {
  digitalStatement: {
    productionStatistics: "Production Statistics",
    equipmentName: "Equipment Name",
    productModel: "Product Model",
    date: "Date",
    shift: "Shift",
    resetStatus: "Reset",
    alarmStatus: "Alarm",
    initializationStatus: "Initialization",
    runningStatus: "Running",
    downtimeStatus: "Shutdown",
    yield: "Production output",
    startTime: "Start Time",
    endTime: "End Time",
    selectShift: "Select Shift",
    dayShift: "DayShift",
    nightShift: "NightShift",
    query: "Query",
    reset: "Reset",
    export: "Export",
    equipmentStatus: "Equipment Status",
    statusDuration: "Cumulative status duration",
    statusCount: "Cumulative status count",
    totalFaultTimeUnit: "Total Fault Time",
    totalFaultTime: "Total Fault Time",
    totalAlarmTime: "Total Alarm Time",
    faultAnalysis: "Fault Analysis",
    faultHistory: "Fault History",
    faultCount: "Fault Count",
    faultDuration: "Fault Duration",
    monthly: "Monthly",
    quarterly: "Quarterly",
    yearly: "Yearly",
    noData: "No Data",
    minute: "minute(s)",
    faultRate: "Fault Rate",
    // 新增报警相关字段
    alarmAnalysis: "Alarm Analysis",
    alarmHistory: "Alarm History",
    alarmCount: "Alarm Count",
    alarmDuration: "Alarm Duration",
    alarmTime: "Alarm Time",
    alarmType: "Alarm Type",
    alarmCode: "Alarm Code",
    alarmDescription: "Alarm Description",
    maintenanceDuration: "Maintenance Duration",
    timeWarningInfo: "The time range cannot exceed 7 days",
    queryTableWarningInfo: "There was an error in querying the table data",
  },
}
