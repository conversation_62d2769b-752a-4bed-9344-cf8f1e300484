export default {
  monitor: {
    moduleInformation: "Module info (all default)",
    currentAlarms: "Current alarms",
    equipmentAlarm: "Equipment alarm",
    total: "Total",
    items: "items",
    viewReason: "View cause",
    locateToModule: "Navigate to module",
    alarmTime: "Alarm time:",
    totalDuration: "Total time:",
    faultGuide: "Fault guide",
    checkAvailableSpareParts: "View available spares",
    fillOutMaintenanceWorkOrder: "Submit maintenance work order",
    expand: "Expand",
    collapse: "Collapse",
    equipmentPerformance: "Equipment performance",
    productionStatistics: "Production statistics",
    alarmRanking: "Alarm ranking",
    equipmentMaintenance: "Equipment maintenance",
    productionAlarm: "Production alarm",
    initializationTime: "Initialization duration:",
    runningTime: "Running time:",
    resetTime: "Reset duration:",
    downtime: "Shutdown duration:",
    alarmContinueTime: "Alarm duration:",
    unitMinutes: "(Unit: min)",
    initialization: "Initialization",
    running: "Running",
    reset: "Reset",
    downtimeStatus: "Shutdown",
    alarmStatus: "Alarm",
    equipmentOee: "Equipment OEE",
    timeUtilizationRate: "Time utilization rate",
    performanceUtilizationRate: "Performance utilization rate",
    daysUntilNextMaintenance: "Time until next maintenance:",
    maintenanceDate: "Maintenance date:",
    days: "days",
    none: "None",
    wearablePartsStatusMonitoring: "Wear parts status monitoring",
    remainingDowntimeDistance: "Remaining stop distance (m)",
    laser1: "Laser1",
    laser2: "Laser2",
    theLastShift: "The last shift",
    currentShift: "Current shift",
  },
}
