import { ElMessage } from "element-plus"

function listenProportion(el, minP = 1, maxP = 2) {
  let { width, height } = el.getBoundingClientRect()

  let p = width / height
  if (minP & (p < minP) || maxP & (p > maxP)) {
    ElMessage({
      message: "检测到你的窗口显示比例异常，请手动最大化窗口",
      type: "warning",
      showClose: true,
      grouping: true,
      duration: 1000,
    })
  }
}

export default listenProportion
