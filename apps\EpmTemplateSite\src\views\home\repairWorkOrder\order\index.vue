<template>
  <div class="order">
    <el-form :inline="true" class="search">
      <el-form-item :label="$t('common.time')">
        <el-date-picker
          v-model="form.time"
          type="datetimerange"
          :start-placeholder="$t('common.startTime')"
          end-placeholder="$t('common.endTime')"
          :disabled-date="disabledDate"
          :range-separator="$t('common.to')"
          :clearable="false"
          :shortcuts="setShortcuts('YYYY-MM-DD HH:mm:ss')"
          :default-time="defaultTime"
        />
      </el-form-item>
      <el-form-item :label="$t('repairWorkOrder.faultDescription')">
        <el-input
          v-model="form.keyword"
          :placeholder="$t('repairWorkOrder.faultDescription')"
          clearable
          class="search-form-input"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">{{
          $t("common.queryText")
        }}</el-button>
        <el-button type="primary" @click="reset">{{
          $t("common.resetText")
        }}</el-button>
        <el-button type="primary" :loading="downloading" @click="download">
          <a :href="exportUrl" download>{{ $t("common.export") }}</a>
          <el-icon class="el-icon--right"> <Share /> </el-icon>
        </el-button>
        <el-button type="primary" @click="addOrder"
          >{{ $t("repairWorkOrder.addOrder") }}
          <el-icon class="el-icon--right"><Plus /></el-icon>
        </el-button>
      </el-form-item>
    </el-form>

    <div class="table">
      <HmxTable
        :table-data="tableData"
        :options="tableOptions"
        :columns="tableColumn"
        @size-change="handlerPageSize"
        @current-change="handlerPageIndex"
      >
        <template #faultType="{ row }">
          {{ faultType[row.faultType] }}
        </template>
        <template #state="{ row }">
          <div class="state">
            <span :class="['dot', row.isProcessed ? 'ok' : 'ng']"></span>
            {{
              row.isProcessed
                ? $t("echarts.title.processed")
                : $t("echarts.title.pending")
            }}
          </div>
        </template>
        <template #isQuality="{ row }">
          {{ row.isQuality ? $t("common.yes") : $t("common.no") }}
        </template>
        <template #isVersion="{ row }">
          {{ row.isVersion ? $t("common.yes") : $t("common.no") }}
        </template>
        <template #opera="{ row }">
          <div class="btns">
            <template v-if="row.isProcessed">
              <el-button type="primary" text @click="handleEdit(row, 'look')">{{
                $t("common.view")
              }}</el-button>
            </template>
            <template v-else>
              <el-button
                type="primary"
                text
                @click="handleEdit(row, 'edit')"
                v-if="!row.isProcessed"
                >{{ $t("common.edit") }}</el-button
              >
              <el-button
                type="primary"
                text
                @click="handleEdit(row, 'handle')"
                >{{ $t("repairWorkOrder.handleOrder") }}</el-button
              >
            </template>
          </div>
        </template>
      </HmxTable>
    </div>
    <AddOrder
      :addShow="addShow"
      :orderOpt="orderOpt"
      @closeDialog="closeDialog"
    />
  </div>
</template>
<script setup>
import dayjs from "dayjs"
import { reactive, computed, ref, onMounted } from "vue"
import HmxTable from "@/components/hmx-table/index.vue"
import AddOrder from "./addOrder.vue"
import { formatTime, setShortcuts } from "@/utils"
import { getWorkOrder } from "@/api/front/repairOrder.js"
import { config } from "@/config"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()
let faultType = [
  $t("repairWorkOrder.options.shutdown"),
  $t("repairWorkOrder.options.noShutdown"),
  $t("repairWorkOrder.options.speedReduction"),
  $t("repairWorkOrder.options.other"),
]
let tableColumn = [
  {
    type: "index",
    label: "No.",
    align: "center",
    fixed: "left",
    width: "50",
  },
  {
    prop: "reportTime",
    label: $t("repairWorkOrder.reportTime"),
    width: "170",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "devName",
    label: $t("repairWorkOrder.equipmentName"),
    width: "120",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "faultType",
    label: $t("repairWorkOrder.faultType"),
    align: "center",
    width: "100",
    slot: "faultType",
    showOverflowTooltip: true,
  },
  {
    prop: "state",
    label: $t("repairWorkOrder.status"),
    slot: "state",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "faultPhoenomenon",
    label: $t("repairWorkOrder.faultDescription"),
    align: "center",
    width: "200",
    showOverflowTooltip: true,
  },

  {
    prop: "faultReason",
    label: $t("repairWorkOrder.faultReason"),
    align: "center",
    width: "200",
    showOverflowTooltip: true,
  },
  {
    prop: "faultSolution",
    label: $t("repairWorkOrder.handlingMethod"),
    align: "center",
    width: "200",
    showOverflowTooltip: true,
  },
  {
    prop: "isQuality",
    label: $t("repairWorkOrder.causeAnalysis.relatedToProductQuality"),
    width: "120",
    align: "center",
    slot: "isQuality",
    showOverflowTooltip: true,
  },
  {
    prop: "isVersion",
    label: $t("repairWorkOrder.causeAnalysis.relatedToSoftwareVersion"),
    width: "120",
    align: "center",
    slot: "isVersion",
    showOverflowTooltip: true,
  },
  {
    prop: "repairEndTime",
    label: $t("repairWorkOrder.repairTime"),
    align: "center",
    width: "170",
    showOverflowTooltip: true,
  },
  {
    width: "150",
    label: $t("common.operate"),
    fixed: "right",
    align: "center",
    slot: "opera",
  },
]
// 新增默认时间配置
const defaultTime = [
  new Date(2000, 1, 1, 0, 0, 0), // 起始时间默认00:00:00
  new Date(2000, 1, 1, 23, 59, 59), // 结束时间默认23:59:59
]
let initTime = [dayjs().subtract(7, "day").startOf("day"), dayjs().endOf("day")]
let form = reactive({
  time: initTime.map(t => t.toDate()),
  keyword: "",
})

let tableData = ref([])
let total = ref(0)
let loading = ref(false)
let downloading = ref(false)
let page = reactive({
  pageIndex: 1,
  pageSize: 10,
})

let addShow = ref(false)
let orderOpt = reactive({
  type: "add",
  data: {},
})
let exportUrl = computed(() => {
  const { base_url } = config
  const PATH_URL = base_url[process.env.VUE_APP_BASEPATH]
  const baseUrl = `${PATH_URL}${base_url.homeServerPrefix}`
  let startTime = formatTime(form.time[0])
  let endTime = formatTime(form.time[1])
  return `${baseUrl}/WorkOrder/export?startTime=${startTime}&endTime=${endTime}&keyword=${form.keyword}`
})
const tableOptions = computed(() => {
  return {
    loading: loading.value,
    showPagination: true,
    paginationPosition: "right",
    border: true,
    paginationConfig: {
      total: total.value,
      currentPage: page.pageIndex,
      pageSize: page.pageSize,
    },
  }
})
const params = computed(() => {
  let startTime = formatTime(form.time[0])
  let endTime = formatTime(form.time[1])
  return {
    startTime,
    endTime,
    keyword: form.keyword,
    ...page,
  }
})

onMounted(() => {
  search()
})

const handleEdit = (row, command) => {
  orderOpt.type = command
  orderOpt.data = row
  addShow.value = true
}
function handlerPageSize(pageSize) {
  page.pageSize = pageSize
  page.pageIndex = 1
  getTable(params.value)
}
// 表格页数改变
function handlerPageIndex(pageIndex) {
  page.pageIndex = pageIndex
  getTable(params.value)
}

function disabledDate(time) {
  return time.getTime() > new Date(new Date().getTime())
}

function search() {
  page.pageIndex = 1
  getTable()
}

function reset() {
  form.time = initTime
  form.keyword = ""
  search()
}

async function getTable() {
  tableData.value = []
  let res = await getWorkOrder(params.value)
  tableData.value = res?.items ?? []
  total.value = res?.totalCount ?? 0
}

function addOrder() {
  orderOpt.type = "add"
  addShow.value = true
}

function closeDialog(val) {
  addShow.value = false
  val && reset()
}

function download() {
  downloading.value = true
  let t = setTimeout(() => {
    clearTimeout(t)
    downloading.value = false
  }, 1000)
}
</script>
<style scoped lang="scss">
.order {
  width: 100%;
  height: 100%;
  padding: 0 10px;
  .search {
    height: 50px;
    a {
      color: #fff;
      text-decoration: none;
    }
  }

  .table {
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;

    .state {
      display: flex;
      align-items: center;
      .dot {
        width: 10px;
        height: 10px;
        margin-right: 5px;
        border-radius: 50%;
      }
      .ok {
        background-color: #4eeea1;
      }
      .ng {
        background-color: #ee9d4e;
      }
    }
  }
}
</style>
