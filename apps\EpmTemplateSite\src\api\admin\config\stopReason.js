import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 * 生产管理
 */
// 获取停机时长
export function getDeviceStopTime() {
  return request.get({
    url: `${homeServerPrefix}/StopReason/GetDeviceStopTime`,
  })
}

// 设置停机时长
export function setDeviceStopTime(data) {
  return request.post({
    url: `${homeServerPrefix}/StopReason/SetDeviceStopTime?time=${data}`,
  })
}

// 获取停机原因列表
export function getReasonList() {
  return request.get({
    url: `${homeServerPrefix}/StopReason/GetList`,
  })
}

// 停机原因上报
export function setStopReason(data) {
  return request.post({
    url: `${homeServerPrefix}/StopReason/StopReasonInput`,
    data,
  })
}
