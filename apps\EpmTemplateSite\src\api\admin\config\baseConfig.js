import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 * 系统基础信息配置
 */
// 获取系统名称与logo
export function getSysNameAndLogo() {
  return request.get({
    url: `${homeServerPrefix}/BaseSysConfig/GetSysNameAndLogo`,
  })
}

// 上传logo图片
export function upLoadHomeLogo(data) {
  return request.post({
    url: `${homeServerPrefix}/BaseSysConfig/UpLoadHomeLogo`,
    headers: { "Content-Type": "multipart/form-data" },
    data,
  })
}

export function getLogo() {
  return request.get({
    url: `${homeServerPrefix}/BaseSysConfig/logo`,
    headers: { "Content-Type": "application/x-download" },
    responseType: "blob",
  })
}

// 更新名称和图片
export function modifySysNameAndLogo(data) {
  return request.post({
    url: `${homeServerPrefix}/BaseSysConfig/ModifySysNameAndLogo`,
    data,
  })
}

// 获取数据存储周期
export function getDataStorageCycle() {
  return request.get({
    url: `${homeServerPrefix}/BaseSysConfig/GetDataStorageCycle`,
  })
}

// 更新数据存储周期
export function modifyDataStorageCycle(data) {
  return request.post({
    url: `${homeServerPrefix}/BaseSysConfig/ModifyDataStorageCycle`,
    data,
  })
}
