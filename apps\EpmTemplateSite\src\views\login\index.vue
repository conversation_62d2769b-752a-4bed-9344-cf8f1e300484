<template>
  <div class="login">
    <img class="logo" src="@/assets/images/common/new-logo.png" />
    <el-card class="login-card" shadow="always">
      <div class="login-title">登录 Login</div>
      <ul class="module-switch">
        <li
          @click="changeModule('cn')"
          :class="{ 'module-active': tabIndex == 'cn' }"
        >
          中文用户
        </li>
        <li
          @click="changeModule('en')"
          :class="{ 'module-active': tabIndex == 'en' }"
        >
          English user
        </li>
      </ul>
      <el-form
        v-show="!isCard"
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        class="demo-ruleForm"
        status-icon
      >
        <el-form-item prop="userName">
          <el-input
            v-model="ruleForm.userName"
            size="large"
            class="login-input"
            :placeholder="languageConfig[tabIndex].user"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="pwdCover"
            type="text"
            size="large"
            name="pwd"
            id="pwd"
            class="login-input"
            @input="setPassword"
            :placeholder="languageConfig[tabIndex].password"
          />
        </el-form-item>
      </el-form>
      <div v-show="!isCard" class="btn-box">
        <div class="login-btn" @click="submitForm(ruleFormRef)">
          {{ languageConfig[tabIndex].login }}
        </div>
      </div>
      <div v-show="isCard" class="login-type">
        <img
          :src="require('@/assets/images/common/n-Card.png')"
          alt=""
          class="c-img"
          v-show="tabIndex === 'cn'"
        />
        <img
          :src="require('@/assets/images/common/Card2.png')"
          alt=""
          class="c-img"
          v-show="tabIndex === 'en'"
        />
      </div>
      <el-divider content-position="center"
        ><span class="other-login">{{
          languageConfig[tabIndex].otherLogin
        }}</span></el-divider
      >
      <div class="tabs">
        <el-button text @click="isCard = !isCard">{{
          isCard
            ? languageConfig[tabIndex].loginByAccount
            : languageConfig[tabIndex].loginByCard
        }}</el-button>
        <div class="h-line"></div>
        <el-button text @click="login(touristParams)">{{
          languageConfig[tabIndex].loginByVistor
        }}</el-button>
      </div>
    </el-card>
    <input
      ref="cardRef"
      v-model="cardId"
      type="text"
      placeholder="卡号信息"
      class="c-inp"
      @keyup.enter="cardLogin(cardRef)"
      @blur="toFocus"
    />
  </div>
</template>

<script setup name="Login">
import { ref } from "vue"
import useLoginView from "@/core/useCfCore/useLoginView"
import { languageConfig } from "./config"

const ruleFormRef = ref()
const cardRef = ref()

const {
  ruleForm,
  rules,
  isCard,
  cardId,
  touristParams,
  login,
  toFocus,
  submitForm,
  cardLogin,
  pwdCover,
  setPassword,
} = useLoginView(ruleFormRef, cardRef)

let tabIndex = ref("cn")

const changeModule = i => {
  tabIndex.value = i
}
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: url("~@/assets/images/common/login-bg.png");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  .logo {
    width: 300px;
  }
  .login-card {
    width: 590px;
    min-height: 400px;
    padding: 20px;
    border-radius: 20px;
    // border: 1px solid #f1f1f1;
    border: none;
    background: #fff;
    :deep(.el-card__body) {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .module-switch {
      display: flex;
      margin-bottom: 40px;
      > li {
        width: 220px;
        height: 40px;
        font-size: 20px;
        line-height: 40px;
        font-weight: bold;
        text-align: center;
        cursor: pointer;
        color: #000;
      }
      .module-active {
        border-bottom: 4px solid #0084fe;
        color: #0084fe;
      }
    }
    .login-title {
      width: 100%;
      padding: 10px 0;
      font-weight: bold;
      font-size: 24px;
      color: #000;
    }
    .login-input {
      width: 400px;
      height: 48px;
      color: #000;
      --el-input-border-color: #dcdfe6;
      :deep(.el-input__inner) {
        color: #000;
      }
    }
    .login-btn {
      width: 400px;
      height: 44px;
      line-height: 44px;
      background: #0084fe;
      border-radius: 3px 3px 3px 3px;
      color: #ffffff;
      font-size: 20px;
      text-align: center;
      margin-bottom: 40px;
      cursor: pointer;
    }
    :deep(.el-form-item) {
      display: flex;
      align-items: center;
      padding-bottom: 10px;
      .el-input__icon {
        font-size: 18px;
        color: #333;
      }
    }
    .login-type {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .c-img {
        // width: 150px;
        margin-bottom: 40px;
      }
      .process {
        > div {
          text-align: center;
        }
      }
    }
    .tabs {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
      :deep(.el-button) {
        font-family: Inter-Regular, Inter;
        border-radius: 0;
        border-bottom: 1px solid transparent;
        font-size: 22px;
        color: #000;
      }
    }
    .other-login {
      color: #797979;
      font-size: 20px;
    }
    .h-line {
      width: 1px;
      height: 30px;
      background: #0084fe;
      margin: 0 30px;
    }
    .el-divider--horizontal {
      margin: 20px 0 30px;
    }
    .btn-box {
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      :deep(.el-button) {
        margin: 0 16px;
        padding: 18px 24px;
        font-size: 18px;
      }
    }
  }
  .c-inp {
    position: fixed;
    left: -1000000px;
    width: 300px;
    height: 40px;
    border: 1px solid rgb(0, 188, 212);
    border-radius: 6px;
    padding-left: 20px;
  }
  :deep(.el-button--primary) {
    background: #01c2ff;
    color: #fff;
  }
  :deep(.el-input__wrapper) {
    background: #fff !important;
  }
}
</style>
