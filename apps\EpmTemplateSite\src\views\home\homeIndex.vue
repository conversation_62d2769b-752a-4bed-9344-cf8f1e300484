<template>
  <div class="h-home" ref="homeRef">
    <!-- 3d -->
    <div class="module-box">
      <div class="header">
        <EquipmentStatus
          :data="equipmentStatusInfo.data"
          :option="equipmentStatusInfo.option"
        />
      </div>
      <div class="module">
        <!-- <iframe
          width="100%"
          height="100%"
          :src="moduleUrl"
          frameborder="0"
          ref="tdIframeRef"
          class="td-iframe"
          id="td-iframe"
        >
        </iframe> -->
        <BBLView
          ref="tdIframeRef"
          class="td-iframe"
          id="td-iframe"
          @model-ready="handleModelReady"
        />
        <div class="equip-status">
          <div class="module-info">
            <div class="selectedModule">
              <el-select
                v-model="viewValue"
                :placeholder="$t('monitor.moduleInformation')"
                size="small"
                @change="handleChange"
                popper-class="selectStyle"
                :fit-input-width="true"
                clearable
              >
                <template #prefix>
                  <el-icon><View /></el-icon>
                </template>
                <el-option
                  v-for="item in equipModuleList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <!-- 报警信息总数 -->
          <div class="alarm-count" @click="handleAlarmCountClick">
            <div class="icon">
              <img src="@/assets/images/common/Icon-Alarm.svg" alt="" />
            </div>
            <div class="text">
              {{ $t("monitor.currentAlarms") }}：<span class="red">
                {{ alarmLogs.length }}</span
              >{{ $t("monitor.items") }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="echart-content">
      <div class="panel">
        <!-- <div class="item">
          <box-side
            :boxTitle="$t('monitor.equipmentPerformance')"
            iconName="monitor"
          >
            <TensionMonitor :data="tensionMonitorInfo" />
          </box-side>
        </div> -->
        <div class="item">
          <box-side
            :boxTitle="$t('monitor.productionStatistics')"
            iconName="statistics"
          >
            <OutputStatistics :option="outputStatisticsInfo" />
          </box-side>
        </div>
        <div class="item">
          <box-side :boxTitle="$t('monitor.alarmRanking')" iconName="repair">
            <AlarmRanking :option="alarmRankingInfo" />
          </box-side>
        </div>
        <div class="item">
          <box-side
            :boxTitle="$t('monitor.equipmentMaintenance')"
            iconName="repair"
          >
            <EquipmentMaintenance :option="equipmentMaintenanceInfo" />
          </box-side>
        </div>
      </div>
    </div>

    <!-- 报警信息详情 -->
    <hmx-dialog
      :dialogTitle="$t('monitor.productionAlarm')"
      :isVisable="alarmDetailShow"
      dialogWidth="50%"
      @closeDialog="alarmDetailShow = false"
      customClass="alarm-dialog"
    >
      <template #icon>
        <img src="@/assets/images/box/log.svg" alt="" />
      </template>
      <div class="log-info">
        <log-detail v-if="alarmDetailShow" :logInfo="productionTableData" />
      </div>
    </hmx-dialog>
    <!-- 报警弹窗 -->
    <AlarmCountDialog
      v-model="alarmCountShow"
      :alarmList="alarmLogs"
      :moduleRef="tdIframeRef"
      @handlePosition="handlePosition"
    ></AlarmCountDialog>
    <!-- 故障备件弹窗 -->
    <FaultyDialog v-model="sparePartShow" :loading="spareLoading" />
  </div>
</template>
<script>
export default {
  name: "HomeIndex",
}
</script>
<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from "vue"
import { useStore } from "vuex"
import { triggerHomePush } from "@/api/front/home"
import listenProportion from "@/utils/listenproportion.js"
import boxSide from "./components/side-box.vue"
import TensionMonitor from "./components/TensionMonitor/index.vue"
import OutputStatistics from "./components/OutputStatistics/index.vue"
import AlarmRanking from "./components/AlarmRanking/index.vue"
import EquipmentMaintenance from "./components/EquipmentMaintenance/index.vue"
import EquipmentStatus from "./components/EquipmentStatus/index.vue"
import useEchartsData from "./hooks/useEchartsData"
import { useI18n } from "@xfe/locale"
import LogDetail from "./components/realTimeData/views/LogTableCard/LogDetail.vue"
import AlarmCountDialog from "./components/AlarmCountDialog/index.vue"
import HmxDialog from "@/components/hmx-dialog.vue"
import FaultyDialog from "./components/FaultySpareparts/index.vue"
import BBLView from "../BBLView.vue"

const store = useStore()
const isShowDataPanel = computed(() => store.getters["isShowDataPanel"])

// 切换产量产能/产量
const { t: $t } = useI18n()
const {
  productionTableData,
  alarmDetailShow,
  alarmCountShow,
  handlePosition,
  handleAlarmCountClick,
  alarmLogs,
  moduleUrl,
  tdIframeRef,
  sparePartShow,
  spareLoading,
  handleViewSparepart,
  tensionMonitorInfo,
  outputStatisticsInfo,
  alarmRankingInfo,
  equipmentMaintenanceInfo,
  equipmentStatusInfo,
} = useEchartsData()

// 3D信息对接
window.addEventListener(
  "message",
  async e => {
    const { params } = e.data
    switch (params?.type) {
      case 0:
        alarmCountShow.value = true
        break
      case 1:
        await handleViewSparepart(params.alarm_code)
        break
      case 2:
        await handleViewSparepart(params.alarm_code)
        break
    }
  },
  false
)

// 视角值
const viewValue = ref()
const handleChange = val => {
  if (val === "") {
    val = "root"
  }
  const iframe = document.getElementById("td-iframe")
  iframe.contentWindow.postMessage(
    {
      modelID: val,
    },
    "*"
  )
}

// 下拉设备列表
const equipModuleList = ref([
  { label: "视角", value: "" },
  { label: "负极放卷模组", value: "JE01_M01_A00" },
  { label: "正极放卷模组", value: "JE01_M02_A00" },
  { label: "下隔膜放卷模组", value: "JE01_M03_A00" },
  { label: "上隔膜放卷模组", value: "JE01_M04_A00" },
  { label: "负极自动换卷模组", value: "JE01_M05_A00" },
  { label: "正极自动换卷模组", value: "JE01_M06_A00" },
  { label: "负极放卷张力&毛刷除尘模组", value: "JE01_M07_A00" },
  {
    label: "正极保护胶测长&除尘毛刷&张力&纠偏模组",
    value: "JE01_M08_A00",
  },
  {
    label: "正极卷绕张力&主驱&纠偏模组",
    value: "JE01_M09_A00",
  },
  {
    label: "正极保护胶后缓存模组",
    value: "JE01_M10_A00",
  },
  {
    label: "贴拐角胶后缓存模组",
    value: "JE01_M10_B00",
  },
  {
    label: "负极风刀除尘&行进纠偏&卷绕主驱模组",
    value: "JE01_M12_A00",
  },
  {
    label: "贴拐角胶后负极行进张力&送料纠偏模组缓存模组",
    value: "JE01_M13_A00",
  },
  {
    label: "下隔膜张力&纠偏&测长&除铁&除静电模组",
    value: "JE01_M14_A00",
  },
  {
    label: "正极夹持送料纠偏模组",
    value: "JE01_M15_A00",
  },
  {
    label: "CCD相机模组",
    value: "JE01_M16_A00",
  },
  {
    label: "正极入料模组",
    value: "JE01_M18_A00",
  },
  {
    label: "正极入料夹棍模组",
    value: "JE01_M19_A00",
  },
  {
    label: "上隔膜入料模组",
    value: "JE01_M20_A00",
  },
  {
    label: "极片单卷模组",
    value: "JE01_M22_A00",
  },
  {
    label: "卷绕头模组",
    value: "JE01_M23_A00",
  },
  {
    label: "切隔膜模组",
    value: "JE01_M24_A00",
  },
  {
    label: "贴保护胶模组（下夹紧模块）",
    value: "JE01_M25_A00",
  },
  {
    label: "贴保护胶模组（备胶模块左）",
    value: "JE01_M26_A00",
  },
  {
    label: "贴保护胶模组（备胶模块右）",
    value: "JE01_M26_B00_Cy1",
  },
  {
    label: "贴保护胶模组（备胶模块右）",
    value: "JE01_M26_B00_Cy1",
  },
  {
    label: "贴保护胶模组（左贴胶模块）",
    value: "JE01_M28_A00",
  },
  {
    label: "贴保护胶模组（右贴胶模块）",
    value: "JE01_M29_A00",
  },
  {
    label: "贴拐角胶模组（左贴胶）",
    value: "JE01_M30_A00",
  },
  {
    label: "贴拐角胶模组（右贴胶）",
    value: "JE01_M31_A00",
  },
  {
    label: "贴拐角胶模组（按钮盒）",
    value: "JE01_M32_A00",
  },
  {
    label: "贴大面胶模组",
    value: "JE01_M33_A00",
  },
  {
    label: "下料&预压模组",
    value: "JE01_M34_A00",
  },
  {
    label: "出料热压模组",
    value: "JE01_M35_A00",
  },
  {
    label: "出料CCD拍照模组",
    value: "JE01_M17_A00",
  },
  {
    label: "出料皮带3模组",
    value: "JE01_M36_A00",
  },
  {
    label: "过辊模组",
    value: "JE01_M38_A00",
  },
  {
    label: "负极放卷",
    value: "JE01_M39_A00",
  },
  {
    label: "下隔膜放卷",
    value: "JE01_M39_B00",
  },
  {
    label: "正极放卷",
    value: "JE01_M39_C00",
  },
  {
    label: "上隔膜放卷",
    value: "JE01_M39_D00",
  },
])

// 界面伸缩检测
const homeRef = ref()

function listen() {
  listenProportion(homeRef.value, 1.6, 2)
}

const isFirstLoadHome = computed(() => store.getters["isFirstLoadHome"])
const handleModelReady = () => {
  if (isFirstLoadHome.value) {
    triggerHomePush()
    store.commit("SET_IS_FIRST_LOAD_HOME", false)
  }
}

onMounted(() => {
  window.addEventListener("resize", listen)
})

onUnmounted(() => {
  window.removeEventListener("resize", listen)
})
</script>
<style scoped lang="scss">
.h-home {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  .module-box {
    display: flex;
    flex-direction: column;
    flex: 1;
    z-index: 2;
    height: calc(100vh - 20px);
    margin-right: 30px;
    .header {
      width: 100%;
      height: 100px;
    }
    .module {
      position: relative;
      width: 100%;
      flex: 1;
      .td-iframe {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        .image {
          width: 100%;
          height: 80%;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .equip-status {
        display: flex;
        justify-content: space-between;
        position: absolute;
        top: 0;
        width: 100%;
        height: 50px;
        .selectedModule {
          width: 100%;
          :deep(.el-select) .el-input {
            height: 50px;
          }
        }
        .alarm-count {
          display: flex;
          align-items: center;
          padding: 5px 10px;
          z-index: 999;
          color: #fff;
          background-color: #000;
          border-radius: 50px;
          cursor: pointer;
          .icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            margin-top: -2px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .text {
            margin-left: 3px;
            font-size: 14px;
          }
        }
        .status {
          display: flex;
          align-items: center;
          justify-content: space-between;
          align-items: center;
          border: 1px solid #ccc;
          border-radius: 5px;
          color: var(--g-font-color);
          padding: 10px;
          &_type,
          &_result {
            display: flex;
            align-items: center;
          }
          > div {
            font-weight: bold;
          }
        }

        .abnormal-bg {
          background-color: #ffa7a7;
        }
        .abnormal-color {
          color: #ff0000;
        }
      }
    }
  }

  .echart-content {
    width: 420px;
    height: calc(100vh - 20px);
    display: flex;
    flex-direction: column;
    .mode {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 10px;
      border: 1px solid #004d79;
      border-radius: 10px;
      color: #004d79;
      font-weight: bold;
    }
    .equip {
      .oee {
        font-size: 20px;
        font-weight: bold;
      }
      .property {
        font-size: 16px;
      }
      .skyblue {
        color: #0091e4;
      }
    }
    // 修改panel容器样式
    .panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;

      .item {
        flex: 1;
      }
    }
  }
}
</style>
<style lang="scss">
.alarm-dialog {
  .el-dialog__header {
    background-color: #ee4e4e;
    margin: 0;
    .el-dialog__title {
      color: var(--g-font-color);
      font-weight: 600;
    }
    .el-dialog__headerbtn {
      .el-dialog__close {
        font-size: 24px;
        color: var(--g-font-color);
      }
    }
  }
  .el-dialog__body {
    background-color: rgba(0, 0, 0, 1);
  }
}
</style>
