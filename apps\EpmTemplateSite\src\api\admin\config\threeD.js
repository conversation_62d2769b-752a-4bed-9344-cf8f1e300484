import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

// 增加3D模型库
export function postOPSFilesList(data) {
  return request.post({
    url: `${homeServerPrefix}/OpsThreeDModuleDetail`,
    headers: { "Content-Type": "multipart/form-data" },
    data: data,
  })
}

// 修改3D模型库
export function putOPSFilesList(data, id) {
  return request.put({
    url: `${homeServerPrefix}/OpsThreeDModuleDetail/${id}`,
    headers: { "Content-Type": "multipart/form-data" },
    data: data,
  })
}

/**
 *  导出交互配置表
 */
export function exportExcel() {
  return request.get({
    url: `${homeServerPrefix}/OpsThreeDModuleDetail/export`,
    header: {
      headers: { "Content-Type": "application/x-download" },
    },
    responseType: "blob",
  })
}

export function getModuleInfoById(id) {
  return request.get({
    url: `${homeServerPrefix}/OpsThreeDModuleDetail/${id}`,
  })
}
