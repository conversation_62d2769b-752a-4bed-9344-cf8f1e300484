<template>
  <div class="count">共{{ alarmList.length }}条</div>
  <div class="log-detail">
    <el-scrollbar height="100%">
      <div v-for="(item, index) in alarmList" :key="index" class="log-content">
        <div class="alarm-code child">
          <div class="image">
            <img src="@/assets/images/box/icon-productAlarm.svg" alt="" />
          </div>
          <div class="text">
            电芯条码：{{ item?.alarmContent?.cellBarcode ?? "-" }}
          </div>
        </div>
        <div class="alarm-reason child">
          报警原因：{{ item?.alarmContent?.chinaName ?? "-" }}
          <template
            v-for="(v, index) in item?.alarmContent?.relationData"
            :key="index"
          >
            <div class="alarm-reason-content">
              <div class="name">{{ v.relationName }}：</div>
              <div class="value red">{{ v.value }}，</div>
              <div class="limit">
                上下限值为[
                <span class="red">{{ `${v.upLimit}，${v.lowerLimit}` }}</span> ]
              </div>
            </div>
          </template>
        </div>
        <div class="alarm-time child">
          报警时间：{{ item?.alarmTime ?? "-" }}
        </div>
      </div></el-scrollbar
    >
  </div>
</template>

<script setup name="ProDetail">
import { defineProps, computed } from "vue"

const props = defineProps({
  logInfo: {
    type: Array,
    default: () => [],
  },
})

const alarmList = computed(() => props.logInfo)
</script>

<style lang="scss" scoped>
.log-detail {
  background: #000000;
  color: #fff;
  height: 500px;

  .log-content {
    position: relative;
    margin: 20px;
    padding: 10px 0;
    background-color: #1e2023;
    border: 1px solid #ee9d4e;
    .alarm-code {
      width: 100%;
      display: flex;
      align-items: center;
      .image {
        width: 18px;
        height: 18px;
        margin-right: 10px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .alarm-reason {
      padding: 0 28px;
      &-content {
        display: flex;
        margin: 10px 0;
      }
    }
    .alarm-time {
      padding: 0 28px;
    }
  }
  .child {
    margin: 10px 20px;
  }

  .red {
    color: #ee4e4e;
  }
}
.count {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  height: 30px;
  padding: 0 28px;
  color: #fff;
  font-size: 20px;
}
</style>
