const baseGrid = {
  grid: {
    left: "1%",
    right: "1%",
    top: "2%",
    bottom: 0,
    containLabel: true,
  },
};
export const lineOptions = {
  ...baseGrid,
  xAxis: {
    type: "category",
    axisLine: {
      lineStyle: {
        color: "#fff", // 坐标轴线线的颜色
        type: "solid", // 坐标轴线线的类型（solid实线类型；dashed虚线类型；dotted点状类型）
        fontSize: "16",
      },
    },
    axisLabel: {
      color: "#fff",
    },
    data: [],
  },
  yAxis: {
    type: "value",
    axisLine: {
      show: true,
    },
    axisLabel: {
      color: "#fff", // 刻度标签文字的颜色
      fontSize: "12", // 文字字体大小
    },
  },
  series: [
    {
      data: [],
      type: "line",
    },
  ],
};

export const pieOptions = {
  ...baseGrid,
  tooltip: {
    trigger: "item",
  },
  series: [
    {
      name: "Access From",
      type: "pie",
      radius: "50%",
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: "rgba(0, 0, 0, 0.5)",
        },
      },
    },
  ],
};

export const barOptions = {
  ...baseGrid,
  xAxis: {
    type: "category",
    axisLine: {
      lineStyle: {
        color: "#fff", // 坐标轴线线的颜色
        type: "solid", // 坐标轴线线的类型（solid实线类型；dashed虚线类型；dotted点状类型）
        fontSize: "16",
      },
    },
    axisLabel: {
      color: "#fff",
    },
    data: [],
  },
  yAxis: {
    type: "value",
    axisLine: {
      show: true,
    },
    axisLabel: {
      color: "#fff", // 刻度标签文字的颜色
      fontSize: "12", // 文字字体大小
    },
  },
  series: [
    {
      data: [],
      type: "bar",
      showBackground: true,
      backgroundStyle: {
        color: "rgba(180, 180, 180, 0.2)",
      },
    },
  ],
};
