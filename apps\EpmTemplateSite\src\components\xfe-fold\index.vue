<template>
  <div class="fold-pannel" :style="positionStyle">
    <el-tooltip
      class="box-item"
      effect="dark"
      :content="isFold ? $t('card.title.spread') : $t('card.title.fold')"
      placement="bottom-end"
    >
      <img
        :class="{ isTrabsform: isFold }"
        class="img-icon"
        @click="handleFold"
        src="@/assets/images/common/Unfold.png"
      />
    </el-tooltip>
  </div>
</template>

<script setup>
import { defineEmits, unref, ref, defineProps } from "vue"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()

const emits = defineEmits(["handleFold"])

defineProps({
  positionStyle: {
    type: Object,
    default: () => {
      return {
        top: "16%",
        right: "24%",
      }
    },
  },
})

let isFold = ref(false)

const handleFold = () => {
  isFold.value = !isFold.value
  emits("handleFold", unref(isFold))
}
</script>
<style scoped lang="scss">
.fold-pannel {
  position: absolute;
  z-index: 99;
  cursor: pointer;
  transition: all 0.4s;
  .img-icon {
    width: 32px;
    height: 32px;
  }
}
.isTrabsform {
  transform: rotate(180deg);
}
</style>
