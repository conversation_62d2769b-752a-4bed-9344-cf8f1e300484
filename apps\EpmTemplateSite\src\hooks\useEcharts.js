import {
  nextTick,
  Ref,
  unref,
  watch,
  onActivated,
  onMounted,
  onUnmounted,
} from "vue"
import * as echarts from "echarts"
import "echarts-liquidfill"
import { debounce, cloneDeep } from "@xfe/utils"

function useEcharts(elRef, emits, props, baseOptions) {
  let chartInstance = null // 初始化实例

  // 初始化echarts
  function initCharts() {
    const el = unref(elRef)
    if (!el) {
      return
    }
    chartInstance = echarts.init(el)
  }

  // 设置/更新option
  function setOptions(options) {
    nextTick(() => {
      if (!chartInstance) {
        initCharts()
        if (!chartInstance) return
      }
      let option = cloneDeep(baseOptions)
      chartInstance.setOption(option)
      chartInstance.setOption(options)
      chartInstance.on("click", function (param) {
        emits("chart-click", param)
      })
    })
  }

  // 更新大小
  function resize() {
    if (chartInstance) {
      chartInstance.resize()
    }
  }

  onActivated(() => {
    resize()
  })

  // 显示加载状态
  function showLoading(loadingStyle) {
    if (!chartInstance) {
      initCharts()
    }
    chartInstance.showLoading(loadingStyle)
  }

  // 隐藏加载状态
  function hideLoading() {
    if (!chartInstance) {
      initCharts()
    }
    chartInstance.hideLoading()
  }

  // 获取实例
  function getInstance() {
    if (!chartInstance) {
      initCharts()
    }
    return chartInstance
  }

  const resizeFn = debounce(resize, 500)

  onMounted(async () => {
    await setOptions(props.option)
    // 自适应不同屏幕时改变图表尺寸
    window.addEventListener("resize", resizeFn)
  })

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose()
    }
    window.removeEventListener("resize", resizeFn)
  })

  watch(
    () => props.option,
    val => {
      nextTick(() => {
        const instance = getInstance()
        if (instance) {
          // 修改为保留图表状态的方式更新
          instance.setOption(
            {
              ...cloneDeep(baseOptions),
              ...val,
            },
            {
              notMerge: false,
              lazyUpdate: true, // 添加延迟更新配置
            }
          )
        }
      })
    },
    { deep: true }
  )

  return {
    resize,
    setOptions,
    getInstance,
    showLoading,
    hideLoading,
  }
}

export default useEcharts
