<template>
  <div class="vertical-tab">
    <div class="tab-left">
      <div
        v-for="tab in head"
        :key="tab.id"
        class="head-item"
        @click="select(tab)"
        v-env="tab.envTab || 'all'"
      >
        <div :class="['m-item', modelValue == tab.id ? 't-active' : '']">
          {{ tab.text }}
        </div>
      </div>
    </div>
    <div class="tab-right">
      <template v-if="keepAlive">
        <keep-alive>
          <component :is="activeComp" :exclude="exclude" />
        </keep-alive>
      </template>
      <template v-else>
        <component :is="activeComp" />
      </template>
    </div>
  </div>
</template>
<script setup>
import { computed } from "vue"

let props = defineProps({
  head: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: String,
    default: "",
  },
  keepAlive: {
    type: Boolean,
    default: false,
  },
  exclude: {
    type: [Array, String],
    default: () => [],
  },
})
let emit = defineEmits(["update:modelValue"])
let activeComp = computed(() => {
  let comp = ""
  props.head.some(t => {
    if (t.id == props.modelValue) {
      comp = t.component
    }
  })
  return comp
})

function select(tab) {
  emit("update:modelValue", tab.id)
}
</script>
<style lang="scss" scoped>
$bs-color: 0px 0px 6px 0px rgba(0, 0, 0, 0.1) inset;
.vertical-tab {
  width: 100%;
  height: 100%;
  display: flex;
  .tab-left {
    width: 15%;
    height: 50px;
    padding-top: 20px;
    .head-item {
      height: 60px;
      line-height: 60px;
      padding: 0 20px;
      font-size: 20px;
      .m-item {
        width: 70%;
        height: 40px;
        line-height: 40px;
        text-align: center;
        cursor: pointer;
        border-radius: 4px;
        background-color: #d9d9d9;
      }
      .t-active {
        color: #fff;
        background-color: #409eff;
      }
    }
  }
  .tab-right {
    flex: 1;
    height: 100%;
  }
}
</style>
