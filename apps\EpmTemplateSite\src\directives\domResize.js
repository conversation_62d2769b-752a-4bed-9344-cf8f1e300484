// 监听dom宽高变化
export default {
  mounted(el, binding) {
    // el为绑定的元素，binding为绑定给指令的对象
    let width = "";
    let height = "";
    function isReize() {
      const style = document.defaultView.getComputedStyle(el);
      if (width !== style.width || height !== style.height) {
        binding.value({ width: style.width, height: style.height }); // 关键(这传入的是函数,所以执行此函数)
      }
      width = style.width;
      height = style.height;
    }
    el.vueSetInterval = setInterval(isReize, 500);
  },
  // 元素卸载前也记得清理定时器并且移除监听事件
  beforeMount(el) {
    clearInterval(el.vueSetInterval);
  },
};
