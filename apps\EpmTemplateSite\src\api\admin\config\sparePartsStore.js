import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 *  导出交互配置表
 */
export function exportExcel() {
  return request.get({
    url: `${homeServerPrefix}/SpareParts/export`,
    header: {
      headers: { "Content-Type": "application/x-download" },
    },
    responseType: "blob",
  })
}

/**
 *  导入交互配置表
 */
export function importExcel(data) {
  return request.post({
    url: `${homeServerPrefix}/SpareParts/import`,
    headers: { "Content-Type": "multipart/form-data" },
    data,
  })
}

// 获取存放位置
export function getSparePartsTool() {
  return request.get({
    url: `${homeServerPrefix}/SpareParts/local`,
  })
}

// 获取机台名称
export function getSparePartsMachine() {
  return request.get({
    url: `${homeServerPrefix}/SpareParts/machine`,
  })
}

// 获取模组名称
export function getSparePartsModule() {
  return request.get({
    url: `${homeServerPrefix}/SpareParts/module`,
  })
}

// 获取报警码
export function getSparePartsWarnCode(params) {
  return request.get({
    url: `${homeServerPrefix}/SpareParts/code`,
    params
  })
}

// 获取备件库信息
export function getSpareParts(params) {
  return request.get({
    url: `${homeServerPrefix}/SpareParts/${params}`,
  })
}


// 获取所有未使用3D模组编码
export function getThreeD() {
  return request.get({
    url: `${homeServerPrefix}/VulnerablePartsLife/threeD`,
  })
}