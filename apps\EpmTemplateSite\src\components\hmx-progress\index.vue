<template>
  <!-- 假的进度条 -->
  <el-dialog
    :close-on-click-modal="false"
    :model-value="modelValue"
    :destroy-on-close="true"
    :append-to-body="true"
    :show-close="false"
    :width="400"
    class="hmx-progress"
    :modal="false"
  >
    <template #title>
      <div></div>
    </template>
    <div>
      <p class="tip-text">{{ $t("prompt.prompt_42") }}</p>
      <el-progress :percentage="progress" color="#0084FE" />
    </div>
  </el-dialog>
</template>

<script setup name="HMXProgress">
import { ref, onMounted, onUnmounted, watch } from "vue"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()

const props = defineProps({ modelValue: { type: Boolean, default: true } })

const progress = ref(0)
let timer = null

onMounted(() => {
  timer = setInterval(() => {
    if (progress.value < 81) {
      progress.value = progress.value + 10
    }
  }, 1000)
})
onUnmounted(() => {
  timer = null
})

watch(
  () => props.modelValue,
  v => {
    if (!v) {
      progress.value = 100
    }
  }
)
</script>
<style lang="scss">
.el-dialog.hmx-progress {
  --el-dialog-margin-top: 35vh;
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    background: #566674;
  }
}
</style>
<style lang="scss" scoped>
.tip-text {
  color: #fff;
  margin-bottom: 16px;
}
</style>
