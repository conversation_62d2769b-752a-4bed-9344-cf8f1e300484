<template>
  <div class="base">
    <div class="top">
      <div class="chart">
        <div class="header">
          <div class="left">
            <div class="title">
              {{
                name === $t("digitalStatement.faultAnalysis")
                  ? `${$t("digitalStatement.faultHistory")}Top10`
                  : `${$t("digitalStatement.alarmHistory")}Top10`
              }}
            </div>
            <div class="fault-tab">
              <el-radio-group
                v-model="currentSelectFault"
                @change="handleSelectFault"
              >
                <el-radio-button label="faultCount">{{
                  name === $t("digitalStatement.faultAnalysis")
                    ? $t("digitalStatement.faultCount")
                    : $t("digitalStatement.alarmCount")
                }}</el-radio-button>
                <el-radio-button label="faultTime">{{
                  name === $t("digitalStatement.faultAnalysis")
                    ? $t("digitalStatement.faultDuration")
                    : $t("digitalStatement.alarmDuration")
                }}</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div class="right">
            <div class="time-tab">
              <el-radio-group
                v-model="currentSelectTime"
                @change="handleSelectTime"
              >
                <el-radio-button :label="2">{{
                  $t("digitalStatement.monthly")
                }}</el-radio-button>
                <el-radio-button :label="1">{{
                  $t("digitalStatement.quarterly")
                }}</el-radio-button>
                <el-radio-button :label="0">{{
                  $t("digitalStatement.yearly")
                }}</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </div>
        <div class="content">
          <div class="fault-rank">
            <template v-for="(item, index) in currentRankOption" :key="index">
              <div class="box">
                <div v-if="isEmpty(item.option)" ref="barRef" class="no-data">
                  {{ $t("digitalStatement.noData") }}
                </div>
                <BarRank
                  v-else
                  class="rank"
                  :option="item.option"
                  :unit="unit"
                />
                <div class="title">
                  <div class="legend"></div>
                  <div class="text">{{ item.timeString }}</div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom">
      <el-form :inline="true" class="search">
        <el-form-item :label="$t('common.time')">
          <el-date-picker
            v-model="form.time"
            type="daterange"
            :start-placeholder="$t('digitalStatement.startTime')"
            :end-placeholder="$t('digitalStatement.endTime')"
            :disabled-date="disabledDate"
            :range-separator="$t('common.to')"
            :clearable="false"
            :shortcuts="setShortcuts()"
          />
        </el-form-item>
        <el-form-item :label="$t('digitalStatement.shift')">
          <el-select
            v-model="form.class"
            class="form-input"
            :placeholder="$t('digitalStatement.selectShift')"
            clearable
          >
            <el-option
              v-for="team of teamType"
              :label="team.label"
              :value="team.value"
              :key="team.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search" icon="search">{{
            $t("digitalStatement.query")
          }}</el-button>
          <el-button type="primary" @click="reset" icon="refresh">{{
            $t("digitalStatement.reset")
          }}</el-button>
        </el-form-item>
      </el-form>
      <div class="table">
        <HmxTable
          :table-data="tableData"
          :options="tableOptions"
          :columns="columns"
          @size-change="handlerPageSize"
          @current-change="handlerPageIndex"
        ></HmxTable>
      </div>
    </div>
  </div>
</template>
<script setup>
import dayjs from "dayjs"
import { reactive, computed, ref, onMounted } from "vue"
import BarRank from "./barRank.vue"
import HmxTable from "@/components/hmx-table/index.vue"
import {
  getTableChart,
  getFaultNumberRanking,
  getFaultDurationRanking,
  getAlarmNumberRanking,
  getAlarmDurationRanking,
} from "@/api/front/diaitalState.js"
import { formatTime, setShortcuts } from "@/utils"
import { config } from "@/config"
import { useI18n } from "@xfe/locale"

let props = defineProps({
  tableColumn: {
    type: Array,
    default: () => [],
  },
  url: {
    type: String,
    default: () => "",
  },
  exportUrl: String,
  slot: {
    type: Array,
    default: () => [],
  },
  name: String,
})

const { t: $t } = useI18n()

let currentSelectFault = ref("faultCount")
let currentSelectTime = ref(2)

let initTime = [
  dayjs().subtract(7, "day").format("YYYY-MM-DD"),
  dayjs().format("YYYY-MM-DD"),
]
let form = reactive({
  time: initTime,
  class: "",
})
let teamType = ref([])

let downLoading = ref(false)
let tableData = ref([])
let total = ref(0)
let loading = ref(false)
let page = reactive({
  pageIndex: 1,
  pageSize: 10,
})

let columns = computed(() => {
  return props.tableColumn.filter(item => item.show)
})

const tableOptions = computed(() => {
  return {
    loading: loading.value,
    showPagination: true,
    paginationPosition: "right",
    border: true,
    paginationConfig: {
      total: total.value,
      currentPage: page.pageIndex,
      pageSize: page.pageSize,
    },
  }
})
const params = computed(() => {
  let startDate = formatTime(form.time[0], "YYYY-MM-DD")
  let endDate = formatTime(form.time[1], "YYYY-MM-DD")
  return {
    startDate,
    endDate,
    teamTime: form.class,
    ...page,
  }
})

onMounted(() => {
  getTeam()
  search()
})

function handlerPageSize(pageSize) {
  page.pageSize = pageSize
  page.pageIndex = 1
  getTable(params.value)
}
// 表格页数改变
function handlerPageIndex(pageIndex) {
  page.pageIndex = pageIndex
  getTable(params.value)
}

function disabledDate(time) {
  return time.getTime() > new Date(new Date().getTime())
}

function search() {
  let diffTime = dayjs(form.time[1]).diff(dayjs(form.time[0]))
  if (diffTime > 7 * 24 * 60 * 60 * 1000) {
    return ElMessage.warning($t("digitalStatement.timeWarningInfo"))
  }
  page.pageIndex = 1
  getTable()
  getRankvalue()
}

async function getTeam() {
  teamType.value = [
    {
      value: 1,
      label: $t("digitalStatement.dayShift"),
    },
    {
      value: 2,
      label: $t("digitalStatement.nightShift"),
    },
  ]
}

function reset() {
  form.time = initTime
  form.class = ""
  search()
}

async function getTable() {
  tableData.value = []
  loading.value = true
  try {
    let res = await getTableChart(props.url, params.value)
    tableData.value = res.items ?? []
    total.value = res?.totalCount > 0 ? res?.totalCount : 0
    loading.value = false
  } catch (e) {
    loading.value = false
    console.error($t("digitalStatement.queryTableWarningInfo") + e)
  }
}

// 判断数据是否为空
const isEmpty = option => {
  // 检查 yAxisData（故障名称列表）和 seriesData（故障值列表）是否为空
  return !option?.yAxisData?.length || !option?.seriesData?.length
}

// 单位
const unit = computed(() =>
  currentSelectFault.value === "faultCount"
    ? $t("common.times")
    : $t("common.minutes")
)

let rankOption = reactive({
  timeList: [],
  countList: [],
})
let currentRankOption = computed(() => {
  return currentSelectFault.value == "faultTime"
    ? rankOption.timeList
    : rankOption.countList
})

function getRankvalue() {
  // 如果是故障次数就调用setCounts，否则调用setTimes
  if (currentSelectFault.value === "faultTime") {
    setTimes({ input: currentSelectTime.value })
  } else {
    setCounts({ input: currentSelectTime.value })
  }
}

async function setTimes(params) {
  let res = ""
  if (props.name == $t("digitalStatement.faultAnalysis")) {
    res = await getFaultDurationRanking(params)
  } else {
    res = await getAlarmDurationRanking(params)
  }
  rankOption.timeList = res?.map(item => {
    // 处理每个月份的数据
    let sortedData =
      props.name == $t("digitalStatement.faultAnalysis")
        ? item.faultTimeDtos.sort((a, b) => b.totalTime - a.totalTime)
        : item.alarmTimeDtos.sort((a, b) => b.totalTime - a.totalTime)
    return {
      timeString: item.timeString,
      option: {
        yAxisData:
          props.name == $t("digitalStatement.faultAnalysis")
            ? sortedData.map(d => d.faultName)
            : sortedData.map(d => d.alarmContent),
        seriesData: sortedData.map(d => d.totalTime),
      },
    }
  })
}

async function setCounts(params) {
  let res = ""
  if (props.name == $t("digitalStatement.faultAnalysis")) {
    res = await getFaultNumberRanking(params)
  } else {
    res = await getAlarmNumberRanking(params)
  }
  rankOption.countList = res?.map(item => {
    // 处理每个月份的数据
    let sortedData =
      props.name == $t("digitalStatement.faultAnalysis")
        ? item.faultCountDtos.sort((a, b) => b.totalCount - a.totalCount)
        : item.alarmCountDtos.sort((a, b) => b.totalCount - a.totalCount)
    return {
      timeString: item.timeString,
      option: {
        yAxisData:
          props.name == $t("digitalStatement.faultAnalysis")
            ? sortedData.map(d => d.faultName)
            : sortedData.map(d => d.alarmContent),
        seriesData: sortedData.map(d => d.totalCount),
      },
    }
  })
}

function handleSelectFault() {
  rankOption.timeList = []
  rankOption.countList = []
  getRankvalue()
}

function handleSelectTime() {
  rankOption.timeList = []
  rankOption.countList = []
  getRankvalue()
}
</script>
<style scoped lang="scss">
.base {
  width: 100%;
  height: 100%;
  padding: 0 10px;
  .top {
    width: 100%;
    height: 40%;
    .chart {
      height: 300px;
      display: flex;
      flex-direction: column;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 40px;
        .left {
          display: flex;
          align-items: center;
          .title {
            font-weight: bold;
            margin-right: 20px;
          }
        }
      }
      .content {
        width: 100%;
        height: calc(100% - 40px);
        .fault-rank {
          width: 100%;
          height: 100%;
          display: flex;
          overflow-x: auto;
          overflow-y: visible;
          .box {
            flex: 0 0 25%;
            height: 100%;
            min-width: 25%;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
          }
          .rank {
            flex: 1;
            width: 100%;
          }
          .no-data {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #909399; /* 浅灰色，与常见无数据提示风格一致 */
            font-size: 14px;
          }
          .title {
            display: flex;
            align-items: center;
            height: 20px;
            .legend {
              width: 25px;
              height: 12px;
              border-radius: 2px;
              background-color: #0084fe;
              margin-right: 20px;
            }
            .text {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .bottom {
    width: 100%;
    height: 60%;
    .search {
      height: 50px;
      a {
        color: #fff;
        text-decoration: none;
      }
    }
    .table {
      height: calc(100% - 50px);
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
