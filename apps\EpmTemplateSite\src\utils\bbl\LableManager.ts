import * as BABYLON from "babylonjs"
import * as G<PERSON> from "babylonjs-gui"
import { SceneManager } from "./SceneManager"
import { ModelLoader } from "./ModelLoader"
import { LanguageService } from "./LanguageService"

/**
 * 参数标签数据接口
 */
export interface ParameterLabelData {
  parameterCode: string // 参数代码
  parameterName: string // 参数名称
  parameterValue: string | number // 参数值
  unit?: string // 单位
  meshId?: string // 对应的mesh ID
  modelCode?: string // 模型代码
}

/**
 * 标签管理器
 * 负责在3D场景中显示参数标签
 */
export class LabelManager {
  private sceneManager: SceneManager
  private modelLoader: ModelLoader
  private languageService: LanguageService
  private advancedTexture: GUI.AdvancedDynamicTexture | null = null
  private parameterLabels: Map<string, GUI.Control> = new Map()

  // 参数代码到中文名称的映射
  private readonly parameterMapping = new Map<string, string>([
    ["fjzljc", "放卷张力检测"],
    ["fjzl", "放卷张力"],
    ["fjjj", "放卷卷径"],
    ["flgdfs", "废料箱管道风俗"],
    ["zgdfs", "主管道风速"],
    ["ssjA", "上收卷A/B张力"],
    ["csbccfs", "超声波除尘风速"],
    ["ssjzl", "上收卷张力检测"],
    ["ssjjj", "上收卷卷径"],
    ["xsjjj", "下收卷卷径"],
    ["xsjzljc", "下收卷张力检测"],
    ["xsjA", "下收卷A/B张力"],
    ["gdfs1", "激光1/2皮带管道风速"],
    ["ccfs1", "激光1/2侧面除尘风速"],
  ])

  // 参数名称到ModelCode的映射（根据您提供的映射关系）
  private readonly parameterNameToModelCode = new Map<string, string>([
    ["放卷张力(N)", "fjzl"],
    ["放卷卷径(mm)", "fjjj"],
    ["上收卷A张力(N)", "ssjA"],
    ["上收卷B张力(N)", "ssjA"],
    ["下收卷A张力(N)", "xsjA"],
    ["下收卷B张力(N)", "xsjA"],
    ["上收卷卷径(mm)", "ssjjj"],
    ["下收卷卷径(mm)", "xsjjj"],
    ["除尘主管道风速(m/s)", "zgdfs"],
    ["激光1侧面除尘风速(m/s)", "ccfs1"],
    ["激光2侧面除尘风速(m/s)", "ccfs1"],
    ["激光1皮带管道风速", "gdfs1"],
    ["激光2皮带管道风速", "gdfs1"],
    ["废料箱管道风速", "flgdfs"],
    ["超声波除尘风速", "csbccfs"],
    ["下收卷张力检测", "xsjzljc"],
    ["上收卷张力检测", "ssjzl"],
  ])

  /**
   * 构造函数
   * @param sceneManager 场景管理器实例
   * @param modelLoader 模型加载器实例
   */
  constructor(sceneManager: SceneManager, modelLoader: ModelLoader) {
    this.sceneManager = sceneManager
    this.modelLoader = modelLoader
    this.languageService = LanguageService.getInstance()
    this.initGUI()
  }

  /**
   * 初始化GUI系统
   */
  private initGUI(): void {
    const scene = this.sceneManager.getScene()
    if (!scene) return

    // 创建全屏GUI
    this.advancedTexture = GUI.AdvancedDynamicTexture.CreateFullscreenUI(
      "parameterLabelsUI",
      true,
      scene
    )
  }

  /**
   * 根据参数代码查找对应的mesh并添加标签
   * @param parameterData 参数数据列表
   */
  public updateParameterLabels(parameterData: ParameterLabelData[]): void {
    // 清除现有标签
    this.clearAllLabels()

    parameterData.forEach(data => {
      // 通过参数代码查找mesh
      const mesh = this.findMeshByParameterCode(data.parameterCode)
      if (mesh) {
        this.addParameterLabel(mesh, data)
      } else {
        console.warn(`未找到参数代码 ${data.parameterCode} 对应的mesh`)
      }
    })
  }

  /**
   * 根据真实参数数据更新标签
   * @param realTimeParametersData 真实参数数据
   */
  public updateParameterLabelsFromRealTimeData(realTimeParametersData: any[]): void {
    // 清除现有标签
    this.clearAllLabels()

    // 用于收集同一个ModelCode的多个参数
    const modelCodeGroups = new Map<string, Array<{name: string, value: string, unit: string}>>()

    realTimeParametersData.forEach(moduleData => {
      if (moduleData.Parameters && Array.isArray(moduleData.Parameters)) {
        moduleData.Parameters.forEach((param: any) => {
          const parameterName = param.Parameter
          const parameterValue = param.Value

          // 通过参数名称查找对应的ModelCode
          const modelCode = this.parameterNameToModelCode.get(parameterName)

          if (modelCode) {
            // 提取单位
            const unitMatch = parameterName.match(/\(([^)]+)\)/)
            const unit = unitMatch ? unitMatch[1] : ""

            // 获取参数名称（去掉单位部分）
            const cleanName = parameterName.replace(/\([^)]*\)/, "")

            // 按ModelCode分组
            if (!modelCodeGroups.has(modelCode)) {
              modelCodeGroups.set(modelCode, [])
            }

            modelCodeGroups.get(modelCode)!.push({
              name: cleanName,
              value: parameterValue,
              unit: unit
            })
          }
        })
      }
    })

    // 为每个ModelCode创建标签
    modelCodeGroups.forEach((params, modelCode) => {
      // 获取显示名称
      const displayName = this.parameterMapping.get(modelCode) || modelCode

      // 如果有多个参数，组合显示值
      let combinedValue: string
      let unit = ""

      if (params.length === 1) {
        // 单个参数直接显示
        combinedValue = params[0].value
        unit = params[0].unit
      } else {
        // 多个参数组合显示，格式：值1/值2
        combinedValue = params.map(p => p.value).join("/")
        // 使用第一个参数的单位（假设同组参数单位相同）
        unit = params[0].unit
      }

      const parameterLabel: ParameterLabelData = {
        parameterCode: modelCode,
        parameterName: displayName,
        parameterValue: combinedValue,
        unit: unit,
        modelCode: modelCode
      }

      // 添加标签
      const mesh = this.findMeshByParameterCode(modelCode)
      if (mesh) {
        this.addParameterLabel(mesh, parameterLabel)
      } else {
        console.warn(`未找到参数代码 ${modelCode} 对应的mesh`)
      }
    })
  }

  /**
   * 根据参数代码查找对应的mesh
   * @param parameterCode 参数代码
   * @returns 找到的mesh或null
   */
  private findMeshByParameterCode(
    parameterCode: string
  ): BABYLON.AbstractMesh | null {
    // 首先尝试直接通过参数代码查找mesh
    let mesh = this.modelLoader.findMeshById(parameterCode)

    if (!mesh) {
      // 如果直接查找失败，尝试通过名称查找
      const importedMeshes = this.modelLoader.getImportedMeshes()
      mesh =
        importedMeshes.find(
          m =>
            m.name.toLowerCase().includes(parameterCode.toLowerCase()) ||
            m.id.toLowerCase().includes(parameterCode.toLowerCase())
        ) || null
    }

    return mesh
  }

  /**
   * 为mesh添加参数标签
   * @param mesh 目标mesh
   * @param parameterData 参数数据
   */
  private addParameterLabel(
    mesh: BABYLON.AbstractMesh,
    parameterData: ParameterLabelData
  ): void {
    if (!this.advancedTexture) return

    // 创建标签文本内容（一行显示）
    const displayName = this.getParameterDisplayName(
      parameterData.parameterCode,
      parameterData.parameterName
    )
    const formattedValue = this.formatParameterValue(
      parameterData.parameterValue,
      parameterData.unit
    )
    const labelText = `${displayName}: ${formattedValue}`

    // 创建标签容器
    const labelContainer = new GUI.Container(
      `paramLabel_${parameterData.parameterCode}`
    )
    labelContainer.height = "40px"
    labelContainer.isPointerBlocker = false // 不阻挡鼠标事件

    // 根据文本内容计算宽度
    const textWidth = this.calculateTextWidth(labelText, 14)
    const estimatedWidth = Math.max(120, Math.min(350, textWidth + 32)) // 加上padding
    labelContainer.width = `${estimatedWidth}px`

    // 创建背景矩形
    const background = new GUI.Rectangle(
      `paramBg_${parameterData.parameterCode}`
    )
    background.width = "100%"
    background.height = "100%"
    background.cornerRadius = 6
    background.color = "#ffffff"
    background.thickness = 1
    background.background = "rgba(18, 19, 37, 0.9)"
    background.shadowColor = "rgba(0, 0, 0, 0.3)"
    background.shadowOffsetX = 2
    background.shadowOffsetY = 2
    background.shadowBlur = 4

    // 创建单行文本
    const textBlock = new GUI.TextBlock(
      `paramText_${parameterData.parameterCode}`,
      labelText
    )
    textBlock.color = "#e6e6e6"
    textBlock.fontSize = "14px"
    textBlock.fontFamily = "Microsoft YaHei, Arial, sans-serif"
    textBlock.textHorizontalAlignment = GUI.Control.HORIZONTAL_ALIGNMENT_CENTER
    textBlock.textVerticalAlignment = GUI.Control.VERTICAL_ALIGNMENT_CENTER
    textBlock.paddingLeft = "12px"
    textBlock.paddingRight = "12px"

    // 添加控件到容器
    labelContainer.addControl(background)
    labelContainer.addControl(textBlock)

    // 添加到GUI并链接到mesh
    this.advancedTexture.addControl(labelContainer)
    labelContainer.linkWithMesh(mesh)

    // 设置标签位置偏移（显示在mesh上方）
    // labelContainer.linkOffsetY = "-60px"

    // 存储标签引用
    this.parameterLabels.set(parameterData.parameterCode, labelContainer)
  }

  /**
   * 获取参数显示名称
   * @param parameterCode 参数代码
   * @param parameterName 参数名称
   * @returns 显示名称
   */
  private getParameterDisplayName(
    parameterCode: string,
    parameterName: string
  ): string {
    // 优先使用映射表中的中文名称
    const mappedName = this.parameterMapping.get(parameterCode)
    if (mappedName) {
      return mappedName
    }

    // 如果映射表中没有，使用传入的参数名称
    if (parameterName) {
      return parameterName
    }

    // 最后使用参数代码
    return parameterCode
  }

  /**
   * 格式化参数值显示
   * @param value 参数值
   * @param unit 单位
   * @returns 格式化后的字符串
   */
  private formatParameterValue(value: string | number, unit?: string): string {
    let formattedValue = ""

    if (typeof value === "number") {
      // 数字类型，保留2位小数
      formattedValue = value.toFixed(2)
    } else {
      formattedValue = String(value)
    }

    // 添加单位
    if (unit) {
      formattedValue += ` ${unit}`
    }

    return formattedValue
  }

  /**
   * 计算文本的实际宽度（考虑中英文字符差异）
   * @param text 文本内容
   * @param fontSize 字体大小
   * @returns 估算的文本宽度（像素）
   */
  private calculateTextWidth(text: string, fontSize = 14): number {
    // 使用更精确的文本宽度计算
    if (this.advancedTexture) {
      // 创建临时文本块来测量宽度
      const tempText = new GUI.TextBlock("temp", text)
      tempText.fontSize = `${fontSize}px`
      tempText.fontFamily = "Microsoft YaHei, Arial, sans-serif"

      // 使用 Babylon.js 的文本测量功能
      const context = this.advancedTexture.getContext()
      if (context) {
        context.font = `${fontSize}px Microsoft YaHei, Arial, sans-serif`
        const metrics = context.measureText(text)
        tempText.dispose()
        return metrics.width
      }
      tempText.dispose()
    }

    // 回退到估算方法
    const chineseCharCount = (text.match(/[\u4e00-\u9fa5]/g) || []).length
    const englishCharCount = text.length - chineseCharCount
    const chineseCharWidth = fontSize * 1.0
    const englishCharWidth = fontSize * 0.6

    return (
      chineseCharCount * chineseCharWidth + englishCharCount * englishCharWidth
    )
  }

  /**
   * 更新单个参数标签的值
   * @param parameterCode 参数代码
   * @param newValue 新的参数值
   * @param unit 单位
   */
  public updateParameterValue(
    parameterCode: string,
    newValue: string | number,
    unit?: string
  ): void {
    const labelContainer = this.parameterLabels.get(parameterCode)
    if (!labelContainer) {
      console.warn(`未找到参数代码 ${parameterCode} 对应的标签`)
      return
    }

    // 查找文本控件并更新（一行显示格式）
    const textBlock = labelContainer
      .getDescendants()
      .find(
        control => control.name === `paramText_${parameterCode}`
      ) as GUI.TextBlock

    if (textBlock) {
      const displayName = this.getParameterDisplayName(
        parameterCode,
        parameterCode
      )
      const formattedValue = this.formatParameterValue(newValue, unit)
      const newText = `${displayName}: ${formattedValue}`

      textBlock.text = newText

      // 重新计算容器宽度
      const textWidth = this.calculateTextWidth(newText, 14)
      const estimatedWidth = Math.max(120, Math.min(350, textWidth + 32))
      labelContainer.width = `${estimatedWidth}px`
    }
  }

  /**
   * 显示或隐藏所有参数标签
   * @param visible 是否显示
   */
  public setLabelsVisible(visible: boolean): void {
    this.parameterLabels.forEach(label => {
      label.isVisible = visible
    })
  }

  /**
   * 显示或隐藏特定参数标签
   * @param parameterCode 参数代码
   * @param visible 是否显示
   */
  public setLabelVisible(parameterCode: string, visible: boolean): void {
    const label = this.parameterLabels.get(parameterCode)
    if (label) {
      label.isVisible = visible
    }
  }

  /**
   * 清除所有参数标签
   */
  public clearAllLabels(): void {
    if (!this.advancedTexture) return

    this.parameterLabels.forEach(label => {
      this.advancedTexture?.removeControl(label)
    })

    this.parameterLabels.clear()
  }

  /**
   * 清除特定参数标签
   * @param parameterCode 参数代码
   */
  public clearLabel(parameterCode: string): void {
    const label = this.parameterLabels.get(parameterCode)
    if (label && this.advancedTexture) {
      this.advancedTexture.removeControl(label)
      this.parameterLabels.delete(parameterCode)
    }
  }

  /**
   * 获取所有当前显示的参数代码
   * @returns 参数代码数组
   */
  public getDisplayedParameterCodes(): string[] {
    return Array.from(this.parameterLabels.keys())
  }

  /**
   * 检查是否有参数标签正在显示
   * @returns 是否有标签显示
   */
  public hasLabels(): boolean {
    return this.parameterLabels.size > 0
  }

  /**
   * 添加新的参数映射
   * @param parameterCode 参数代码
   * @param displayName 显示名称
   */
  public addParameterMapping(parameterCode: string, displayName: string): void {
    this.parameterMapping.set(parameterCode, displayName)
  }

  /**
   * 批量添加参数映射
   * @param mappings 参数映射对象
   */
  public addParameterMappings(mappings: Record<string, string>): void {
    Object.entries(mappings).forEach(([code, name]) => {
      this.parameterMapping.set(code, name)
    })
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.clearAllLabels()
    if (this.advancedTexture) {
      this.advancedTexture.dispose()
      this.advancedTexture = null
    }
  }


}
