<template>
  <hmx-dialog
    dialogWidth="50%"
    :dialogTitle="'预览图'"
    customClass="hymson-dialog user-dialog"
    :isVisable="options.show"
    :isFooter="false"
    @closeDialog="closeFun"
  >
    <div class="preview-img">
      <img :src="options.imgUrl" alt="" />
    </div>
  </hmx-dialog>
</template>

<script setup name="UserDialog">
import { reactive, ref, toRefs, watch } from "vue"
import HmxDialog from "@/components/hmx-dialog.vue"
const emit = defineEmits(["onCancel"])

const props = defineProps({
  options: {
    type: Object,
    default: () => {
      return {
        show: false,
        imgUrl: null,
      }
    },
  },
})

// 关闭弹框
const closeFun = () => {
  emit("onCancel")
}
</script>

<style lang="scss">
.user-dialog.el-dialog {
  .add-user-ruleForm {
    padding: 0 20px 0 0;

    .el-form-item__label {
      flex: none;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
    }

    .el-form-item__content {
      .el-select,
      .el-input,
      .el-input-number {
        flex: none;
        width: 100%;
      }
    }
  }
}
</style>

<style scoped lang="scss">
.preview-img {
  width: 100%;
  height: 500px;
  padding: 0 20px 0 0;
  // background-color: white;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
  }
}
.form {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .form-left {
    width: 100%;

    .reference-life {
      display: flex;

      .reference-life-left {
        width: 75%;
      }

      .reference-life-right {
        width: 20%;
      }
    }
  }
}

.cover {
  width: 54px;
  height: 54px;
  margin-right: 20px;
  cursor: pointer;
  background: url("@/assets/images/common/cover.png") no-repeat 100%;
  img {
    width: 100%;
    height: 100%;
  }
}
.flex-c-c {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
