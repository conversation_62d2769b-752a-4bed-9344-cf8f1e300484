import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 * 电机调试
 */
// 设置参数
export function MotorSetParam(data) {
  return request.post({
    url: `${homeServerPrefix}/QuickStart/MotorSetParam`,
    data,
  })
}

//动作控制
export function MotorOperate(data) {
  return request.post({
    url: `${homeServerPrefix}/QuickStart/MotorOperate`,
    data,
  })
}

/**
 * IO操作
 */

// 控制开关
export function IOOperate(data) {
  return request.post({
    url: `${homeServerPrefix}/QuickStart/IOOperate`,
    data,
  })
}

/**
 * 参数设置
 * */

// 设置参数
export function SetParam(data) {
  return request.post({
    url: `${homeServerPrefix}/QuickStart/SetParam`,
    data,
  })
}

/**
 * 运行监控
 */
// 重启服务
export function RebootServer() {
  return request.post({
    url: `${homeServerPrefix}/SystemSetting/RebootServer`,
  })
}
