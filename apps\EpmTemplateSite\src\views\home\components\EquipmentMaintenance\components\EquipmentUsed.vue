<template>
  <div class="b-chart" ref="equipmentMaintenanceRef"></div>
</template>

<script setup name="BarCharts">
import {
  ref,
  defineEmits,
  defineProps,
  defineExpose,
  watch,
  nextTick,
} from "vue"
import useEcharts from "@/hooks/useEcharts"
import { equipmentMaintenanceEcharts } from "../../echartsConfig.js"
const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  isShow: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  "chart-click", // 点击chart
])

let equipmentMaintenanceRef = ref(null)
const { resize } = useEcharts(
  equipmentMaintenanceRef,
  emits,
  props,
  equipmentMaintenanceEcharts
)
defineExpose({
  resize,
})
watch(
  () => props.isShow,
  v => {
    v &&
      nextTick(() => {
        resize()
      })
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.b-chart {
  width: 100%;
  height: 100%;
}
</style>
