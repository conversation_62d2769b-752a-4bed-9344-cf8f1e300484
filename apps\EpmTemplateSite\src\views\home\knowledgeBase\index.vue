<template>
  <div class="know p-z2">
    <div class="tab-head">
      <div
        v-for="tab in head"
        :key="tab.id"
        class="head-item"
        @click="select(tab)"
      >
        <div :class="['m-item', activeHead == tab.id ? 't-active' : '']">
          {{ tab.text }}
        </div>
      </div>
    </div>
    <div class="tab-body">
      <el-form class="form" :inline="true" v-if="activeHead !== 'top'">
        <el-form-item :label="$t('knowledgeBase.faultPhenomenon')">
          <el-input
            v-model="form.phenomenon"
            clearable
            :placeholder="$t('knowledgeBase.faultPhenomenon')"
            class="search-form-input"
          />
        </el-form-item>
        <el-form-item :label="$t('knowledgeBase.faultCause')">
          <el-input
            v-model="form.reason"
            clearable
            :placeholder="$t('knowledgeBase.faultCause')"
            class="search-form-input"
          />
        </el-form-item>
        <el-form-item :label="$t('knowledgeBase.handlingMeasures')">
          <el-input
            v-model="form.solution"
            clearable
            :placeholder="$t('knowledgeBase.handlingMeasures')"
            class="search-form-input"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()">{{
            $t("common.queryText")
          }}</el-button>
          <el-button type="primary" @click="reset">{{
            $t("common.resetText")
          }}</el-button>
          <el-button type="primary" @click="upload" :loading="uploading"
            >{{ $t("common.import") }}
            <el-icon class="el-icon--right"> <Upload /> </el-icon>
          </el-button>
          <el-button type="primary" @click="handleExport" :loading="uploading"
            >{{ $t("common.export") }}
            <el-icon class="el-icon--right"> <Share /> </el-icon>
          </el-button>
          <el-button type="primary" @click="handleAdd"
            >{{ $t("common.add") }}
            <el-icon class="el-icon--right"> <Plus /> </el-icon>
          </el-button>
        </el-form-item>
      </el-form>
      <div class="cont" v-loading="loading">
        <div class="phenomenon list">
          <RecycleScroller
            v-if="knowData.phenomenon?.length"
            style="height: 100%"
            :items="knowData.phenomenon"
            :item-size="36"
            key-field="shortId"
            v-slot="{ item }"
            :infinite-scroll-delay="30"
          >
            <div
              :class="[
                'l-item',
                activeKey.pkey == item.shortId ? 'active-key' : '',
              ]"
              @click="getRason(item)"
              :key="item.id"
            >
              <el-tooltip
                effect="dark"
                placement="right"
                :offset="-200"
                :show-arrow="false"
                :append-to-body="true"
                popper-class="virtual-list-tooltip"
              >
                <template #content>
                  <div>
                    {{ $t("knowledgeBase.referenceCount") }}:{{
                      item.relatedNum
                    }}
                  </div>
                  <div>{{ item.content }}</div>
                </template>
                <div class="flex-between">
                  <div
                    class="no"
                    :title="
                      $t('knowledgeBase.referenced') +
                      item.relatedNum +
                      $t('common.times')
                    "
                  >
                    {{ item.relatedNum }}
                  </div>
                  <div class="text">{{ item.content }}</div>
                </div>
              </el-tooltip>
              <el-button
                @click.stop="handleEditKnowledge(item, 'phenomenon')"
                size="small"
                v-if="activeHead !== 'top'"
                >{{ $t("common.edit") }}</el-button
              >
            </div>
          </RecycleScroller>
          <template v-if="knowData.phenomenon?.length == 0">
            <div class="no-data">{{ $t("common.noData") }}</div>
          </template>
        </div>
        <div class="reason list">
          <RecycleScroller
            v-if="knowData.reason?.length"
            style="height: 100%"
            :items="knowData.reason"
            :item-size="36"
            key-field="shortId"
            v-slot="{ item }"
            :infinite-scroll-delay="30"
          >
            <div
              :class="[
                'l-item',
                activeKey.rkey == item.shortId ? 'active-key' : '',
              ]"
              @click="getSolution(item)"
              :key="item.id"
            >
              <el-tooltip
                effect="dark"
                placement="right"
                :offset="-200"
                :show-arrow="false"
                :append-to-body="true"
                popper-class="virtual-list-tooltip"
              >
                <template #content>
                  <div>
                    {{ $t("knowledgeBase.referenceCount") }}:{{
                      item.relatedNum
                    }}
                  </div>
                  <div>{{ item.content }}</div>
                </template>
                <div class="flex-between">
                  <div
                    class="no"
                    :title="
                      $t('knowledgeBase.referenced') +
                      item.relatedNum +
                      $t('common.times')
                    "
                  >
                    {{ item.relatedNum }}
                  </div>
                  <div class="text">{{ item.content }}</div>
                </div>
              </el-tooltip>
              <el-button
                @click.stop="handleEditKnowledge(item, 'reason')"
                size="small"
                v-if="activeHead !== 'top'"
                >{{ $t("common.edit") }}</el-button
              >
            </div>
          </RecycleScroller>
          <template v-if="knowData.reason?.length == 0">
            <div class="no-data">{{ $t("common.noData") }}</div>
          </template>
        </div>
        <div class="solution list">
          <RecycleScroller
            v-if="knowData.solution?.length"
            style="height: 100%"
            :items="knowData.solution"
            :item-size="36"
            key-field="shortId"
            v-slot="{ item }"
            :infinite-scroll-delay="30"
          >
            <div class="l-item" :key="item.id">
              <el-tooltip
                effect="dark"
                placement="right"
                :offset="-200"
                :show-arrow="false"
                :append-to-body="true"
                popper-class="virtual-list-tooltip"
              >
                <template #content>
                  <div>
                    {{ $t("knowledgeBase.referenceCount") }}:{{
                      item.relatedNum
                    }}
                  </div>
                  <div>{{ item.content }}</div>
                </template>
                <div class="flex-between">
                  <div
                    class="no"
                    :title="
                      $t('knowledgeBase.referenced') +
                      item.relatedNum +
                      $t('common.times')
                    "
                  >
                    {{ item.relatedNum }}
                  </div>
                  <div class="text">{{ item.content }}</div>
                </div>
              </el-tooltip>
              <el-button
                @click.stop="handleEditKnowledge(item, 'solution')"
                size="small"
                v-if="activeHead !== 'top'"
                >{{ $t("common.edit") }}</el-button
              >
            </div>
          </RecycleScroller>
          <template v-if="knowData.solution?.length == 0">
            <div class="no-data">{{ $t("common.noData") }}</div>
          </template>
        </div>
      </div>
      <input v-show="false" ref="fileRef" type="file" @change="getFile" />
    </div>
    <HmxProgress v-model="exportLoading" v-if="exportLoading" />
  </div>
  <!-- 记录 -->
  <RecordDialog
    :kbType="activeKType"
    :isShow="isRecordShow"
    @onClose="handleCloseRecordDialog"
    @onSure="handleConfirmRecord"
  />
  <!-- 编辑单条知识库 -->
  <EditDialog
    :isShow="isEditShow"
    :curRecord="curRecord"
    @onClose="handleCloseEditDialog"
    @onSure="handleConfirmEdit"
  />
</template>
<script setup>
import { ref, reactive, onMounted, watch, computed, nextTick } from "vue"
import dayjs from "dayjs"
import { useRoute } from "vue-router"
import {
  getKnowledgeBaseRanking,
  searchKnowledgeBase,
  getKnowledgeBaseById,
  importExcel,
  exportExcel,
} from "@/api/front/knowleage.js"
import HmxProgress from "@/components/hmx-progress"
import { useI18n } from "@xfe/locale"
import { exportByBlob } from "@xfe/utils"
import RecordDialog from "./components/record-dialog.vue"
import EditDialog from "./components/edit-dialog.vue"
import { useMessage } from "@/hooks/web/useMessage"
import { isEmpty } from "lodash-es"

const { createMessage } = useMessage()
const { t: $t } = useI18n()
let route = useRoute()
let head = [
  { id: "equip", text: $t("knowledgeBase.equipmentElectrical"), kbType: 1 },
  { id: "technology", text: $t("knowledgeBase.process"), kbType: 2 },
  { id: "quality", text: $t("knowledgeBase.quality"), kbType: 3 },
  { id: "top", text: $t("knowledgeBase.monthlyTop10"), kbType: 4 },
]
let fileRef = ref()
let uploading = ref(false)
let activeHead = ref("equip")
let activeKType = computed(
  () => head.find(item => item.id == activeHead.value).kbType
)
let loading = ref(false)
let form = reactive({
  phenomenon: "",
  reason: "",
  solution: "",
  id: "",
})
let knowData = reactive({
  phenomenon: [],
  reason: [],
  solution: [],
})
let dataLength = reactive({
  pLength: 50,
  rLength: 50,
  sLength: 50,
})
let activeKey = reactive({
  pkey: "",
  rkey: "",
})

function select(tab) {
  form.phenomenon = ""
  form.reason = ""
  form.solution = ""
  form.id = ""
  activeHead.value = tab.id
  search()
}
async function search(clear, isSingle = false) {
  if (clear != true) {
    activeKey.pkey = ""
    activeKey.rkey = ""
  }
  let knowledgeCategory = ""
  for (let item of head) {
    if (item.id == activeHead.value) {
      knowledgeCategory = item.kbType
    }
  }
  knowData.phenomenon = []
  knowData.reason = []
  knowData.solution = []
  // 查询展示默认前50条
  dataLength.pLength = 50
  dataLength.rLength = 50
  dataLength.sLength = 50

  loading.value = true
  let res = null
  // 这里单独处理Top10的情况
  if (isSingle) {
    res = await getKnowledgeBaseById(form.id)
  } else if (knowledgeCategory === 4) {
    res = await getKnowledgeBaseRanking()
  } else {
    res = await searchKnowledgeBase({ knowledgeCategory, ...form })
  }
  knowData.phenomenon =
    res?.phenomenonContent?.sort((a, b) => b.relatedNum - a.relatedNum) || []
  knowData.reason =
    res?.reasonContent?.sort((a, b) => b.relatedNum - a.relatedNum) ?? []
  knowData.solution =
    res?.solutionContent?.sort((a, b) => b.relatedNum - a.relatedNum) ?? []
  loading.value = false
}

function reset() {
  form.phenomenon = ""
  form.reason = ""
  form.solution = ""
  form.id = ""
  activeKey.pkey = ""
  activeKey.rkey = ""
  search()
}

async function getRason(data) {
  activeKey.pkey = activeKey.pkey === data.shortId ? "" : data.shortId
  activeKey.rkey = "" //清空二级选项
  form.phenomenon = data.phenomenon
  form.reason = ""
  form.solution = ""
  form.id = activeKey.pkey ? data.shortId : ""
  search(true, !!activeKey.pkey)
}

async function getSolution(data) {
  activeKey.rkey = activeKey.rkey === data.shortId ? "" : data.shortId
  form.reason = data.reason
  form.solution = ""
  form.id = activeKey.rkey ? data.shortId : ""
  // 分为两种情况
  if (activeKey.pkey && !activeKey.rkey) {
    // 一级和二级都有选中的情况
    form.id = activeKey.pkey
    search(true, true)
  } else {
    // 单独选中二级的情况
    search(true, !!activeKey.rkey)
  }
}

function loadphenomenon() {
  dataLength.pLength += 10
}
function loadreason() {
  dataLength.rLength += 10
}
function loadsolution() {
  dataLength.sLength += 10
}

onMounted(async () => {
  await search(form)
  // 确保在knowData更新后执行
  nextTick(() => {
    const val = route?.query || ""
    if (!isEmpty(val)) {
      form.phenomenon = val?.phenomenon
      // 查找匹配的现象条目
      const targetItem = knowData.phenomenon.find(
        item => item.content === val?.phenomenon
      )
      getRason(targetItem)
    }
  })
})

function upload() {
  fileRef.value.click()
}

const exportLoading = ref(false)
async function handleExport() {
  exportLoading.value = true
  const res = await exportExcel().catch(() => {
    exportLoading.value = false
  })
  const date = dayjs(new Date().getTime())
    .format("YYYY-MM-DD HH:mm:ss")
    .toString()
  await exportByBlob(res, date + $t("knowledgeBase.templateTable"))
  exportLoading.value = false
}

async function getFile(e) {
  uploading.value = true
  try {
    const files = e.target.files
    if (!files || files.length === 0) {
      createMessage(
        $t("prompt.prompt_41") + $t("common.noFileSelected"),
        "warning"
      )
      return
    }

    let formDate = new FormData()
    formDate.append("file", files[0])
    await importExcel(formDate)
    createMessage($t("prompt.prompt_10"), "success")
    search(true)
  } catch (error) {
    createMessage(
      error?.response?.data?.data?.message || $t("prompt.prompt_41"),
      "error"
    )
  } finally {
    uploading.value = false
    e.target.value = ""
  }
}

// 新增记录
const isRecordShow = ref(false)
const handleAdd = () => {
  isRecordShow.value = true
}
const handleCloseRecordDialog = () => {
  isRecordShow.value = false
}
const handleConfirmRecord = () => {
  search(form)
}

// 编辑记录
const isEditShow = ref(false)
const curRecord = ref({ id: "", type: "", content: "" })
const handleEditKnowledge = (item, type) => {
  isEditShow.value = true
  curRecord.value = {
    id: item.shortId,
    type,
    content: item.content,
  }
}
const handleCloseEditDialog = () => {
  isEditShow.value = false
  curRecord.value = {
    id: "",
    type: "",
    content: "",
  }
}
const handleConfirmEdit = () => {
  search(form)
}
</script>
<style lang="scss" scoped>
$bs-color: 0px 0px 6px 0px rgba(0, 0, 0, 0.1) inset;
.know {
  width: 100%;
  height: 100%;
  background-color: var(--front-layout-background);
  .tab-head {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-start;
    color: var(--g-font-color);
    font-size: 16px;
    align-items: center;
    border-bottom: 1px solid rgba(0, 132, 254, 0.75);
    padding: 0 10px;

    .head-item {
      .m-item {
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        min-width: 100px;
        padding: 0 20px;
        text-align: center;
        cursor: pointer;
      }
      .t-active {
        color: #fff;
        background-color: #0048ff;
      }
    }
  }
  .tab-body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: calc(100% - 50px);
    .form {
      width: 100%;
      height: 60px;
      margin-top: 10px;
      padding: 0 10px;
      .a-btn {
        color: #fff;
        text-decoration: none;
      }
    }
    .cont {
      width: 100%;
      height: calc(100% - 70px);
      display: flex;
      padding: 0 50px;
      .list {
        flex: 1;
        height: 100%;
        padding: 5px 0;
        border-radius: 4px;
        overflow: auto;
        .l-item {
          cursor: pointer;
          display: flex;
          padding: 0 10px;
          border-radius: 3px;
          font-size: 16px;
          line-height: 36px;
          display: flex;
          align-items: center;

          .no {
            width: 24px;
            height: 24px;
            line-height: 24px;
            border-radius: 50%;
            color: #000;
            text-align: center;
            margin-right: 10px;
          }
          .text {
            flex: 1;
            color: var(--g-font-color);
          }
        }
        .l-item:hover {
          font-size: 18px;
          .no {
            transform: scale(1.2);
          }
        }
        .active-key {
          background-color: rgba(64, 158, 255, 1);
        }
        .no-data {
          text-align: center;
          color: #d9d9d9;
        }
      }
    }
    .phenomenon {
      cursor: pointer;
      border: 1px solid #ee4e4e;
      background: rgba(238, 78, 78, 0.1);
      .no {
        background-color: #ee4e4e;
      }
    }
    .reason {
      margin: 0 10px;
      border: 1px solid #eecb4e;
      background: rgba(238, 203, 78, 0.1);
      .no {
        background-color: #eecb4e;
      }
    }
    .solution {
      border: 1px solid #4eeea1;
      background: rgba(78, 238, 161, 0.1);
      .no {
        background-color: #4eeea1;
      }
    }
  }
}
.flex-between {
  width: 100%;
  display: flex;
  align-items: center;
}
</style>

<style>
.virtual-list-tooltip {
  /* 限制tooltip最大宽度避免撑开布局 */
  max-width: 400px;

  /* 确保tooltip不会影响滚动容器高度 */
  position: fixed !important;
  z-index: 9999;
}
</style>
