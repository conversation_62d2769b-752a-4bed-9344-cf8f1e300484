<template>
  <div class="line" ref="lineRef"></div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, watch, computed } from "vue";
import * as echarts from "echarts";
import { debounce } from "@/utils/index.js";
import { useStore } from "vuex";
const store = useStore();
let themeColor = computed(() => store.state.theme.themeColor);
let lineRef = ref();
let containerRef = ref();
let chart = null;
let initFlag = ref(false);
let props = defineProps({
  isHistory: {
    type: Boolean,
    default: false,
  },
  isLegend: {
    // 是否需要初始化值
    type: Boolean,
    default: false,
  },
  option: {
    type: Object,
    default: {
      // xAxis: {
      //   data: [1, 2, 3, 4, 5, 6, 7],
      // },
      // series: [
      //   {
      //     data: [120, 132, 101, 134, 90, 230, 210],
      //   },
      // ],
      // {
      //   data: [130, 110, 120, 77, 100, 220, 150],
      // },
    },
  },
});
let timer = null;
// onMounted(async () => {});
onMounted(() => {
  chart = echarts.init(lineRef.value);
  init();
  //自适应不同屏幕时改变图表尺寸
  window.addEventListener("resize", cancalDebounce);
  timer = setTimeout(() => {
    resize();
  }, 100);
});
function tinit() {
  let { width, height } = getWH(containerRef.value);
  chart = echarts.init(lineRef.value, null, {
    width,
    height,
  });
  init();
  initFlag.value = true;
  window.addEventListener("resize", cancalDebounce);
}

//参数container为图表盒子节点.charts为图表节点

function getWH(container) {
  function getStyle(el, name) {
    if (window.getComputedStyle) {
      return window.getComputedStyle(el, null);
    } else {
      return el.currentStyle;
    }
  }
  var wi = getStyle(container, "width").width;
  var hi = getStyle(container, "height").height;
  return {
    width: parseFloat(wi),
    height: parseFloat(hi),
  };
}

onUnmounted(() => {
  chart && chart.dispose();
  window.removeEventListener("resize", cancalDebounce);
  clearTimeout(timer);
});

watch(
  props.option,
  val => {
    chart && chart.setOption(val);
  },
  { deep: true }
);

function init() {
  let option = {
    title: {
      text: "产量/产能统计",
      textStyle: {
        color: "#fff",
        fontSize: 14,
      },
    },
    legend: {
      orient: "horizontal",
      show: props.isLegend ? true : false,
      x: "right",
      y: 5,
      textStyle: {
        color: "#fff", // 图例文字颜色
        fontSize: 10,
      },
    },
    grid: {
      top: "20%",
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      axisLabel: {
        color: themeColor.value === "dark" ? "rgba(255,255,255,.6)" : "#000",
        fontSize: "12",
      },
    },
    yAxis: [
      {
        type: "value",
        axisLabel: {
          color: themeColor.value === "dark" ? "rgba(255,255,255,.6)" : "#000",
          fontSize: "12",
        },
      },
    ],
    series: [
      {
        type: "line",
        // stack: "Total",
        smooth: true,
        emphasis: {
          focus: "series",
        },
        lineStyle: {
          color: "rgba(95, 213, 236, 1)",
        },
        areaStyle: {
          // 颜色自上而下渐变
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              // 1代表上面
              offset: 0,
              color: "rgba(95, 213, 236, .6)",
            },
            {
              offset: 1,
              color: "rgba(95, 213, 236, .1)",
            },
          ]),
          opacity: 1, // 填充区域透明度
        },
      },
    ],
  };
  if (props.isHistory) {
    delete option.series[0].areaStyle;
  }
  chart.setOption(option);
  chart.setOption(props.option);
}
function resize() {
  chart && chart.resize();
}

const cancalDebounce = debounce(resize, 500);

defineExpose({
  resize,
});
</script>

<style lang="scss">
.line {
  width: 100%;
  height: 100%;
}
</style>
