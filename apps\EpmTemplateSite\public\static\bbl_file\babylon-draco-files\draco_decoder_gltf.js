
var DracoDecoderModule = (function() {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
  return (
function(DracoDecoderModule) {
  DracoDecoderModule = DracoDecoderModule || {};

var Module=typeof DracoDecoderModule!=="undefined"?DracoDecoderModule:{};var readyPromiseResolve,readyPromiseReject;Module["ready"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});var isRuntimeInitialized=false;var isModuleParsed=false;Module["onRuntimeInitialized"]=function(){isRuntimeInitialized=true;if(isModuleParsed){if(typeof Module["onModuleLoaded"]==="function"){Module["onModuleLoaded"](Module)}}};Module["onModuleParsed"]=function(){isModuleParsed=true;if(isRuntimeInitialized){if(typeof Module["onModuleLoaded"]==="function"){Module["onModuleLoaded"](Module)}}};function isVersionSupported(versionString){if(typeof versionString!=="string")return false;const version=versionString.split(".");if(version.length<2||version.length>3)return false;if(version[0]==1&&version[1]>=0&&version[1]<=5)return true;if(version[0]!=0||version[1]>10)return false;return true}Module["isVersionSupported"]=isVersionSupported;var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram="./this.program";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=typeof window==="object";var ENVIRONMENT_IS_WORKER=typeof importScripts==="function";var ENVIRONMENT_IS_NODE=typeof process==="object"&&typeof process.versions==="object"&&typeof process.versions.node==="string";var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;function logExceptionOnExit(e){if(e instanceof ExitStatus)return;var toLog=e;err("exiting due to exception: "+toLog)}var nodeFS;var nodePath;if(ENVIRONMENT_IS_NODE){if(ENVIRONMENT_IS_WORKER){scriptDirectory=require("path").dirname(scriptDirectory)+"/"}else{scriptDirectory=__dirname+"/"}read_=function shell_read(filename,binary){var ret=tryParseAsDataURI(filename);if(ret){return binary?ret:ret.toString()}if(!nodeFS)nodeFS=require("fs");if(!nodePath)nodePath=require("path");filename=nodePath["normalize"](filename);return nodeFS["readFileSync"](filename,binary?null:"utf8")};readBinary=function readBinary(filename){var ret=read_(filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};readAsync=function readAsync(filename,onload,onerror){var ret=tryParseAsDataURI(filename);if(ret){onload(ret)}if(!nodeFS)nodeFS=require("fs");if(!nodePath)nodePath=require("path");filename=nodePath["normalize"](filename);nodeFS["readFile"](filename,function(err,data){if(err)onerror(err);else onload(data.buffer)})};if(process["argv"].length>1){thisProgram=process["argv"][1].replace(/\\/g,"/")}arguments_=process["argv"].slice(2);quit_=function(status,toThrow){if(keepRuntimeAlive()){process["exitCode"]=status;throw toThrow}logExceptionOnExit(toThrow);process["exit"](status)};Module["inspect"]=function(){return"[Emscripten Module object]"}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}else{scriptDirectory=""}{read_=function(url){try{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText}catch(err){var data=tryParseAsDataURI(url);if(data){return intArrayToString(data)}throw err}};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){try{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}catch(err){var data=tryParseAsDataURI(url);if(data){return data}throw err}}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}var data=tryParseAsDataURI(url);if(data){onload(data.buffer);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime=Module["noExitRuntime"]||true;var WebAssembly={Memory:function(opts){this.buffer=new ArrayBuffer(opts["initial"]*65536)},Module:function(binary){},Instance:function(module,info){this.exports=(
// EMSCRIPTEN_START_ASM
function instantiate(la){function c(d){d.set=function(a,b){this[a]=b};d.get=function(a){return this[a]};return d}var e;var f=new Uint8Array(123);for(var a=25;a>=0;--a){f[48+a]=52+a;f[65+a]=a;f[97+a]=26+a}f[43]=62;f[47]=63;function l(m,n,o){var g,h,a=0,i=n,j=o.length,k=n+(j*3>>2)-(o[j-2]=="=")-(o[j-1]=="=");for(;a<j;a+=4){g=f[o.charCodeAt(a+1)];h=f[o.charCodeAt(a+2)];m[i++]=f[o.charCodeAt(a)]<<2|g>>4;if(i<k)m[i++]=g<<4|h>>2;if(i<k)m[i++]=h<<6|f[o.charCodeAt(a+3)]}}function p(q){l(e,1028,"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");l(e,8932,"AQAAAAMAAAAFAAAABwAAAAAAAAC4IwAA+AAAAPkAAAD6AAAA+wAAAE41ZHJhY28yMk1lc2hUcmF2ZXJzYWxTZXF1ZW5jZXJJTlNfMjhNYXhQcmVkaWN0aW9uRGVncmVlVHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzJfRUVFRUVFAE41ZHJhY28xNVBvaW50c1NlcXVlbmNlckUAAAAAGCwAAJMjAABALAAADCMAALAjAAD/////AAAAAKgkAAD8AAAA/QAAAP4AAABONWRyYWNvMjhNYXhQcmVkaWN0aW9uRGVncmVlVHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzFfRUVFRQBONWRyYWNvMTNUcmF2ZXJzZXJCYXNlSU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzFfRUVFRQAAGCwAAEUkAABALAAA3CMAAKAkAAAAAAAAoCQAAP8AAAAAAQAA/gAAAAAAAABgJQAAAQEAAAIBAAADAQAABAEAAE41ZHJhY28yMk1lc2hUcmF2ZXJzYWxTZXF1ZW5jZXJJTlNfMTlEZXB0aEZpcnN0VHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzJfRUVFRUVFAAAAQCwAAOAkAACwIwAAAAAAAOAlAAAFAQAABgEAAP4AAABONWRyYWNvMTlEZXB0aEZpcnN0VHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzFfRUVFRQBALAAAgCUAAKAkAAAAAAAAkCYAAAcBAAAIAQAACQEAAAoBAABONWRyYWNvMjJNZXNoVHJhdmVyc2FsU2VxdWVuY2VySU5TXzE5RGVwdGhGaXJzdFRyYXZlcnNlcklOU18yNE1lc2hBdHRyaWJ1dGVDb3JuZXJUYWJsZUVOU18zNk1lc2hBdHRyaWJ1dGVJbmRpY2VzRW5jb2RpbmdPYnNlcnZlcklTMl9FRUVFRUUAAEAsAAAEJgAAsCM=");l(e,9892,"/CYAAMsAAAALAQAAzQAAAM4AAAAMAQAAzwAAANAAAADRAAAA0gAAANMAAADUAAAA1QAAAA0BAABONWRyYWNvMjFNZXNoU2VxdWVudGlhbERlY29kZXJFAEAsAADcJgAA+B8AAAAAAAA8JwAADgEAAA8BAAAQAQAAEQEAAE41ZHJhY28xNUxpbmVhclNlcXVlbmNlckUAAABALAAAICcAALAjAAAAAAAAvCcAAMsAAAASAQAAEwEAAM4AAAAiAAAAFAEAANAAAADRAAAA0g==");l(e,10112,"RmFpbGVkIHRvIHBhcnNlIERyYWNvIGhlYWRlci4ATjVkcmFjbzE3UG9pbnRDbG91ZERlY29kZXJFAAAAGCwAAJ4nAAABAAAAAQAAAAIAAAACAAAABAAAAAQAAAAIAAAACAAAAAQAAAAIAAAAAQ==");l(e,10243,"wAAAAMAAAADAAAAAwP//////////AAAAAEAoAAAVAQAAFgEAABcBAAAYAQAATjVkcmFjbzRNZXNoRQAAAEAsAAAwKAAAhCgAAP////8AAAAAAAAAAIQoAAAZAQAAGgEAABsBAAAcAQAATjVkcmFjbzEwUG9pbnRDbG91ZEUAAAAAGCwAAGwo");l(e,10388,"AgAAAAMAAAAFAAAABwAAAAsAAAANAAAAEQAAABMAAAAXAAAAHQAAAB8AAAAlAAAAKQAAACsAAAAvAAAANQAAADsAAAA9AAAAQwAAAEcAAABJAAAATwAAAFMAAABZAAAAYQAAAGUAAABnAAAAawAAAG0AAABxAAAAfwAAAIMAAACJAAAAiwAAAJUAAACXAAAAnQAAAKMAAACnAAAArQAAALMAAAC1AAAAvwAAAMEAAADFAAAAxwAAANMAAAABAAAACwAAAA0AAAARAAAAEwAAABcAAAAdAAAAHwAAACUAAAApAAAAKwAAAC8AAAA1AAAAOwAAAD0AAABDAAAARwAAAEkAAABPAAAAUwAAAFkAAABhAAAAZQAAAGcAAABrAAAAbQAAAHEAAAB5AAAAfwAAAIMAAACJAAAAiwAAAI8AAACVAAAAlwAAAJ0AAACjAAAApwAAAKkAAACtAAAAswAAALUAAAC7AAAAvwAAAMEAAADFAAAAxwAAANEAAAAwMDAxMDIwMzA0MDUwNjA3MDgwOTEwMTExMjEzMTQxNTE2MTcxODE5MjAyMTIyMjMyNDI1MjYyNzI4MjkzMDMxMzIzMzM0MzUzNjM3MzgzOTQwNDE0MjQzNDQ0NTQ2NDc0ODQ5NTA1MTUyNTM1NDU1NTY1NzU4NTk2MDYxNjI2MzY0NjU2NjY3Njg2OTcwNzE3MjczNzQ3NTc2Nzc3ODc5ODA4MTgyODM4NDg1ODY4Nzg4ODk5MDkxOTI5Mzk0OTU5Njk3OTg5OQ==");l(e,10980,"CgAAAGQAAADoAwAAECcAAKCGAQBAQg8AgJaYAADh9QUAypo7AAAAACwrAAAdAQAAHgEAAB8BAABTdDlleGNlcHRpb24AAAAAGCwAABwrAAAAAAAAWCsAAA0AAAAgAQAAIQEAAFN0MTFsb2dpY19lcnJvcgBALAAASCsAACwrAAAAAAAAjCsAAA0AAAAiAQAAIQEAAFN0MTJsZW5ndGhfZXJyb3IAAAAAQCwAAHgrAABYKwAAU3Q5dHlwZV9pbmZvAAAAABgsAACYKwAATjEwX19jeHhhYml2MTE2X19zaGltX3R5cGVfaW5mb0UAAAAAQCwAALArAACoKwAATjEwX19jeHhhYml2MTE3X19jbGFzc190eXBlX2luZm9FAAAAQCwAAOArAADUKwAAAAAAAAQsAAAjAQAAJAEAACUBAAAmAQAAJwEAACgBAAApAQAAKgEAAAAAAACILAAAIwEAACsBAAAlAQAAJgEAACcBAAAsAQAALQEAAC4BAABOMTBfX2N4eGFiaXYxMjBfX3NpX2NsYXNzX3R5cGVfaW5mb0UAAAAAQCwAAGAsAAAELA==");l(e,11413,"L1A=")}var r=new ArrayBuffer(16);var s=new Int32Array(r);var t=new Float32Array(r);var u=new Float64Array(r);function v(w){t[2]=w}function x(y){return s[y]}function ja(ka){var z=ka.a;var A=z.buffer;z.grow=ha;var B=new Int8Array(A);var C=new Int16Array(A);var D=new Int32Array(A);var E=new Uint8Array(A);var F=new Uint16Array(A);var G=new Uint32Array(A);var H=new Float32Array(A);var I=new Float64Array(A);var J=Math.imul;var K=Math.fround;var L=Math.abs;var M=Math.clz32;var N=Math.min;var O=Math.max;var P=Math.floor;var Q=Math.ceil;var R=Math.trunc;var S=Math.sqrt;var T=ka.abort;var U=NaN;var V=Infinity;var W=ka.b;var X=ka.c;var Y=ka.d;var Z=ka.e;var _=ka.f;var $=5254912;var aa=0;
// EMSCRIPTEN_START_FUNCS
function jc(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0;if(!a){return 1}g=D[c+20>>2];l=D[c+12>>2];e=D[c+16>>2];a:{if((g|0)>=(l|0)&e>>>0>=G[c+8>>2]|(g|0)>(l|0)){break a}l=E[e+D[c>>2]|0];e=e+1|0;g=e?g:g+1|0;D[c+16>>2]=e;D[c+20>>2]=g;b:{switch(l|0){case 0:j=a;g=b;n=d;a=0;d=0;l=$+-64|0;$=l;D[l+56>>2]=0;D[l+48>>2]=0;D[l+52>>2]=0;D[l+40>>2]=0;D[l+44>>2]=0;D[l+32>>2]=0;D[l+36>>2]=0;D[l+24>>2]=0;D[l+28>>2]=0;D[l+16>>2]=0;D[l+20>>2]=0;D[l+8>>2]=0;D[l+12>>2]=0;c:{if(!ic(l+8|0,c)){break c}if(!Hd(l+8|0,c)|(D[l+20>>2]?0:j)){break c}cc(c,0,0);if(j){r=g<<2;s=D[l+36>>2];u=D[l+48>>2];v=D[l+24>>2];while(1){i=D[l+56>>2];d:{if(i>>>0>16383){break d}a=D[l+52>>2];while(1){if((a|0)<=0){break d}a=a-1|0;D[l+52>>2]=a;i=E[a+u|0]|i<<8;D[l+56>>2]=i;if(i>>>0<16384){continue}break}}a=i&4095;k=D[v+(a<<2)>>2];b=s+(k<<3)|0;D[l+56>>2]=(J(D[b>>2],i>>>12|0)+a|0)-D[b+4>>2];e:{if((g|0)<=0){break e}a=0;if(!E[c+36|0]|k>>>0>32){break c}if(k){w=k&-2;x=k&1;e=d+g|0;b=D[c+32>>2];while(1){m=D[c+28>>2];q=D[c+24>>2];i=0;a=b;f=0;p=0;if((k|0)!=1){while(1){h=(a>>>3|0)+q|0;f:{if(h>>>0>=m>>>0){h=0;break f}h=E[h|0];b=a+1|0;D[c+32>>2]=b;h=h>>>(a&7)&1;a=b}h=h<<i|f;f=0;t=(a>>>3|0)+q|0;if(t>>>0<m>>>0){f=E[t|0];b=a+1|0;D[c+32>>2]=b;f=f>>>(a&7)&1;a=b}t=i|1;i=i+2|0;f=h|f<<t;p=p+2|0;if((w|0)!=(p|0)){continue}break}}h=n+(d<<2)|0;if(x){p=m;m=(a>>>3|0)+q|0;if(p>>>0>m>>>0){m=E[m|0];b=a+1|0;D[c+32>>2]=b;a=m>>>(a&7)&1}else{a=0}f=a<<i|f}D[h>>2]=f;d=d+1|0;if((e|0)!=(d|0)){continue}break}d=e;break e}pa(n+(d<<2)|0,0,r);d=d+g|0}o=g+o|0;if(j>>>0>o>>>0){continue}break}}B[c+36|0]=0;d=D[c+20>>2];a=0;b=D[c+32>>2]+7|0;a=b>>>0<7?1:a;g=a<<29|b>>>3;b=g+D[c+16>>2]|0;a=(a>>>3|0)+d|0;D[c+16>>2]=b;D[c+20>>2]=b>>>0<g>>>0?a+1|0:a;a=1}b=D[l+36>>2];if(b){D[l+40>>2]=b;ma(b)}b=D[l+24>>2];if(b){D[l+28>>2]=b;ma(b)}b=D[l+8>>2];if(b){D[l+12>>2]=b;ma(b)}$=l- -64|0;return a;case 1:break b;default:break a}}b=0;g=D[c+20>>2];e=D[c+12>>2];f=D[c+16>>2];g:{if((g|0)>=(e|0)&f>>>0>=G[c+8>>2]|(e|0)<(g|0)){break g}e=E[f+D[c>>2]|0];f=f+1|0;g=f?g:g+1|0;D[c+16>>2]=f;D[c+20>>2]=g;h:{switch(e-1|0){case 4:f=$+-64|0;$=f;D[f+56>>2]=0;D[f+48>>2]=0;D[f+52>>2]=0;D[f+40>>2]=0;D[f+44>>2]=0;D[f+32>>2]=0;D[f+36>>2]=0;D[f+24>>2]=0;D[f+28>>2]=0;D[f+16>>2]=0;D[f+20>>2]=0;D[f+8>>2]=0;D[f+12>>2]=0;i:{if(!ic(f+8|0,c)|(D[f+20>>2]?0:a)){break i}if(!Hd(f+8|0,c)){break i}if(!a){b=1;break i}c=D[f+36>>2];g=D[f+48>>2];j=D[f+24>>2];h=D[f+56>>2];while(1){j:{if(h>>>0>16383){break j}b=D[f+52>>2];while(1){if((b|0)<=0){break j}b=b-1|0;D[f+52>>2]=b;h=E[b+g|0]|h<<8;D[f+56>>2]=h;if(h>>>0<16384){continue}break}}b=h&4095;n=D[j+(b<<2)>>2];e=c+(n<<3)|0;h=(J(D[e>>2],h>>>12|0)+b|0)-D[e+4>>2]|0;D[f+56>>2]=h;D[(o<<2)+d>>2]=n;b=1;o=o+1|0;if((o|0)!=(a|0)){continue}break}}a=D[f+36>>2];if(a){D[f+40>>2]=a;ma(a)}a=D[f+24>>2];if(a){D[f+28>>2]=a;ma(a)}a=D[f+8>>2];if(a){D[f+12>>2]=a;ma(a)}$=f- -64|0;break g;case 8:l=a;e=$+-64|0;$=e;D[e+48>>2]=0;D[e+40>>2]=0;D[e+44>>2]=0;D[e+32>>2]=0;D[e+36>>2]=0;D[e+24>>2]=0;D[e+28>>2]=0;D[e+16>>2]=0;D[e+20>>2]=0;D[e+8>>2]=0;D[e+12>>2]=0;D[e>>2]=0;D[e+4>>2]=0;k:{l:{if(!F[c+38>>1]){break l}if(!Sa(1,e+12|0,c)){break l}i=D[e+12>>2];b=D[e>>2];a=D[e+4>>2]-b>>2;m:{if(i>>>0>a>>>0){sa(e,i-a|0);i=D[e+12>>2];break m}if(a>>>0<=i>>>0){break m}D[e+4>>2]=b+(i<<2)}a=1;if(!i){break k}q=D[c+8>>2];o=D[c+12>>2];p=D[e>>2];f=0;while(1){g=D[c+20>>2];b=D[c+16>>2];a=0;if((o|0)<=(g|0)&q>>>0<=b>>>0|(g|0)>(o|0)){break k}r=D[c>>2];m=E[r+b|0];a=b+1|0;g=a?g:g+1|0;k=a;D[c+16>>2]=a;a=g;D[c+20>>2]=a;b=m>>>2|0;h=0;n:{o:{p:{q:{g=m&3;switch(g|0){case 0:break o;case 3:break q;default:break p}}b=b+f|0;a=0;if(b>>>0>=i>>>0){break k}pa(p+(f<<2)|0,0,(m&252)+4|0);f=b;break n}while(1){if((a|0)>=(o|0)&k>>>0>=q>>>0|(a|0)>(o|0)){break l}i=E[k+r|0];k=k+1|0;a=k?a:a+1|0;D[c+16>>2]=k;D[c+20>>2]=a;b=i<<(h<<3|6)|b;h=h+1|0;if((g|0)!=(h|0)){continue}break}}D[p+(f<<2)>>2]=b}f=f+1|0;i=D[e+12>>2];if(f>>>0<i>>>0){continue}break}o=e+16|0;q=D[e>>2];a=D[e+16>>2];b=D[e+20>>2]-a|0;f=b>>2;r:{if(f>>>0<=8191){sa(o,8192-f|0);break r}if((b|0)==32768){break r}D[e+20>>2]=a+32768}a=e+28|0;f=D[a>>2];b=D[e+32>>2]-f>>3;s:{if(b>>>0<i>>>0){bb(a,i-b|0);f=D[a>>2];break s}if(b>>>0>i>>>0){D[e+32>>2]=(i<<3)+f}if(!i){break l}}h=0;a=0;while(1){g=q+(h<<2)|0;k=D[g>>2];b=a;m=(h<<3)+f|0;D[m+4>>2]=a;D[m>>2]=k;g=D[g>>2];a=g+a|0;if(a>>>0>8192){break l}t:{if(a>>>0<=b>>>0){break t}m=D[o>>2];k=0;p=g&7;if(p){while(1){D[m+(b<<2)>>2]=h;b=b+1|0;k=k+1|0;if((p|0)!=(k|0)){continue}break}}if(g-1>>>0<=6){break t}while(1){g=m+(b<<2)|0;D[g>>2]=h;D[g+28>>2]=h;D[g+24>>2]=h;D[g+20>>2]=h;D[g+16>>2]=h;D[g+12>>2]=h;D[g+8>>2]=h;D[g+4>>2]=h;b=b+8|0;if((b|0)!=(a|0)){continue}break}}h=h+1|0;if((i|0)!=(h|0)){continue}break}n=(a|0)==8192}a=n}u:{if(!a|(D[e+12>>2]?0:l)){break u}if(!Oa(1,e+56|0,c)){break u}a=D[c+8>>2];b=D[c+16>>2];f=a-b|0;g=D[e+60>>2];n=D[c+20>>2];h=D[c+12>>2]-(n+(a>>>0<b>>>0)|0)|0;a=D[e+56>>2];if((g|0)==(h|0)&f>>>0<a>>>0|g>>>0>h>>>0){break u}g=g+n|0;f=a+b|0;g=f>>>0<a>>>0?g+1|0:g;D[c+16>>2]=f;D[c+20>>2]=g;if((a|0)<=0){break u}c=b+D[c>>2]|0;D[e+40>>2]=c;b=a-1|0;f=c+b|0;g=E[f|0];v:{if(g>>>0<=63){D[e+44>>2]=b;a=E[f|0]&63;break v}w:{switch((g>>>6|0)-1|0){case 0:if(a>>>0<2){break u}b=a-2|0;D[e+44>>2]=b;a=(a+c|0)-2|0;a=E[a+1|0]<<8&16128|E[a|0];break v;case 1:if(a>>>0<3){break u}b=a-3|0;D[e+44>>2]=b;a=(a+c|0)-3|0;a=E[a+2|0]<<16&4128768|E[a+1|0]<<8|E[a|0];break v;default:break w}}b=a-4|0;D[e+44>>2]=b;a=(a+c|0)-4|0;a=E[a+2|0]<<16|E[a+3|0]<<24&1056964608|E[a+1|0]<<8|E[a|0]}f=a+32768|0;D[e+48>>2]=f;if(f>>>0>8388607){break u}if(!l){j=1;break u}g=D[e+28>>2];a=0;n=D[e+16>>2];while(1){x:{if(f>>>0>32767){break x}while(1){if((b|0)<=0){break x}b=b-1|0;D[e+44>>2]=b;f=E[b+c|0]|f<<8;D[e+48>>2]=f;if(f>>>0<32768){continue}break}}j=f&8191;h=D[n+(j<<2)>>2];i=g+(h<<3)|0;f=(J(D[i>>2],f>>>13|0)+j|0)-D[i+4>>2]|0;D[e+48>>2]=f;D[(a<<2)+d>>2]=h;j=1;a=a+1|0;if((l|0)!=(a|0)){continue}break}}a=D[e+28>>2];if(a){D[e+32>>2]=a;ma(a)}a=D[e+16>>2];if(a){D[e+20>>2]=a;ma(a)}a=D[e>>2];if(a){D[e+4>>2]=a;ma(a)}$=e- -64|0;b=j;break g;case 9:l=a;e=$+-64|0;$=e;D[e+48>>2]=0;D[e+40>>2]=0;D[e+44>>2]=0;D[e+32>>2]=0;D[e+36>>2]=0;D[e+24>>2]=0;D[e+28>>2]=0;D[e+16>>2]=0;D[e+20>>2]=0;D[e+8>>2]=0;D[e+12>>2]=0;D[e>>2]=0;D[e+4>>2]=0;y:{z:{if(!F[c+38>>1]){break z}if(!Sa(1,e+12|0,c)){break z}i=D[e+12>>2];b=D[e>>2];a=D[e+4>>2]-b>>2;A:{if(i>>>0>a>>>0){sa(e,i-a|0);i=D[e+12>>2];break A}if(a>>>0<=i>>>0){break A}D[e+4>>2]=b+(i<<2)}a=1;if(!i){break y}m=D[c+8>>2];o=D[c+12>>2];q=D[e>>2];f=0;while(1){b=D[c+20>>2];h=D[c+16>>2];a=0;if((o|0)<=(b|0)&m>>>0<=h>>>0|(b|0)>(o|0)){break y}p=D[c>>2];g=E[p+h|0];a=b;b=h+1|0;a=b?a:a+1|0;k=b;D[c+16>>2]=b;D[c+20>>2]=a;b=g>>>2|0;h=0;B:{C:{D:{E:{r=g&3;switch(r|0){case 0:break C;case 3:break E;default:break D}}b=b+f|0;a=0;if(b>>>0>=i>>>0){break y}pa(q+(f<<2)|0,0,(g&252)+4|0);f=b;break B}while(1){if((a|0)>=(o|0)&k>>>0>=m>>>0|(a|0)>(o|0)){break z}i=E[k+p|0];g=a;a=k+1|0;g=a?g:g+1|0;k=a;D[c+16>>2]=a;a=g;D[c+20>>2]=a;b=i<<(h<<3|6)|b;h=h+1|0;if((r|0)!=(h|0)){continue}break}}D[q+(f<<2)>>2]=b}f=f+1|0;i=D[e+12>>2];if(f>>>0<i>>>0){continue}break}o=e+16|0;q=D[e>>2];a=D[e+16>>2];b=D[e+20>>2]-a|0;f=b>>2;F:{if(f>>>0<=32767){sa(o,32768-f|0);break F}if((b|0)==131072){break F}D[e+20>>2]=a+131072}a=e+28|0;f=D[a>>2];b=D[e+32>>2]-f>>3;G:{if(b>>>0<i>>>0){bb(a,i-b|0);f=D[a>>2];break G}if(b>>>0>i>>>0){D[e+32>>2]=(i<<3)+f}if(!i){break z}}h=0;a=0;while(1){g=q+(h<<2)|0;k=D[g>>2];b=a;m=(h<<3)+f|0;D[m+4>>2]=a;D[m>>2]=k;g=D[g>>2];a=g+a|0;if(a>>>0>32768){break z}H:{if(a>>>0<=b>>>0){break H}m=D[o>>2];k=0;p=g&7;if(p){while(1){D[m+(b<<2)>>2]=h;b=b+1|0;k=k+1|0;if((p|0)!=(k|0)){continue}break}}if(g-1>>>0<=6){break H}while(1){g=m+(b<<2)|0;D[g>>2]=h;D[g+28>>2]=h;D[g+24>>2]=h;D[g+20>>2]=h;D[g+16>>2]=h;D[g+12>>2]=h;D[g+8>>2]=h;D[g+4>>2]=h;b=b+8|0;if((b|0)!=(a|0)){continue}break}}h=h+1|0;if((i|0)!=(h|0)){continue}break}n=(a|0)==32768}a=n}I:{if(!a|(D[e+12>>2]?0:l)){break I}if(!Oa(1,e+56|0,c)){break I}a=D[c+8>>2];f=D[c+16>>2];b=f;g=a-b|0;n=D[e+60>>2];h=D[c+20>>2];a=D[c+12>>2]-(h+(a>>>0<b>>>0)|0)|0;b=D[e+56>>2];if((n|0)==(a|0)&g>>>0<b>>>0|a>>>0<n>>>0){break I}a=h+n|0;g=b+f|0;a=g>>>0<b>>>0?a+1|0:a;D[c+16>>2]=g;D[c+20>>2]=a;a=b;if((a|0)<=0){break I}c=f+D[c>>2]|0;D[e+40>>2]=c;b=a-1|0;f=c+b|0;g=E[f|0];J:{if(g>>>0<=63){D[e+44>>2]=b;a=E[f|0]&63;break J}K:{switch((g>>>6|0)-1|0){case 0:if(a>>>0<2){break I}b=a-2|0;D[e+44>>2]=b;a=(a+c|0)-2|0;a=E[a+1|0]<<8&16128|E[a|0];break J;case 1:if(a>>>0<3){break I}b=a-3|0;D[e+44>>2]=b;a=(a+c|0)-3|0;a=E[a+2|0]<<16&4128768|E[a+1|0]<<8|E[a|0];break J;default:break K}}b=a-4|0;D[e+44>>2]=b;a=(a+c|0)-4|0;a=E[a+2|0]<<16|E[a+3|0]<<24&1056964608|E[a+1|0]<<8|E[a|0]}f=a+131072|0;D[e+48>>2]=f;if(f>>>0>33554431){break I}if(!l){j=1;break I}g=D[e+28>>2];a=0;n=D[e+16>>2];while(1){L:{if(f>>>0>131071){break L}while(1){if((b|0)<=0){break L}b=b-1|0;D[e+44>>2]=b;f=E[b+c|0]|f<<8;D[e+48>>2]=f;if(f>>>0<131072){continue}break}}j=f&32767;h=D[n+(j<<2)>>2];i=g+(h<<3)|0;f=(J(D[i>>2],f>>>15|0)+j|0)-D[i+4>>2]|0;D[e+48>>2]=f;D[(a<<2)+d>>2]=h;j=1;a=a+1|0;if((l|0)!=(a|0)){continue}break}}a=D[e+28>>2];if(a){D[e+32>>2]=a;ma(a)}a=D[e+16>>2];if(a){D[e+20>>2]=a;ma(a)}a=D[e>>2];if(a){D[e+4>>2]=a;ma(a)}$=e- -64|0;b=j;break g;case 10:l=a;e=$+-64|0;$=e;D[e+48>>2]=0;D[e+40>>2]=0;D[e+44>>2]=0;D[e+32>>2]=0;D[e+36>>2]=0;D[e+24>>2]=0;D[e+28>>2]=0;D[e+16>>2]=0;D[e+20>>2]=0;D[e+8>>2]=0;D[e+12>>2]=0;D[e>>2]=0;D[e+4>>2]=0;M:{N:{if(!F[c+38>>1]){break N}if(!Sa(1,e+12|0,c)){break N}i=D[e+12>>2];b=D[e>>2];a=D[e+4>>2]-b>>2;O:{if(i>>>0>a>>>0){sa(e,i-a|0);i=D[e+12>>2];break O}if(a>>>0<=i>>>0){break O}D[e+4>>2]=b+(i<<2)}a=1;if(!i){break M}q=D[c+8>>2];o=D[c+12>>2];p=D[e>>2];f=0;while(1){g=D[c+20>>2];b=D[c+16>>2];a=0;if((o|0)<=(g|0)&q>>>0<=b>>>0|(g|0)>(o|0)){break M}r=D[c>>2];m=E[r+b|0];a=b+1|0;g=a?g:g+1|0;k=a;D[c+16>>2]=a;a=g;D[c+20>>2]=a;b=m>>>2|0;h=0;P:{Q:{R:{S:{s=m&3;switch(s|0){case 0:break Q;case 3:break S;default:break R}}b=b+f|0;a=0;if(b>>>0>=i>>>0){break M}pa(p+(f<<2)|0,0,(m&252)+4|0);f=b;break P}while(1){if((a|0)>=(o|0)&k>>>0>=q>>>0|(a|0)>(o|0)){break N}i=E[k+r|0];g=a;a=k+1|0;g=a?g:g+1|0;k=a;D[c+16>>2]=a;a=g;D[c+20>>2]=a;b=i<<(h<<3|6)|b;h=h+1|0;if((s|0)!=(h|0)){continue}break}}D[p+(f<<2)>>2]=b}f=f+1|0;i=D[e+12>>2];if(f>>>0<i>>>0){continue}break}o=e+16|0;q=D[e>>2];a=D[e+16>>2];b=D[e+20>>2]-a|0;f=b>>2;T:{if(f>>>0<=65535){sa(o,65536-f|0);break T}if((b|0)==262144){break T}D[e+20>>2]=a+262144}a=e+28|0;f=D[a>>2];b=D[e+32>>2]-f>>3;U:{if(b>>>0<i>>>0){bb(a,i-b|0);f=D[a>>2];break U}if(b>>>0>i>>>0){D[e+32>>2]=(i<<3)+f}if(!i){break N}}h=0;a=0;while(1){g=q+(h<<2)|0;k=D[g>>2];b=a;m=(h<<3)+f|0;D[m+4>>2]=a;D[m>>2]=k;g=D[g>>2];a=g+a|0;if(a>>>0>65536){break N}V:{if(a>>>0<=b>>>0){break V}m=D[o>>2];k=0;p=g&7;if(p){while(1){D[m+(b<<2)>>2]=h;b=b+1|0;k=k+1|0;if((p|0)!=(k|0)){continue}break}}if(g-1>>>0<=6){break V}while(1){g=m+(b<<2)|0;D[g>>2]=h;D[g+28>>2]=h;D[g+24>>2]=h;D[g+20>>2]=h;D[g+16>>2]=h;D[g+12>>2]=h;D[g+8>>2]=h;D[g+4>>2]=h;b=b+8|0;if((b|0)!=(a|0)){continue}break}}h=h+1|0;if((i|0)!=(h|0)){continue}break}n=(a|0)==65536}a=n}W:{if(!a|(D[e+12>>2]?0:l)){break W}if(!Oa(1,e+56|0,c)){break W}a=D[c+8>>2];b=D[c+16>>2];f=a-b|0;g=D[e+60>>2];n=D[c+20>>2];h=D[c+12>>2]-(n+(a>>>0<b>>>0)|0)|0;a=D[e+56>>2];if((g|0)==(h|0)&f>>>0<a>>>0|g>>>0>h>>>0){break W}g=g+n|0;f=a+b|0;g=f>>>0<a>>>0?g+1|0:g;D[c+16>>2]=f;D[c+20>>2]=g;if((a|0)<=0){break W}c=b+D[c>>2]|0;D[e+40>>2]=c;b=a-1|0;f=c+b|0;g=E[f|0];X:{if(g>>>0<=63){D[e+44>>2]=b;a=E[f|0]&63;break X}Y:{switch((g>>>6|0)-1|0){case 0:if(a>>>0<2){break W}b=a-2|0;D[e+44>>2]=b;a=(a+c|0)-2|0;a=E[a+1|0]<<8&16128|E[a|0];break X;case 1:if(a>>>0<3){break W}b=a-3|0;D[e+44>>2]=b;a=(a+c|0)-3|0;a=E[a+2|0]<<16&4128768|E[a+1|0]<<8|E[a|0];break X;default:break Y}}b=a-4|0;D[e+44>>2]=b;a=(a+c|0)-4|0;a=E[a+2|0]<<16|E[a+3|0]<<24&1056964608|E[a+1|0]<<8|E[a|0]}f=a+262144|0;D[e+48>>2]=f;if(f>>>0>67108863){break W}if(!l){j=1;break W}g=D[e+28>>2];a=0;n=D[e+16>>2];while(1){Z:{if(f>>>0>262143){break Z}while(1){if((b|0)<=0){break Z}b=b-1|0;D[e+44>>2]=b;f=E[b+c|0]|f<<8;D[e+48>>2]=f;if(f>>>0<262144){continue}break}}j=f&65535;h=D[n+(j<<2)>>2];i=g+(h<<3)|0;f=(J(D[i>>2],f>>>16|0)+j|0)-D[i+4>>2]|0;D[e+48>>2]=f;D[(a<<2)+d>>2]=h;j=1;a=a+1|0;if((l|0)!=(a|0)){continue}break}}a=D[e+28>>2];if(a){D[e+32>>2]=a;ma(a)}a=D[e+16>>2];if(a){D[e+20>>2]=a;ma(a)}a=D[e>>2];if(a){D[e+4>>2]=a;ma(a)}$=e- -64|0;b=j;break g;case 11:l=a;e=$+-64|0;$=e;D[e+48>>2]=0;D[e+40>>2]=0;D[e+44>>2]=0;D[e+32>>2]=0;D[e+36>>2]=0;D[e+24>>2]=0;D[e+28>>2]=0;D[e+16>>2]=0;D[e+20>>2]=0;D[e+8>>2]=0;D[e+12>>2]=0;D[e>>2]=0;D[e+4>>2]=0;_:{$:{if(!F[c+38>>1]){break $}if(!Sa(1,e+12|0,c)){break $}i=D[e+12>>2];b=D[e>>2];a=D[e+4>>2]-b>>2;aa:{if(i>>>0>a>>>0){sa(e,i-a|0);i=D[e+12>>2];break aa}if(a>>>0<=i>>>0){break aa}D[e+4>>2]=b+(i<<2)}a=1;if(!i){break _}q=D[c+8>>2];o=D[c+12>>2];p=D[e>>2];f=0;while(1){g=D[c+20>>2];b=D[c+16>>2];a=0;if((o|0)<=(g|0)&q>>>0<=b>>>0|(g|0)>(o|0)){break _}r=D[c>>2];m=E[r+b|0];a=b+1|0;g=a?g:g+1|0;k=a;D[c+16>>2]=a;a=g;D[c+20>>2]=a;b=m>>>2|0;h=0;ba:{ca:{da:{ea:{g=m&3;switch(g|0){case 0:break ca;case 3:break ea;default:break da}}b=b+f|0;a=0;if(b>>>0>=i>>>0){break _}pa(p+(f<<2)|0,0,(m&252)+4|0);f=b;break ba}while(1){if((a|0)>=(o|0)&k>>>0>=q>>>0|(a|0)>(o|0)){break $}i=E[k+r|0];k=k+1|0;a=k?a:a+1|0;D[c+16>>2]=k;D[c+20>>2]=a;b=i<<(h<<3|6)|b;h=h+1|0;if((g|0)!=(h|0)){continue}break}}D[p+(f<<2)>>2]=b}f=f+1|0;i=D[e+12>>2];if(f>>>0<i>>>0){continue}break}o=e+16|0;q=D[e>>2];a=D[e+16>>2];b=D[e+20>>2]-a|0;f=b>>2;fa:{if(f>>>0<=262143){sa(o,262144-f|0);break fa}if((b|0)==1048576){break fa}D[e+20>>2]=a- -1048576}a=e+28|0;f=D[a>>2];b=D[e+32>>2]-f>>3;ga:{if(b>>>0<i>>>0){bb(a,i-b|0);f=D[a>>2];break ga}if(b>>>0>i>>>0){D[e+32>>2]=(i<<3)+f}if(!i){break $}}h=0;a=0;while(1){g=q+(h<<2)|0;k=D[g>>2];b=a;m=(h<<3)+f|0;D[m+4>>2]=a;D[m>>2]=k;g=D[g>>2];a=g+a|0;if(a>>>0>262144){break $}ha:{if(a>>>0<=b>>>0){break ha}m=D[o>>2];k=0;p=g&7;if(p){while(1){D[m+(b<<2)>>2]=h;b=b+1|0;k=k+1|0;if((p|0)!=(k|0)){continue}break}}if(g-1>>>0<=6){break ha}while(1){g=m+(b<<2)|0;D[g>>2]=h;D[g+28>>2]=h;D[g+24>>2]=h;D[g+20>>2]=h;D[g+16>>2]=h;D[g+12>>2]=h;D[g+8>>2]=h;D[g+4>>2]=h;b=b+8|0;if((b|0)!=(a|0)){continue}break}}h=h+1|0;if((i|0)!=(h|0)){continue}break}n=(a|0)==262144}a=n}ia:{if(!a|(D[e+12>>2]?0:l)){break ia}if(!Oa(1,e+56|0,c)){break ia}a=D[c+8>>2];b=D[c+16>>2];f=a-b|0;g=D[e+60>>2];n=D[c+20>>2];h=D[c+12>>2]-(n+(a>>>0<b>>>0)|0)|0;a=D[e+56>>2];if((g|0)==(h|0)&f>>>0<a>>>0|g>>>0>h>>>0){break ia}g=g+n|0;f=a+b|0;g=f>>>0<a>>>0?g+1|0:g;D[c+16>>2]=f;D[c+20>>2]=g;if((a|0)<=0){break ia}c=b+D[c>>2]|0;D[e+40>>2]=c;b=a-1|0;f=c+b|0;g=E[f|0];ja:{if(g>>>0<=63){D[e+44>>2]=b;a=E[f|0]&63;break ja}ka:{switch((g>>>6|0)-1|0){case 0:if(a>>>0<2){break ia}b=a-2|0;D[e+44>>2]=b;a=(a+c|0)-2|0;a=E[a+1|0]<<8&16128|E[a|0];break ja;case 1:if(a>>>0<3){break ia}b=a-3|0;D[e+44>>2]=b;a=(a+c|0)-3|0;a=E[a+2|0]<<16&4128768|E[a+1|0]<<8|E[a|0];break ja;default:break ka}}b=a-4|0;D[e+44>>2]=b;a=(a+c|0)-4|0;a=E[a+2|0]<<16|E[a+3|0]<<24&1056964608|E[a+1|0]<<8|E[a|0]}f=a- -1048576|0;D[e+48>>2]=f;if(f>>>0>268435455){break ia}if(!l){j=1;break ia}g=D[e+28>>2];a=0;n=D[e+16>>2];while(1){la:{if(f>>>0>1048575){break la}while(1){if((b|0)<=0){break la}b=b-1|0;D[e+44>>2]=b;f=E[b+c|0]|f<<8;D[e+48>>2]=f;if(f>>>0<1048576){continue}break}}j=f&262143;h=D[n+(j<<2)>>2];i=g+(h<<3)|0;f=(J(D[i>>2],f>>>18|0)+j|0)-D[i+4>>2]|0;D[e+48>>2]=f;D[(a<<2)+d>>2]=h;j=1;a=a+1|0;if((l|0)!=(a|0)){continue}break}}a=D[e+28>>2];if(a){D[e+32>>2]=a;ma(a)}a=D[e+16>>2];if(a){D[e+20>>2]=a;ma(a)}a=D[e>>2];if(a){D[e+4>>2]=a;ma(a)}$=e- -64|0;b=j;break g;case 12:l=a;e=$+-64|0;$=e;D[e+48>>2]=0;D[e+40>>2]=0;D[e+44>>2]=0;D[e+32>>2]=0;D[e+36>>2]=0;D[e+24>>2]=0;D[e+28>>2]=0;D[e+16>>2]=0;D[e+20>>2]=0;D[e+8>>2]=0;D[e+12>>2]=0;D[e>>2]=0;D[e+4>>2]=0;ma:{na:{if(!F[c+38>>1]){break na}if(!Sa(1,e+12|0,c)){break na}i=D[e+12>>2];b=D[e>>2];a=D[e+4>>2]-b>>2;oa:{if(i>>>0>a>>>0){sa(e,i-a|0);i=D[e+12>>2];break oa}if(a>>>0<=i>>>0){break oa}D[e+4>>2]=b+(i<<2)}a=1;if(!i){break ma}m=D[c+8>>2];o=D[c+12>>2];q=D[e>>2];f=0;while(1){b=D[c+20>>2];h=D[c+16>>2];a=0;if((o|0)<=(b|0)&m>>>0<=h>>>0|(b|0)>(o|0)){break ma}p=D[c>>2];g=E[p+h|0];a=b;b=h+1|0;a=b?a:a+1|0;k=b;D[c+16>>2]=b;D[c+20>>2]=a;b=g>>>2|0;h=0;pa:{qa:{ra:{sa:{r=g&3;switch(r|0){case 0:break qa;case 3:break sa;default:break ra}}b=b+f|0;a=0;if(b>>>0>=i>>>0){break ma}pa(q+(f<<2)|0,0,(g&252)+4|0);f=b;break pa}while(1){if((a|0)>=(o|0)&k>>>0>=m>>>0|(a|0)>(o|0)){break na}i=E[k+p|0];g=a;a=k+1|0;g=a?g:g+1|0;k=a;D[c+16>>2]=a;a=g;D[c+20>>2]=a;b=i<<(h<<3|6)|b;h=h+1|0;if((r|0)!=(h|0)){continue}break}}D[q+(f<<2)>>2]=b}f=f+1|0;i=D[e+12>>2];if(f>>>0<i>>>0){continue}break}o=e+16|0;q=D[e>>2];a=D[e+16>>2];b=D[e+20>>2]-a|0;f=b>>2;ta:{if(f>>>0<=524287){sa(o,524288-f|0);break ta}if((b|0)==2097152){break ta}D[e+20>>2]=a+2097152}a=e+28|0;f=D[a>>2];b=D[e+32>>2]-f>>3;ua:{if(b>>>0<i>>>0){bb(a,i-b|0);f=D[a>>2];break ua}if(b>>>0>i>>>0){D[e+32>>2]=(i<<3)+f}if(!i){break na}}h=0;a=0;while(1){g=q+(h<<2)|0;k=D[g>>2];b=a;m=(h<<3)+f|0;D[m+4>>2]=a;D[m>>2]=k;g=D[g>>2];a=g+a|0;if(a>>>0>524288){break na}va:{if(a>>>0<=b>>>0){break va}m=D[o>>2];k=0;p=g&7;if(p){while(1){D[m+(b<<2)>>2]=h;b=b+1|0;k=k+1|0;if((p|0)!=(k|0)){continue}break}}if(g-1>>>0<=6){break va}while(1){g=m+(b<<2)|0;D[g>>2]=h;D[g+28>>2]=h;D[g+24>>2]=h;D[g+20>>2]=h;D[g+16>>2]=h;D[g+12>>2]=h;D[g+8>>2]=h;D[g+4>>2]=h;b=b+8|0;if((b|0)!=(a|0)){continue}break}}h=h+1|0;if((i|0)!=(h|0)){continue}break}n=(a|0)==524288}a=n}wa:{if(!a|(D[e+12>>2]?0:l)){break wa}if(!Oa(1,e+56|0,c)){break wa}a=D[c+8>>2];f=D[c+16>>2];b=f;g=a-b|0;n=D[e+60>>2];h=D[c+20>>2];a=D[c+12>>2]-(h+(a>>>0<b>>>0)|0)|0;b=D[e+56>>2];if((n|0)==(a|0)&g>>>0<b>>>0|a>>>0<n>>>0){break wa}a=h+n|0;g=b+f|0;a=g>>>0<b>>>0?a+1|0:a;D[c+16>>2]=g;D[c+20>>2]=a;a=b;if((a|0)<=0){break wa}c=f+D[c>>2]|0;D[e+40>>2]=c;b=a-1|0;f=c+b|0;g=E[f|0];xa:{if(g>>>0<=63){D[e+44>>2]=b;a=E[f|0]&63;break xa}ya:{switch((g>>>6|0)-1|0){case 0:if(a>>>0<2){break wa}b=a-2|0;D[e+44>>2]=b;a=(a+c|0)-2|0;a=E[a+1|0]<<8&16128|E[a|0];break xa;case 1:if(a>>>0<3){break wa}b=a-3|0;D[e+44>>2]=b;a=(a+c|0)-3|0;a=E[a+2|0]<<16&4128768|E[a+1|0]<<8|E[a|0];break xa;default:break ya}}b=a-4|0;D[e+44>>2]=b;a=(a+c|0)-4|0;a=E[a+2|0]<<16|E[a+3|0]<<24&1056964608|E[a+1|0]<<8|E[a|0]}f=a+2097152|0;D[e+48>>2]=f;if(f>>>0>536870911){break wa}if(!l){j=1;break wa}g=D[e+28>>2];a=0;n=D[e+16>>2];while(1){za:{if(f>>>0>2097151){break za}while(1){if((b|0)<=0){break za}b=b-1|0;D[e+44>>2]=b;f=E[b+c|0]|f<<8;D[e+48>>2]=f;if(f>>>0<2097152){continue}break}}j=f&524287;h=D[n+(j<<2)>>2];i=g+(h<<3)|0;f=(J(D[i>>2],f>>>19|0)+j|0)-D[i+4>>2]|0;D[e+48>>2]=f;D[(a<<2)+d>>2]=h;j=1;a=a+1|0;if((l|0)!=(a|0)){continue}break}}a=D[e+28>>2];if(a){D[e+32>>2]=a;ma(a)}a=D[e+16>>2];if(a){D[e+20>>2]=a;ma(a)}a=D[e>>2];if(a){D[e+4>>2]=a;ma(a)}$=e- -64|0;b=j;break g;case 17:b=Gd(a,c,d);break g;case 0:case 1:case 2:case 3:case 5:case 6:case 7:f=0;j=$+-64|0;$=j;D[j+48>>2]=0;D[j+40>>2]=0;D[j+44>>2]=0;D[j+32>>2]=0;D[j+36>>2]=0;D[j+24>>2]=0;D[j+28>>2]=0;D[j+16>>2]=0;D[j+20>>2]=0;D[j+8>>2]=0;D[j+12>>2]=0;D[j>>2]=0;D[j+4>>2]=0;Aa:{if(!ic(j,c)|(D[j+12>>2]?0:a)){break Aa}if(!Oa(1,j+56|0,c)){break Aa}b=D[c+8>>2];n=D[c+16>>2];g=n;e=b-g|0;l=D[j+60>>2];h=D[c+20>>2];g=D[c+12>>2]-(h+(b>>>0<g>>>0)|0)|0;b=D[j+56>>2];if((l|0)==(g|0)&e>>>0<b>>>0|g>>>0<l>>>0){break Aa}g=h+l|0;e=b+n|0;g=e>>>0<b>>>0?g+1|0:g;D[c+16>>2]=e;D[c+20>>2]=g;if((b|0)<=0){break Aa}g=n+D[c>>2]|0;D[j+40>>2]=g;c=b-1|0;n=g+c|0;e=E[n|0];Ba:{if(e>>>0<=63){D[j+44>>2]=c;b=E[n|0]&63;break Ba}Ca:{switch((e>>>6|0)-1|0){case 0:if(b>>>0<2){break Aa}c=b-2|0;D[j+44>>2]=c;b=(b+g|0)-2|0;b=E[b+1|0]<<8&16128|E[b|0];break Ba;case 1:if(b>>>0<3){break Aa}c=b-3|0;D[j+44>>2]=c;b=(b+g|0)-3|0;b=E[b+2|0]<<16&4128768|E[b+1|0]<<8|E[b|0];break Ba;default:break Ca}}c=b-4|0;D[j+44>>2]=c;b=(b+g|0)-4|0;b=E[b+2|0]<<16|E[b+3|0]<<24&1056964608|E[b+1|0]<<8|E[b|0]}h=b+16384|0;D[j+48>>2]=h;if(h>>>0>4194303){break Aa}if(!a){f=1;break Aa}b=D[j+28>>2];n=D[j+16>>2];while(1){Da:{if(h>>>0>16383){break Da}while(1){if((c|0)<=0){break Da}c=c-1|0;D[j+44>>2]=c;h=E[c+g|0]|h<<8;D[j+48>>2]=h;if(h>>>0<16384){continue}break}}f=h&4095;e=D[n+(f<<2)>>2];l=b+(e<<3)|0;h=(J(D[l>>2],h>>>12|0)+f|0)-D[l+4>>2]|0;D[j+48>>2]=h;D[(k<<2)+d>>2]=e;f=1;k=k+1|0;if((k|0)!=(a|0)){continue}break}}a=D[j+28>>2];if(a){D[j+32>>2]=a;ma(a)}a=D[j+16>>2];if(a){D[j+20>>2]=a;ma(a)}a=D[j>>2];if(a){D[j+4>>2]=a;ma(a)}$=j- -64|0;b=f;break g;case 13:case 14:case 15:case 16:break h;default:break g}}b=Gd(a,c,d)}f=b}return f}function ch(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,H=0,I=0,K=0,L=0,M=0,N=0;u=$+-64|0;$=u;D[a+132>>2]=0;if(D[a+148>>2]){b=D[a+144>>2];if(b){while(1){h=D[b>>2];ma(b);b=h;if(b){continue}break}}b=0;D[a+144>>2]=0;h=D[a+140>>2];a:{if(!h){break a}if(h-1>>>0>=3){o=h&-4;while(1){g=b<<2;D[g+D[a+136>>2]>>2]=0;D[D[a+136>>2]+(g|4)>>2]=0;D[D[a+136>>2]+(g|8)>>2]=0;D[D[a+136>>2]+(g|12)>>2]=0;b=b+4|0;k=k+4|0;if((o|0)!=(k|0)){continue}break}}h=h&3;if(!h){break a}while(1){D[D[a+136>>2]+(b<<2)>>2]=0;b=b+1|0;c=c+1|0;if((h|0)!=(c|0)){continue}break}}D[a+148>>2]=0}b:{c:{if(!Da(1,u+60|0,D[D[a+4>>2]+32>>2])){break c}D[a+156>>2]=D[u+60>>2];if(!Da(1,u+56|0,D[D[a+4>>2]+32>>2])){break c}c=D[u+56>>2];if(c>>>0>1431655765|G[a+156>>2]>J(c,3)>>>0){break c}h=D[D[a+4>>2]+32>>2];g=D[h+8>>2];o=D[h+12>>2];b=D[h+20>>2];d=g;g=D[h+16>>2];if((o|0)<=(b|0)&d>>>0<=g>>>0|(b|0)>(o|0)){break c}o=E[g+D[h>>2]|0];g=g+1|0;b=g?b:b+1|0;D[h+16>>2]=g;D[h+20>>2]=b;if(!Da(1,u+52|0,h)){break c}s=D[u+52>>2];if(s>>>0>c>>>0|c>>>0>s+((s>>>0)/3|0)>>>0){break c}if(!Da(1,u+48|0,D[D[a+4>>2]+32>>2])){break c}b=D[u+48>>2];if(b>>>0>s>>>0){break c}D[a+28>>2]=D[a+24>>2];g=na(88);Wc(g);h=D[a+8>>2];D[a+8>>2]=g;if(h){ab(h);if(!D[a+8>>2]){break c}}D[a+164>>2]=D[a+160>>2];Hb(a+160|0,c);D[a+176>>2]=D[a+172>>2];Hb(a+172|0,c);D[a- -64>>2]=0;D[a+92>>2]=-1;D[a+84>>2]=-1;D[a+88>>2]=-1;D[a+40>>2]=D[a+36>>2];D[a+52>>2]=D[a+48>>2];D[a+76>>2]=D[a+72>>2];I=a+216|0;xd(I);wd(I,o);if(!Vc(D[a+8>>2],c,b+D[a+156>>2]|0)){break c}h=D[a+156>>2];B[u+8|0]=1;Ea(a+120|0,b+h|0,u+8|0);if((vd(a,D[D[a+4>>2]+32>>2])|0)==-1){break c}f=a+232|0;ud(f,a);M=a,N=ba[D[D[a>>2]+36>>2]](a)|0,D[M+380>>2]=N;D[a+372>>2]=o;D[a+384>>2]=b+D[a+156>>2];b=u+8|0;C[b+38>>1]=0;D[b>>2]=0;D[b+8>>2]=0;D[b+12>>2]=0;D[b+16>>2]=0;D[b+20>>2]=0;D[b+24>>2]=0;D[b+28>>2]=0;B[b+29|0]=0;B[b+30|0]=0;B[b+31|0]=0;B[b+32|0]=0;B[b+33|0]=0;B[b+34|0]=0;B[b+35|0]=0;B[b+36|0]=0;K=b;o=b;g=0;e=$-16|0;$=e;d:{if(!Ka(f+80|0,f)){break d}if(!rd(f)){break d}b=D[f+4>>2];D[o>>2]=D[f>>2];D[o+4>>2]=b;b=D[f+36>>2];D[o+32>>2]=D[f+32>>2];D[o+36>>2]=b;b=D[f+28>>2];D[o+24>>2]=D[f+24>>2];D[o+28>>2]=b;b=D[f+20>>2];D[o+16>>2]=D[f+16>>2];D[o+20>>2]=b;b=D[f+12>>2];D[o+8>>2]=D[f+8>>2];D[o+12>>2]=b;D[f+176>>2]=2;D[f+180>>2]=7;b=D[f+152>>2];if((b|0)<0){break d}D[e+12>>2]=0;g=2;k=D[f+156>>2];h=D[f+160>>2]-k>>2;e:{if(h>>>0<b>>>0){xa(f+156|0,b-h|0,e+12|0);g=D[f+176>>2];c=D[f+180>>2];break e}c=7;if(b>>>0>=h>>>0){break e}D[f+160>>2]=k+(b<<2)}h=f+184|0;c=(c-g|0)+1|0;b=D[f+188>>2];g=D[f+184>>2];k=(b-g|0)/12|0;f:{if(c>>>0>k>>>0){b=c-k|0;d=D[h+8>>2];g=D[h+4>>2];g:{if(b>>>0<=(d-g|0)/12>>>0){if(b){b=J((J(b,12)-12>>>0)/12|0,12)+12|0;g=pa(g,0,b)+b|0}D[h+4>>2]=g;break g}h:{i:{j:{k=D[h>>2];p=(g-k|0)/12|0;c=p+b|0;if(c>>>0<357913942){d=(d-k|0)/12|0;q=d<<1;d=d>>>0<178956970?c>>>0>q>>>0?c:q:357913941;if(d){if(d>>>0>=357913942){break j}i=na(J(d,12))}c=J(p,12)+i|0;p=J((J(b,12)-12>>>0)/12|0,12)+12|0;b=pa(c,0,p);p=b+p|0;i=J(d,12)+i|0;if((g|0)==(k|0)){break i}while(1){c=c-12|0;g=g-12|0;D[c>>2]=D[g>>2];D[c+4>>2]=D[g+4>>2];D[c+8>>2]=D[g+8>>2];D[g+8>>2]=0;D[g>>2]=0;D[g+4>>2]=0;if((g|0)!=(k|0)){continue}break}D[h+8>>2]=i;b=D[h+4>>2];D[h+4>>2]=p;g=D[h>>2];D[h>>2]=c;if((b|0)==(g|0)){break h}while(1){c=b-12|0;k=D[c>>2];if(k){D[b-8>>2]=k;ma(k)}b=c;if((b|0)!=(g|0)){continue}break}break h}break b}ra(1326);T()}D[h+8>>2]=i;D[h+4>>2]=p;D[h>>2]=b}if(g){ma(g)}}c=D[f+188>>2];break f}if(c>>>0>=k>>>0){c=b;break f}c=g+J(c,12)|0;if((b|0)!=(c|0)){while(1){g=b-12|0;k=D[g>>2];if(k){D[b-8>>2]=k;ma(k)}b=g;if((b|0)!=(c|0)){continue}break}}D[f+188>>2]=c}i=f+196|0;g=D[f+184>>2];b=(c-g|0)/12|0;d=D[f+196>>2];k=D[f+200>>2]-d>>2;k:{if(b>>>0>k>>>0){sa(i,b-k|0);g=D[f+184>>2];c=D[f+188>>2];break k}if(b>>>0>=k>>>0){break k}D[f+200>>2]=d+(b<<2)}if((c|0)==(g|0)){g=1;break d}b=0;while(1){l:{if(!Da(1,e+8|0,o)){break l}g=D[e+8>>2];c=D[f+148>>2];if(g>>>0>(D[c+4>>2]-D[c>>2]>>2>>>0)/3>>>0){break l}if(g){c=D[h>>2];q=J(b,12);k=c+q|0;d=D[k>>2];p=D[k+4>>2]-d>>2;m:{if(p>>>0<g>>>0){sa(k,g-p|0);c=D[h>>2];break m}if(g>>>0>=p>>>0){break m}D[k+4>>2]=d+(g<<2)}jc(g,1,o,D[c+q>>2]);D[D[i>>2]+(b<<2)>>2]=g}g=1;b=b+1|0;if(b>>>0<(D[f+188>>2]-D[f+184>>2]|0)/12>>>0){continue}break d}break}g=0}$=e+16|0;n:{if(!g){break n}c=0;b=0;g=0;q=0;h=0;o=0;k=0;p=0;m=$-96|0;$=m;D[m+72>>2]=0;D[m+64>>2]=0;D[m+68>>2]=0;D[m+48>>2]=0;D[m+52>>2]=0;D[m+40>>2]=0;D[m+44>>2]=0;D[m+56>>2]=1065353216;D[m+32>>2]=0;D[m+24>>2]=0;D[m+28>>2]=0;i=1;f=a;x=D[a+124>>2];o:{p:{q:{r:{if((s|0)<=0){break r}z=f+232|0;L=D[f+216>>2]!=D[f+220>>2];s:{while(1){a=k;k=a+1|0;t:{u:{v:{d=D[f+404>>2];if((d|0)==-1){D[f+400>>2]=7;break v}e=-1;j=D[f+428>>2]+(d<<2)|0;l=D[j>>2];d=l-1|0;D[j>>2]=d;if((l|0)<=0){break q}j=D[D[D[f+416>>2]+J(D[f+404>>2],12)>>2]+(d<<2)>>2];d=D[(j<<2)+8928>>2];D[f+400>>2]=d;if(!j){if((c|0)==(h|0)){break q}d=-1;i=D[f+8>>2];r=D[i+24>>2];t=c-4|0;e=D[t>>2];b=-1;w:{if((e|0)==-1){break w}l=e+1|0;l=(l>>>0)%3|0?l:e-2|0;b=-1;if((l|0)==-1){break w}b=D[D[i>>2]+(l<<2)>>2]}j=D[r+(b<<2)>>2];if((j|0)!=-1){d=j+1|0;d=(d>>>0)%3|0?d:j-2|0}l=D[i+12>>2];j=J(a,3);a=j+1|0;D[l+(e<<2)>>2]=a;w=a<<2;D[w+l>>2]=e;n=j+2|0;D[l+(d<<2)>>2]=n;y=n<<2;D[y+l>>2]=d;l=-1;a=-1;x:{if((e|0)==-1){break x}y:{if((e>>>0)%3|0){e=e-1|0;break y}e=e+2|0;a=-1;if((e|0)==-1){break x}}a=D[D[i>>2]+(e<<2)>>2]}z:{if((d|0)==-1){break z}e=d+1|0;e=(e>>>0)%3|0?e:d-2|0;if((e|0)==-1){break z}l=D[D[i>>2]+(e<<2)>>2]}e=-1;if((a|0)==(b|0)|(b|0)==(l|0)){break q}e=D[i>>2];D[e+(j<<2)>>2]=b;D[e+w>>2]=l;D[e+y>>2]=a;if((a|0)!=-1){D[r+(a<<2)>>2]=n}a=D[f+120>>2]+(b>>>3&536870908)|0;e=D[a>>2];M=a,N=di(b)&e,D[M>>2]=N;D[t>>2]=j;b=h;fc(z,j);break t}A:{switch(d-1|0){case 2:case 4:if((c|0)==(h|0)){break q}d=D[f+8>>2];e=D[d+12>>2];n=(j|0)==3;j=J(a,3);r=(n?2:1)+j|0;t=r<<2;w=c-4|0;b=D[w>>2];D[e+t>>2]=b;D[e+(b<<2)>>2]=r;Ra(d+24|0);e=-1;c=D[f+8>>2];l=D[c+24>>2];if((x|0)<D[c+28>>2]-l>>2){break q}c=D[c>>2];e=D[d+28>>2]-D[d+24>>2]|0;d=(e>>2)-1|0;D[c+t>>2]=d;if(e){D[l+(d<<2)>>2]=r}e=n?j:j+2|0;r=c+(j+n<<2)|0;B:{if((b|0)==-1){D[c+(e<<2)>>2]=-1;d=-1;break B}C:{D:{E:{if((b>>>0)%3|0){d=b-1|0;break E}d=b+2|0;if((d|0)==-1){break D}}d=D[c+(d<<2)>>2];D[c+(e<<2)>>2]=d;if((d|0)==-1){break C}D[l+(d<<2)>>2]=e;break C}D[c+(e<<2)>>2]=-1}d=b+1|0;b=(d>>>0)%3|0?d:b-2|0;d=-1;if((b|0)==-1){break B}d=D[c+(b<<2)>>2]}D[r>>2]=d;D[w>>2]=j;b=h;break u;case 6:break v;case 0:break A;default:break q}}if((b|0)==(c|0)){break q}g=c-4|0;l=D[g>>2];D[m+68>>2]=g;n=D[m+44>>2];F:{if(!n){break F}e=D[m+40>>2];r=ci(n)>>>0>1;d=a&n+2147483647;G:{if(!r){break G}d=a;if(a>>>0<n>>>0){break G}d=(a>>>0)%(n>>>0)|0}j=d;e=D[e+(j<<2)>>2];if(!e){break F}e=D[e>>2];if(!e){break F}H:{if(!r){d=n-1|0;while(1){n=D[e+4>>2];I:{if((n|0)!=(a|0)){if((j|0)==(d&n)){break I}break F}if((a|0)==D[e+8>>2]){break H}}e=D[e>>2];if(e){continue}break}break F}while(1){d=D[e+4>>2];J:{if((d|0)!=(a|0)){if(d>>>0>=n>>>0){d=(d>>>0)%(n>>>0)|0}if((d|0)==(j|0)){break J}break F}if((a|0)==D[e+8>>2]){break H}}e=D[e>>2];if(e){continue}break}break F}if((g|0)!=(v|0)){D[g>>2]=D[e+12>>2];D[m+68>>2]=c;g=c;break F}c=v-b|0;d=c>>2;h=d+1|0;if(h>>>0>=1073741824){break b}g=c>>1;g=d>>>0<536870911?g>>>0<h>>>0?h:g:1073741823;if(g){if(g>>>0>=1073741824){break p}h=na(g<<2)}else{h=0}d=h+(d<<2)|0;D[d>>2]=D[e+12>>2];v=(g<<2)+h|0;g=d+4|0;if((c|0)>0){oa(h,b,c)}D[m+72>>2]=v;D[m+68>>2]=g;D[m+64>>2]=h;if(b){ma(b)}b=h}if((b|0)==(g|0)){break s}w=g-4|0;c=D[w>>2];e=(c|0)==-1;j=D[f+8>>2];if(!e&D[D[j+12>>2]+(c<<2)>>2]!=-1){break s}n=D[j+12>>2];if((l|0)!=-1&D[n+(l<<2)>>2]!=-1){break s}r=J(a,3);t=r+2|0;D[n+(c<<2)>>2]=t;a=t<<2;D[a+n>>2]=c;i=r+1|0;D[n+(l<<2)>>2]=i;y=i<<2;D[n+y>>2]=l;K:{L:{M:{if(!e){if((c>>>0)%3|0){d=c-1|0;break M}d=c+2|0;if((d|0)!=-1){break M}i=D[j>>2];d=-1;break L}d=-1;i=D[j>>2];D[i+(r<<2)>>2]=-1;e=-1;break K}i=D[j>>2];d=D[i+(d<<2)>>2]}D[(r<<2)+i>>2]=d;e=c+1|0;c=(e>>>0)%3|0?e:c-2|0;e=-1;if((c|0)==-1){break K}e=D[(c<<2)+i>>2]}D[i+y>>2]=e;N:{if((l|0)==-1){D[a+i>>2]=-1;e=-1;c=-1;break N}O:{P:{Q:{if((l>>>0)%3|0){e=l-1|0;break Q}e=l+2|0;if((e|0)==-1){break P}}c=a+i|0;a=D[(e<<2)+i>>2];D[c>>2]=a;if((a|0)==-1){break O}D[D[j+24>>2]+(a<<2)>>2]=t;break O}D[a+i>>2]=-1}e=-1;a=l+1|0;a=(a>>>0)%3|0?a:l-2|0;c=-1;if((a|0)==-1){break N}e=a;c=D[(a<<2)+i>>2]}a=D[f+388>>2];l=d<<2;t=a+l|0;y=a;a=c<<2;D[t>>2]=D[t>>2]+D[y+a>>2];j=D[j+24>>2];a=j+a|0;if((d|0)!=-1){D[j+l>>2]=D[a>>2]}R:{if((e|0)==-1){break R}while(1){D[(e<<2)+i>>2]=d;j=e+1|0;e=(j>>>0)%3|0?j:e-2|0;if((e|0)==-1){break R}e=D[n+(e<<2)>>2];if((e|0)==-1){break R}j=e+1|0;e=(j>>>0)%3|0?j:e-2|0;if((e|0)!=-1){continue}break}}D[a>>2]=-1;S:{if(L){break S}if((p|0)!=(A|0)){D[p>>2]=c;p=p+4|0;D[m+28>>2]=p;break S}T:{a=A-q|0;i=a>>2;o=i+1|0;if(o>>>0<1073741824){e=a>>1;e=i>>>0<536870911?e>>>0<o>>>0?o:e:1073741823;if(e){if(e>>>0>=1073741824){break T}o=na(e<<2)}else{o=0}i=o+(i<<2)|0;D[i>>2]=c;A=(e<<2)+o|0;p=i+4|0;if((a|0)>0){oa(o,q,a)}D[m+32>>2]=A;D[m+28>>2]=p;D[m+24>>2]=o;if(q){ma(q)}q=o;break S}break b}break p}D[w>>2]=r;c=g;fc(z,r);break t}j=D[f+8>>2];Ra(j+24|0);e=-1;d=D[f+8>>2];g=J(a,3);l=D[j+28>>2]-D[j+24>>2]|0;j=l>>2;r=j-1|0;D[D[d>>2]+(g<<2)>>2]=r;Ra(d+24|0);n=g+1|0;D[D[d>>2]+(n<<2)>>2]=(D[d+28>>2]-D[d+24>>2]>>2)-1;d=D[f+8>>2];Ra(d+24|0);t=g+2|0;D[D[d>>2]+(t<<2)>>2]=(D[d+28>>2]-D[d+24>>2]>>2)-1;w=D[f+8>>2];d=D[w+24>>2];if((x|0)<D[w+28>>2]-d>>2){break q}U:{V:{if(!l){D[d+(j<<2)>>2]=n;e=1;break V}D[d+(r<<2)>>2]=g;e=0;if((l|0)==-4){break V}D[d+(j<<2)>>2]=n;e=j+1|0;if((e|0)==-1){break U}}D[d+(e<<2)>>2]=t}if((c|0)!=(v|0)){D[c>>2]=g;g=c+4|0;D[m+68>>2]=g;break u}c=v-h|0;d=c>>2;b=d+1|0;if(b>>>0>=1073741824){break b}e=c>>1;e=d>>>0<536870911?b>>>0>e>>>0?b:e:1073741823;if(e){if(e>>>0>=1073741824){break p}b=na(e<<2)}else{b=0}d=b+(d<<2)|0;D[d>>2]=g;v=(e<<2)+b|0;g=d+4|0;if((c|0)>0){oa(b,h,c)}D[m+72>>2]=v;D[m+68>>2]=g;D[m+64>>2]=b;if(h){ma(h)}h=b}fc(z,D[g-4>>2]);e=D[f+40>>2];W:{if((e|0)==D[f+36>>2]){break W}d=s+(a^-1)|0;j=g-4|0;while(1){a=D[e-8>>2];if(a>>>0>d>>>0){break s}if((a|0)!=(d|0)){break W}l=E[e-4|0];a=e-12|0;c=D[a>>2];D[f+40>>2]=a;if((c|0)<0){break s}a=D[j>>2];D[m+20>>2]=s+(c^-1);c=m+20|0;D[m+88>>2]=c;qd(m,m+40|0,c,m+88|0);e=D[m>>2];X:{if(l&1){c=-1;if((a|0)==-1){break X}c=a+1|0;c=(c>>>0)%3|0?c:a-2|0;break X}c=-1;if((a|0)==-1){break X}c=a-1|0;if((a>>>0)%3|0){break X}c=a+2|0}D[e+12>>2]=c;e=D[f+40>>2];if((e|0)!=D[f+36>>2]){continue}break}}c=g}i=(k|0)<(s|0);if((k|0)!=(s|0)){continue}break}k=s;break r}e=-1;if(i&1){break q}}e=-1;c=D[f+8>>2];if((x|0)<D[c+28>>2]-D[c+24>>2]>>2){break q}if((g|0)!=(h|0)){s=f+60|0;r=f+312|0;while(1){g=g-4|0;j=D[g>>2];D[m+68>>2]=g;Y:{if(Ga(r)){n=D[f+8>>2];l=D[n>>2];if(((D[n+4>>2]-l>>2>>>0)/3|0)<=(k|0)){break q}b=-1;a=-1;d=D[n+24>>2];c=-1;Z:{if((j|0)==-1){break Z}q=j+1|0;q=(q>>>0)%3|0?q:j-2|0;c=-1;if((q|0)==-1){break Z}c=D[l+(q<<2)>>2]}q=c;c=D[d+(q<<2)>>2];_:{if((c|0)==-1){break _}i=c+1|0;c=(i>>>0)%3|0?i:c-2|0;if((c|0)==-1){break _}b=c+1|0;b=(b>>>0)%3|0?b:c-2|0;if((b|0)!=-1){a=D[l+(b<<2)>>2]}b=c}v=-1;i=-1;d=D[d+(a<<2)>>2];c=-1;$:{if((d|0)==-1){break $}x=d+1|0;d=(x>>>0)%3|0?x:d-2|0;c=-1;if((d|0)==-1){break $}c=d+1|0;i=d;c=(c>>>0)%3|0?c:d-2|0;if((c|0)!=-1){c=D[l+(c<<2)>>2]}else{c=-1}}d=D[n+12>>2];n=J(k,3);x=n<<2;D[d+x>>2]=j;D[d+(j<<2)>>2]=n;j=n+1|0;z=j<<2;D[z+d>>2]=b;D[d+(b<<2)>>2]=j;b=n+2|0;A=b<<2;D[A+d>>2]=i;D[d+(i<<2)>>2]=b;D[l+x>>2]=a;i=l+z|0;D[i>>2]=c;c=l+A|0;D[c>>2]=q;d=j>>>0<n>>>0?-1:a;a=D[f+120>>2];q=(d>>>3&536870908)+a|0;l=D[q>>2];M=q,N=di(d)&l,D[M>>2]=N;v=(j|0)!=-1?D[i>>2]:v;i=a+(v>>>3&536870908)|0;d=D[i>>2];M=i,N=di(v)&d,D[M>>2]=N;d=-1;d=(b|0)!=-1?D[c>>2]:d;a=a+(d>>>3&536870908)|0;b=D[a>>2];M=a,N=di(d)&b,D[M>>2]=N;d=D[f+64>>2];a=D[f+68>>2];if((d|0)==a<<5){if((d+1|0)<0){break b}if(d>>>0<=1073741822){b=d+32&-32;a=a<<6;a=a>>>0<b>>>0?b:a}else{a=2147483647}Ta(s,a);d=D[f+64>>2]}k=k+1|0;D[f+64>>2]=d+1;a=D[f+60>>2]+(d>>>3&536870908)|0;D[a>>2]=D[a>>2]|1<<d;a=D[f+76>>2];if((a|0)!=D[f+80>>2]){D[a>>2]=n;D[f+76>>2]=a+4;break Y}b=D[f+72>>2];c=a-b|0;d=c>>2;a=d+1|0;if(a>>>0>=1073741824){break b}i=c>>1;i=d>>>0<536870911?a>>>0>i>>>0?a:i:1073741823;if(i){if(i>>>0>=1073741824){break p}a=na(i<<2)}else{a=0}d=a+(d<<2)|0;D[d>>2]=n;if((c|0)>0){oa(a,b,c)}D[f+80>>2]=a+(i<<2);D[f+76>>2]=d+4;D[f+72>>2]=a;if(!b){break Y}ma(b);break Y}c=D[f+64>>2];a=D[f+68>>2];if((c|0)==a<<5){if((c+1|0)<0){break b}if(c>>>0<=1073741822){b=c+32&-32;a=a<<6;a=a>>>0<b>>>0?b:a}else{a=2147483647}Ta(s,a);c=D[f+64>>2]}D[f+64>>2]=c+1;a=D[f+60>>2]+(c>>>3&536870908)|0;b=D[a>>2];M=a,N=di(c)&b,D[M>>2]=N;a=D[f+76>>2];if((a|0)!=D[f+80>>2]){D[a>>2]=j;D[f+76>>2]=a+4;break Y}b=D[f+72>>2];c=a-b|0;d=c>>2;a=d+1|0;if(a>>>0>=1073741824){break b}i=c>>1;i=d>>>0<536870911?a>>>0>i>>>0?a:i:1073741823;if(i){if(i>>>0>=1073741824){break p}a=na(i<<2)}else{a=0}d=a+(d<<2)|0;D[d>>2]=j;if((c|0)>0){oa(a,b,c)}D[f+80>>2]=a+(i<<2);D[f+76>>2]=d+4;D[f+72>>2]=a;if(!b){break Y}ma(b)}if((g|0)!=(h|0)){continue}break}c=D[f+8>>2]}if(((D[c+4>>2]-D[c>>2]>>2>>>0)/3|0)!=(k|0)){break q}d=D[c+24>>2];e=D[c+28>>2]-d>>2;if((o|0)==(p|0)){o=p;break q}a=o;while(1){b=D[a>>2];g=e-1|0;i=(g<<2)+d|0;if(D[i>>2]==-1){while(1){g=e-2|0;e=e-1|0;i=(g<<2)+d|0;if(D[i>>2]==-1){continue}break}}if(b>>>0<=g>>>0){D[m>>2]=c;d=D[i>>2];B[m+12|0]=1;D[m+8>>2]=d;D[m+4>>2]=d;if((d|0)!=-1){while(1){D[D[c>>2]+(d<<2)>>2]=b;kc(m);c=D[f+8>>2];d=D[m+8>>2];if((d|0)!=-1){continue}break}}d=D[c+24>>2];k=d+(g<<2)|0;if((b|0)!=-1){D[(b<<2)+d>>2]=D[k>>2]}D[k>>2]=-1;k=1<<b;s=D[f+120>>2];b=s+(b>>>3&536870908)|0;s=s+(g>>>3&536870908)|0;g=1<<g;if(D[s>>2]&g){k=k|D[b>>2]}else{k=D[b>>2]&(k^-1)}D[b>>2]=k;D[s>>2]=D[s>>2]&(g^-1);e=e-1|0}a=a+4|0;if((p|0)!=(a|0)){continue}break}}if(o){ma(o)}a=D[m+48>>2];if(a){while(1){b=D[a>>2];ma(a);a=b;if(a){continue}break}}a=D[m+40>>2];D[m+40>>2]=0;if(a){ma(a)}if(h){D[m+68>>2]=h;ma(h)}$=m+96|0;break o}ra(1326);T()}if((e|0)==-1){break n}a=D[K+16>>2];b=a+D[K>>2]|0;h=D[K+8>>2];h=h-a|0;a=D[D[f+4>>2]+32>>2];C[a+38>>1]=F[a+38>>1];D[a>>2]=b;D[a+16>>2]=0;D[a+20>>2]=0;D[a+8>>2]=h;D[a+12>>2]=0;aa:{if(D[f+216>>2]==D[f+220>>2]){break aa}a=D[f+8>>2];if(D[a+4>>2]==D[a>>2]){break aa}b=0;while(1){if(td(f,b)){b=b+3|0;a=D[f+8>>2];if(b>>>0<D[a+4>>2]-D[a>>2]>>2>>>0){continue}break aa}break}break n}if(E[f+308|0]){B[f+308|0]=0;b=D[f+292>>2];a=0;h=D[f+304>>2]+7|0;a=h>>>0<7?1:a;c=a>>>3|0;h=a<<29|h>>>3;a=h+D[f+288>>2]|0;b=b+c|0;D[f+288>>2]=a;D[f+292>>2]=a>>>0<h>>>0?b+1|0:b}b=D[f+216>>2];if((b|0)!=D[f+220>>2]){while(1){a=J(H,144);Tc((a+b|0)+4|0,D[f+8>>2]);c=D[I>>2];h=a+c|0;b=D[h+132>>2];h=D[h+136>>2];if((b|0)!=(h|0)){while(1){Rc((a+c|0)+4|0,D[b>>2]);c=D[I>>2];b=b+4|0;if((h|0)!=(b|0)){continue}break}}Sc((a+c|0)+4|0);H=H+1|0;b=D[f+216>>2];if(H>>>0<(D[f+220>>2]-b|0)/144>>>0){continue}break}}a=D[f+8>>2];Gb(f+184|0,D[a+28>>2]-D[a+24>>2]>>2);c=D[f+216>>2];if((c|0)!=D[f+220>>2]){b=0;while(1){a=J(b,144)+c|0;h=D[a+60>>2]-D[a+56>>2]>>2;c=a+104|0;a=D[f+8>>2];a=D[a+28>>2]-D[a+24>>2]>>2;Gb(c,(a|0)>(h|0)?a:h);b=b+1|0;c=D[f+216>>2];if(b>>>0<(D[f+220>>2]-c|0)/144>>>0){continue}break}}H=sd(f,e)}}$=u- -64|0;return H|0}qa();T()}function eh(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,H=0,I=0,K=0,L=0;v=$+-64|0;$=v;D[a+132>>2]=0;if(D[a+148>>2]){b=D[a+144>>2];if(b){while(1){e=D[b>>2];ma(b);b=e;if(e){continue}break}}b=0;D[a+144>>2]=0;e=D[a+140>>2];a:{if(!e){break a}if(e-1>>>0>=3){h=e&-4;while(1){c=b<<2;D[c+D[a+136>>2]>>2]=0;D[D[a+136>>2]+(c|4)>>2]=0;D[D[a+136>>2]+(c|8)>>2]=0;D[D[a+136>>2]+(c|12)>>2]=0;b=b+4|0;d=d+4|0;if((h|0)!=(d|0)){continue}break}}e=e&3;if(!e){break a}while(1){D[D[a+136>>2]+(b<<2)>>2]=0;b=b+1|0;n=n+1|0;if((e|0)!=(n|0)){continue}break}}D[a+148>>2]=0}b:{if(!Da(1,v+60|0,D[D[a+4>>2]+32>>2])){break b}D[a+156>>2]=D[v+60>>2];if(!Da(1,v+56|0,D[D[a+4>>2]+32>>2])){break b}d=D[v+56>>2];if(d>>>0>1431655765|G[a+156>>2]>J(d,3)>>>0){break b}e=D[D[a+4>>2]+32>>2];c=D[e+8>>2];n=D[e+12>>2];b=D[e+20>>2];f=c;c=D[e+16>>2];if((n|0)<=(b|0)&f>>>0<=c>>>0|(b|0)>(n|0)){break b}n=E[c+D[e>>2]|0];c=c+1|0;b=c?b:b+1|0;D[e+16>>2]=c;D[e+20>>2]=b;if(!Da(1,v+52|0,e)){break b}m=D[v+52>>2];if(m>>>0>d>>>0|d>>>0>m+((m>>>0)/3|0)>>>0){break b}if(!Da(1,v+48|0,D[D[a+4>>2]+32>>2])){break b}e=D[v+48>>2];if(e>>>0>m>>>0){break b}D[a+28>>2]=D[a+24>>2];c=na(88);Wc(c);b=D[a+8>>2];D[a+8>>2]=c;if(b){ab(b);if(!D[a+8>>2]){break b}}D[a+164>>2]=D[a+160>>2];Hb(a+160|0,d);D[a+176>>2]=D[a+172>>2];Hb(a+172|0,d);D[a- -64>>2]=0;D[a+92>>2]=-1;D[a+84>>2]=-1;D[a+88>>2]=-1;D[a+40>>2]=D[a+36>>2];D[a+52>>2]=D[a+48>>2];D[a+76>>2]=D[a+72>>2];H=a+216|0;xd(H);wd(H,n);if(!Vc(D[a+8>>2],d,e+D[a+156>>2]|0)){break b}b=D[a+156>>2];B[v+8|0]=1;Ea(a+120|0,b+e|0,v+8|0);if((vd(a,D[D[a+4>>2]+32>>2])|0)==-1){break b}e=a+232|0;ud(e,a);D[a+372>>2]=n;o=v+8|0;C[o+38>>1]=0;D[o>>2]=0;D[o+8>>2]=0;D[o+12>>2]=0;D[o+16>>2]=0;D[o+20>>2]=0;D[o+24>>2]=0;D[o+28>>2]=0;B[o+29|0]=0;B[o+30|0]=0;B[o+31|0]=0;B[o+32|0]=0;B[o+33|0]=0;B[o+34|0]=0;B[o+35|0]=0;B[o+36|0]=0;b=0;n=$-16|0;$=n;d=D[e+4>>2];D[e+40>>2]=D[e>>2];D[e+44>>2]=d;d=D[e+36>>2];D[e+72>>2]=D[e+32>>2];D[e+76>>2]=d;c=D[e+28>>2];d=e- -64|0;D[d>>2]=D[e+24>>2];D[d+4>>2]=c;d=D[e+20>>2];D[e+56>>2]=D[e+16>>2];D[e+60>>2]=d;d=D[e+12>>2];D[e+48>>2]=D[e+8>>2];D[e+52>>2]=d;c:{d:{if(cc(e+40|0,1,n+8|0)){d=D[e+44>>2];D[e>>2]=D[e+40>>2];D[e+4>>2]=d;d=D[e+76>>2];D[e+32>>2]=D[e+72>>2];D[e+36>>2]=d;d=D[e+68>>2];D[e+24>>2]=D[e+64>>2];D[e+28>>2]=d;c=D[e+60>>2];h=c;d=D[e+56>>2];D[e+16>>2]=d;D[e+20>>2]=c;f=D[e+52>>2];c=D[e+48>>2];D[e+8>>2]=c;D[e+12>>2]=f;g=c-d|0;k=D[n+12>>2];f=f-((c>>>0<d>>>0)+h|0)|0;c=D[n+8>>2];if((k|0)==(f|0)&g>>>0>=c>>>0|f>>>0>k>>>0){break d}}break c}f=c+d|0;d=h+k|0;D[e+16>>2]=f;D[e+20>>2]=c>>>0>f>>>0?d+1|0:d;if(!Ka(e+80|0,e)){break c}if(!rd(e)){break c}b=D[e+4>>2];D[o>>2]=D[e>>2];D[o+4>>2]=b;b=D[e+36>>2];D[o+32>>2]=D[e+32>>2];D[o+36>>2]=b;b=D[e+28>>2];D[o+24>>2]=D[e+24>>2];D[o+28>>2]=b;b=D[e+20>>2];D[o+16>>2]=D[e+16>>2];D[o+20>>2]=b;b=D[e+12>>2];D[o+8>>2]=D[e+8>>2];D[o+12>>2]=b;b=1}$=n+16|0;e:{if(!b){break e}d=0;e=0;b=0;n=0;k=$-96|0;$=k;D[k+72>>2]=0;D[k+64>>2]=0;D[k+68>>2]=0;D[k+48>>2]=0;D[k+52>>2]=0;D[k+40>>2]=0;D[k+44>>2]=0;D[k+56>>2]=1065353216;D[k+32>>2]=0;D[k+24>>2]=0;D[k+28>>2]=0;i=1;h=a;t=D[a+124>>2];f:{g:{h:{i:{j:{k:{l:{m:{if((m|0)<=0){break m}I=D[h+216>>2]!=D[h+220>>2];while(1){f=r;r=f+1|0;n:{o:{p:{q:{r:{s:{t:{u:{v:{w:{x:{if(!E[h+308|0]){break x}y:{z:{g=D[h+296>>2];c=D[h+304>>2];a=g+(c>>>3|0)|0;l=D[h+300>>2];if(a>>>0>=l>>>0){break z}j=E[a|0];a=c+1|0;D[h+304>>2]=a;if(!(j>>>(c&7)&1)){break z}j=a>>>3|0;p=g+j|0;A:{if(p>>>0>=l>>>0){c=a;a=0;break A}p=E[p|0];c=c+2|0;D[h+304>>2]=c;j=c>>>3|0;a=p>>>(a&7)&1}g=g+j|0;if(g>>>0<l>>>0){g=E[g|0];D[h+304>>2]=c+1;c=g>>>(c&7)<<1&2}else{c=0}a=(a|c)<<1;switch(a-1|0){case 0:case 2:case 4:break h;case 5:break w;case 1:case 3:break y;default:break x}}if((b|0)==(e|0)){c=-1;break l}j=-1;g=D[h+8>>2];p=D[g+24>>2];s=e-4|0;c=D[s>>2];a=-1;B:{if((c|0)==-1){break B}i=c+1|0;i=(i>>>0)%3|0?i:c-2|0;a=-1;if((i|0)==-1){break B}a=D[D[g>>2]+(i<<2)>>2]}d=D[p+(a<<2)>>2];if((d|0)!=-1){i=d+1|0;j=(i>>>0)%3|0?i:d-2|0}d=D[g+12>>2];f=J(f,3);i=f+1|0;D[d+(c<<2)>>2]=i;w=i<<2;D[w+d>>2]=c;l=f+2|0;D[d+(j<<2)>>2]=l;x=l<<2;D[x+d>>2]=j;i=-1;d=-1;C:{if((c|0)==-1){break C}D:{if((c>>>0)%3|0){c=c-1|0;break D}c=c+2|0;d=-1;if((c|0)==-1){break C}}d=D[D[g>>2]+(c<<2)>>2]}E:{if((j|0)==-1){break E}c=j+1|0;c=(c>>>0)%3|0?c:j-2|0;if((c|0)==-1){break E}i=D[D[g>>2]+(c<<2)>>2]}c=-1;if((a|0)==(d|0)|(a|0)==(i|0)){break l}c=D[g>>2];D[c+(f<<2)>>2]=a;D[c+w>>2]=i;D[c+x>>2]=d;if((d|0)!=-1){D[p+(d<<2)>>2]=l}d=D[h+120>>2]+(a>>>3&536870908)|0;c=D[d>>2];K=d,L=di(a)&c,D[K>>2]=L;D[s>>2]=f;d=b;break n}if((b|0)==(e|0)){c=-1;break l}d=D[h+8>>2];c=D[d+12>>2];g=J(f,3);p=(a|1)==5;j=g+(p?2:1)|0;w=j<<2;x=e-4|0;a=D[x>>2];D[c+w>>2]=a;D[c+(a<<2)>>2]=j;Ra(d+24|0);c=-1;s=D[h+8>>2];l=D[s+24>>2];if((t|0)<D[s+28>>2]-l>>2){break l}c=D[s>>2];d=D[d+28>>2]-D[d+24>>2]|0;s=(d>>2)-1|0;D[c+w>>2]=s;if(d){D[l+(s<<2)>>2]=j}j=p?g:g+2|0;p=c+(g+p<<2)|0;F:{if((a|0)==-1){D[c+(j<<2)>>2]=-1;d=-1;break F}G:{H:{I:{if((a>>>0)%3|0){d=a-1|0;break I}d=a+2|0;if((d|0)==-1){break H}}d=D[c+(d<<2)>>2];D[c+(j<<2)>>2]=d;if((d|0)==-1){break G}D[l+(d<<2)>>2]=j;break G}D[c+(j<<2)>>2]=-1}j=a+1|0;a=(j>>>0)%3|0?j:a-2|0;d=-1;if((a|0)==-1){break F}d=D[c+(a<<2)>>2]}D[p>>2]=d;D[x>>2]=g;d=b;break v}if((b|0)==(e|0)){c=-1;break l}a=e-4|0;l=D[a>>2];D[k+68>>2]=a;j=D[k+44>>2];J:{if(!j){e=a;break J}g=ci(j)>>>0>1;K:{if(!g){d=f&j+2147483647;break K}d=f;if(j>>>0>d>>>0){break K}d=(f>>>0)%(j>>>0)|0}c=D[D[k+40>>2]+(d<<2)>>2];if(!c){e=a;break J}c=D[c>>2];if(!c){e=a;break J}L:{if(!g){g=j-1|0;while(1){j=D[c+4>>2];M:{if((j|0)!=(f|0)){if((g&j)==(d|0)){break M}e=a;break J}if((f|0)==D[c+8>>2]){break L}}c=D[c>>2];if(c){continue}break}e=a;break J}while(1){g=D[c+4>>2];N:{if((g|0)!=(f|0)){if(g>>>0>=j>>>0){g=(g>>>0)%(j>>>0)|0}if((d|0)==(g|0)){break N}e=a;break J}if((f|0)==D[c+8>>2]){break L}}c=D[c>>2];if(c){continue}break}e=a;break J}if((a|0)!=(u|0)){D[a>>2]=D[c+12>>2];D[k+68>>2]=e;break J}a=u-b|0;d=a>>2;e=d+1|0;if(e>>>0>=1073741824){break t}q=a>>1;e=d>>>0<536870911?e>>>0>q>>>0?e:q:1073741823;if(e){if(e>>>0>=1073741824){break g}q=na(e<<2)}else{q=0}d=q+(d<<2)|0;D[d>>2]=D[c+12>>2];u=(e<<2)+q|0;e=d+4|0;if((a|0)>0){oa(q,b,a)}D[k+72>>2]=u;D[k+68>>2]=e;D[k+64>>2]=q;if(!b){break J}ma(b)}if((e|0)==(q|0)){break u}w=e-4|0;a=D[w>>2];d=(a|0)==-1;b=D[h+8>>2];if(!d&D[D[b+12>>2]+(a<<2)>>2]!=-1){break u}g=D[b+12>>2];if((l|0)!=-1&D[g+(l<<2)>>2]!=-1){break u}f=J(f,3);s=f+2|0;D[g+(a<<2)>>2]=s;p=s<<2;D[p+g>>2]=a;c=f+1|0;D[g+(l<<2)>>2]=c;x=c<<2;D[x+g>>2]=l;if(d){break s}if((a>>>0)%3|0){i=a-1|0;break q}i=a+2|0;if((i|0)!=-1){break q}j=D[b>>2];d=-1;break p}j=D[h+8>>2];Ra(j+24|0);c=-1;g=D[h+8>>2];a=J(f,3);l=D[j+28>>2]-D[j+24>>2]|0;j=l>>2;s=j-1|0;D[D[g>>2]+(a<<2)>>2]=s;Ra(g+24|0);p=a+1|0;D[D[g>>2]+(p<<2)>>2]=(D[g+28>>2]-D[g+24>>2]>>2)-1;g=D[h+8>>2];Ra(g+24|0);w=a+2|0;D[D[g>>2]+(w<<2)>>2]=(D[g+28>>2]-D[g+24>>2]>>2)-1;x=D[h+8>>2];g=D[x+24>>2];if((t|0)<D[x+28>>2]-g>>2){break l}O:{P:{if(!l){D[g+(j<<2)>>2]=p;c=1;break P}D[g+(s<<2)>>2]=a;c=0;if((l|0)==-4){break P}D[g+(j<<2)>>2]=p;c=j+1|0;if((c|0)==-1){break O}}D[g+(c<<2)>>2]=w}if((e|0)!=(u|0)){D[e>>2]=a;e=e+4|0;D[k+68>>2]=e;break v}b=e-d|0;c=b>>2;e=c+1|0;if(e>>>0>=1073741824){break r}q=b>>1;e=c>>>0<536870911?e>>>0>q>>>0?e:q:1073741823;if(e){if(e>>>0>=1073741824){break g}q=na(e<<2)}else{q=0}c=q+(c<<2)|0;D[c>>2]=a;u=(e<<2)+q|0;e=c+4|0;if((b|0)>0){oa(q,d,b)}D[k+72>>2]=u;D[k+68>>2]=e;D[k+64>>2]=q;if(d){ma(d)}b=q;d=b}c=D[h+40>>2];if((c|0)==D[h+36>>2]){break n}f=m+(f^-1)|0;g=e-4|0;while(1){a=D[c-8>>2];if(a>>>0>f>>>0){break u}if((a|0)!=(f|0)){break n}j=E[c-4|0];a=c-12|0;c=D[a>>2];D[h+40>>2]=a;if((c|0)<0){break u}a=D[g>>2];D[k+20>>2]=m+(c^-1);c=k+20|0;D[k+88>>2]=c;qd(k,k+40|0,c,k+88|0);p=D[k>>2];Q:{if(j&1){c=-1;if((a|0)==-1){break Q}c=a+1|0;c=(c>>>0)%3|0?c:a-2|0;break Q}c=-1;if((a|0)==-1){break Q}c=a-1|0;if((a>>>0)%3|0){break Q}c=a+2|0}D[p+12>>2]=c;c=D[h+40>>2];if((c|0)!=D[h+36>>2]){continue}break}break n}c=-1;if(i&1){break l}break m}qa();T()}d=-1;j=D[b>>2];D[j+(f<<2)>>2]=-1;c=-1;break o}qa();T()}j=D[b>>2];d=D[j+(i<<2)>>2]}D[(f<<2)+j>>2]=d;i=a+1|0;a=(i>>>0)%3|0?i:a-2|0;c=-1;if((a|0)==-1){break o}c=D[(a<<2)+j>>2]}D[j+x>>2]=c;R:{if((l|0)==-1){D[j+p>>2]=-1;i=-1;c=-1;break R}S:{T:{U:{if((l>>>0)%3|0){c=l-1|0;break U}c=l+2|0;if((c|0)==-1){break T}}a=D[(c<<2)+j>>2];D[j+p>>2]=a;if((a|0)==-1){break S}D[D[b+24>>2]+(a<<2)>>2]=s;break S}D[j+p>>2]=-1}i=-1;a=l+1|0;a=(a>>>0)%3|0?a:l-2|0;c=-1;if((a|0)==-1){break R}i=D[(a<<2)+j>>2];c=a}b=D[b+24>>2];a=b+(i<<2)|0;if((d|0)!=-1){D[b+(d<<2)>>2]=D[a>>2]}V:{if((c|0)==-1){break V}while(1){D[(c<<2)+j>>2]=d;b=c+1|0;b=(b>>>0)%3|0?b:c-2|0;if((b|0)==-1){break V}b=D[g+(b<<2)>>2];if((b|0)==-1){break V}c=b+1|0;c=(c>>>0)%3|0?c:b-2|0;if((c|0)!=-1){continue}break}}D[a>>2]=-1;W:{if(I){break W}if((y|0)!=(z|0)){D[y>>2]=i;y=y+4|0;D[k+28>>2]=y;break W}X:{b=z-n|0;c=b>>2;a=c+1|0;if(a>>>0<1073741824){d=b>>1;d=c>>>0<536870911?a>>>0>d>>>0?a:d:1073741823;if(d){if(d>>>0>=1073741824){break X}a=na(d<<2)}else{a=0}c=a+(c<<2)|0;D[c>>2]=i;z=a+(d<<2)|0;y=c+4|0;if((b|0)>0){oa(a,n,b)}D[k+32>>2]=z;D[k+28>>2]=y;D[k+24>>2]=a;if(n){ma(n)}n=a;break W}qa();T()}break g}D[w>>2]=f;b=q;d=b}i=(m|0)>(r|0);if((m|0)!=(r|0)){continue}break}r=m}c=-1;i=D[h+8>>2];if((t|0)<D[i+28>>2]-D[i+24>>2]>>2){break l}if((e|0)!=(q|0)){a=h+60|0;p=h+312|0;while(1){e=e-4|0;l=D[e>>2];D[k+68>>2]=e;Y:{if(Ga(p)){t=D[h+8>>2];u=D[t>>2];if(((D[t+4>>2]-u>>2>>>0)/3|0)<=(r|0)){break l}b=-1;d=-1;m=D[t+24>>2];f=-1;Z:{if((l|0)==-1){break Z}g=l+1|0;g=(g>>>0)%3|0?g:l-2|0;f=-1;if((g|0)==-1){break Z}f=D[u+(g<<2)>>2]}g=f;f=D[m+(g<<2)>>2];_:{if((f|0)==-1){break _}i=f+1|0;f=(i>>>0)%3|0?i:f-2|0;if((f|0)==-1){break _}b=f+1|0;b=(b>>>0)%3|0?b:f-2|0;if((b|0)!=-1){d=D[u+(b<<2)>>2]}b=f}i=-1;j=-1;m=D[m+(d<<2)>>2];f=-1;$:{if((m|0)==-1){break $}z=m+1|0;m=(z>>>0)%3|0?z:m-2|0;f=-1;if((m|0)==-1){break $}f=m+1|0;f=(f>>>0)%3|0?f:m-2|0;if((f|0)!=-1){j=D[u+(f<<2)>>2]}f=m}t=D[t+12>>2];m=J(r,3);z=m<<2;D[t+z>>2]=l;D[t+(l<<2)>>2]=m;l=m+1|0;s=l<<2;D[s+t>>2]=b;D[t+(b<<2)>>2]=l;b=m+2|0;I=b<<2;D[I+t>>2]=f;D[t+(f<<2)>>2]=b;D[u+z>>2]=d;f=u+s|0;D[f>>2]=j;j=u+I|0;D[j>>2]=g;g=l>>>0<m>>>0?-1:d;d=D[h+120>>2];u=(g>>>3&536870908)+d|0;t=D[u>>2];K=u,L=di(g)&t,D[K>>2]=L;i=(l|0)!=-1?D[f>>2]:i;f=d+(i>>>3&536870908)|0;g=D[f>>2];K=f,L=di(i)&g,D[K>>2]=L;i=-1;i=(b|0)!=-1?D[j>>2]:i;b=d+(i>>>3&536870908)|0;d=D[b>>2];K=b,L=di(i)&d,D[K>>2]=L;i=D[h+64>>2];b=D[h+68>>2];if((i|0)==b<<5){if((i+1|0)<0){break k}if(i>>>0<=1073741822){d=i+32&-32;b=b<<6;b=b>>>0<d>>>0?d:b}else{b=2147483647}Ta(a,b);i=D[h+64>>2]}r=r+1|0;D[h+64>>2]=i+1;b=D[h+60>>2]+(i>>>3&536870908)|0;D[b>>2]=D[b>>2]|1<<i;b=D[h+76>>2];if((b|0)!=D[h+80>>2]){D[b>>2]=m;D[h+76>>2]=b+4;break Y}d=D[h+72>>2];f=b-d|0;i=f>>2;b=i+1|0;if(b>>>0>=1073741824){break j}g=f>>1;g=i>>>0<536870911?b>>>0>g>>>0?b:g:1073741823;if(g){if(g>>>0>=1073741824){break g}b=na(g<<2)}else{b=0}i=b+(i<<2)|0;D[i>>2]=m;if((f|0)>0){oa(b,d,f)}D[h+80>>2]=b+(g<<2);D[h+76>>2]=i+4;D[h+72>>2]=b;if(!d){break Y}ma(d);break Y}b=D[h+64>>2];d=D[h+68>>2];if((b|0)==d<<5){if((b+1|0)<0){break k}if(b>>>0<=1073741822){b=b+32&-32;d=d<<6;b=b>>>0>d>>>0?b:d}else{b=2147483647}Ta(a,b);b=D[h+64>>2]}D[h+64>>2]=b+1;d=D[h+60>>2]+(b>>>3&536870908)|0;f=D[d>>2];K=d,L=di(b)&f,D[K>>2]=L;b=D[h+76>>2];if((b|0)!=D[h+80>>2]){D[b>>2]=l;D[h+76>>2]=b+4;break Y}d=D[h+72>>2];f=b-d|0;g=f>>2;b=g+1|0;if(b>>>0>=1073741824){break j}m=f>>1;m=g>>>0<536870911?b>>>0>m>>>0?b:m:1073741823;if(m){if(m>>>0>=1073741824){break i}b=na(m<<2)}else{b=0}g=b+(g<<2)|0;D[g>>2]=l;if((f|0)>0){oa(b,d,f)}D[h+80>>2]=b+(m<<2);D[h+76>>2]=g+4;D[h+72>>2]=b;if(!d){break Y}ma(d)}if((e|0)!=(q|0)){continue}break}i=D[h+8>>2]}if(((D[i+4>>2]-D[i>>2]>>2>>>0)/3|0)!=(r|0)){break l}r=D[i+24>>2];c=D[i+28>>2]-r>>2;if((n|0)==(y|0)){n=y;break l}d=n;while(1){a=D[d>>2];e=c-1|0;j=(e<<2)+r|0;if(D[j>>2]==-1){while(1){e=c-2|0;c=c-1|0;j=(e<<2)+r|0;if(D[j>>2]==-1){continue}break}}if(a>>>0<=e>>>0){D[k>>2]=i;r=D[j>>2];B[k+12|0]=1;D[k+8>>2]=r;D[k+4>>2]=r;if((r|0)!=-1){while(1){D[D[i>>2]+(r<<2)>>2]=a;kc(k);i=D[h+8>>2];r=D[k+8>>2];if((r|0)!=-1){continue}break}}r=D[i+24>>2];b=r+(e<<2)|0;if((a|0)!=-1){D[(a<<2)+r>>2]=D[b>>2]}D[b>>2]=-1;b=1<<a;f=D[h+120>>2];a=f+(a>>>3&536870908)|0;f=f+(e>>>3&536870908)|0;e=1<<e;if(D[f>>2]&e){b=b|D[a>>2]}else{b=D[a>>2]&(b^-1)}D[a>>2]=b;D[f>>2]=D[f>>2]&(e^-1);c=c-1|0}d=d+4|0;if((y|0)!=(d|0)){continue}break}}if(n){ma(n)}e=D[k+48>>2];if(e){while(1){a=D[e>>2];ma(e);e=a;if(a){continue}break}}a=D[k+40>>2];D[k+40>>2]=0;if(a){ma(a)}if(q){D[k+68>>2]=q;ma(q)}$=k+96|0;break f}qa();T()}qa();T()}ra(1326)}T()}ra(1326);T()}if((c|0)==-1){break e}a=D[o+16>>2];e=a+D[o>>2]|0;b=D[o+8>>2];b=b-a|0;a=D[D[h+4>>2]+32>>2];C[a+38>>1]=F[a+38>>1];D[a>>2]=e;D[a+16>>2]=0;D[a+20>>2]=0;D[a+8>>2]=b;D[a+12>>2]=0;aa:{if(D[h+216>>2]==D[h+220>>2]){break aa}a=D[h+8>>2];if(D[a+4>>2]==D[a>>2]){break aa}b=0;while(1){if(td(h,b)){b=b+3|0;a=D[h+8>>2];if(b>>>0<D[a+4>>2]-D[a>>2]>>2>>>0){continue}break aa}break}break e}if(E[h+308|0]){B[h+308|0]=0;e=D[h+292>>2];a=0;b=D[h+304>>2]+7|0;a=b>>>0<7?1:a;d=a>>>3|0;b=a<<29|b>>>3;a=b+D[h+288>>2]|0;d=e+d|0;D[h+288>>2]=a;D[h+292>>2]=a>>>0<b>>>0?d+1|0:d}b=D[h+216>>2];if((b|0)!=D[h+220>>2]){while(1){a=J(A,144);Tc((a+b|0)+4|0,D[h+8>>2]);n=D[H>>2];e=a+n|0;b=D[e+132>>2];e=D[e+136>>2];if((b|0)!=(e|0)){while(1){Rc((a+n|0)+4|0,D[b>>2]);n=D[H>>2];b=b+4|0;if((e|0)!=(b|0)){continue}break}}Sc((a+n|0)+4|0);A=A+1|0;b=D[h+216>>2];if(A>>>0<(D[h+220>>2]-b|0)/144>>>0){continue}break}}a=D[h+8>>2];Gb(h+184|0,D[a+28>>2]-D[a+24>>2]>>2);n=D[h+216>>2];if((n|0)!=D[h+220>>2]){b=0;while(1){a=J(b,144)+n|0;e=D[a+60>>2]-D[a+56>>2]>>2;d=a+104|0;a=D[h+8>>2];a=D[a+28>>2]-D[a+24>>2]>>2;Gb(d,(a|0)>(e|0)?a:e);b=b+1|0;n=D[h+216>>2];if(b>>>0<(D[h+220>>2]-n|0)/144>>>0){continue}break}}A=sd(h,c)}}$=v- -64|0;return A|0}function lf(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;a:{b:{c:{d:{e:{switch(d-1|0){case 0:a=0;l=$-16|0;$=l;j=D[b+80>>2];g=B[c+24|0];f:{if((J(j,g)|0)!=(e|0)){break f}b=D[c+28>>2]!=1;a=E[c+84|0];if(!(b|!a)){oa(f,D[D[c>>2]>>2]+D[c+48>>2]|0,e);a=1;break f}d=0;D[l+8>>2]=0;D[l>>2]=0;D[l+4>>2]=0;if(g){if((g|0)<0){break d}h=na(g);D[l>>2]=h;pa(h,0,g)}g:{h:{if(!j){break h}if(!b){if(g){o=g&-4;k=g&3;b=0;s=g-1>>>0<3;while(1){e=D[D[c>>2]>>2];q=D[c+40>>2];a=D[c+48>>2]+$h(q,D[c+44>>2],a&255?d:D[D[c+68>>2]+(d<<2)>>2],0)|0;e=oa(h,e+a|0,q);m=0;a=0;i=0;if(!s){while(1){g=b+f|0;B[g|0]=E[a+e|0];B[g+1|0]=E[e+(a|1)|0];B[g+2|0]=E[e+(a|2)|0];B[g+3|0]=E[e+(a|3)|0];a=a+4|0;b=b+4|0;i=i+4|0;if((o|0)!=(i|0)){continue}break}}if(k){while(1){B[b+f|0]=E[a+e|0];a=a+1|0;b=b+1|0;m=m+1|0;if((k|0)!=(m|0)){continue}break}}a=1;d=d+1|0;if((j|0)==(d|0)){break g}a=E[c+84|0];continue}}m=D[c>>2];e=D[c+48>>2];s=D[c+68>>2];f=D[c+44>>2];c=D[c+40>>2];q=c;b=0;if((j|0)!=1){p=j&-2;r=a&255;while(1){g=b|1;n=D[m>>2];k=$h(c,f,r?b:D[s+(b<<2)>>2],0)+e|0;k=oa(h,n+k|0,q);n=D[m>>2];if(!r){g=D[s+(g<<2)>>2]}g=$h(c,f,g,0)+e|0;oa(k,g+n|0,q);b=b+2|0;i=i+2|0;if((p|0)!=(i|0)){continue}break}}if(!(j&1)){break h}g=D[m>>2];if(!(a&255)){b=D[s+(b<<2)>>2]}a=$h(c,f,b,0)+e|0;oa(h,a+g|0,q);break h}if(!g){b=0;d=1;while(1){if(!Db(c,a&255?b:D[D[c+68>>2]+(b<<2)>>2],B[c+24|0],h)){break h}b=b+1|0;d=j>>>0>b>>>0;if((b|0)==(j|0)){break h}a=E[c+84|0];continue}}i=g&-4;k=g&3;b=0;o=g-1>>>0<3;d=1;e=0;while(1){if(!Db(c,a&255?e:D[D[c+68>>2]+(e<<2)>>2],B[c+24|0],h)){break h}d=0;a=0;m=0;if(!o){while(1){g=b+f|0;B[g|0]=E[a+h|0];B[g+1|0]=E[(a|1)+h|0];B[g+2|0]=E[(a|2)+h|0];B[g+3|0]=E[(a|3)+h|0];a=a+4|0;b=b+4|0;m=m+4|0;if((i|0)!=(m|0)){continue}break}}if(k){while(1){B[b+f|0]=E[a+h|0];a=a+1|0;b=b+1|0;d=d+1|0;if((k|0)!=(d|0)){continue}break}}e=e+1|0;if((j|0)!=(e|0)){d=e>>>0<j>>>0;a=E[c+84|0];continue}break}a=e>>>0>=j>>>0;break g}a=d^1;if(!h){break f}}ma(h)}break c;case 2:a=0;m=$-16|0;$=m;d=B[c+24|0];g=d<<1;j=D[b+80>>2];i:{if((J(g,j)|0)!=(e|0)){break i}b=D[c+28>>2]!=3;a=E[c+84|0];if(!(b|!a)){oa(f,D[D[c>>2]>>2]+D[c+48>>2]|0,e);a=1;break i}D[m+8>>2]=0;D[m>>2]=0;D[m+4>>2]=0;if(d){if((d|0)<0){break d}h=na(g);D[m>>2]=h;pa(h,0,g)}j:{k:{if(!j){break k}if(!b){o=D[c>>2];g=D[c+48>>2];q=D[c+68>>2];k=D[c+44>>2];l=D[c+40>>2];r=l;if(d){u=d&-4;t=d&3;b=0;v=a&255;w=d-1>>>0<3;d=0;while(1){c=D[o>>2];a=$h(l,k,v?d:D[q+(d<<2)>>2],0)+g|0;i=oa(h,c+a|0,r);e=0;a=0;c=0;if(!w){while(1){p=(b<<1)+f|0;n=a<<1;C[p>>1]=F[n+i>>1];C[p+2>>1]=F[i+(n|2)>>1];C[p+4>>1]=F[i+(n|4)>>1];C[p+6>>1]=F[i+(n|6)>>1];a=a+4|0;b=b+4|0;c=c+4|0;if((u|0)!=(c|0)){continue}break}}if(t){while(1){C[(b<<1)+f>>1]=F[i+(a<<1)>>1];a=a+1|0;b=b+1|0;e=e+1|0;if((t|0)!=(e|0)){continue}break}}a=1;d=d+1|0;if((j|0)!=(d|0)){continue}break}break j}b=0;if((j|0)!=1){p=j&-2;f=a&255;d=0;while(1){c=b|1;n=D[o>>2];e=$h(l,k,f?b:D[q+(b<<2)>>2],0)+g|0;e=oa(h,n+e|0,r);n=D[o>>2];if(!f){c=D[q+(c<<2)>>2]}c=$h(l,k,c,0)+g|0;oa(e,c+n|0,r);b=b+2|0;d=d+2|0;if((p|0)!=(d|0)){continue}break}}if(!(j&1)){break k}c=D[o>>2];if(!(a&255)){b=D[q+(b<<2)>>2]}a=$h(l,k,b,0)+g|0;oa(h,a+c|0,r);break k}if(!d){b=0;i=1;while(1){if(!Bb(c,a&255?b:D[D[c+68>>2]+(b<<2)>>2],B[c+24|0],h)){break k}b=b+1|0;i=j>>>0>b>>>0;if((b|0)==(j|0)){break k}a=E[c+84|0];continue}}o=d&-4;l=d&3;b=0;s=d-1>>>0<3;i=1;d=0;while(1){if(!Bb(c,a&255?d:D[D[c+68>>2]+(d<<2)>>2],B[c+24|0],h)){break k}i=0;a=0;e=0;if(!s){while(1){g=(b<<1)+f|0;k=a<<1;C[g>>1]=F[k+h>>1];C[g+2>>1]=F[(k|2)+h>>1];C[g+4>>1]=F[(k|4)+h>>1];C[g+6>>1]=F[(k|6)+h>>1];a=a+4|0;b=b+4|0;e=e+4|0;if((o|0)!=(e|0)){continue}break}}if(l){while(1){C[(b<<1)+f>>1]=F[(a<<1)+h>>1];a=a+1|0;b=b+1|0;i=i+1|0;if((l|0)!=(i|0)){continue}break}}d=d+1|0;if((j|0)!=(d|0)){i=d>>>0<j>>>0;a=E[c+84|0];continue}break}a=d>>>0>=j>>>0;break j}a=i^1;if(!h){break i}}ma(h)}break b;case 4:a=0;m=$-16|0;$=m;d=B[c+24|0];g=d<<2;j=D[b+80>>2];l:{if((J(g,j)|0)!=(e|0)){break l}b=D[c+28>>2]!=5;a=E[c+84|0];if(!(b|!a)){oa(f,D[D[c>>2]>>2]+D[c+48>>2]|0,e);a=1;break l}D[m+8>>2]=0;D[m>>2]=0;D[m+4>>2]=0;if(d){if((d|0)<0){break d}h=na(g);D[m>>2]=h;pa(h,0,g)}m:{n:{if(!j){break n}if(!b){o=D[c>>2];g=D[c+48>>2];q=D[c+68>>2];k=D[c+44>>2];l=D[c+40>>2];r=l;if(d){u=d&-4;t=d&3;b=0;v=a&255;w=d-1>>>0<3;d=0;while(1){c=D[o>>2];a=$h(l,k,v?d:D[q+(d<<2)>>2],0)+g|0;i=oa(h,c+a|0,r);e=0;a=0;c=0;if(!w){while(1){p=(b<<2)+f|0;n=a<<2;D[p>>2]=D[n+i>>2];D[p+4>>2]=D[i+(n|4)>>2];D[p+8>>2]=D[i+(n|8)>>2];D[p+12>>2]=D[i+(n|12)>>2];a=a+4|0;b=b+4|0;c=c+4|0;if((u|0)!=(c|0)){continue}break}}if(t){while(1){D[(b<<2)+f>>2]=D[i+(a<<2)>>2];a=a+1|0;b=b+1|0;e=e+1|0;if((t|0)!=(e|0)){continue}break}}a=1;d=d+1|0;if((j|0)!=(d|0)){continue}break}break m}b=0;if((j|0)!=1){p=j&-2;f=a&255;d=0;while(1){c=b|1;n=D[o>>2];e=$h(l,k,f?b:D[q+(b<<2)>>2],0)+g|0;e=oa(h,n+e|0,r);n=D[o>>2];if(!f){c=D[q+(c<<2)>>2]}c=$h(l,k,c,0)+g|0;oa(e,c+n|0,r);b=b+2|0;d=d+2|0;if((p|0)!=(d|0)){continue}break}}if(!(j&1)){break n}c=D[o>>2];if(!(a&255)){b=D[q+(b<<2)>>2]}a=$h(l,k,b,0)+g|0;oa(h,a+c|0,r);break n}if(!d){b=0;i=1;while(1){if(!zb(c,a&255?b:D[D[c+68>>2]+(b<<2)>>2],B[c+24|0],h)){break n}b=b+1|0;i=j>>>0>b>>>0;if((b|0)==(j|0)){break n}a=E[c+84|0];continue}}o=d&-4;l=d&3;b=0;s=d-1>>>0<3;i=1;d=0;while(1){if(!zb(c,a&255?d:D[D[c+68>>2]+(d<<2)>>2],B[c+24|0],h)){break n}i=0;a=0;e=0;if(!s){while(1){g=(b<<2)+f|0;k=a<<2;D[g>>2]=D[k+h>>2];D[g+4>>2]=D[(k|4)+h>>2];D[g+8>>2]=D[(k|8)+h>>2];D[g+12>>2]=D[(k|12)+h>>2];a=a+4|0;b=b+4|0;e=e+4|0;if((o|0)!=(e|0)){continue}break}}if(l){while(1){D[(b<<2)+f>>2]=D[(a<<2)+h>>2];a=a+1|0;b=b+1|0;i=i+1|0;if((l|0)!=(i|0)){continue}break}}d=d+1|0;if((j|0)!=(d|0)){i=d>>>0<j>>>0;a=E[c+84|0];continue}break}a=d>>>0>=j>>>0;break m}a=i^1;if(!h){break l}}ma(h)}break b;case 1:a=0;l=$-16|0;$=l;j=D[b+80>>2];g=B[c+24|0];o:{if((J(j,g)|0)!=(e|0)){break o}b=D[c+28>>2]!=2;a=E[c+84|0];if(!(b|!a)){oa(f,D[D[c>>2]>>2]+D[c+48>>2]|0,e);a=1;break o}d=0;D[l+8>>2]=0;D[l>>2]=0;D[l+4>>2]=0;if(g){if((g|0)<0){break d}h=na(g);D[l>>2]=h;pa(h,0,g)}p:{q:{if(!j){break q}if(!b){if(g){o=g&-4;k=g&3;b=0;s=g-1>>>0<3;while(1){e=D[D[c>>2]>>2];q=D[c+40>>2];a=D[c+48>>2]+$h(q,D[c+44>>2],a&255?d:D[D[c+68>>2]+(d<<2)>>2],0)|0;e=oa(h,e+a|0,q);m=0;a=0;i=0;if(!s){while(1){g=b+f|0;B[g|0]=E[a+e|0];B[g+1|0]=E[e+(a|1)|0];B[g+2|0]=E[e+(a|2)|0];B[g+3|0]=E[e+(a|3)|0];a=a+4|0;b=b+4|0;i=i+4|0;if((o|0)!=(i|0)){continue}break}}if(k){while(1){B[b+f|0]=E[a+e|0];a=a+1|0;b=b+1|0;m=m+1|0;if((k|0)!=(m|0)){continue}break}}a=1;d=d+1|0;if((j|0)==(d|0)){break p}a=E[c+84|0];continue}}m=D[c>>2];e=D[c+48>>2];s=D[c+68>>2];f=D[c+44>>2];c=D[c+40>>2];q=c;b=0;if((j|0)!=1){p=j&-2;r=a&255;while(1){g=b|1;n=D[m>>2];k=$h(c,f,r?b:D[s+(b<<2)>>2],0)+e|0;k=oa(h,n+k|0,q);n=D[m>>2];if(!r){g=D[s+(g<<2)>>2]}g=$h(c,f,g,0)+e|0;oa(k,g+n|0,q);b=b+2|0;i=i+2|0;if((p|0)!=(i|0)){continue}break}}if(!(j&1)){break q}g=D[m>>2];if(!(a&255)){b=D[s+(b<<2)>>2]}a=$h(c,f,b,0)+e|0;oa(h,a+g|0,q);break q}if(!g){b=0;d=1;while(1){if(!Cb(c,a&255?b:D[D[c+68>>2]+(b<<2)>>2],B[c+24|0],h)){break q}b=b+1|0;d=j>>>0>b>>>0;if((b|0)==(j|0)){break q}a=E[c+84|0];continue}}i=g&-4;k=g&3;b=0;o=g-1>>>0<3;d=1;e=0;while(1){if(!Cb(c,a&255?e:D[D[c+68>>2]+(e<<2)>>2],B[c+24|0],h)){break q}d=0;a=0;m=0;if(!o){while(1){g=b+f|0;B[g|0]=E[a+h|0];B[g+1|0]=E[(a|1)+h|0];B[g+2|0]=E[(a|2)+h|0];B[g+3|0]=E[(a|3)+h|0];a=a+4|0;b=b+4|0;m=m+4|0;if((i|0)!=(m|0)){continue}break}}if(k){while(1){B[b+f|0]=E[a+h|0];a=a+1|0;b=b+1|0;d=d+1|0;if((k|0)!=(d|0)){continue}break}}e=e+1|0;if((j|0)!=(e|0)){d=e>>>0<j>>>0;a=E[c+84|0];continue}break}a=e>>>0>=j>>>0;break p}a=d^1;if(!h){break o}}ma(h)}break c;case 3:a=0;m=$-16|0;$=m;d=B[c+24|0];g=d<<1;j=D[b+80>>2];r:{if((J(g,j)|0)!=(e|0)){break r}b=D[c+28>>2]!=4;a=E[c+84|0];if(!(b|!a)){oa(f,D[D[c>>2]>>2]+D[c+48>>2]|0,e);a=1;break r}D[m+8>>2]=0;D[m>>2]=0;D[m+4>>2]=0;if(d){if((d|0)<0){break d}h=na(g);D[m>>2]=h;pa(h,0,g)}s:{t:{if(!j){break t}if(!b){o=D[c>>2];g=D[c+48>>2];q=D[c+68>>2];k=D[c+44>>2];l=D[c+40>>2];r=l;if(d){u=d&-4;t=d&3;b=0;v=a&255;w=d-1>>>0<3;d=0;while(1){c=D[o>>2];a=$h(l,k,v?d:D[q+(d<<2)>>2],0)+g|0;i=oa(h,c+a|0,r);e=0;a=0;c=0;if(!w){while(1){p=(b<<1)+f|0;n=a<<1;C[p>>1]=F[n+i>>1];C[p+2>>1]=F[i+(n|2)>>1];C[p+4>>1]=F[i+(n|4)>>1];C[p+6>>1]=F[i+(n|6)>>1];a=a+4|0;b=b+4|0;c=c+4|0;if((u|0)!=(c|0)){continue}break}}if(t){while(1){C[(b<<1)+f>>1]=F[i+(a<<1)>>1];a=a+1|0;b=b+1|0;e=e+1|0;if((t|0)!=(e|0)){continue}break}}a=1;d=d+1|0;if((j|0)!=(d|0)){continue}break}break s}b=0;if((j|0)!=1){p=j&-2;f=a&255;d=0;while(1){c=b|1;n=D[o>>2];e=$h(l,k,f?b:D[q+(b<<2)>>2],0)+g|0;e=oa(h,n+e|0,r);n=D[o>>2];if(!f){c=D[q+(c<<2)>>2]}c=$h(l,k,c,0)+g|0;oa(e,c+n|0,r);b=b+2|0;d=d+2|0;if((p|0)!=(d|0)){continue}break}}if(!(j&1)){break t}c=D[o>>2];if(!(a&255)){b=D[q+(b<<2)>>2]}a=$h(l,k,b,0)+g|0;oa(h,a+c|0,r);break t}if(!d){b=0;i=1;while(1){if(!Ab(c,a&255?b:D[D[c+68>>2]+(b<<2)>>2],B[c+24|0],h)){break t}b=b+1|0;i=j>>>0>b>>>0;if((b|0)==(j|0)){break t}a=E[c+84|0];continue}}o=d&-4;l=d&3;b=0;s=d-1>>>0<3;i=1;d=0;while(1){if(!Ab(c,a&255?d:D[D[c+68>>2]+(d<<2)>>2],B[c+24|0],h)){break t}i=0;a=0;e=0;if(!s){while(1){g=(b<<1)+f|0;k=a<<1;C[g>>1]=F[k+h>>1];C[g+2>>1]=F[(k|2)+h>>1];C[g+4>>1]=F[(k|4)+h>>1];C[g+6>>1]=F[(k|6)+h>>1];a=a+4|0;b=b+4|0;e=e+4|0;if((o|0)!=(e|0)){continue}break}}if(l){while(1){C[(b<<1)+f>>1]=F[(a<<1)+h>>1];a=a+1|0;b=b+1|0;i=i+1|0;if((l|0)!=(i|0)){continue}break}}d=d+1|0;if((j|0)!=(d|0)){i=d>>>0<j>>>0;a=E[c+84|0];continue}break}a=d>>>0>=j>>>0;break s}a=i^1;if(!h){break r}}ma(h)}break b;case 5:a=0;m=$-16|0;$=m;d=B[c+24|0];g=d<<2;j=D[b+80>>2];u:{if((J(g,j)|0)!=(e|0)){break u}b=D[c+28>>2]!=6;a=E[c+84|0];if(!(b|!a)){oa(f,D[D[c>>2]>>2]+D[c+48>>2]|0,e);a=1;break u}D[m+8>>2]=0;D[m>>2]=0;D[m+4>>2]=0;if(d){if((d|0)<0){break d}h=na(g);D[m>>2]=h;pa(h,0,g)}v:{w:{if(!j){break w}if(!b){o=D[c>>2];g=D[c+48>>2];q=D[c+68>>2];k=D[c+44>>2];l=D[c+40>>2];r=l;if(d){u=d&-4;t=d&3;b=0;v=a&255;w=d-1>>>0<3;d=0;while(1){c=D[o>>2];a=$h(l,k,v?d:D[q+(d<<2)>>2],0)+g|0;i=oa(h,c+a|0,r);e=0;a=0;c=0;if(!w){while(1){p=(b<<2)+f|0;n=a<<2;D[p>>2]=D[n+i>>2];D[p+4>>2]=D[i+(n|4)>>2];D[p+8>>2]=D[i+(n|8)>>2];D[p+12>>2]=D[i+(n|12)>>2];a=a+4|0;b=b+4|0;c=c+4|0;if((u|0)!=(c|0)){continue}break}}if(t){while(1){D[(b<<2)+f>>2]=D[i+(a<<2)>>2];a=a+1|0;b=b+1|0;e=e+1|0;if((t|0)!=(e|0)){continue}break}}a=1;d=d+1|0;if((j|0)!=(d|0)){continue}break}break v}b=0;if((j|0)!=1){p=j&-2;f=a&255;d=0;while(1){c=b|1;n=D[o>>2];e=$h(l,k,f?b:D[q+(b<<2)>>2],0)+g|0;e=oa(h,n+e|0,r);n=D[o>>2];if(!f){c=D[q+(c<<2)>>2]}c=$h(l,k,c,0)+g|0;oa(e,c+n|0,r);b=b+2|0;d=d+2|0;if((p|0)!=(d|0)){continue}break}}if(!(j&1)){break w}c=D[o>>2];if(!(a&255)){b=D[q+(b<<2)>>2]}a=$h(l,k,b,0)+g|0;oa(h,a+c|0,r);break w}if(!d){b=0;i=1;while(1){if(!yb(c,a&255?b:D[D[c+68>>2]+(b<<2)>>2],B[c+24|0],h)){break w}b=b+1|0;i=j>>>0>b>>>0;if((b|0)==(j|0)){break w}a=E[c+84|0];continue}}o=d&-4;l=d&3;b=0;s=d-1>>>0<3;i=1;d=0;while(1){if(!yb(c,a&255?d:D[D[c+68>>2]+(d<<2)>>2],B[c+24|0],h)){break w}i=0;a=0;e=0;if(!s){while(1){g=(b<<2)+f|0;k=a<<2;D[g>>2]=D[k+h>>2];D[g+4>>2]=D[(k|4)+h>>2];D[g+8>>2]=D[(k|8)+h>>2];D[g+12>>2]=D[(k|12)+h>>2];a=a+4|0;b=b+4|0;e=e+4|0;if((o|0)!=(e|0)){continue}break}}if(l){while(1){D[(b<<2)+f>>2]=D[(a<<2)+h>>2];a=a+1|0;b=b+1|0;i=i+1|0;if((l|0)!=(i|0)){continue}break}}d=d+1|0;if((j|0)!=(d|0)){i=d>>>0<j>>>0;a=E[c+84|0];continue}break}a=d>>>0>=j>>>0;break v}a=i^1;if(!h){break u}}ma(h)}break b;case 8:break e;default:break a}}a=0;o=$-16|0;$=o;k=B[c+24|0];d=k<<2;j=D[b+80>>2];x:{if((J(d,j)|0)!=(e|0)){break x}e=D[c+28>>2];D[o+8>>2]=0;D[o>>2]=0;D[o+4>>2]=0;b=0;y:{z:{A:{B:{if(!k){break B}if((k|0)<0){break A}b=na(d);D[o>>2]=b;g=(k<<2)+b|0;D[o+8>>2]=g;d=d-4|0;l=(d>>>2|0)+1&7;C:{if(!l){a=b;break C}a=b;while(1){D[a>>2]=-1073741824;a=a+4|0;h=h+1|0;if((l|0)!=(h|0)){continue}break}}if(d>>>0<28){break B}while(1){D[a+24>>2]=-1073741824;D[a+28>>2]=-1073741824;D[a+16>>2]=-1073741824;D[a+20>>2]=-1073741824;D[a+8>>2]=-1073741824;D[a+12>>2]=-1073741824;D[a>>2]=-1073741824;D[a+4>>2]=-1073741824;a=a+32|0;if((g|0)!=(a|0)){continue}break}}if(!j){break z}if((e|0)==9){a=0;s=D[c>>2];d=D[c+48>>2];r=D[c+68>>2];p=E[c+84|0];e=D[c+44>>2];g=D[c+40>>2];n=g;if((k|0)<=0){if((j|0)!=1){k=j&-2;c=0;while(1){f=a|1;l=D[s>>2];h=$h(g,e,p?a:D[r+(a<<2)>>2],0)+d|0;h=oa(b,l+h|0,n);l=D[s>>2];if(!p){f=D[r+(f<<2)>>2]}f=$h(g,e,f,0)+d|0;oa(h,f+l|0,n);a=a+2|0;c=c+2|0;if((k|0)!=(c|0)){continue}break}}if(!(j&1)){break z}c=D[s>>2];if(!p){a=D[r+(a<<2)>>2]}a=$h(g,e,a,0)+d|0;oa(b,a+c|0,n);break z}v=k&-4;t=k&3;h=0;w=k-1>>>0<3;while(1){c=D[s>>2];a=$h(g,e,p?i:D[r+(i<<2)>>2],0)+d|0;c=oa(b,c+a|0,n);l=0;a=0;u=0;if(!w){while(1){k=(h<<2)+f|0;m=a<<2;H[k>>2]=H[m+c>>2];H[k+4>>2]=H[c+(m|4)>>2];H[k+8>>2]=H[c+(m|8)>>2];H[k+12>>2]=H[c+(m|12)>>2];a=a+4|0;h=h+4|0;u=u+4|0;if((v|0)!=(u|0)){continue}break}}if(t){while(1){H[(h<<2)+f>>2]=H[c+(a<<2)>>2];a=a+1|0;h=h+1|0;l=l+1|0;if((t|0)!=(l|0)){continue}break}}a=1;i=i+1|0;if((j|0)!=(i|0)){continue}break}break y}m=1;if((k|0)<=0){a=0;while(1){if(!mb(c,E[c+84|0]?a:D[D[c+68>>2]+(a<<2)>>2],B[c+24|0],b)){break z}a=a+1|0;m=j>>>0>a>>>0;if((a|0)!=(j|0)){continue}break}break z}s=k&-4;g=k&3;h=0;k=k-1>>>0<3;while(1){if(!mb(c,E[c+84|0]?i:D[D[c+68>>2]+(i<<2)>>2],B[c+24|0],b)){break z}m=0;a=0;l=0;if(!k){while(1){d=(h<<2)+f|0;e=a<<2;H[d>>2]=H[e+b>>2];H[d+4>>2]=H[(e|4)+b>>2];H[d+8>>2]=H[(e|8)+b>>2];H[d+12>>2]=H[(e|12)+b>>2];a=a+4|0;h=h+4|0;l=l+4|0;if((s|0)!=(l|0)){continue}break}}if(g){while(1){H[(h<<2)+f>>2]=H[(a<<2)+b>>2];a=a+1|0;h=h+1|0;m=m+1|0;if((g|0)!=(m|0)){continue}break}}i=i+1|0;m=j>>>0>i>>>0;if((i|0)!=(j|0)){continue}break}a=i>>>0>=j>>>0;break y}qa();T()}a=m^1;if(!b){break x}}ma(b)}$=o+16|0;h=a&1;break a}qa();T()}$=l+16|0;h=a&1;break a}$=m+16|0;h=a&1}return h|0}function wf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,C=0,F=0,G=0,H=0,I=0,K=0,L=0,M=0,N=0;z=c;c=0;m=$-96|0;$=m;k=m+16|0;pa(k,0,76);D[m+92>>2]=-1;D[m+8>>2]=0;D[m>>2]=0;D[m+4>>2]=0;r=$-16|0;$=r;D[k+68>>2]=0;D[k+72>>2]=0;D[k>>2]=b;t=$-16|0;$=t;x=b;a=D[b+20>>2];a:{if((D[b+24>>2]-a|0)<=0){break a}a=D[a>>2];if((a|0)==-1){break a}c=D[D[b+8>>2]+(a<<2)>>2]}b:{c:{d:{if(!c){a=0;break d}a=D[x+100>>2];f=D[x+96>>2];D[t+8>>2]=0;D[t>>2]=0;D[t+4>>2]=0;h=a-f|0;b=(h|0)/12|0;e:{if(!h){break e}if(b>>>0>=357913942){break c}d=na(h);D[t>>2]=d;D[t+8>>2]=d+J(b,12);a=0;i=d;d=J((h-12>>>0)/12|0,12)+12|0;h=pa(i,0,d);D[t+4>>2]=d+h;if(E[c+84|0]){c=b>>>0>1?b:1;d=c&1;if(b>>>0>=2){i=c&-2;while(1){c=J(a,12);b=c+f|0;l=D[b+4>>2];n=D[b>>2];c=c+h|0;D[c+8>>2]=D[b+8>>2];D[c>>2]=n;D[c+4>>2]=l;c=J(a|1,12);b=c+f|0;l=D[b+8>>2];n=D[b+4>>2];c=c+h|0;D[c>>2]=D[b>>2];D[c+4>>2]=n;D[c+8>>2]=l;a=a+2|0;e=e+2|0;if((i|0)!=(e|0)){continue}break}}if(!d){break e}b=J(a,12);a=b+f|0;c=D[a+4>>2];e=D[a>>2];b=b+h|0;D[b+8>>2]=D[a+8>>2];D[b>>2]=e;D[b+4>>2]=c;break e}d=b>>>0>1?b:1;a=D[c+68>>2];while(1){c=J(e,12);b=c+f|0;i=D[a+(D[b>>2]<<2)>>2];l=D[a+(D[b+4>>2]<<2)>>2];c=c+h|0;D[c+8>>2]=D[a+(D[b+8>>2]<<2)>>2];D[c+4>>2]=l;D[c>>2]=i;e=e+1|0;if((d|0)!=(e|0)){continue}break}}F=$-16|0;$=F;d=na(88);D[d>>2]=0;D[d+4>>2]=0;D[d+56>>2]=0;D[d+48>>2]=0;D[d+52>>2]=0;D[d+40>>2]=0;D[d+44>>2]=0;D[d+32>>2]=0;D[d+36>>2]=0;D[d+24>>2]=0;D[d+28>>2]=0;D[d+16>>2]=0;D[d+20>>2]=0;D[d+8>>2]=0;D[d+12>>2]=0;a=d- -64|0;D[a>>2]=0;D[a+4>>2]=0;D[d+72>>2]=0;D[d+76>>2]=0;D[d+80>>2]=0;D[d+84>>2]=0;D[d+60>>2]=d;A=$-16|0;$=A;D[d+80>>2]=0;D[d+84>>2]=0;a=D[d+76>>2];D[d+76>>2]=0;if(a){ma(a)}D[d+68>>2]=0;D[d+72>>2]=0;b=d- -64|0;a=D[b>>2];D[b>>2]=0;if(a){ma(a)}e=D[t>>2];b=D[t+4>>2]-e|0;l=(b|0)/12|0;a=J(l,3);c=D[d>>2];f=D[d+4>>2]-c>>2;f:{if(a>>>0>f>>>0){sa(d,a-f|0);e=D[t>>2];b=D[t+4>>2]-e|0;l=(b|0)/12|0;c=D[d>>2];break f}if(a>>>0>=f>>>0){break f}D[d+4>>2]=(a<<2)+c}if(b){h=l>>>0>1?l:1;a=0;while(1){f=J(a,12);b=f+c|0;f=f+e|0;D[b>>2]=D[f>>2];D[b+4>>2]=D[f+4>>2];D[b+8>>2]=D[f+8>>2];a=a+1|0;if((h|0)!=(a|0)){continue}break}}D[A+12>>2]=-1;f=$-48|0;$=f;g:{h:{i:{w=A+12|0;j:{if(!w){break j}i=D[d+4>>2];n=D[d>>2];b=i-n|0;p=b>>2;c=D[d+12>>2];a=D[d+16>>2]-c>>2;k:{if(p>>>0>a>>>0){xa(d+12|0,p-a|0,10260);i=D[d+4>>2];n=D[d>>2];b=i-n|0;p=b>>2;break k}if(a>>>0<=p>>>0){break k}D[d+16>>2]=c+(p<<2)}c=0;D[f+40>>2]=0;D[f+32>>2]=0;D[f+36>>2]=0;l:{if(!b){D[f+24>>2]=0;D[f+16>>2]=0;D[f+20>>2]=0;break l}m:{if((b|0)>=0){c=na(b);D[f+36>>2]=c;D[f+32>>2]=c;D[f+40>>2]=(p<<2)+c;a=c;b=0;while(1){h=D[(b<<2)+n>>2];a=a-c>>2;n:{if(h>>>0<a>>>0){break n}D[f+16>>2]=0;e=h+1|0;if(e>>>0>a>>>0){xa(f+32|0,e-a|0,f+16|0);n=D[d>>2];i=D[d+4>>2];c=D[f+32>>2];break n}if(a>>>0<=e>>>0){break n}D[f+36>>2]=(e<<2)+c}a=(h<<2)+c|0;D[a>>2]=D[a>>2]+1;b=b+1|0;a=i-n|0;p=a>>2;if(b>>>0>=p>>>0){break m}a=D[f+36>>2];continue}}break i}D[f+24>>2]=0;D[f+16>>2]=0;D[f+20>>2]=0;if(!a){break l}if(p>>>0>=536870912){break h}b=a<<1;a=na(b);D[f+16>>2]=a;e=a+(p<<3)|0;D[f+24>>2]=e;pa(a,255,b);D[f+20>>2]=e}i=0;D[f+8>>2]=0;D[f>>2]=0;D[f+4>>2]=0;a=D[f+36>>2]-c|0;h=a>>2;o:{if(!a){break o}if((a|0)<0){break h}s=na(a);D[f>>2]=s;D[f+8>>2]=(h<<2)+s;b=a;a=pa(s,0,a);D[f+4>>2]=b+a;e=h>>>0>1?h:1;l=e&3;b=0;if(e-1>>>0>=3){q=e&-4;while(1){e=g<<2;D[e+a>>2]=b;y=e|4;b=D[c+e>>2]+b|0;D[y+a>>2]=b;v=e|8;b=b+D[c+y>>2]|0;D[v+a>>2]=b;e=e|12;b=b+D[c+v>>2]|0;D[e+a>>2]=b;b=b+D[c+e>>2]|0;g=g+4|0;j=j+4|0;if((q|0)!=(j|0)){continue}break}}if(!l){break o}while(1){e=g<<2;D[e+a>>2]=b;g=g+1|0;b=D[c+e>>2]+b|0;o=o+1|0;if((l|0)!=(o|0)){continue}break}}if(p){y=D[d+12>>2];while(1){G=i<<2;b=G+n|0;j=-1;e=i+1|0;a=(e>>>0)%3|0?e:i-2|0;if((a|0)!=-1){j=D[(a<<2)+n>>2]}a=D[b>>2];p:{q:{if(!((i>>>0)%3|0)){o=-1;b=i+2|0;if((b|0)!=-1){o=D[(b<<2)+n>>2]}if(!((a|0)==(j|0)|(a|0)==(o|0))&(j|0)!=(o|0)){break q}D[d+40>>2]=D[d+40>>2]+1;e=i+3|0;break p}o=D[b-4>>2]}b=o<<2;v=D[b+c>>2];r:{s:{if((v|0)<=0){break s}l=D[f+16>>2];b=D[b+s>>2];g=0;while(1){q=l+(b<<3)|0;u=D[q>>2];if((u|0)==-1){break s}t:{if((j|0)!=(u|0)){break t}q=D[q+4>>2];if((q|0)!=-1){u=D[(q<<2)+n>>2]}else{u=-1}if((u|0)==(a|0)){break t}while(1){u:{a=b;g=g+1|0;if((v|0)<=(g|0)){break u}u=l+(a<<3)|0;b=a+1|0;H=l+(b<<3)|0;I=D[H>>2];D[u>>2]=I;D[u+4>>2]=D[H+4>>2];if((I|0)!=-1){continue}}break}D[l+(a<<3)>>2]=-1;if((q|0)==-1){break s}D[y+G>>2]=q;D[y+(q<<2)>>2]=i;break r}b=b+1|0;g=g+1|0;if((v|0)!=(g|0)){continue}break}}a=j<<2;l=D[a+c>>2];if((l|0)<=0){break r}j=D[f+16>>2];b=D[a+s>>2];g=0;while(1){a=j+(b<<3)|0;if(D[a>>2]==-1){D[a>>2]=o;D[a+4>>2]=i;break r}b=b+1|0;g=g+1|0;if((l|0)!=(g|0)){continue}break}}}i=e;if(p>>>0>i>>>0){continue}break}}D[w>>2]=h;if(s){ma(s)}a=D[f+16>>2];if(a){D[f+20>>2]=a;ma(a)}a=D[f+32>>2];if(!a){break j}D[f+36>>2]=a;ma(a)}$=f+48|0;y=(w|0)!=0;if(y){j=$-32|0;$=j;o=D[d>>2];a=D[d+4>>2];D[j+24>>2]=0;D[j+16>>2]=0;D[j+20>>2]=0;b=a-o|0;v:{if(!b){break v}if((b|0)<0){break h}b=b>>2;g=b-1>>>5|0;e=g+1|0;c=na(e<<2);D[j+24>>2]=e;D[j+16>>2]=c;D[j+20>>2]=b;D[c+((b>>>0<33?0:g)<<2)>>2]=0;g=c;c=b>>>5<<2;g=pa(g,0,c);b=b&31;if(!b){break v}c=c+g|0;D[c>>2]=D[c>>2]&(-1>>>32-b^-1)}D[j+8>>2]=0;D[j>>2]=0;while(1){w:{p=0;e=0;if((a|0)==(o|0)){break w}while(1){c=D[j+16>>2];x:{if(D[c+(e>>>3&536870908)>>2]>>>e&1){break x}g=D[j>>2];D[j+4>>2]=g;b=D[d+12>>2];a=e;while(1){y:{f=a+1|0;h=a;a=(f>>>0)%3|0?f:a-2|0;if((a|0)==-1){break y}a=D[b+(a<<2)>>2];if((a|0)==-1){break y}f=a+1|0;a=(f>>>0)%3|0?f:a-2|0;if((e|0)==(a|0)|(a|0)==-1){break y}if(!(D[(a>>>3&536870908)+c>>2]>>>a&1)){continue}}break}b=g;l=h;z:{A:{while(1){a=(l>>>3&536870908)+c|0;D[a>>2]=D[a>>2]|1<<l;a=l+1|0;f=(a>>>0)%3|0?a:l-2|0;v=(l>>>0)%3|0;n=(v?-1:2)+l|0;q=n<<2;B:{if((b|0)==(g|0)){break B}w=D[(f<<2)+o>>2];s=D[d+12>>2];a=b;if((n|0)!=-1){u=s+q|0;while(1){C:{if((w|0)!=D[a>>2]){break C}c=D[a+4>>2];i=D[u>>2];if((c|0)==(i|0)){break C}g=-1;a=-1;if((c|0)==-1){break z}break A}a=a+8|0;if((g|0)!=(a|0)){continue}break}break B}while(1){if((w|0)==D[a>>2]){i=-1;n=-1;c=D[a+4>>2];if((c|0)!=-1){break A}}a=a+8|0;if((g|0)!=(a|0)){continue}break}}n=D[o+q>>2];D:{if(D[j+8>>2]!=(g|0)){D[g>>2]=n;D[g+4>>2]=f;g=g+8|0;D[j+4>>2]=g;break D}c=g-b|0;g=c>>3;a=g+1|0;if(a>>>0>=536870912){break h}i=c>>2;i=g>>>0<268435455?a>>>0>i>>>0?a:i:536870911;if(i){if(i>>>0>=536870912){break i}a=na(i<<3)}else{a=0}g=a+(g<<3)|0;D[g>>2]=n;D[g+4>>2]=f;g=g+8|0;if((c|0)>0){oa(a,b,c)}D[j+8>>2]=a+(i<<3);D[j+4>>2]=g;D[j>>2]=a;if(!b){break D}ma(b)}E:{F:{if(v){a=l-1|0;break F}a=l+2|0;if((a|0)==-1){break E}}a=D[D[d+12>>2]+(a<<2)>>2];if((a|0)==-1){break E}l=a+((a>>>0)%3|0?-1:2)|0;if((h|0)==(l|0)|(l|0)==-1){break E}o=D[d>>2];b=D[j>>2];c=D[j+16>>2];continue}break}o=D[d>>2];break x}g=c;a=D[s+(c<<2)>>2]}if((i|0)!=-1){D[s+(i<<2)>>2]=-1}if((a|0)!=-1){D[D[d+12>>2]+(a<<2)>>2]=-1}a=D[d+12>>2];D[a+(n<<2)>>2]=-1;D[a+(g<<2)>>2]=-1;p=1}e=e+1|0;a=D[d+4>>2];if(e>>>0<a-o>>2>>>0){continue}break}if(p){continue}}break}a=D[j>>2];if(a){ma(a)}a=D[j+16>>2];if(a){ma(a)}$=j+32|0;e=0;n=0;p=0;h=$-32|0;$=h;a=D[A+12>>2];D[d+36>>2]=a;o=d+24|0;c=D[d+24>>2];b=D[d+28>>2]-c>>2;G:{H:{if(b>>>0<a>>>0){xa(o,a-b|0,10260);D[h+24>>2]=0;D[h+16>>2]=0;D[h+20>>2]=0;break H}if(a>>>0<b>>>0){D[d+28>>2]=c+(a<<2)}D[h+24>>2]=0;D[h+16>>2]=0;D[h+20>>2]=0;if(!a){break G}}if((a|0)<0){break h}b=a-1>>>5|0;c=b+1|0;e=na(c<<2);D[h+24>>2]=c;D[h+16>>2]=e;D[h+20>>2]=a;D[((a>>>0<33?0:b)<<2)+e>>2]=0;b=a>>>5<<2;c=pa(e,0,b);g=a&31;if(!g){break G}b=b+c|0;D[b>>2]=D[b>>2]&(-1>>>32-g^-1)}i=D[d>>2];l=D[d+4>>2];D[h+8>>2]=0;D[h>>2]=0;D[h+4>>2]=0;b=l-i|0;I:{if(!b){break I}if((b|0)<0){break h}b=b>>2;c=b-1>>>5|0;g=c+1|0;j=na(g<<2);D[h+8>>2]=g;D[h>>2]=j;D[h+4>>2]=b;D[((b>>>0<33?0:c)<<2)+j>>2]=0;c=b>>>5<<2;g=pa(j,0,c);f=b&31;if(f){c=c+g|0;D[c>>2]=D[c>>2]&(-1>>>32-f^-1)}if(b>>>0<3){break I}while(1){q=J(n,3);g=(q<<2)+i|0;b=D[g>>2];c=-1;f=q+1|0;if((f|0)!=-1){c=D[(f<<2)+i>>2]}J:{if((b|0)==(c|0)){break J}f=b;b=D[g+8>>2];if((f|0)==(b|0)){break J}s=0;if((b|0)==(c|0)){break J}while(1){g=s+q|0;if(!(D[(g>>>3&536870908)+j>>2]>>>g&1)){b=D[(g<<2)+i>>2];c=1<<b;j=b>>>5|0;l=c&D[(j<<2)+e>>2];if(l){c=D[d+28>>2];K:{if((c|0)!=D[d+32>>2]){D[c>>2]=-1;D[d+28>>2]=c+4;break K}e=D[o>>2];f=c-e|0;j=f>>2;c=j+1|0;if(c>>>0>=1073741824){break h}i=f>>1;i=j>>>0<536870911?c>>>0>i>>>0?c:i:1073741823;if(i){if(i>>>0>=1073741824){break i}c=na(i<<2)}else{c=0}j=c+(j<<2)|0;D[j>>2]=-1;if((f|0)>0){oa(c,e,f)}D[d+32>>2]=c+(i<<2);D[d+28>>2]=j+4;D[d+24>>2]=c;if(!e){break K}ma(e)}c=D[d+52>>2];L:{if((c|0)!=D[d+56>>2]){D[c>>2]=b;D[d+52>>2]=c+4;break L}e=D[d+48>>2];f=c-e|0;j=f>>2;c=j+1|0;if(c>>>0>=1073741824){break h}i=f>>1;i=j>>>0<536870911?c>>>0>i>>>0?c:i:1073741823;if(i){if(i>>>0>=1073741824){break i}c=na(i<<2)}else{c=0}j=c+(j<<2)|0;D[j>>2]=b;if((f|0)>0){oa(c,e,f)}D[d+56>>2]=c+(i<<2);D[d+52>>2]=j+4;D[d+48>>2]=c;if(!e){break L}ma(e)}c=D[h+20>>2];b=D[h+24>>2];if((c|0)==b<<5){if((c+1|0)<0){break h}e=h+16|0;if(c>>>0<=1073741822){c=c+32&-32;b=b<<6;b=b>>>0<c>>>0?c:b}else{b=2147483647}Ta(e,b);c=D[h+20>>2]}D[h+20>>2]=c+1;b=D[h+16>>2]+(c>>>3&536870908)|0;e=D[b>>2];M=b,N=di(c)&e,D[M>>2]=N;c=1<<a;j=a>>>5|0;b=a;a=a+1|0}f=a;e=D[h+16>>2];a=e+(j<<2)|0;D[a>>2]=D[a>>2]|c;v=D[d+24>>2]+(b<<2)|0;w=D[d+12>>2];i=D[d>>2];j=D[h>>2];a=g;M:{N:{O:{P:{Q:{R:{while(1){if((a|0)==-1){break R}c=(a>>>3&536870908)+j|0;D[c>>2]=D[c>>2]|1<<a;D[v>>2]=a;if(l){D[(a<<2)+i>>2]=b}u=a+1|0;a=(u>>>0)%3|0?u:a-2|0;c=-1;S:{if((a|0)==-1){break S}a=D[w+(a<<2)>>2];c=-1;if((a|0)==-1){break S}c=a+1|0;c=(c>>>0)%3|0?c:a-2|0}a=c;if((g|0)!=(a|0)){continue}break}if((g|0)!=-1){break M}a=1;break Q}if((g>>>0)%3|0){a=g-1|0;break Q}a=g+2|0;if((a|0)==-1){break P}}a=D[w+(a<<2)>>2];if((a|0)==-1){break P}if(!((a>>>0)%3|0)){break O}a=a-1|0;i=D[d>>2];j=D[h>>2];break N}i=D[d>>2];j=D[h>>2];break M}i=D[d>>2];j=D[h>>2];a=a+2|0;if((a|0)==-1){break M}}c=D[d+12>>2];while(1){g=(a>>>3&536870908)+j|0;D[g>>2]=D[g>>2]|1<<a;if(l){D[(a<<2)+i>>2]=b}T:{if((a>>>0)%3|0){a=a-1|0;break T}a=a+2|0;if((a|0)==-1){break M}}a=D[c+(a<<2)>>2];if((a|0)==-1){break M}a=a+((a>>>0)%3|0?-1:2)|0;if((a|0)!=-1){continue}break}}a=f}s=s+1|0;if((s|0)!=3){continue}break}i=D[d>>2];l=D[d+4>>2]}n=n+1|0;if(n>>>0<(l-i>>2>>>0)/3>>>0){continue}break}e=D[h+16>>2]}D[d+44>>2]=0;a=D[h+20>>2];if(a){b=a&31;g=(a>>>3&536870908)+e|0;a=0;c=e;while(1){if(!(D[c>>2]>>>a&1)){p=p+1|0;D[d+44>>2]=p}f=(a|0)==31;a=f?0:a+1|0;c=(f<<2)+c|0;if((g|0)!=(c|0)|(a|0)!=(b|0)){continue}break}}a=D[h>>2];if(a){ma(a);e=D[h+16>>2]}if(e){ma(e)}$=h+32|0}$=A+16|0;if(!y){D[F+8>>2]=0;ab(d);d=0}$=F+16|0;a=d;break g}ra(1326);T()}qa();T()}b=D[t>>2];if(!b){break d}D[t+4>>2]=b;ma(b)}$=t+16|0;break b}qa();T()}b=D[k+4>>2];D[k+4>>2]=a;if(b){ab(b);a=D[k+4>>2]}U:{if(!a){break U}a=D[x+100>>2];b=D[x+96>>2];B[r+12|0]=0;Ea(k+56|0,(a-b|0)/12|0,r+12|0);a=D[x+100>>2];b=D[x+96>>2];if((a|0)==(b|0)){K=1;break U}while(1){if(!(D[D[k+56>>2]+(C>>>3&536870908)>>2]>>>C&1)){a=J(C,3);Tb(k,0,a);b=D[k+8>>2];c=D[k+12>>2];Tb(k,1,a+1|0);g=D[k+20>>2];e=D[k+24>>2];Tb(k,2,a+2|0);g=e-g>>2;b=c-b|0;c=b>>2;a=g>>>0>c>>>0;c=D[k+36>>2]-D[k+32>>2]>>2>>>0>(a?g:c)>>>0?2:a?1:b?0:-1;V:{if(D[k+68>>2]<=0){break V}D[r+12>>2]=D[k+76>>2];D[r+8>>2]=m;Qa(r+8|0,r+12|0);a=D[((c<<2)+k|0)+44>>2];if((a|0)<0){a=-1}else{b=(a>>>0)/3|0;a=D[(D[D[k>>2]+96>>2]+J(b,12)|0)+(a-J(b,3)<<2)>>2]}D[r+12>>2]=a;D[r+8>>2]=m;Qa(r+8|0,r+12|0);b=D[k+72>>2];D[k+72>>2]=b+2;if(!(b&1)){break V}D[r+12>>2]=a;D[r+8>>2]=m;Qa(r+8|0,r+12|0);D[k+72>>2]=D[k+72>>2]+1}b=$-16|0;$=b;D[k+68>>2]=D[k+68>>2]+1;a=J(c,12)+k|0;g=D[a+12>>2]-D[a+8>>2]|0;W:{if((g|0)<=0){break W}a=-1;c=D[((c<<2)+k|0)+44>>2];e=(c>>>0)/3|0;h=(c|0)==-1;f=h?-1:e;d=D[k+56>>2]+(f>>>3&536870908)|0;D[d>>2]=D[d>>2]|1<<f;D[k+72>>2]=D[k+72>>2]+1;D[b+12>>2]=(c|0)>=0?D[(D[D[k>>2]+96>>2]+J(e,12)|0)+((c>>>0)%3<<2)>>2]:-1;D[b+8>>2]=m;Qa(b+8|0,b+12|0);X:{if(!h){f=c+1|0;f=(f>>>0)%3|0?f:c-2|0;if((f|0)>=0){h=(f>>>0)/3|0;d=D[(D[D[k>>2]+96>>2]+J(h,12)|0)+(f-J(h,3)<<2)>>2]}else{d=-1}D[b+12>>2]=d;D[b+8>>2]=m;Qa(b+8|0,b+12|0);e=c+(c-J(e,3)|0?-1:2)|0;if((e|0)<0){break X}a=(e>>>0)/3|0;a=D[(D[D[k>>2]+96>>2]+J(a,12)|0)+(e-J(a,3)<<2)>>2];break X}D[b+12>>2]=-1;D[b+8>>2]=m;Qa(b+8|0,b+12|0)}D[k+76>>2]=a;D[b+12>>2]=a;D[b+8>>2]=m;a=-1;Qa(b+8|0,b+12|0);a=(c|0)!=-1?D[D[D[k+4>>2]+12>>2]+(c<<2)>>2]:a;if(g>>>0<=7){break W}c=g>>>2|0;h=c>>>0>1?c:1;c=1;while(1){g=a;f=(a>>>0)/3|0;a=(a|0)==-1?-1:f;e=D[k+56>>2]+(a>>>3&536870908)|0;D[e>>2]=D[e>>2]|1<<a;D[k+72>>2]=D[k+72>>2]+1;a=-1;a=(g|0)>=0?D[(D[D[k>>2]+96>>2]+J(f,12)|0)+((g>>>0)%3<<2)>>2]:a;D[k+76>>2]=a;D[b+12>>2]=a;D[b+8>>2]=m;Qa(b+8|0,b+12|0);Y:{Z:{_:{if(c&1){e=-1;if((g|0)==-1){break Y}if((g|0)!=(J(f,3)|0)){a=g-1|0;break Z}a=g+2|0;break _}e=-1;if((g|0)==-1){break Y}a=g+1|0;a=(a>>>0)%3|0?a:g-2|0}e=-1;if((a|0)==-1){break Y}}e=D[D[D[k+4>>2]+12>>2]+(a<<2)>>2]}a=e;c=c+1|0;if((h|0)!=(c|0)){continue}break}}$=b+16|0;b=D[x+96>>2];a=D[x+100>>2]}K=1;C=C+1|0;if(C>>>0<(a-b|0)/12>>>0){continue}break}}$=r+16|0;$:{if(K){a=D[z>>2];if(a){D[z+4>>2]=a;ma(a)}D[z>>2]=D[m>>2];D[z+4>>2]=D[m+4>>2];D[z+8>>2]=D[m+8>>2];L=D[m+84>>2];break $}a=D[m>>2];if(!a){break $}D[m+4>>2]=a;ma(a)}a=D[m+72>>2];if(a){ma(a)}a=D[m+48>>2];if(a){D[m+52>>2]=a;ma(a)}a=D[m+36>>2];if(a){D[m+40>>2]=a;ma(a)}a=D[m+24>>2];if(a){D[m+28>>2]=a;ma(a)}a=D[m+20>>2];D[m+20>>2]=0;if(a){ab(a)}$=m+96|0;return L|0}function Ac(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;l=$-16|0;$=l;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{k:{if(a>>>0<=244){e=D[2881];h=a>>>0<11?16:a+11&-8;c=h>>>3|0;b=e>>>c|0;if(b&3){d=c+((b^-1)&1)|0;b=d<<3;f=D[b+11572>>2];a=f+8|0;c=D[f+8>>2];b=b+11564|0;l:{if((c|0)==(b|0)){m=11524,n=di(d)&e,D[m>>2]=n;break l}D[c+12>>2]=b;D[b+8>>2]=c}b=d<<3;D[f+4>>2]=b|3;b=b+f|0;D[b+4>>2]=D[b+4>>2]|1;break a}k=D[2883];if(k>>>0>=h>>>0){break k}if(b){a=2<<c;a=(0-a|a)&b<<c;b=(0-a&a)-1|0;a=b>>>12&16;c=a;b=b>>>a|0;a=b>>>5&8;c=c|a;b=b>>>a|0;a=b>>>2&4;c=c|a;b=b>>>a|0;a=b>>>1&2;c=c|a;b=b>>>a|0;a=b>>>1&1;c=(c|a)+(b>>>a|0)|0;a=c<<3;g=D[a+11572>>2];b=D[g+8>>2];a=a+11564|0;m:{if((b|0)==(a|0)){e=di(c)&e;D[2881]=e;break m}D[b+12>>2]=a;D[a+8>>2]=b}a=g+8|0;D[g+4>>2]=h|3;d=g+h|0;b=c<<3;f=b-h|0;D[d+4>>2]=f|1;D[b+g>>2]=f;if(k){b=k>>>3|0;c=(b<<3)+11564|0;g=D[2886];b=1<<b;n:{if(!(b&e)){D[2881]=b|e;b=c;break n}b=D[c+8>>2]}D[c+8>>2]=g;D[b+12>>2]=g;D[g+12>>2]=c;D[g+8>>2]=b}D[2886]=d;D[2883]=f;break a}j=D[2882];if(!j){break k}b=(j&0-j)-1|0;a=b>>>12&16;c=a;b=b>>>a|0;a=b>>>5&8;c=c|a;b=b>>>a|0;a=b>>>2&4;c=c|a;b=b>>>a|0;a=b>>>1&2;c=c|a;b=b>>>a|0;a=b>>>1&1;b=D[((c|a)+(b>>>a|0)<<2)+11828>>2];d=(D[b+4>>2]&-8)-h|0;c=b;while(1){o:{a=D[c+16>>2];if(!a){a=D[c+20>>2];if(!a){break o}}c=(D[a+4>>2]&-8)-h|0;f=c>>>0<d>>>0;d=f?c:d;b=f?a:b;c=a;continue}break}i=D[b+24>>2];f=D[b+12>>2];if((f|0)!=(b|0)){a=D[b+8>>2];D[a+12>>2]=f;D[f+8>>2]=a;break b}c=b+20|0;a=D[c>>2];if(!a){a=D[b+16>>2];if(!a){break j}c=b+16|0}while(1){g=c;f=a;c=a+20|0;a=D[c>>2];if(a){continue}c=f+16|0;a=D[f+16>>2];if(a){continue}break}D[g>>2]=0;break b}h=-1;if(a>>>0>4294967231){break k}a=a+11|0;h=a&-8;j=D[2882];if(!j){break k}d=0-h|0;e=0;p:{if(h>>>0<256){break p}e=31;if(h>>>0>16777215){break p}a=a>>>8|0;g=a+1048320>>>16&8;a=a<<g;c=a+520192>>>16&4;a=a<<c;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(c|g))|0;e=(a<<1|h>>>a+21&1)+28|0}c=D[(e<<2)+11828>>2];q:{r:{s:{if(!c){a=0;break s}a=0;b=h<<((e|0)==31?0:25-(e>>>1|0)|0);while(1){t:{g=(D[c+4>>2]&-8)-h|0;if(g>>>0>=d>>>0){break t}f=c;d=g;if(d){break t}d=0;a=c;break r}g=D[c+20>>2];c=D[((b>>>29&4)+c|0)+16>>2];a=g?(g|0)==(c|0)?a:g:a;b=b<<1;if(c){continue}break}}if(!(a|f)){f=0;a=2<<e;a=(0-a|a)&j;if(!a){break k}b=(a&0-a)-1|0;a=b>>>12&16;c=a;b=b>>>a|0;a=b>>>5&8;c=c|a;b=b>>>a|0;a=b>>>2&4;c=c|a;b=b>>>a|0;a=b>>>1&2;c=c|a;b=b>>>a|0;a=b>>>1&1;a=D[((c|a)+(b>>>a|0)<<2)+11828>>2]}if(!a){break q}}while(1){b=(D[a+4>>2]&-8)-h|0;c=b>>>0<d>>>0;d=c?b:d;f=c?a:f;b=D[a+16>>2];if(b){a=b}else{a=D[a+20>>2]}if(a){continue}break}}if(!f|D[2883]-h>>>0<=d>>>0){break k}e=D[f+24>>2];b=D[f+12>>2];if((f|0)!=(b|0)){a=D[f+8>>2];D[a+12>>2]=b;D[b+8>>2]=a;break c}c=f+20|0;a=D[c>>2];if(!a){a=D[f+16>>2];if(!a){break i}c=f+16|0}while(1){g=c;b=a;c=a+20|0;a=D[c>>2];if(a){continue}c=b+16|0;a=D[b+16>>2];if(a){continue}break}D[g>>2]=0;break c}c=D[2883];if(c>>>0>=h>>>0){d=D[2886];b=c-h|0;u:{if(b>>>0>=16){D[2883]=b;a=d+h|0;D[2886]=a;D[a+4>>2]=b|1;D[c+d>>2]=b;D[d+4>>2]=h|3;break u}D[2886]=0;D[2883]=0;D[d+4>>2]=c|3;a=c+d|0;D[a+4>>2]=D[a+4>>2]|1}a=d+8|0;break a}i=D[2884];if(i>>>0>h>>>0){b=i-h|0;D[2884]=b;c=D[2887];a=c+h|0;D[2887]=a;D[a+4>>2]=b|1;D[c+4>>2]=h|3;a=c+8|0;break a}a=0;j=h+47|0;if(D[2999]){c=D[3001]}else{D[3002]=-1;D[3003]=-1;D[3e3]=4096;D[3001]=4096;D[2999]=l+12&-16^1431655768;D[3004]=0;D[2992]=0;c=4096}g=j+c|0;f=0-c|0;c=g&f;if(c>>>0<=h>>>0){break a}d=D[2991];if(d){b=D[2989];e=b+c|0;if(d>>>0<e>>>0|b>>>0>=e>>>0){break a}}if(E[11968]&4){break f}v:{w:{d=D[2887];if(d){a=11972;while(1){b=D[a>>2];if(b>>>0<=d>>>0&d>>>0<b+D[a+4>>2]>>>0){break w}a=D[a+8>>2];if(a){continue}break}}b=Wa(0);if((b|0)==-1){break g}e=c;d=D[3e3];a=d-1|0;if(a&b){e=(c-b|0)+(a+b&0-d)|0}if(e>>>0<=h>>>0|e>>>0>2147483646){break g}d=D[2991];if(d){a=D[2989];f=a+e|0;if(d>>>0<f>>>0|a>>>0>=f>>>0){break g}}a=Wa(e);if((b|0)!=(a|0)){break v}break e}e=f&g-i;if(e>>>0>2147483646){break g}b=Wa(e);if((b|0)==(D[a>>2]+D[a+4>>2]|0)){break h}a=b}if(!((a|0)==-1|h+48>>>0<=e>>>0)){b=D[3001];b=b+(j-e|0)&0-b;if(b>>>0>2147483646){b=a;break e}if((Wa(b)|0)!=-1){e=b+e|0;b=a;break e}Wa(0-e|0);break g}b=a;if((a|0)!=-1){break e}break g}f=0;break b}b=0;break c}if((b|0)!=-1){break e}}D[2992]=D[2992]|4}if(c>>>0>2147483646){break d}b=Wa(c);a=Wa(0);if((b|0)==-1|(a|0)==-1|a>>>0<=b>>>0){break d}e=a-b|0;if(e>>>0<=h+40>>>0){break d}}a=D[2989]+e|0;D[2989]=a;if(a>>>0>G[2990]){D[2990]=a}x:{y:{z:{g=D[2887];if(g){a=11972;while(1){d=D[a>>2];c=D[a+4>>2];if((d+c|0)==(b|0)){break z}a=D[a+8>>2];if(a){continue}break}break y}a=D[2885];if(!(a>>>0<=b>>>0?a:0)){D[2885]=b}a=0;D[2994]=e;D[2993]=b;D[2889]=-1;D[2890]=D[2999];D[2996]=0;while(1){d=a<<3;c=d+11564|0;D[d+11572>>2]=c;D[d+11576>>2]=c;a=a+1|0;if((a|0)!=32){continue}break}d=e-40|0;a=b+8&7?-8-b&7:0;c=d-a|0;D[2884]=c;a=a+b|0;D[2887]=a;D[a+4>>2]=c|1;D[(b+d|0)+4>>2]=40;D[2888]=D[3003];break x}if(E[a+12|0]&8|d>>>0>g>>>0|b>>>0<=g>>>0){break y}D[a+4>>2]=c+e;a=g+8&7?-8-g&7:0;c=a+g|0;D[2887]=c;b=D[2884]+e|0;a=b-a|0;D[2884]=a;D[c+4>>2]=a|1;D[(b+g|0)+4>>2]=40;D[2888]=D[3003];break x}if(G[2885]>b>>>0){D[2885]=b}c=b+e|0;a=11972;A:{B:{C:{D:{E:{F:{while(1){if((c|0)!=D[a>>2]){a=D[a+8>>2];if(a){continue}break F}break}if(!(E[a+12|0]&8)){break E}}a=11972;while(1){c=D[a>>2];if(c>>>0<=g>>>0){f=c+D[a+4>>2]|0;if(f>>>0>g>>>0){break D}}a=D[a+8>>2];continue}}D[a>>2]=b;D[a+4>>2]=D[a+4>>2]+e;j=(b+8&7?-8-b&7:0)+b|0;D[j+4>>2]=h|3;e=c+(c+8&7?-8-c&7:0)|0;i=h+j|0;c=e-i|0;if((e|0)==(g|0)){D[2887]=i;a=D[2884]+c|0;D[2884]=a;D[i+4>>2]=a|1;break B}if(D[2886]==(e|0)){D[2886]=i;a=D[2883]+c|0;D[2883]=a;D[i+4>>2]=a|1;D[a+i>>2]=a;break B}a=D[e+4>>2];if((a&3)==1){g=a&-8;G:{if(a>>>0<=255){d=D[e+8>>2];a=a>>>3|0;b=D[e+12>>2];if((b|0)==(d|0)){m=11524,n=D[2881]&di(a),D[m>>2]=n;break G}D[d+12>>2]=b;D[b+8>>2]=d;break G}h=D[e+24>>2];b=D[e+12>>2];H:{if((e|0)!=(b|0)){a=D[e+8>>2];D[a+12>>2]=b;D[b+8>>2]=a;break H}I:{a=e+20|0;d=D[a>>2];if(d){break I}a=e+16|0;d=D[a>>2];if(d){break I}b=0;break H}while(1){f=a;b=d;a=b+20|0;d=D[a>>2];if(d){continue}a=b+16|0;d=D[b+16>>2];if(d){continue}break}D[f>>2]=0}if(!h){break G}d=D[e+28>>2];a=(d<<2)+11828|0;J:{if(D[a>>2]==(e|0)){D[a>>2]=b;if(b){break J}m=11528,n=D[2882]&di(d),D[m>>2]=n;break G}D[h+(D[h+16>>2]==(e|0)?16:20)>>2]=b;if(!b){break G}}D[b+24>>2]=h;a=D[e+16>>2];if(a){D[b+16>>2]=a;D[a+24>>2]=b}a=D[e+20>>2];if(!a){break G}D[b+20>>2]=a;D[a+24>>2]=b}e=e+g|0;c=c+g|0}D[e+4>>2]=D[e+4>>2]&-2;D[i+4>>2]=c|1;D[c+i>>2]=c;if(c>>>0<=255){a=c>>>3|0;b=(a<<3)+11564|0;c=D[2881];a=1<<a;K:{if(!(c&a)){D[2881]=a|c;a=b;break K}a=D[b+8>>2]}D[b+8>>2]=i;D[a+12>>2]=i;D[i+12>>2]=b;D[i+8>>2]=a;break B}a=31;if(c>>>0<=16777215){a=c>>>8|0;f=a+1048320>>>16&8;a=a<<f;d=a+520192>>>16&4;a=a<<d;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(d|f))|0;a=(a<<1|c>>>a+21&1)+28|0}D[i+28>>2]=a;D[i+16>>2]=0;D[i+20>>2]=0;f=(a<<2)+11828|0;d=D[2882];b=1<<a;L:{if(!(d&b)){D[2882]=b|d;D[f>>2]=i;D[i+24>>2]=f;break L}a=c<<((a|0)==31?0:25-(a>>>1|0)|0);b=D[f>>2];while(1){d=b;if((D[b+4>>2]&-8)==(c|0)){break C}b=a>>>29|0;a=a<<1;f=d+(b&4)|0;b=D[f+16>>2];if(b){continue}break}D[f+16>>2]=i;D[i+24>>2]=d}D[i+12>>2]=i;D[i+8>>2]=i;break B}d=e-40|0;a=b+8&7?-8-b&7:0;c=d-a|0;D[2884]=c;a=a+b|0;D[2887]=a;D[a+4>>2]=c|1;D[(b+d|0)+4>>2]=40;D[2888]=D[3003];a=(f+(f-39&7?39-f&7:0)|0)-47|0;c=a>>>0<g+16>>>0?g:a;D[c+4>>2]=27;a=D[2996];D[c+16>>2]=D[2995];D[c+20>>2]=a;a=D[2994];D[c+8>>2]=D[2993];D[c+12>>2]=a;D[2995]=c+8;D[2994]=e;D[2993]=b;D[2996]=0;a=c+24|0;while(1){D[a+4>>2]=7;b=a+8|0;a=a+4|0;if(b>>>0<f>>>0){continue}break}if((c|0)==(g|0)){break x}D[c+4>>2]=D[c+4>>2]&-2;f=c-g|0;D[g+4>>2]=f|1;D[c>>2]=f;if(f>>>0<=255){a=f>>>3|0;b=(a<<3)+11564|0;c=D[2881];a=1<<a;M:{if(!(c&a)){D[2881]=a|c;a=b;break M}a=D[b+8>>2]}D[b+8>>2]=g;D[a+12>>2]=g;D[g+12>>2]=b;D[g+8>>2]=a;break x}a=31;D[g+16>>2]=0;D[g+20>>2]=0;if(f>>>0<=16777215){a=f>>>8|0;d=a+1048320>>>16&8;a=a<<d;c=a+520192>>>16&4;a=a<<c;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(c|d))|0;a=(a<<1|f>>>a+21&1)+28|0}D[g+28>>2]=a;d=(a<<2)+11828|0;c=D[2882];b=1<<a;N:{if(!(c&b)){D[2882]=b|c;D[d>>2]=g;D[g+24>>2]=d;break N}a=f<<((a|0)==31?0:25-(a>>>1|0)|0);b=D[d>>2];while(1){c=b;if((f|0)==(D[b+4>>2]&-8)){break A}b=a>>>29|0;a=a<<1;d=c+(b&4)|0;b=D[d+16>>2];if(b){continue}break}D[d+16>>2]=g;D[g+24>>2]=c}D[g+12>>2]=g;D[g+8>>2]=g;break x}a=D[d+8>>2];D[a+12>>2]=i;D[d+8>>2]=i;D[i+24>>2]=0;D[i+12>>2]=d;D[i+8>>2]=a}a=j+8|0;break a}a=D[c+8>>2];D[a+12>>2]=g;D[c+8>>2]=g;D[g+24>>2]=0;D[g+12>>2]=c;D[g+8>>2]=a}a=D[2884];if(a>>>0<=h>>>0){break d}b=a-h|0;D[2884]=b;c=D[2887];a=c+h|0;D[2887]=a;D[a+4>>2]=b|1;D[c+4>>2]=h|3;a=c+8|0;break a}D[2879]=48;a=0;break a}O:{if(!e){break O}c=D[f+28>>2];a=(c<<2)+11828|0;P:{if(D[a>>2]==(f|0)){D[a>>2]=b;if(b){break P}j=di(c)&j;D[2882]=j;break O}D[e+(D[e+16>>2]==(f|0)?16:20)>>2]=b;if(!b){break O}}D[b+24>>2]=e;a=D[f+16>>2];if(a){D[b+16>>2]=a;D[a+24>>2]=b}a=D[f+20>>2];if(!a){break O}D[b+20>>2]=a;D[a+24>>2]=b}Q:{if(d>>>0<=15){a=d+h|0;D[f+4>>2]=a|3;a=a+f|0;D[a+4>>2]=D[a+4>>2]|1;break Q}D[f+4>>2]=h|3;e=f+h|0;D[e+4>>2]=d|1;D[d+e>>2]=d;if(d>>>0<=255){a=d>>>3|0;b=(a<<3)+11564|0;c=D[2881];a=1<<a;R:{if(!(c&a)){D[2881]=a|c;a=b;break R}a=D[b+8>>2]}D[b+8>>2]=e;D[a+12>>2]=e;D[e+12>>2]=b;D[e+8>>2]=a;break Q}a=31;if(d>>>0<=16777215){a=d>>>8|0;g=a+1048320>>>16&8;a=a<<g;c=a+520192>>>16&4;a=a<<c;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(c|g))|0;a=(a<<1|d>>>a+21&1)+28|0}D[e+28>>2]=a;D[e+16>>2]=0;D[e+20>>2]=0;b=(a<<2)+11828|0;S:{c=1<<a;T:{if(!(c&j)){D[2882]=c|j;D[b>>2]=e;break T}a=d<<((a|0)==31?0:25-(a>>>1|0)|0);h=D[b>>2];while(1){b=h;if((D[b+4>>2]&-8)==(d|0)){break S}c=a>>>29|0;a=a<<1;c=(c&4)+b|0;h=D[c+16>>2];if(h){continue}break}D[c+16>>2]=e}D[e+24>>2]=b;D[e+12>>2]=e;D[e+8>>2]=e;break Q}a=D[b+8>>2];D[a+12>>2]=e;D[b+8>>2]=e;D[e+24>>2]=0;D[e+12>>2]=b;D[e+8>>2]=a}a=f+8|0;break a}U:{if(!i){break U}c=D[b+28>>2];a=(c<<2)+11828|0;V:{if(D[a>>2]==(b|0)){D[a>>2]=f;if(f){break V}m=11528,n=di(c)&j,D[m>>2]=n;break U}D[i+(D[i+16>>2]==(b|0)?16:20)>>2]=f;if(!f){break U}}D[f+24>>2]=i;a=D[b+16>>2];if(a){D[f+16>>2]=a;D[a+24>>2]=f}a=D[b+20>>2];if(!a){break U}D[f+20>>2]=a;D[a+24>>2]=f}W:{if(d>>>0<=15){a=d+h|0;D[b+4>>2]=a|3;a=a+b|0;D[a+4>>2]=D[a+4>>2]|1;break W}D[b+4>>2]=h|3;f=b+h|0;D[f+4>>2]=d|1;D[d+f>>2]=d;if(k){a=k>>>3|0;c=(a<<3)+11564|0;g=D[2886];a=1<<a;X:{if(!(a&e)){D[2881]=a|e;a=c;break X}a=D[c+8>>2]}D[c+8>>2]=g;D[a+12>>2]=g;D[g+12>>2]=c;D[g+8>>2]=a}D[2886]=f;D[2883]=d}a=b+8|0}$=l+16|0;return a|0}function Kd(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;h=$-80|0;$=h;e=D[c+36>>2];D[h+72>>2]=D[c+32>>2];D[h+76>>2]=e;f=D[c+28>>2];e=h- -64|0;D[e>>2]=D[c+24>>2];D[e+4>>2]=f;e=D[c+20>>2];D[h+56>>2]=D[c+16>>2];D[h+60>>2]=e;e=D[c+12>>2];D[h+48>>2]=D[c+8>>2];D[h+52>>2]=e;e=D[c+4>>2];D[h+40>>2]=D[c>>2];D[h+44>>2]=e;ec(a,h+40|0,h+24|0);a:{if(D[a>>2]){break a}l=a+4|0;if(B[a+15|0]<0){ma(D[l>>2])}if(E[h+31|0]!=1){b=na(32);c=E[1590]|E[1591]<<8|(E[1592]<<16|E[1593]<<24);B[b+16|0]=c;B[b+17|0]=c>>>8;B[b+18|0]=c>>>16;B[b+19|0]=c>>>24;c=E[1586]|E[1587]<<8|(E[1588]<<16|E[1589]<<24);d=E[1582]|E[1583]<<8|(E[1584]<<16|E[1585]<<24);B[b+8|0]=d;B[b+9|0]=d>>>8;B[b+10|0]=d>>>16;B[b+11|0]=d>>>24;B[b+12|0]=c;B[b+13|0]=c>>>8;B[b+14|0]=c>>>16;B[b+15|0]=c>>>24;c=E[1578]|E[1579]<<8|(E[1580]<<16|E[1581]<<24);d=E[1574]|E[1575]<<8|(E[1576]<<16|E[1577]<<24);B[b|0]=d;B[b+1|0]=d>>>8;B[b+2|0]=d>>>16;B[b+3|0]=d>>>24;B[b+4|0]=c;B[b+5|0]=c>>>8;B[b+6|0]=c>>>16;B[b+7|0]=c>>>24;B[b+20|0]=0;D[a>>2]=-1;ta(l,b,20);ma(b);break a}i=$-16|0;$=i;b:{c:{switch(E[h+32|0]){case 0:e=na(48);Fd(e);D[e>>2]=9896;D[h+8>>2]=0;D[h+12>>2]=0;D[h>>2]=0;D[h+4>>2]=0;D[h+16>>2]=e;break b;case 1:e=na(52);Fd(e);D[e+48>>2]=0;D[e>>2]=8204;D[h+8>>2]=0;D[h+12>>2]=0;D[h>>2]=0;D[h+4>>2]=0;D[h+16>>2]=e;break b;default:break c}}f=na(32);e=E[1664]|E[1665]<<8|(E[1666]<<16|E[1667]<<24);B[f+24|0]=e;B[f+25|0]=e>>>8;B[f+26|0]=e>>>16;B[f+27|0]=e>>>24;e=E[1660]|E[1661]<<8|(E[1662]<<16|E[1663]<<24);g=E[1656]|E[1657]<<8|(E[1658]<<16|E[1659]<<24);B[f+16|0]=g;B[f+17|0]=g>>>8;B[f+18|0]=g>>>16;B[f+19|0]=g>>>24;B[f+20|0]=e;B[f+21|0]=e>>>8;B[f+22|0]=e>>>16;B[f+23|0]=e>>>24;e=E[1652]|E[1653]<<8|(E[1654]<<16|E[1655]<<24);g=E[1648]|E[1649]<<8|(E[1650]<<16|E[1651]<<24);B[f+8|0]=g;B[f+9|0]=g>>>8;B[f+10|0]=g>>>16;B[f+11|0]=g>>>24;B[f+12|0]=e;B[f+13|0]=e>>>8;B[f+14|0]=e>>>16;B[f+15|0]=e>>>24;e=E[1644]|E[1645]<<8|(E[1646]<<16|E[1647]<<24);g=E[1640]|E[1641]<<8|(E[1642]<<16|E[1643]<<24);B[f|0]=g;B[f+1|0]=g>>>8;B[f+2|0]=g>>>16;B[f+3|0]=g>>>24;B[f+4|0]=e;B[f+5|0]=e>>>8;B[f+6|0]=e>>>16;B[f+7|0]=e>>>24;B[f+28|0]=0;D[i>>2]=-1;e=i|4;ta(e,f,28);k=B[i+15|0];D[h>>2]=D[i>>2];g=h+4|0;d:{if((k|0)>=0){k=D[e+4>>2];D[g>>2]=D[e>>2];D[g+4>>2]=k;D[g+8>>2]=D[e+8>>2];break d}ta(g,D[i+4>>2],D[i+8>>2])}D[h+16>>2]=0;if(B[i+15|0]<0){ma(D[i+4>>2])}ma(f)}$=i+16|0;e=D[h>>2];e:{if(e){D[a>>2]=e;if(B[h+15|0]>=0){a=h|4;b=D[a+4>>2];D[l>>2]=D[a>>2];D[l+4>>2]=b;D[l+8>>2]=D[a+8>>2];break e}ta(l,D[h+4>>2],D[h+8>>2]);break e}e=D[h+16>>2];D[h+16>>2]=0;D[e+44>>2]=d;i=$-32|0;$=i;D[e+32>>2]=c;D[e+40>>2]=b;D[e+4>>2]=d;ec(a,c,i+16|0);f:{if(D[a>>2]){break f}f=a+4|0;if(B[a+15|0]<0){ma(D[f>>2])}b=E[i+23|0];if((ba[D[D[e>>2]+8>>2]](e)|0)!=(b|0)){b=na(64);c=E[1448]|E[1449]<<8;B[b+48|0]=c;B[b+49|0]=c>>>8;c=E[1444]|E[1445]<<8|(E[1446]<<16|E[1447]<<24);d=E[1440]|E[1441]<<8|(E[1442]<<16|E[1443]<<24);B[b+40|0]=d;B[b+41|0]=d>>>8;B[b+42|0]=d>>>16;B[b+43|0]=d>>>24;B[b+44|0]=c;B[b+45|0]=c>>>8;B[b+46|0]=c>>>16;B[b+47|0]=c>>>24;c=E[1436]|E[1437]<<8|(E[1438]<<16|E[1439]<<24);d=E[1432]|E[1433]<<8|(E[1434]<<16|E[1435]<<24);B[b+32|0]=d;B[b+33|0]=d>>>8;B[b+34|0]=d>>>16;B[b+35|0]=d>>>24;B[b+36|0]=c;B[b+37|0]=c>>>8;B[b+38|0]=c>>>16;B[b+39|0]=c>>>24;c=E[1428]|E[1429]<<8|(E[1430]<<16|E[1431]<<24);d=E[1424]|E[1425]<<8|(E[1426]<<16|E[1427]<<24);B[b+24|0]=d;B[b+25|0]=d>>>8;B[b+26|0]=d>>>16;B[b+27|0]=d>>>24;B[b+28|0]=c;B[b+29|0]=c>>>8;B[b+30|0]=c>>>16;B[b+31|0]=c>>>24;c=E[1420]|E[1421]<<8|(E[1422]<<16|E[1423]<<24);d=E[1416]|E[1417]<<8|(E[1418]<<16|E[1419]<<24);B[b+16|0]=d;B[b+17|0]=d>>>8;B[b+18|0]=d>>>16;B[b+19|0]=d>>>24;B[b+20|0]=c;B[b+21|0]=c>>>8;B[b+22|0]=c>>>16;B[b+23|0]=c>>>24;c=E[1412]|E[1413]<<8|(E[1414]<<16|E[1415]<<24);d=E[1408]|E[1409]<<8|(E[1410]<<16|E[1411]<<24);B[b+8|0]=d;B[b+9|0]=d>>>8;B[b+10|0]=d>>>16;B[b+11|0]=d>>>24;B[b+12|0]=c;B[b+13|0]=c>>>8;B[b+14|0]=c>>>16;B[b+15|0]=c>>>24;c=E[1404]|E[1405]<<8|(E[1406]<<16|E[1407]<<24);d=E[1400]|E[1401]<<8|(E[1402]<<16|E[1403]<<24);B[b|0]=d;B[b+1|0]=d>>>8;B[b+2|0]=d>>>16;B[b+3|0]=d>>>24;B[b+4|0]=c;B[b+5|0]=c>>>8;B[b+6|0]=c>>>16;B[b+7|0]=c>>>24;B[b+50|0]=0;D[a>>2]=-1;ta(f,b,50);ma(b);break f}c=E[i+21|0];B[e+36|0]=c;d=E[i+22|0];B[e+37|0]=d;if((c|0)!=2){b=na(32);c=E[1571]|E[1572]<<8;B[b+24|0]=c;B[b+25|0]=c>>>8;c=E[1567]|E[1568]<<8|(E[1569]<<16|E[1570]<<24);d=E[1563]|E[1564]<<8|(E[1565]<<16|E[1566]<<24);B[b+16|0]=d;B[b+17|0]=d>>>8;B[b+18|0]=d>>>16;B[b+19|0]=d>>>24;B[b+20|0]=c;B[b+21|0]=c>>>8;B[b+22|0]=c>>>16;B[b+23|0]=c>>>24;c=E[1559]|E[1560]<<8|(E[1561]<<16|E[1562]<<24);d=E[1555]|E[1556]<<8|(E[1557]<<16|E[1558]<<24);B[b+8|0]=d;B[b+9|0]=d>>>8;B[b+10|0]=d>>>16;B[b+11|0]=d>>>24;B[b+12|0]=c;B[b+13|0]=c>>>8;B[b+14|0]=c>>>16;B[b+15|0]=c>>>24;c=E[1551]|E[1552]<<8|(E[1553]<<16|E[1554]<<24);d=E[1547]|E[1548]<<8|(E[1549]<<16|E[1550]<<24);B[b|0]=d;B[b+1|0]=d>>>8;B[b+2|0]=d>>>16;B[b+3|0]=d>>>24;B[b+4|0]=c;B[b+5|0]=c>>>8;B[b+6|0]=c>>>16;B[b+7|0]=c>>>24;B[b+26|0]=0;D[a>>2]=-5;ta(f,b,26);ma(b);break f}b=b?2:3;if((b|0)!=(d|0)){b=na(32);c=E[1544]|E[1545]<<8;B[b+24|0]=c;B[b+25|0]=c>>>8;c=E[1540]|E[1541]<<8|(E[1542]<<16|E[1543]<<24);d=E[1536]|E[1537]<<8|(E[1538]<<16|E[1539]<<24);B[b+16|0]=d;B[b+17|0]=d>>>8;B[b+18|0]=d>>>16;B[b+19|0]=d>>>24;B[b+20|0]=c;B[b+21|0]=c>>>8;B[b+22|0]=c>>>16;B[b+23|0]=c>>>24;c=E[1532]|E[1533]<<8|(E[1534]<<16|E[1535]<<24);d=E[1528]|E[1529]<<8|(E[1530]<<16|E[1531]<<24);B[b+8|0]=d;B[b+9|0]=d>>>8;B[b+10|0]=d>>>16;B[b+11|0]=d>>>24;B[b+12|0]=c;B[b+13|0]=c>>>8;B[b+14|0]=c>>>16;B[b+15|0]=c>>>24;c=E[1524]|E[1525]<<8|(E[1526]<<16|E[1527]<<24);d=E[1520]|E[1521]<<8|(E[1522]<<16|E[1523]<<24);B[b|0]=d;B[b+1|0]=d>>>8;B[b+2|0]=d>>>16;B[b+3|0]=d>>>24;B[b+4|0]=c;B[b+5|0]=c>>>8;B[b+6|0]=c>>>16;B[b+7|0]=c>>>24;B[b+26|0]=0;D[a>>2]=-5;ta(f,b,26);ma(b);break f}C[D[e+32>>2]+38>>1]=b|512;g:{if(C[i+26>>1]>=0){break g}k=$-16|0;$=k;d=na(36);b=d;D[b+4>>2]=0;D[b+8>>2]=0;D[b+24>>2]=0;D[b+28>>2]=0;b=b+16|0;D[b>>2]=0;D[b+4>>2]=0;D[d>>2]=d+4;D[d+32>>2]=0;D[d+12>>2]=b;D[k>>2]=0;c=D[e+32>>2];m=$-16|0;$=m;b=0;h:{if(!d){break h}D[k>>2]=c;D[m+12>>2]=0;b=0;if(!eb(1,m+12|0,c)){break h}p=D[m+12>>2];if(p){while(1){i:{if(eb(1,m+8|0,D[k>>2])){b=na(28);D[b+4>>2]=0;D[b+8>>2]=0;c=b+16|0;D[c>>2]=0;D[c+4>>2]=0;D[b>>2]=b+4;D[b+12>>2]=c;D[b+24>>2]=D[m+8>>2];if(Pc(k,b)){break i}Ca(b+12|0,D[b+16>>2]);Ba(b,D[b+4>>2]);ma(b)}b=0;break h}g=$-16|0;$=g;D[g+8>>2]=b;j:{if(!b){D[g+8>>2]=0;break j}c=D[d+28>>2];k:{if(c>>>0<G[d+32>>2]){D[g+8>>2]=0;D[c>>2]=b;D[d+28>>2]=c+4;break k}c=0;l:{m:{n:{j=D[d+24>>2];o=D[d+28>>2]-j>>2;b=o+1|0;if(b>>>0<1073741824){j=D[d+32>>2]-j|0;n=j>>1;b=j>>2>>>0<536870911?b>>>0>n>>>0?b:n:1073741823;if(b){if(b>>>0>=1073741824){break n}c=na(b<<2)}n=D[g+8>>2];D[g+8>>2]=0;j=(o<<2)+c|0;D[j>>2]=n;o=(b<<2)+c|0;n=j+4|0;c=D[d+28>>2];b=D[d+24>>2];if((c|0)==(b|0)){break m}while(1){c=c-4|0;r=D[c>>2];D[c>>2]=0;j=j-4|0;D[j>>2]=r;if((b|0)!=(c|0)){continue}break}D[d+32>>2]=o;c=D[d+28>>2];D[d+28>>2]=n;b=D[d+24>>2];D[d+24>>2]=j;if((b|0)==(c|0)){break l}while(1){c=c-4|0;j=D[c>>2];D[c>>2]=0;if(j){Ca(j+12|0,D[j+16>>2]);Ba(j,D[j+4>>2]);ma(j)}if((b|0)!=(c|0)){continue}break}break l}qa();T()}ra(1326);T()}D[d+32>>2]=o;D[d+28>>2]=n;D[d+24>>2]=j}if(b){ma(b)}}b=D[g+8>>2];D[g+8>>2]=0;if(!b){break j}Ca(b+12|0,D[b+16>>2]);Ba(b,D[b+4>>2]);ma(b)}$=g+16|0;q=q+1|0;if((q|0)!=(p|0)){continue}break}}b=Pc(k,d)}$=m+16|0;o:{if(b){c=D[e+4>>2];b=D[c+4>>2];D[c+4>>2]=d;if(b){dc(b)}D[a>>2]=0;D[a+4>>2]=0;D[a+8>>2]=0;D[a+12>>2]=0;break o}b=na(32);c=E[1693]|E[1694]<<8;B[b+24|0]=c;B[b+25|0]=c>>>8;c=E[1689]|E[1690]<<8|(E[1691]<<16|E[1692]<<24);g=E[1685]|E[1686]<<8|(E[1687]<<16|E[1688]<<24);B[b+16|0]=g;B[b+17|0]=g>>>8;B[b+18|0]=g>>>16;B[b+19|0]=g>>>24;B[b+20|0]=c;B[b+21|0]=c>>>8;B[b+22|0]=c>>>16;B[b+23|0]=c>>>24;c=E[1681]|E[1682]<<8|(E[1683]<<16|E[1684]<<24);g=E[1677]|E[1678]<<8|(E[1679]<<16|E[1680]<<24);B[b+8|0]=g;B[b+9|0]=g>>>8;B[b+10|0]=g>>>16;B[b+11|0]=g>>>24;B[b+12|0]=c;B[b+13|0]=c>>>8;B[b+14|0]=c>>>16;B[b+15|0]=c>>>24;c=E[1673]|E[1674]<<8|(E[1675]<<16|E[1676]<<24);g=E[1669]|E[1670]<<8|(E[1671]<<16|E[1672]<<24);B[b|0]=g;B[b+1|0]=g>>>8;B[b+2|0]=g>>>16;B[b+3|0]=g>>>24;B[b+4|0]=c;B[b+5|0]=c>>>8;B[b+6|0]=c>>>16;B[b+7|0]=c>>>24;B[b+26|0]=0;D[a>>2]=-1;ta(a+4|0,b,26);ma(b);D[k+8>>2]=0;dc(d)}$=k+16|0;if(D[a>>2]){break f}if(B[f+11|0]>=0){break g}ma(D[f>>2])}if(!(ba[D[D[e>>2]+12>>2]](e)|0)){b=na(48);B[b+32|0]=E[1518];c=E[1514]|E[1515]<<8|(E[1516]<<16|E[1517]<<24);d=E[1510]|E[1511]<<8|(E[1512]<<16|E[1513]<<24);B[b+24|0]=d;B[b+25|0]=d>>>8;B[b+26|0]=d>>>16;B[b+27|0]=d>>>24;B[b+28|0]=c;B[b+29|0]=c>>>8;B[b+30|0]=c>>>16;B[b+31|0]=c>>>24;c=E[1506]|E[1507]<<8|(E[1508]<<16|E[1509]<<24);d=E[1502]|E[1503]<<8|(E[1504]<<16|E[1505]<<24);B[b+16|0]=d;B[b+17|0]=d>>>8;B[b+18|0]=d>>>16;B[b+19|0]=d>>>24;B[b+20|0]=c;B[b+21|0]=c>>>8;B[b+22|0]=c>>>16;B[b+23|0]=c>>>24;c=E[1498]|E[1499]<<8|(E[1500]<<16|E[1501]<<24);d=E[1494]|E[1495]<<8|(E[1496]<<16|E[1497]<<24);B[b+8|0]=d;B[b+9|0]=d>>>8;B[b+10|0]=d>>>16;B[b+11|0]=d>>>24;B[b+12|0]=c;B[b+13|0]=c>>>8;B[b+14|0]=c>>>16;B[b+15|0]=c>>>24;c=E[1490]|E[1491]<<8|(E[1492]<<16|E[1493]<<24);d=E[1486]|E[1487]<<8|(E[1488]<<16|E[1489]<<24);B[b|0]=d;B[b+1|0]=d>>>8;B[b+2|0]=d>>>16;B[b+3|0]=d>>>24;B[b+4|0]=c;B[b+5|0]=c>>>8;B[b+6|0]=c>>>16;B[b+7|0]=c>>>24;B[b+33|0]=0;D[a>>2]=-1;ta(f,b,33);ma(b);break f}if(!(ba[D[D[e>>2]+20>>2]](e)|0)){b=Fb(i,1696);D[a>>2]=-1;if(B[b+11|0]>=0){b=D[i+4>>2];D[f>>2]=D[i>>2];D[f+4>>2]=b;D[f+8>>2]=D[i+8>>2];break f}ta(f,D[b>>2],D[b+4>>2]);if(B[b+11|0]>=0){break f}ma(D[b>>2]);break f}if(!(ba[D[D[e>>2]+24>>2]](e)|0)){b=Fb(i,1451);D[a>>2]=-1;if(B[b+11|0]>=0){b=D[i+4>>2];D[f>>2]=D[i>>2];D[f+4>>2]=b;D[f+8>>2]=D[i+8>>2];break f}ta(f,D[b>>2],D[b+4>>2]);if(B[b+11|0]>=0){break f}ma(D[b>>2]);break f}D[a>>2]=0;D[a+4>>2]=0;D[a+8>>2]=0;D[a+12>>2]=0}$=i+32|0;if(!D[a>>2]){if(B[l+11|0]<0){ma(D[l>>2])}D[a>>2]=0;D[a+4>>2]=0;D[a+8>>2]=0;D[a+12>>2]=0}ba[D[D[e>>2]+4>>2]](e)}a=D[h+16>>2];D[h+16>>2]=0;if(a){ba[D[D[a>>2]+4>>2]](a)}if(B[h+15|0]>=0){break a}ma(D[h+4>>2])}$=h+80|0}function Pc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;k=$-48|0;$=k;c=na(8);D[c+4>>2]=b;D[c>>2]=0;b=c+8|0;D[k+40>>2]=b;D[k+36>>2]=b;D[k+32>>2]=c;a:{b:{c:{d:{while(1){b=b-8|0;j=D[b+4>>2];g=D[b>>2];D[k+36>>2]=b;if(g){D[k+24>>2]=0;D[k+16>>2]=0;D[k+20>>2]=0;c=1;b=D[a>>2];e=D[b+8>>2];h=D[b+12>>2];d=D[b+20>>2];f=D[b+16>>2];e:{if((h|0)<=(d|0)&f>>>0>=e>>>0|(d|0)>(h|0)){break e}e=E[f+D[b>>2]|0];f=f+1|0;d=f?d:d+1|0;D[b+16>>2]=f;D[b+20>>2]=d;Qb(k+16|0,e);if(e){d=D[a>>2];m=Rb(k+16|0);f=D[d+20>>2];i=D[d+16>>2];b=e;n=i+e|0;h=D[d+12>>2];f=e>>>0>n>>>0?f+1|0:f;if(n>>>0>G[d+8>>2]&(h|0)<=(f|0)|(f|0)>(h|0)){break e}oa(m,i+D[d>>2]|0,e);c=D[d+20>>2];e=b+D[d+16>>2]|0;c=e>>>0<b>>>0?c+1|0:c;D[d+16>>2]=e;D[d+20>>2]=c}j=na(24);b=j;D[b+4>>2]=0;D[b+8>>2]=0;b=b+16|0;D[b>>2]=0;D[b+4>>2]=0;D[j>>2]=j+4;D[j+12>>2]=b;e=$-32|0;$=e;f=g+12|0;c=k+16|0;l=_a(f,c);p=g+16|0;f:{if((l|0)==(p|0)){D[e+16>>2]=c;b=D[f+4>>2];g:{h:{if(b){d=E[c+11|0];g=d<<24>>24<0;h=g?D[c>>2]:c;g=g?D[c+4>>2]:d;c=f+4|0;while(1){d=E[b+27|0];n=d<<24>>24<0;d=n?D[b+20>>2]:d;m=d>>>0<g>>>0;i:{j:{k:{l:{m:{i=m?d:g;n:{if(i){o=b+16|0;n=n?D[o>>2]:o;o=va(h,n,i);if(!o){if(d>>>0>g>>>0){break n}break m}if((o|0)>=0){break m}break n}if(d>>>0<=g>>>0){break l}}d=D[b>>2];if(d){break i}break h}d=va(n,h,i);if(d){break k}}if(m){break j}break g}if((d|0)>=0){break g}}c=b+4|0;d=D[b+4>>2];if(!d){break g}b=c}c=b;b=d;continue}}b=f+4|0}c=b}d=D[c>>2];if(d){b=0}else{d=na(32);h=d+16|0;g=D[e+16>>2];o:{if(B[g+11|0]>=0){n=D[g+4>>2];D[h>>2]=D[g>>2];D[h+4>>2]=n;D[h+8>>2]=D[g+8>>2];break o}ta(h,D[g>>2],D[g+4>>2])}D[d+8>>2]=b;D[d>>2]=0;D[d+4>>2]=0;D[d+28>>2]=0;D[c>>2]=d;b=D[D[f>>2]>>2];if(b){D[f>>2]=b;b=D[c>>2]}else{b=d}pb(D[f+4>>2],b);D[f+8>>2]=D[f+8>>2]+1;b=1}B[e+28|0]=b;D[e+24>>2]=d;c=D[e+24>>2];b=D[c+28>>2];D[c+28>>2]=j;if(!b){break f}Ca(b+12|0,D[b+16>>2]);Ba(b,D[b+4>>2]);ma(b);break f}if(!j){break f}Ca(j+12|0,D[j+16>>2]);Ba(j,D[j+4>>2]);ma(j)}$=e+32|0;c=(l|0)!=(p|0)}if(B[k+27|0]<0){ma(D[k+16>>2])}if(c){break b}}if(!j){break b}D[k+16>>2]=0;if(!eb(1,k+16|0,D[a>>2])){break b}b=0;r=D[k+16>>2];if(r){while(1){n=0;i=$-32|0;$=i;D[i+24>>2]=0;D[i+16>>2]=0;D[i+20>>2]=0;c=D[a>>2];e=D[c+8>>2];p:{q:{f=D[c+12>>2];d=D[c+20>>2];g=D[c+16>>2];r:{if((f|0)<=(d|0)&g>>>0>=e>>>0|(d|0)>(f|0)){break r}f=E[g+D[c>>2]|0];e=c;c=d;d=g+1|0;c=d?c:c+1|0;D[e+16>>2]=d;D[e+20>>2]=c;Qb(i+16|0,f);if(f){e=D[a>>2];m=Rb(i+16|0);d=D[e+20>>2];l=D[e+16>>2];c=f;h=l+c|0;g=D[e+12>>2];d=c>>>0>h>>>0?d+1|0:d;if(h>>>0>G[e+8>>2]&(g|0)<=(d|0)|(d|0)>(g|0)){break r}oa(m,l+D[e>>2]|0,f);d=D[e+20>>2];f=c+D[e+16>>2]|0;d=f>>>0<c>>>0?d+1|0:d;D[e+16>>2]=f;D[e+20>>2]=d}D[i+12>>2]=0;if(!eb(1,i+12|0,D[a>>2])){break r}c=D[i+12>>2];if(!c){break r}D[i+8>>2]=0;D[i>>2]=0;D[i+4>>2]=0;if((c|0)<0){break q}d=na(c);D[i>>2]=d;e=c+d|0;D[i+8>>2]=e;m=pa(d,0,c);D[i+4>>2]=e;e=D[a>>2];n=D[e+8>>2];g=D[e+12>>2];f=D[e+20>>2];l=D[e+16>>2];h=c+l|0;f=h>>>0<c>>>0?f+1|0:f;n=(f|0)<=(g|0)&h>>>0<=n>>>0|(f|0)<(g|0);if(n){oa(m,l+D[e>>2]|0,c);d=c;f=c+D[e+16>>2]|0;c=D[e+20>>2];D[e+16>>2]=f;D[e+20>>2]=d>>>0>f>>>0?c+1|0:c;g=$-48|0;$=g;d=_a(j,i+16|0);if((d|0)!=(j+4|0)){e=D[d+4>>2];s:{if(!e){f=D[d+8>>2];if(D[f>>2]==(d|0)){break s}c=d+8|0;while(1){e=D[c>>2];c=e+8|0;f=D[e+8>>2];if((e|0)!=D[f>>2]){continue}break}break s}while(1){f=e;e=D[e>>2];if(e){continue}break}}if(D[j>>2]==(d|0)){D[j>>2]=f}D[j+8>>2]=D[j+8>>2]-1;f=D[j+4>>2];h=d;t:{u:{e=D[d>>2];if(e){c=D[h+4>>2];if(!c){break u}while(1){d=c;c=D[c>>2];if(c){continue}break}}e=D[d+4>>2];if(e){break u}e=0;m=1;break t}D[e+8>>2]=D[d+8>>2];m=0}l=D[d+8>>2];c=D[l>>2];v:{if((d|0)==(c|0)){D[l>>2]=e;if((d|0)==(f|0)){c=0;f=e;break v}c=D[l+4>>2];break v}D[l+4>>2]=e}o=!E[d+12|0];if((d|0)!=(h|0)){l=D[h+8>>2];D[d+8>>2]=l;D[l+(((h|0)!=D[D[h+8>>2]>>2])<<2)>>2]=d;l=D[h>>2];D[d>>2]=l;D[l+8>>2]=d;l=D[h+4>>2];D[d+4>>2]=l;if(l){D[l+8>>2]=d}B[d+12|0]=E[h+12|0];f=(f|0)==(h|0)?d:f}w:{if(o|!f){break w}if(m){while(1){e=E[c+12|0];x:{d=D[c+8>>2];if(D[d>>2]!=(c|0)){if(!e){B[c+12|0]=1;B[d+12|0]=0;e=D[d+4>>2];m=D[e>>2];D[d+4>>2]=m;if(m){D[m+8>>2]=d}D[e+8>>2]=D[d+8>>2];m=D[d+8>>2];D[(((d|0)!=D[m>>2])<<2)+m>>2]=e;D[e>>2]=d;D[d+8>>2]=e;d=c;c=D[c>>2];f=(c|0)==(f|0)?d:f;c=D[c+4>>2]}y:{z:{d=D[c>>2];A:{if(!(E[d+12|0]?0:d)){e=D[c+4>>2];if(E[e+12|0]?0:e){break A}B[c+12|0]=0;c=D[c+8>>2];B:{if((f|0)==(c|0)){c=f;break B}if(E[c+12|0]){break x}}B[c+12|0]=1;break w}e=D[c+4>>2];if(!e){break z}}if(E[e+12|0]){break z}d=c;break y}B[d+12|0]=1;B[c+12|0]=0;e=D[d+4>>2];D[c>>2]=e;if(e){D[e+8>>2]=c}D[d+8>>2]=D[c+8>>2];e=D[c+8>>2];D[((D[e>>2]!=(c|0))<<2)+e>>2]=d;D[d+4>>2]=c;D[c+8>>2]=d;e=c}c=D[d+8>>2];B[d+12|0]=E[c+12|0];B[c+12|0]=1;B[e+12|0]=1;d=D[c+4>>2];e=D[d>>2];D[c+4>>2]=e;if(e){D[e+8>>2]=c}D[d+8>>2]=D[c+8>>2];e=D[c+8>>2];D[(((c|0)!=D[e>>2])<<2)+e>>2]=d;D[d>>2]=c;D[c+8>>2]=d;break w}if(!e){B[c+12|0]=1;B[d+12|0]=0;e=D[c+4>>2];D[d>>2]=e;if(e){D[e+8>>2]=d}D[c+8>>2]=D[d+8>>2];e=D[d+8>>2];D[(((d|0)!=D[e>>2])<<2)+e>>2]=c;D[c+4>>2]=d;D[d+8>>2]=c;f=(d|0)==(f|0)?c:f;c=D[d>>2]}e=D[c>>2];C:{if(!(!e|E[e+12|0])){d=c;break C}d=D[c+4>>2];if(!(E[d+12|0]?0:d)){B[c+12|0]=0;c=D[c+8>>2];if((c|0)!=(f|0)?E[c+12|0]:0){break x}B[c+12|0]=1;break w}if(e){if(!E[e+12|0]){d=c;break C}d=D[c+4>>2]}B[d+12|0]=1;B[c+12|0]=0;e=D[d>>2];D[c+4>>2]=e;if(e){D[e+8>>2]=c}D[d+8>>2]=D[c+8>>2];e=D[c+8>>2];D[((D[e>>2]!=(c|0))<<2)+e>>2]=d;D[d>>2]=c;D[c+8>>2]=d;e=c}c=D[d+8>>2];B[d+12|0]=E[c+12|0];B[c+12|0]=1;B[e+12|0]=1;d=D[c>>2];e=D[d+4>>2];D[c>>2]=e;if(e){D[e+8>>2]=c}D[d+8>>2]=D[c+8>>2];e=D[c+8>>2];D[(((c|0)!=D[e>>2])<<2)+e>>2]=d;D[d+4>>2]=c;D[c+8>>2]=d;break w}d=c;c=D[c+8>>2];c=D[(((d|0)==D[c>>2])<<2)+c>>2];continue}}B[e+12|0]=1}c=D[h+28>>2];if(c){D[h+32>>2]=c;ma(c)}if(B[h+27|0]<0){ma(D[h+16>>2])}ma(h)}D[g>>2]=0;D[g+4>>2]=0;D[g+8>>2]=0;c=D[i+4>>2]-D[i>>2]|0;gb(g,c);oa(D[g>>2],D[i>>2],c);D:{if(B[i+27|0]>=0){D[g+24>>2]=D[i+24>>2];c=D[i+20>>2];D[g+16>>2]=D[i+16>>2];D[g+20>>2]=c;break D}ta(g+16|0,D[i+16>>2],D[i+20>>2])}D[g+36>>2]=0;D[g+28>>2]=0;D[g+32>>2]=0;gb(g+28|0,D[g+4>>2]-D[g>>2]|0);c=D[g>>2];oa(D[g+28>>2],c,D[g+4>>2]-c|0);f=g+16|0;d=f;c=D[j+4>>2];E:{F:{if(c){e=E[d+11|0];h=e<<24>>24<0;m=h?D[d>>2]:d;h=h?D[d+4>>2]:e;d=j+4|0;while(1){e=E[c+27|0];l=e<<24>>24<0;e=l?D[c+20>>2]:e;p=e>>>0<h>>>0;G:{H:{I:{J:{K:{o=p?e:h;L:{if(o){q=c+16|0;l=l?D[q>>2]:q;q=va(m,l,o);if(!q){if(e>>>0>h>>>0){break L}break K}if((q|0)>=0){break K}break L}if(e>>>0<=h>>>0){break J}}e=D[c>>2];if(e){break G}break F}e=va(l,m,o);if(e){break I}}if(p){break H}break E}if((e|0)>=0){break E}}d=c+4|0;e=D[c+4>>2];if(!e){break E}c=d}d=c;c=e;continue}}c=j+4|0}d=c}e=D[d>>2];if(e){c=0}else{e=na(40);D[e+24>>2]=D[f+8>>2];h=D[f+4>>2];D[e+16>>2]=D[f>>2];D[e+20>>2]=h;D[f>>2]=0;D[f+4>>2]=0;D[f+8>>2]=0;D[e+36>>2]=0;h=e+28|0;D[h>>2]=0;D[h+4>>2]=0;gb(h,D[f+16>>2]-D[f+12>>2]|0);m=D[h>>2];h=D[f+12>>2];oa(m,h,D[f+16>>2]-h|0);D[e+8>>2]=c;D[e>>2]=0;D[e+4>>2]=0;D[d>>2]=e;c=D[D[j>>2]>>2];if(c){D[j>>2]=c;c=D[d>>2]}else{c=e}pb(D[j+4>>2],c);D[j+8>>2]=D[j+8>>2]+1;c=1}B[g+44|0]=c;D[g+40>>2]=e;c=D[g+28>>2];if(c){D[g+32>>2]=c;ma(c)}if(B[g+27|0]<0){ma(D[g+16>>2])}c=D[g>>2];if(c){D[g+4>>2]=c;ma(c)}$=g+48|0;d=D[i>>2];if(!d){break r}}D[i+4>>2]=d;ma(d)}if(B[i+27|0]<0){ma(D[i+16>>2])}$=i+32|0;break p}qa();T()}if(!n){break b}b=b+1|0;if((r|0)!=(b|0)){continue}break}}D[k+12>>2]=0;if(!eb(1,k+12|0,D[a>>2])){break b}b=D[a>>2];c=D[b+8>>2];d=D[b+16>>2];e=c-d|0;f=D[k+12>>2];b=D[b+12>>2]-(D[b+20>>2]+(c>>>0<d>>>0)|0)|0;if(e>>>0<f>>>0&(b|0)<=0|(b|0)<0){break b}c=0;b=D[k+36>>2];if(f){while(1){e=D[k+40>>2];M:{if(e>>>0>b>>>0){D[b+4>>2]=0;D[b>>2]=j;b=b+8|0;D[k+36>>2]=b;break M}d=D[k+32>>2];g=b-d|0;h=g>>3;b=h+1|0;if(b>>>0>=536870912){break d}e=e-d|0;n=e>>2;b=e>>3>>>0<268435455?b>>>0>n>>>0?b:n:536870911;if(b>>>0>=536870912){break c}m=h<<3;h=b<<3;e=na(h);b=m+e|0;D[b+4>>2]=0;D[b>>2]=j;b=b+8|0;if((g|0)>0){oa(e,d,g)}D[k+40>>2]=e+h;D[k+36>>2]=b;D[k+32>>2]=e;if(!d){break M}ma(d)}c=c+1|0;if((f|0)!=(c|0)){continue}break}}if(D[k+32>>2]!=(b|0)){continue}break}a=1;break a}qa();T()}ra(1326);T()}b=D[k+32>>2];a=0}if(b){ma(b)}$=k+48|0;return a}function he(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;j=$-48|0;$=j;a:{if((c|0)!=1){break a}f=D[a+4>>2];a=D[a+12>>2];D[j+40>>2]=0;D[j+32>>2]=0;D[j+36>>2]=0;D[j+24>>2]=0;D[j+28>>2]=0;D[j+16>>2]=0;D[j+20>>2]=0;D[j+8>>2]=0;D[j+12>>2]=0;e=j+8|0;b:{if((b|0)==-2){break b}l=D[D[D[f+4>>2]+8>>2]+(a<<2)>>2];if((ba[D[D[f>>2]+8>>2]](f)|0)==1){k=$-32|0;$=k;g=D[D[D[f+4>>2]+8>>2]+(a<<2)>>2];c:{d:{e:{if((ba[D[D[f>>2]+8>>2]](f)|0)!=1|b-1>>>0>5){break e}i=ba[D[D[f>>2]+36>>2]](f)|0;h=ba[D[D[f>>2]+44>>2]](f,a)|0;if(!i|!h){break e}c=ba[D[D[f>>2]+40>>2]](f,a)|0;if(c){a=D[f+44>>2];D[k+12>>2]=c;D[k+8>>2]=a;D[k+20>>2]=h;D[k+16>>2]=h+12;f=k+8|0;a=0;f:{g:{switch(b-1|0){case 0:a=na(60);D[a+4>>2]=g;D[a>>2]=2988;g=D[e+8>>2];h=D[e+12>>2];i=D[e+16>>2];d=D[e+20>>2];c=D[e>>2];b=D[e+4>>2];D[a+40>>2]=0;D[a+32>>2]=0;D[a+36>>2]=0;D[a+24>>2]=i;D[a+28>>2]=d;D[a+16>>2]=g;D[a+20>>2]=h;D[a+8>>2]=c;D[a+12>>2]=b;b=D[e+24>>2];d=D[e+28>>2]-b|0;if(d){if((d|0)<0){break d}c=na(d);D[a+32>>2]=c;D[a+40>>2]=c+(d>>2<<2);m=a,n=oa(c,b,d)+d|0,D[m+36>>2]=n}b=D[f+4>>2];D[a+44>>2]=D[f>>2];D[a+48>>2]=b;b=D[f+12>>2];D[a+52>>2]=D[f+8>>2];D[a+56>>2]=b;D[a>>2]=2280;break f;case 3:a=na(112);D[a+4>>2]=g;D[a>>2]=2988;g=D[e+8>>2];h=D[e+12>>2];i=D[e+16>>2];d=D[e+20>>2];c=D[e>>2];b=D[e+4>>2];D[a+40>>2]=0;D[a+32>>2]=0;D[a+36>>2]=0;D[a+24>>2]=i;D[a+28>>2]=d;D[a+16>>2]=g;D[a+20>>2]=h;D[a+8>>2]=c;D[a+12>>2]=b;b=D[e+24>>2];d=D[e+28>>2]-b|0;if(d){if((d|0)<0){break d}c=na(d);D[a+32>>2]=c;D[a+40>>2]=c+(d>>2<<2);m=a,n=oa(c,b,d)+d|0,D[m+36>>2]=n}b=D[f+4>>2];D[a+44>>2]=D[f>>2];D[a+48>>2]=b;b=D[f+12>>2];D[a+52>>2]=D[f+8>>2];D[a+56>>2]=b;D[a+60>>2]=0;D[a+64>>2]=0;D[a>>2]=3044;D[a+68>>2]=0;D[a+72>>2]=0;D[a+76>>2]=0;D[a+80>>2]=0;D[a+84>>2]=0;D[a+88>>2]=0;D[a+92>>2]=0;D[a+96>>2]=0;D[a+100>>2]=0;D[a+104>>2]=0;D[a+108>>2]=0;break f;case 4:a=na(104);D[a+4>>2]=g;D[a>>2]=2988;g=D[e+8>>2];h=D[e+12>>2];i=D[e+16>>2];d=D[e+20>>2];c=D[e>>2];b=D[e+4>>2];D[a+40>>2]=0;D[a+32>>2]=0;D[a+36>>2]=0;D[a+24>>2]=i;D[a+28>>2]=d;D[a+16>>2]=g;D[a+20>>2]=h;D[a+8>>2]=c;D[a+12>>2]=b;b=D[e+24>>2];d=D[e+28>>2]-b|0;if(d){if((d|0)<0){break d}c=na(d);D[a+32>>2]=c;D[a+40>>2]=c+(d>>2<<2);m=a,n=oa(c,b,d)+d|0,D[m+36>>2]=n}b=D[f+4>>2];D[a+44>>2]=D[f>>2];D[a+48>>2]=b;c=D[f+8>>2];b=D[f+12>>2];D[a+84>>2]=0;D[a+76>>2]=0;D[a+80>>2]=0;D[a+60>>2]=0;D[a+64>>2]=0;D[a>>2]=3292;D[a+52>>2]=c;D[a+56>>2]=b;b=D[f+4>>2];D[a+88>>2]=D[f>>2];D[a+92>>2]=b;b=D[f+12>>2];D[a+96>>2]=D[f+8>>2];D[a+100>>2]=b;break f;case 5:break g;default:break f}}a=na(128);D[a+4>>2]=g;D[a>>2]=2988;g=D[e+8>>2];h=D[e+12>>2];i=D[e+16>>2];d=D[e+20>>2];c=D[e>>2];b=D[e+4>>2];D[a+40>>2]=0;D[a+32>>2]=0;D[a+36>>2]=0;D[a+24>>2]=i;D[a+28>>2]=d;D[a+16>>2]=g;D[a+20>>2]=h;D[a+8>>2]=c;D[a+12>>2]=b;h:{i:{c=D[e+28>>2]-D[e+24>>2]|0;if(c){if((c|0)<0){break i}b=na(c);D[a+32>>2]=b;D[a+36>>2]=b;D[a+40>>2]=b+(c>>2<<2);c=D[e+24>>2];d=D[e+28>>2]-c|0;if((d|0)>0){b=oa(b,c,d)+d|0}D[a+36>>2]=b}D[a>>2]=2932;b=D[f+4>>2];D[a+44>>2]=D[f>>2];D[a+48>>2]=b;b=D[f+12>>2];D[a+52>>2]=D[f+8>>2];D[a+56>>2]=b;b=a- -64|0;D[b>>2]=0;D[b+4>>2]=0;D[a+60>>2]=4156;D[a>>2]=3528;b=D[f+4>>2];D[a+72>>2]=D[f>>2];D[a+76>>2]=b;b=D[f+12>>2];D[a+80>>2]=D[f+8>>2];D[a+84>>2]=b;D[a+104>>2]=1065353216;D[a+108>>2]=-1;D[a+96>>2]=-1;D[a+100>>2]=-1;D[a+88>>2]=1;D[a+92>>2]=-1;D[a+60>>2]=3764;D[a+112>>2]=0;D[a+116>>2]=0;B[a+117|0]=0;B[a+118|0]=0;B[a+119|0]=0;B[a+120|0]=0;B[a+121|0]=0;B[a+122|0]=0;B[a+123|0]=0;B[a+124|0]=0;break h}qa();T()}break f}d=a;break e}a=D[f+44>>2];D[k+12>>2]=i;D[k+8>>2]=a;D[k+20>>2]=h;D[k+16>>2]=h+12;f=k+8|0;a=0;j:{k:{switch(b-1|0){case 0:a=na(60);D[a+4>>2]=g;D[a>>2]=2988;g=D[e+8>>2];h=D[e+12>>2];i=D[e+16>>2];d=D[e+20>>2];c=D[e>>2];b=D[e+4>>2];D[a+40>>2]=0;D[a+32>>2]=0;D[a+36>>2]=0;D[a+24>>2]=i;D[a+28>>2]=d;D[a+16>>2]=g;D[a+20>>2]=h;D[a+8>>2]=c;D[a+12>>2]=b;b=D[e+24>>2];d=D[e+28>>2]-b|0;if(d){if((d|0)<0){break d}c=na(d);D[a+32>>2]=c;D[a+40>>2]=c+(d>>2<<2);m=a,n=oa(c,b,d)+d|0,D[m+36>>2]=n}b=D[f+4>>2];D[a+44>>2]=D[f>>2];D[a+48>>2]=b;b=D[f+12>>2];D[a+52>>2]=D[f+8>>2];D[a+56>>2]=b;D[a>>2]=4184;break j;case 3:a=na(112);D[a+4>>2]=g;D[a>>2]=2988;g=D[e+8>>2];h=D[e+12>>2];i=D[e+16>>2];d=D[e+20>>2];c=D[e>>2];b=D[e+4>>2];D[a+40>>2]=0;D[a+32>>2]=0;D[a+36>>2]=0;D[a+24>>2]=i;D[a+28>>2]=d;D[a+16>>2]=g;D[a+20>>2]=h;D[a+8>>2]=c;D[a+12>>2]=b;b=D[e+24>>2];d=D[e+28>>2]-b|0;if(d){if((d|0)<0){break d}c=na(d);D[a+32>>2]=c;D[a+40>>2]=c+(d>>2<<2);m=a,n=oa(c,b,d)+d|0,D[m+36>>2]=n}b=D[f+4>>2];D[a+44>>2]=D[f>>2];D[a+48>>2]=b;b=D[f+12>>2];D[a+52>>2]=D[f+8>>2];D[a+56>>2]=b;D[a+60>>2]=0;D[a+64>>2]=0;D[a>>2]=4608;D[a+68>>2]=0;D[a+72>>2]=0;D[a+76>>2]=0;D[a+80>>2]=0;D[a+84>>2]=0;D[a+88>>2]=0;D[a+92>>2]=0;D[a+96>>2]=0;D[a+100>>2]=0;D[a+104>>2]=0;D[a+108>>2]=0;break j;case 4:a=na(104);D[a+4>>2]=g;D[a>>2]=2988;g=D[e+8>>2];h=D[e+12>>2];i=D[e+16>>2];d=D[e+20>>2];c=D[e>>2];b=D[e+4>>2];D[a+40>>2]=0;D[a+32>>2]=0;D[a+36>>2]=0;D[a+24>>2]=i;D[a+28>>2]=d;D[a+16>>2]=g;D[a+20>>2]=h;D[a+8>>2]=c;D[a+12>>2]=b;b=D[e+24>>2];d=D[e+28>>2]-b|0;if(d){if((d|0)<0){break d}c=na(d);D[a+32>>2]=c;D[a+40>>2]=c+(d>>2<<2);m=a,n=oa(c,b,d)+d|0,D[m+36>>2]=n}b=D[f+4>>2];D[a+44>>2]=D[f>>2];D[a+48>>2]=b;c=D[f+8>>2];b=D[f+12>>2];D[a+84>>2]=0;D[a+76>>2]=0;D[a+80>>2]=0;D[a+60>>2]=0;D[a+64>>2]=0;D[a>>2]=4844;D[a+52>>2]=c;D[a+56>>2]=b;b=D[f+4>>2];D[a+88>>2]=D[f>>2];D[a+92>>2]=b;b=D[f+12>>2];D[a+96>>2]=D[f+8>>2];D[a+100>>2]=b;break j;case 5:break k;default:break j}}a=na(128);D[a+4>>2]=g;D[a>>2]=2988;g=D[e+8>>2];h=D[e+12>>2];i=D[e+16>>2];d=D[e+20>>2];c=D[e>>2];b=D[e+4>>2];D[a+40>>2]=0;D[a+32>>2]=0;D[a+36>>2]=0;D[a+24>>2]=i;D[a+28>>2]=d;D[a+16>>2]=g;D[a+20>>2]=h;D[a+8>>2]=c;D[a+12>>2]=b;l:{m:{c=D[e+28>>2]-D[e+24>>2]|0;if(c){if((c|0)<0){break m}b=na(c);D[a+32>>2]=b;D[a+36>>2]=b;D[a+40>>2]=b+(c>>2<<2);c=D[e+24>>2];d=D[e+28>>2]-c|0;if((d|0)>0){b=oa(b,c,d)+d|0}D[a+36>>2]=b}D[a>>2]=4552;b=D[f+4>>2];D[a+44>>2]=D[f>>2];D[a+48>>2]=b;b=D[f+12>>2];D[a+52>>2]=D[f+8>>2];D[a+56>>2]=b;b=a- -64|0;D[b>>2]=0;D[b+4>>2]=0;D[a+60>>2]=5652;D[a>>2]=5068;b=D[f+4>>2];D[a+72>>2]=D[f>>2];D[a+76>>2]=b;b=D[f+12>>2];D[a+80>>2]=D[f+8>>2];D[a+84>>2]=b;D[a+104>>2]=1065353216;D[a+108>>2]=-1;D[a+96>>2]=-1;D[a+100>>2]=-1;D[a+88>>2]=1;D[a+92>>2]=-1;D[a+60>>2]=5288;D[a+112>>2]=0;D[a+116>>2]=0;B[a+117|0]=0;B[a+118|0]=0;B[a+119|0]=0;B[a+120|0]=0;B[a+121|0]=0;B[a+122|0]=0;B[a+123|0]=0;B[a+124|0]=0;break l}qa();T()}break j}d=a}$=k+32|0;break c}qa();T()}if(d){break b}}d=na(44);D[d+4>>2]=l;D[d>>2]=2988;g=D[e+8>>2];h=D[e+12>>2];i=D[e+16>>2];c=D[e+20>>2];b=D[e>>2];a=D[e+4>>2];D[d+40>>2]=0;D[d+32>>2]=0;D[d+36>>2]=0;D[d+24>>2]=i;D[d+28>>2]=c;D[d+16>>2]=g;D[d+20>>2]=h;D[d+8>>2]=b;D[d+12>>2]=a;n:{a=D[e+24>>2];c=D[e+28>>2]-a|0;if(c){if((c|0)<0){break n}b=na(c);D[d+32>>2]=b;D[d+40>>2]=b+(c>>2<<2);m=d,n=oa(b,a,c)+c|0,D[m+36>>2]=n}D[d>>2]=5680;break b}qa();T()}a=D[j+32>>2];if(!a){break a}D[j+36>>2]=a;ma(a)}$=j+48|0;return d|0}function $d(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,E=0,F=0,G=0,H=0,I=0,K=0;h=$+-64|0;$=h;D[a+8>>2]=e;f=a+32|0;g=D[f>>2];d=D[a+36>>2]-g>>2;a:{b:{if(d>>>0<e>>>0){sa(f,e-d|0);D[h+56>>2]=0;D[h+60>>2]=0;D[h+48>>2]=0;D[h+52>>2]=0;D[h+40>>2]=0;D[h+44>>2]=0;D[h+32>>2]=0;D[h+36>>2]=0;D[h+24>>2]=0;D[h+28>>2]=0;D[h+16>>2]=0;D[h+20>>2]=0;D[h>>2]=0;break b}if(d>>>0>e>>>0){D[a+36>>2]=g+(e<<2)}D[h+56>>2]=0;D[h+60>>2]=0;D[h+48>>2]=0;D[h+52>>2]=0;D[h+40>>2]=0;D[h+44>>2]=0;D[h+32>>2]=0;D[h+36>>2]=0;D[h+24>>2]=0;D[h+28>>2]=0;D[h+16>>2]=0;D[h+20>>2]=0;D[h>>2]=0;d=0;if(!e){break a}}xa(h+16|0,e,h);i=D[h+28>>2];d=D[h+32>>2]}D[h>>2]=0;d=d-i>>2;c:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break c}D[h+32>>2]=(e<<2)+i;break c}xa(h+16|12,e-d|0,h)}D[h>>2]=0;f=D[h+40>>2];d=D[h+44>>2]-f>>2;d:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break d}D[h+44>>2]=f+(e<<2);break d}xa(h+40|0,e-d|0,h)}D[h>>2]=0;f=D[h+52>>2];d=D[h+56>>2]-f>>2;e:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break e}D[h+56>>2]=f+(e<<2);break e}xa(h+52|0,e-d|0,h)}i=0;f:{if(D[a+8>>2]<=0){break f}j=D[a+32>>2];g=D[h+16>>2];while(1){d=i<<2;f=D[d+g>>2];m=D[a+16>>2];g:{if((f|0)>(m|0)){D[d+j>>2]=m;break g}d=d+j|0;m=D[a+12>>2];if((m|0)>(f|0)){D[d>>2]=m;break g}D[d>>2]=f}i=i+1|0;d=D[a+8>>2];if((i|0)<(d|0)){continue}break}if((d|0)<=0){break f}d=0;while(1){g=d<<2;f=g+c|0;g=D[b+g>>2]+D[g+j>>2]|0;D[f>>2]=g;h:{if((g|0)>D[a+16>>2]){g=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break h}g=g+D[a+20>>2]|0}D[f>>2]=g}d=d+1|0;if((d|0)<D[a+8>>2]){continue}break}}E=D[a+52>>2];q=D[a+48>>2];w=na(16);d=w;D[d>>2]=0;D[d+4>>2]=0;D[d+8>>2]=0;D[d+12>>2]=0;D[h+8>>2]=0;D[h>>2]=0;D[h+4>>2]=0;i:{if(e){if(e>>>0>=1073741824){break i}d=e<<2;v=na(d);D[h>>2]=v;D[h+8>>2]=d+v;pa(v,0,d)}p=1;d=D[a+56>>2];z=D[d>>2];d=D[d+4>>2]-z|0;j:{if((d|0)<5){break j}d=d>>2;F=(d|0)>2?d:2;G=d>>>0>1?d:1;A=e&-2;B=e&1;H=e&-4;C=e&3;x=e-1|0;I=e<<2;m=1;while(1){k:{l:{m:{n:{if((m|0)!=(G|0)){f=D[(m<<2)+z>>2];d=(f>>>0)%3|0;o:{p:{if((f|0)==-1){break p}j=0;g=f+2|0;y=(d|0)!=0|(g|0)!=-1;n=1;r=d?f-1|0:g;o=1<<r;s=r>>>5|0;K=D[q>>2];d=f;q:{while(1){r:{if(D[(d>>>3&536870908)+K>>2]>>>d&1){break r}g=D[D[D[q+64>>2]+12>>2]+(d<<2)>>2];if((g|0)==-1){break r}k=D[E>>2];i=D[q+28>>2];p=D[k+(D[i+(g<<2)>>2]<<2)>>2];if((p|0)>=(m|0)){break r}l=g+1|0;l=D[k+(D[i+(((l>>>0)%3|0?l:g-2|0)<<2)>>2]<<2)>>2];if((l|0)>=(m|0)){break r}i=D[k+(D[i+(g+((g>>>0)%3|0?-1:2)<<2)>>2]<<2)>>2];if((i|0)>=(m|0)){break r}s:{if(!e){break s}g=D[(h+16|0)+J(j,12)>>2];k=J(e,i);l=J(e,l);p=J(e,p);i=0;u=0;if(x){while(1){D[g+(i<<2)>>2]=(D[(i+k<<2)+c>>2]+D[(i+l<<2)+c>>2]|0)-D[(i+p<<2)+c>>2];t=i|1;D[g+(t<<2)>>2]=(D[(k+t<<2)+c>>2]+D[(l+t<<2)+c>>2]|0)-D[(p+t<<2)+c>>2];i=i+2|0;u=u+2|0;if((A|0)!=(u|0)){continue}break}}if(!B){break s}D[g+(i<<2)>>2]=(D[(i+k<<2)+c>>2]+D[(i+l<<2)+c>>2]|0)-D[(i+p<<2)+c>>2]}g=4;j=j+1|0;if((j|0)==4){break q}}t:{if(n&1){i=d-2|0;g=d+1|0;d=-1;g=(g>>>0)%3|0?g:i;if((g|0)==-1|D[D[q>>2]+(g>>>3&536870908)>>2]>>>g&1){break t}g=D[D[D[q+64>>2]+12>>2]+(g<<2)>>2];if((g|0)==-1){break t}d=g+1|0;d=(d>>>0)%3|0?d:g-2|0;break t}u:{if((d>>>0)%3|0){i=d-1|0;break u}i=d+2|0;d=-1;if((i|0)==-1){break t}}d=-1;if(D[D[q>>2]+(i>>>3&536870908)>>2]>>>i&1){break t}g=D[D[D[q+64>>2]+12>>2]+(i<<2)>>2];if((g|0)==-1){break t}if((g>>>0)%3|0){d=g-1|0;break t}d=g+2|0}v:{if((d|0)==(f|0)){break v}if(!((n^1)&1|(d|0)!=-1)){if(!y|o&D[D[q>>2]+(s<<2)>>2]){break v}d=D[D[D[q+64>>2]+12>>2]+(r<<2)>>2];if((d|0)==-1){break v}n=0;d=(d>>>0)%3|0?d-1|0:d+2|0}if((d|0)!=-1){continue}}break}g=j;if((g|0)<=0){break p}}if(e){pa(D[h>>2],0,I)}d=g-1|0;t=(d<<2)+w|0;d=J(d,12)+a|0;u=d;y=D[d- -64>>2];p=0;n=0;d=0;while(1){f=D[t>>2];D[t>>2]=f+1;if(f>>>0>=y>>>0){break j}w:{if(D[D[u+60>>2]+(f>>>3&536870908)>>2]>>>f&1){break w}d=d+1|0;if(!e){break w}j=D[h>>2];k=D[(h+16|0)+J(n,12)>>2];r=0;i=0;f=0;if(x>>>0>=3){while(1){l=i<<2;o=l+j|0;D[o>>2]=D[o>>2]+D[k+l>>2];o=l|4;s=o+j|0;D[s>>2]=D[s>>2]+D[k+o>>2];o=l|8;s=o+j|0;D[s>>2]=D[s>>2]+D[k+o>>2];l=l|12;o=l+j|0;D[o>>2]=D[o>>2]+D[k+l>>2];i=i+4|0;f=f+4|0;if((H|0)!=(f|0)){continue}break}}if(!C){break w}while(1){f=i<<2;l=f+j|0;D[l>>2]=D[l>>2]+D[f+k>>2];i=i+1|0;r=r+1|0;if((C|0)!=(r|0)){continue}break}}n=n+1|0;if((n|0)!=(g|0)){continue}break}n=J(e,m);f=n;if(!d){break o}if(!e){break l}f=D[h>>2];i=0;g=0;if(x){break n}break m}f=J(e,m)}if(D[a+8>>2]<=0){break k}n=(J(m-1|0,e)<<2)+c|0;j=D[a+32>>2];i=0;while(1){d=i<<2;g=D[d+n>>2];k=D[a+16>>2];x:{if((g|0)>(k|0)){D[d+j>>2]=k;break x}d=d+j|0;k=D[a+12>>2];if((k|0)>(g|0)){D[d>>2]=k;break x}D[d>>2]=g}i=i+1|0;g=D[a+8>>2];if((i|0)<(g|0)){continue}break}d=0;if((g|0)<=0){break k}f=f<<2;i=f+c|0;n=b+f|0;while(1){g=d<<2;f=g+i|0;g=D[g+n>>2]+D[g+j>>2]|0;D[f>>2]=g;y:{if((g|0)>D[a+16>>2]){g=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break y}g=g+D[a+20>>2]|0}D[f>>2]=g}d=d+1|0;if((d|0)<D[a+8>>2]){continue}break}break k}ua();T()}while(1){j=i<<2;k=j+f|0;D[k>>2]=D[k>>2]/(d|0);j=f+(j|4)|0;D[j>>2]=D[j>>2]/(d|0);i=i+2|0;g=g+2|0;if((A|0)!=(g|0)){continue}break}}if(!B){break l}f=f+(i<<2)|0;D[f>>2]=D[f>>2]/(d|0)}if(D[a+8>>2]<=0){break k}j=D[a+32>>2];i=0;while(1){d=i<<2;f=D[d+v>>2];g=D[a+16>>2];z:{if((f|0)>(g|0)){D[d+j>>2]=g;break z}d=d+j|0;g=D[a+12>>2];if((g|0)>(f|0)){D[d>>2]=g;break z}D[d>>2]=f}i=i+1|0;f=D[a+8>>2];if((i|0)<(f|0)){continue}break}d=0;if((f|0)<=0){break k}f=n<<2;i=f+c|0;n=b+f|0;while(1){g=d<<2;f=g+i|0;g=D[g+n>>2]+D[g+j>>2]|0;D[f>>2]=g;A:{if((g|0)>D[a+16>>2]){g=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break A}g=g+D[a+20>>2]|0}D[f>>2]=g}d=d+1|0;if((d|0)<D[a+8>>2]){continue}break}}p=1;m=m+1|0;if((F|0)!=(m|0)){continue}break}}a=D[h>>2];if(a){ma(a)}ma(w);a=D[h+52>>2];if(a){D[h+56>>2]=a;ma(a)}a=D[h+40>>2];if(a){D[h+44>>2]=a;ma(a)}a=D[h+28>>2];if(a){D[h+32>>2]=a;ma(a)}a=D[h+16>>2];if(a){D[h+20>>2]=a;ma(a)}$=h- -64|0;return p|0}qa();T()}function mb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0;a:{b:{if(!d){break b}c:{d:{switch(D[a+28>>2]-1|0){case 0:i=1;e:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break e}e=D[a>>2];h=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=b;b=h+b|0;g=D[e+4>>2];e=g-h|0;if(!E[a+32|0]){if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=B[b|0];f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break e}b=b+1|0;if(g>>>0>b>>>0){continue}break}break a}if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=K(B[b|0])/K(127);f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break e}b=b+1|0;if(g>>>0>b>>>0){continue}break}break a}if((c|0)>(e|0)){break c}break b;case 1:i=1;f:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break f}e=D[a>>2];h=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=b;b=h+b|0;g=D[e+4>>2];e=g-h|0;if(!E[a+32|0]){if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=E[b|0];f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break f}b=b+1|0;if(g>>>0>b>>>0){continue}break}break a}if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=K(E[b|0])/K(255);f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break f}b=b+1|0;if(g>>>0>b>>>0){continue}break}break a}if((c|0)>(e|0)){break c}break b;case 2:i=1;g:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break g}e=D[a>>2];h=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=b;b=h+b|0;g=D[e+4>>2];e=g-h|0;if(!E[a+32|0]){if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=C[b>>1];f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break g}b=b+2|0;if(g>>>0>b>>>0){continue}break}break a}if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=K(C[b>>1])/K(32767);f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break g}b=b+2|0;if(g>>>0>b>>>0){continue}break}break a}if((c|0)>(e|0)){break c}break b;case 3:i=1;h:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break h}e=D[a>>2];h=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=b;b=h+b|0;g=D[e+4>>2];e=g-h|0;if(!E[a+32|0]){if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=F[b>>1];f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break h}b=b+2|0;if(g>>>0>b>>>0){continue}break}break a}if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=K(F[b>>1])/K(65535);f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break h}b=b+2|0;if(g>>>0>b>>>0){continue}break}break a}if((c|0)>(e|0)){break c}break b;case 4:i=1;i:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break i}e=D[a>>2];h=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=b;b=h+b|0;g=D[e+4>>2];e=g-h|0;if(!E[a+32|0]){if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=D[b>>2];f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break i}b=b+4|0;if(g>>>0>b>>>0){continue}break}break a}if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=K(D[b>>2])*K(4.656612873077393e-10);f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break i}b=b+4|0;if(g>>>0>b>>>0){continue}break}break a}if((c|0)>(e|0)){break c}break b;case 5:i=1;j:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break j}e=D[a>>2];h=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=b;b=h+b|0;g=D[e+4>>2];e=g-h|0;if(!E[a+32|0]){if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=G[b>>2];f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break j}b=b+4|0;if(g>>>0>b>>>0){continue}break}break a}if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=K(G[b>>2])*K(2.3283064365386963e-10);f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break j}b=b+4|0;if(g>>>0>b>>>0){continue}break}break a}if((c|0)>(e|0)){break c}break b;case 6:i=1;k:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break k}e=D[a>>2];h=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=b;b=h+b|0;g=D[e+4>>2];e=g-h|0;if(!E[a+32|0]){if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=+G[b>>2]+ +D[b+4>>2]*4294967296;f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break k}b=b+8|0;if(g>>>0>b>>>0){continue}break}break a}if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=K(+G[b>>2]+ +D[b+4>>2]*4294967296)*K(1.0842021724855044e-19);f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break k}b=b+8|0;if(g>>>0>b>>>0){continue}break}break a}if((c|0)>(e|0)){break c}break b;case 7:i=1;l:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break l}e=D[a>>2];h=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=b;b=h+b|0;g=D[e+4>>2];e=g-h|0;if(!E[a+32|0]){if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=+G[b>>2]+ +G[b+4>>2]*4294967296;f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break l}b=b+8|0;if(g>>>0>b>>>0){continue}break}break a}if((e|0)<=(f|0)){break a}f=0;while(1){H[(f<<2)+d>>2]=K(+G[b>>2]+ +G[b+4>>2]*4294967296)*K(5.421010862427522e-20);f=f+1|0;e=B[a+24|0];if((f|0)>=(((c|0)<(e|0)?c:e)|0)){break l}b=b+8|0;if(g>>>0>b>>>0){continue}break}break a}if((c|0)>(e|0)){break c}break b;case 8:i=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;g=D[e+4>>2];while(1){if(b>>>0>=g>>>0){break a}H[(f<<2)+d>>2]=H[b>>2];b=b+4|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 9:i=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;g=D[e+4>>2];while(1){if(b>>>0>=g>>>0){break a}H[(f<<2)+d>>2]=I[b>>3];b=b+8|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 10:break d;default:break b}}i=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){g=D[a>>2];e=D[g>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=e+b|0;while(1){if(G[g+4>>2]<=b>>>0){break a}H[(f<<2)+d>>2]=E[b|0]?K(1):K(0);b=b+1|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)<=(e|0)){break b}}pa((e<<2)+d|0,0,c-e<<2)}return i}return 0}function Vh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,E=0,F=0,G=0,H=0,I=0;h=$+-64|0;$=h;D[a+8>>2]=e;f=a+32|0;g=D[f>>2];d=D[a+36>>2]-g>>2;a:{b:{if(d>>>0<e>>>0){sa(f,e-d|0);D[h+56>>2]=0;D[h+60>>2]=0;D[h+48>>2]=0;D[h+52>>2]=0;D[h+40>>2]=0;D[h+44>>2]=0;D[h+32>>2]=0;D[h+36>>2]=0;D[h+24>>2]=0;D[h+28>>2]=0;D[h+16>>2]=0;D[h+20>>2]=0;D[h>>2]=0;break b}if(d>>>0>e>>>0){D[a+36>>2]=g+(e<<2)}D[h+56>>2]=0;D[h+60>>2]=0;D[h+48>>2]=0;D[h+52>>2]=0;D[h+40>>2]=0;D[h+44>>2]=0;D[h+32>>2]=0;D[h+36>>2]=0;D[h+24>>2]=0;D[h+28>>2]=0;D[h+16>>2]=0;D[h+20>>2]=0;D[h>>2]=0;d=0;if(!e){break a}}xa(h+16|0,e,h);i=D[h+28>>2];d=D[h+32>>2]}D[h>>2]=0;d=d-i>>2;c:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break c}D[h+32>>2]=(e<<2)+i;break c}xa(h+16|12,e-d|0,h)}D[h>>2]=0;f=D[h+40>>2];d=D[h+44>>2]-f>>2;d:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break d}D[h+44>>2]=f+(e<<2);break d}xa(h+40|0,e-d|0,h)}D[h>>2]=0;f=D[h+52>>2];d=D[h+56>>2]-f>>2;e:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break e}D[h+56>>2]=f+(e<<2);break e}xa(h+52|0,e-d|0,h)}i=0;f:{if(D[a+8>>2]<=0){break f}k=D[a+32>>2];g=D[h+16>>2];while(1){d=i<<2;f=D[d+g>>2];n=D[a+16>>2];g:{if((f|0)>(n|0)){D[d+k>>2]=n;break g}d=d+k|0;n=D[a+12>>2];if((n|0)>(f|0)){D[d>>2]=n;break g}D[d>>2]=f}i=i+1|0;d=D[a+8>>2];if((i|0)<(d|0)){continue}break}if((d|0)<=0){break f}d=0;while(1){g=d<<2;f=g+c|0;g=D[b+g>>2]+D[g+k>>2]|0;D[f>>2]=g;h:{if((g|0)>D[a+16>>2]){j=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break h}j=g+D[a+20>>2]|0}D[f>>2]=j}d=d+1|0;if((d|0)<D[a+8>>2]){continue}break}}E=D[a+52>>2];y=D[a+48>>2];v=na(16);d=v;D[d>>2]=0;D[d+4>>2]=0;D[d+8>>2]=0;D[d+12>>2]=0;D[h+8>>2]=0;D[h>>2]=0;D[h+4>>2]=0;i:{if(e){if(e>>>0>=1073741824){break i}d=e<<2;u=na(d);D[h>>2]=u;D[h+8>>2]=d+u;pa(u,0,d)}r=1;d=D[a+56>>2];z=D[d>>2];d=D[d+4>>2]-z|0;j:{if((d|0)<5){break j}d=d>>2;F=(d|0)>2?d:2;G=d>>>0>1?d:1;A=e&-2;B=e&1;H=e&-4;C=e&3;w=e-1|0;I=e<<2;n=1;while(1){k:{l:{m:{n:{if((n|0)!=(G|0)){f=D[(n<<2)+z>>2];d=(f>>>0)%3|0;o:{p:{if((f|0)==-1){break p}k=0;g=f+2|0;x=(d|0)!=0|(g|0)!=-1;s=D[y+12>>2];q=s+((d?f-1|0:g)<<2)|0;o=1;d=f;q:{while(1){g=D[s+(d<<2)>>2];r:{if((g|0)==-1){break r}l=-1;r=D[E>>2];m=D[y>>2];i=r+(D[m+(g<<2)>>2]<<2)|0;p=g+1|0;p=(p>>>0)%3|0?p:g-2|0;if((p|0)!=-1){l=D[m+(p<<2)>>2]}p=D[i>>2];s:{t:{if((g>>>0)%3|0){i=g-1|0;break t}i=g+2|0;j=-1;if((i|0)==-1){break s}}j=D[m+(i<<2)>>2]}if((n|0)<=(p|0)){break r}i=D[r+(l<<2)>>2];if((i|0)>=(n|0)){break r}l=D[r+(j<<2)>>2];if((l|0)>=(n|0)){break r}g=D[(h+16|0)+J(k,12)>>2];u:{if(!e){break u}l=J(e,l);m=J(e,i);r=J(e,p);i=0;j=0;if(w){while(1){D[g+(i<<2)>>2]=(D[(i+l<<2)+c>>2]+D[(i+m<<2)+c>>2]|0)-D[(i+r<<2)+c>>2];p=i|1;D[g+(p<<2)>>2]=(D[(l+p<<2)+c>>2]+D[(m+p<<2)+c>>2]|0)-D[(p+r<<2)+c>>2];i=i+2|0;j=j+2|0;if((A|0)!=(j|0)){continue}break}}if(!B){break u}D[g+(i<<2)>>2]=(D[(i+l<<2)+c>>2]+D[(i+m<<2)+c>>2]|0)-D[(i+r<<2)+c>>2]}g=4;k=k+1|0;if((k|0)==4){break q}}v:{if(o&1){i=d+1|0;d=(i>>>0)%3|0?i:d-2|0;j=-1;if((d|0)==-1){break v}d=D[s+(d<<2)>>2];j=-1;if((d|0)==-1){break v}g=d+1|0;j=(g>>>0)%3|0?g:d-2|0;break v}w:{if((d>>>0)%3|0){i=d-1|0;break w}i=d+2|0;j=-1;if((i|0)==-1){break v}}d=D[s+(i<<2)>>2];j=-1;if((d|0)==-1){break v}j=d-1|0;if((d>>>0)%3|0){break v}j=d+2|0}d=j;x:{if((f|0)==(d|0)){break x}if(!((o^1)&1|(d|0)!=-1)){if(!x){break x}d=D[q>>2];if((d|0)==-1){break x}o=0;d=(d>>>0)%3|0?d-1|0:d+2|0}if((d|0)!=-1){continue}}break}g=k;if((g|0)<=0){break p}}if(e){pa(D[h>>2],0,I)}d=g-1|0;p=(d<<2)+v|0;d=J(d,12)+a|0;j=d;x=D[d- -64>>2];r=0;o=0;d=0;while(1){f=D[p>>2];D[p>>2]=f+1;if(f>>>0>=x>>>0){break j}y:{if(D[D[j+60>>2]+(f>>>3&536870908)>>2]>>>f&1){break y}d=d+1|0;if(!e){break y}k=D[h>>2];s=D[(h+16|0)+J(o,12)>>2];l=0;i=0;f=0;if(w>>>0>=3){while(1){m=i<<2;q=m+k|0;D[q>>2]=D[q>>2]+D[m+s>>2];q=m|4;t=q+k|0;D[t>>2]=D[t>>2]+D[s+q>>2];q=m|8;t=q+k|0;D[t>>2]=D[t>>2]+D[s+q>>2];m=m|12;q=m+k|0;D[q>>2]=D[q>>2]+D[m+s>>2];i=i+4|0;f=f+4|0;if((H|0)!=(f|0)){continue}break}}if(!C){break y}while(1){f=i<<2;m=f+k|0;D[m>>2]=D[m>>2]+D[f+s>>2];i=i+1|0;l=l+1|0;if((C|0)!=(l|0)){continue}break}}o=o+1|0;if((o|0)!=(g|0)){continue}break}o=J(e,n);f=o;if(!d){break o}if(!e){break l}f=D[h>>2];i=0;g=0;if(w){break n}break m}f=J(e,n)}if(D[a+8>>2]<=0){break k}o=(J(n-1|0,e)<<2)+c|0;k=D[a+32>>2];i=0;while(1){d=i<<2;g=D[d+o>>2];l=D[a+16>>2];z:{if((g|0)>(l|0)){D[d+k>>2]=l;break z}d=d+k|0;l=D[a+12>>2];if((l|0)>(g|0)){D[d>>2]=l;break z}D[d>>2]=g}i=i+1|0;g=D[a+8>>2];if((i|0)<(g|0)){continue}break}d=0;if((g|0)<=0){break k}f=f<<2;i=f+c|0;o=b+f|0;while(1){g=d<<2;f=g+i|0;g=D[g+o>>2]+D[g+k>>2]|0;D[f>>2]=g;A:{if((g|0)>D[a+16>>2]){j=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break A}j=g+D[a+20>>2]|0}D[f>>2]=j}d=d+1|0;if((d|0)<D[a+8>>2]){continue}break}break k}ua();T()}while(1){k=i<<2;l=k+f|0;D[l>>2]=D[l>>2]/(d|0);k=f+(k|4)|0;D[k>>2]=D[k>>2]/(d|0);i=i+2|0;g=g+2|0;if((A|0)!=(g|0)){continue}break}}if(!B){break l}f=f+(i<<2)|0;D[f>>2]=D[f>>2]/(d|0)}if(D[a+8>>2]<=0){break k}k=D[a+32>>2];i=0;while(1){d=i<<2;f=D[d+u>>2];g=D[a+16>>2];B:{if((f|0)>(g|0)){D[d+k>>2]=g;break B}d=d+k|0;g=D[a+12>>2];if((g|0)>(f|0)){D[d>>2]=g;break B}D[d>>2]=f}i=i+1|0;f=D[a+8>>2];if((i|0)<(f|0)){continue}break}d=0;if((f|0)<=0){break k}f=o<<2;i=f+c|0;o=b+f|0;while(1){g=d<<2;f=g+i|0;g=D[g+o>>2]+D[g+k>>2]|0;D[f>>2]=g;C:{if((g|0)>D[a+16>>2]){j=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break C}j=g+D[a+20>>2]|0}D[f>>2]=j}d=d+1|0;if((d|0)<D[a+8>>2]){continue}break}}r=1;n=n+1|0;if((F|0)!=(n|0)){continue}break}}a=D[h>>2];if(a){ma(a)}ma(v);a=D[h+52>>2];if(a){D[h+56>>2]=a;ma(a)}a=D[h+40>>2];if(a){D[h+44>>2]=a;ma(a)}a=D[h+28>>2];if(a){D[h+32>>2]=a;ma(a)}a=D[h+16>>2];if(a){D[h+20>>2]=a;ma(a)}$=h- -64|0;return r|0}qa();T()}function kf(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;m=$-16|0;$=m;D[m+12>>2]=b;b=na(32);D[m>>2]=b;D[m+4>>2]=24;D[m+8>>2]=-2147483616;B[b+24|0]=0;d=E[1308]|E[1309]<<8|(E[1310]<<16|E[1311]<<24);c=E[1304]|E[1305]<<8|(E[1306]<<16|E[1307]<<24);B[b+16|0]=c;B[b+17|0]=c>>>8;B[b+18|0]=c>>>16;B[b+19|0]=c>>>24;B[b+20|0]=d;B[b+21|0]=d>>>8;B[b+22|0]=d>>>16;B[b+23|0]=d>>>24;d=E[1300]|E[1301]<<8|(E[1302]<<16|E[1303]<<24);c=E[1296]|E[1297]<<8|(E[1298]<<16|E[1299]<<24);B[b+8|0]=c;B[b+9|0]=c>>>8;B[b+10|0]=c>>>16;B[b+11|0]=c>>>24;B[b+12|0]=d;B[b+13|0]=d>>>8;B[b+14|0]=d>>>16;B[b+15|0]=d>>>24;d=E[1292]|E[1293]<<8|(E[1294]<<16|E[1295]<<24);c=E[1288]|E[1289]<<8|(E[1290]<<16|E[1291]<<24);B[b|0]=c;B[b+1|0]=c>>>8;B[b+2|0]=c>>>16;B[b+3|0]=c>>>24;B[b+4|0]=d;B[b+5|0]=d>>>8;B[b+6|0]=d>>>16;B[b+7|0]=d>>>24;h=$-48|0;$=h;k=a;d=a+16|0;a=D[d>>2];a:{b:{if(!a){break b}c=D[m+12>>2];b=d;while(1){f=(c|0)>D[a+16>>2];b=f?b:a;a=D[(f<<2)+a>>2];if(a){continue}break}if((b|0)==(d|0)){break b}if((c|0)>=D[b+16>>2]){break a}}t=h+24|0;a=t;D[a+4>>2]=0;D[a+8>>2]=0;u=a+4|0;D[a>>2]=u;a=D[m+12>>2];c=h+16|0;D[c>>2]=0;D[c+4>>2]=0;D[h+8>>2]=a;D[h+12>>2]=c;b=D[t>>2];if((u|0)!=(b|0)){l=h+8|4;while(1){f=b;g=b+16|0;q=$-16|0;$=q;i=q+12|0;a=q+8|0;c:{d:{e:{f:{g:{h:{r=l+4|0;i:{if((r|0)==(c|0)){break i}b=E[c+27|0];e=b<<24>>24<0;n=E[g+11|0];v=n<<24>>24;j=(v|0)<0;b=e?D[c+20>>2]:b;n=j?D[g+4>>2]:n;o=b>>>0<n>>>0;p=o?b:n;if(p){j=j?D[g>>2]:g;s=c+16|0;e=e?D[s>>2]:s;s=va(j,e,p);if(!s){if(b>>>0>n>>>0){break i}break h}if((s|0)>=0){break h}break i}if(b>>>0<=n>>>0){break g}}e=D[c>>2];j:{a=c;k:{if((a|0)==D[l>>2]){break k}l:{if(e){b=e;while(1){a=b;b=D[b+4>>2];if(b){continue}break}break l}a=c+8|0;if((c|0)==D[D[c+8>>2]>>2]){while(1){b=D[a>>2];a=b+8|0;if((b|0)==D[D[b+8>>2]>>2]){continue}break}}a=D[a>>2]}j=E[g+11|0];b=j<<24>>24<0;o=E[a+27|0];n=o<<24>>24<0;m:{j=b?D[g+4>>2]:j;o=n?D[a+20>>2]:o;p=j>>>0<o>>>0?j:o;if(p){r=a+16|0;b=va(n?D[r>>2]:r,b?D[g>>2]:g,p);if(b){break m}}if(j>>>0>o>>>0){break k}break j}if((b|0)>=0){break j}}if(!e){D[i>>2]=c;a=c;break c}D[i>>2]=a;a=a+4|0;break c}a=Id(l,i,g);break c}b=va(e,j,p);if(b){break f}}if(o){break e}break d}if((b|0)>=0){break d}}e=D[c+4>>2];n:{if(e){b=e;while(1){a=b;b=D[b>>2];if(b){continue}break}break n}a=D[c+8>>2];if((c|0)==D[a>>2]){break n}b=c+8|0;while(1){j=D[b>>2];b=j+8|0;a=D[j+8>>2];if((j|0)!=D[a>>2]){continue}break}}o:{p:{if((a|0)==(r|0)){break p}j=E[a+27|0];b=j<<24>>24<0;q:{j=b?D[a+20>>2]:j;o=j>>>0<n>>>0?j:n;if(o){p=a+16|0;b=va((v|0)<0?D[g>>2]:g,b?D[p>>2]:p,o);if(b){break q}}if(j>>>0>n>>>0){break p}break o}if((b|0)>=0){break o}}if(!e){D[i>>2]=c;a=c+4|0;break c}D[i>>2]=a;break c}a=Id(l,i,g);break c}D[i>>2]=c;D[a>>2]=c}b=a;a=D[b>>2];if(a){b=0}else{a=na(40);e=a+16|0;r:{if(B[g+11|0]>=0){i=D[g+4>>2];D[e>>2]=D[g>>2];D[e+4>>2]=i;D[e+8>>2]=D[g+8>>2];break r}ta(e,D[g>>2],D[g+4>>2])}e=a+28|0;s:{if(B[g+23|0]>=0){i=D[g+16>>2];D[e>>2]=D[g+12>>2];D[e+4>>2]=i;D[e+8>>2]=D[g+20>>2];break s}ta(e,D[g+12>>2],D[g+16>>2])}D[a+8>>2]=D[q+12>>2];D[a>>2]=0;D[a+4>>2]=0;D[b>>2]=a;e=D[D[l>>2]>>2];if(e){D[l>>2]=e;b=D[b>>2]}else{b=a}pb(D[l+4>>2],b);D[l+8>>2]=D[l+8>>2]+1;b=1}B[h+44|0]=b;D[h+40>>2]=a;$=q+16|0;a=D[f+4>>2];t:{if(!a){b=D[f+8>>2];if((f|0)==D[b>>2]){break t}a=f+8|0;while(1){f=D[a>>2];a=f+8|0;b=D[f+8>>2];if((f|0)!=D[b>>2]){continue}break}break t}while(1){b=a;a=D[b>>2];if(a){continue}break}}if((b|0)!=(u|0)){continue}break}}a=D[d>>2];u:{if(a){d=k+16|0;f=D[h+8>>2];while(1){b=D[a+16>>2];v:{if((b|0)>(f|0)){b=D[a>>2];if(b){break v}d=a;break u}if((b|0)>=(f|0)){break u}d=a+4|0;b=D[a+4>>2];if(!b){break u}a=d}d=a;a=b;continue}}a=d}b=D[d>>2];if(!b){b=na(32);D[b+16>>2]=D[h+8>>2];D[b+20>>2]=D[h+12>>2];f=b+24|0;e=D[h+16>>2];D[f>>2]=e;g=D[h+20>>2];D[b+28>>2]=g;w:{if(!g){D[b+20>>2]=f;break w}D[e+8>>2]=f;D[h+16>>2]=0;D[h+20>>2]=0;D[h+12>>2]=c}D[b+8>>2]=a;D[b>>2]=0;D[b+4>>2]=0;D[d>>2]=b;a=D[D[k+12>>2]>>2];if(a){D[k+12>>2]=a;a=D[d>>2]}else{a=b}pb(D[k+16>>2],a);D[k+20>>2]=D[k+20>>2]+1}hb(h+8|4,D[h+16>>2]);hb(t,D[t+4>>2])}$=h+48|0;k=$-48|0;$=k;c=$-32|0;$=c;g=c+32|0;d=c+21|0;a=d;f=g-a|0;x:{if(!((f|0)<=9&(f|0)<(1-(G[2744]>1)|0))){B[a|0]=49;D[c+8>>2]=a+1;a=0;break x}D[c+8>>2]=g;a=61}D[c+12>>2]=a;h=$-16|0;$=h;a=k+8|0;e=$-16|0;$=e;y:{l=D[c+8>>2];f=l-d|0;if(f>>>0<=4294967279){z:{if(f>>>0<=10){B[a+11|0]=f;c=a;break z}if(f>>>0>=11){i=f+16&-16;c=i-1|0;c=(c|0)==11?i:c}else{c=10}i=c+1|0;c=na(i);D[a>>2]=c;D[a+8>>2]=i|-2147483648;D[a+4>>2]=f}while(1){if((d|0)!=(l|0)){B[c|0]=E[d|0];c=c+1|0;d=d+1|0;continue}break}B[e+15|0]=0;B[c|0]=E[e+15|0];$=e+16|0;break y}Aa();T()}$=h+16|0;$=g;D[k+32>>2]=m;c=b+20|0;a=D[c+4>>2];A:{B:{if(a){b=E[m+11|0];d=b<<24>>24<0;e=d?D[m>>2]:m;f=d?D[m+4>>2]:b;b=c+4|0;while(1){d=E[a+27|0];g=d<<24>>24<0;d=g?D[a+20>>2]:d;h=d>>>0<f>>>0;C:{D:{E:{F:{G:{l=h?d:f;H:{if(l){i=a+16|0;g=g?D[i>>2]:i;i=va(e,g,l);if(!i){if(d>>>0>f>>>0){break H}break G}if((i|0)>=0){break G}break H}if(d>>>0<=f>>>0){break F}}d=D[a>>2];if(d){break C}break B}d=va(g,e,l);if(d){break E}}if(h){break D}break A}if((d|0)>=0){break A}}b=a+4|0;d=D[a+4>>2];if(!d){break A}a=b}b=a;a=d;continue}}a=c+4|0}b=a}d=D[b>>2];if(d){a=0}else{d=na(40);e=d+16|0;f=D[k+32>>2];I:{if(B[f+11|0]>=0){g=D[f+4>>2];D[e>>2]=D[f>>2];D[e+4>>2]=g;D[e+8>>2]=D[f+8>>2];break I}ta(e,D[f>>2],D[f+4>>2])}D[d+8>>2]=a;D[d>>2]=0;D[d+4>>2]=0;D[d+36>>2]=0;D[d+28>>2]=0;D[d+32>>2]=0;D[b>>2]=d;a=D[D[c>>2]>>2];if(a){D[c>>2]=a;a=D[b>>2]}else{a=d}pb(D[c+4>>2],a);D[c+8>>2]=D[c+8>>2]+1;a=1}B[k+44|0]=a;D[k+40>>2]=d;a=D[k+40>>2];if(B[a+39|0]<0){ma(D[a+28>>2])}b=D[k+12>>2];D[a+28>>2]=D[k+8>>2];D[a+32>>2]=b;D[a+36>>2]=D[k+16>>2];$=k+48|0;if(B[m+11|0]<0){ma(D[m>>2])}$=m+16|0}function ge(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;i=a;a:{b:{c:{d:{e:{f:{g:{h:{k=D[a+8>>2];switch(D[k+28>>2]-1|0){case 4:break c;case 5:break d;case 2:break e;case 3:break f;case 0:break g;case 1:break h;default:break a}}c=B[k+24|0];d=na((c|0)>=0?c:-1);a=D[i+16>>2];if(D[a+80>>2]){h=D[D[a>>2]>>2]+D[a+48>>2]|0}else{h=0}if(!b){break b}if((c|0)>0){k=c&-4;m=c&3;e=c-1>>>0<3;while(1){a=0;g=0;if(!e){while(1){j=h+(f<<2)|0;B[a+d|0]=D[j>>2];B[(a|1)+d|0]=D[j+4>>2];B[(a|2)+d|0]=D[j+8>>2];B[(a|3)+d|0]=D[j+12>>2];a=a+4|0;f=f+4|0;g=g+4|0;if((k|0)!=(g|0)){continue}break}}g=0;if(m){while(1){B[a+d|0]=D[h+(f<<2)>>2];a=a+1|0;f=f+1|0;g=g+1|0;if((m|0)!=(g|0)){continue}break}}oa(D[D[D[i+8>>2]+64>>2]>>2]+n|0,d,c);n=c+n|0;l=l+1|0;if((l|0)!=(b|0)){continue}break}break b}oa(D[D[k+64>>2]>>2],d,c);if((b|0)==1){break b}e=b-1|0;h=e&1;a=0;if((b|0)!=2){b=e&-2;while(1){a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);f=f+2|0;if((b|0)!=(f|0)){continue}break}}if(!h){break b}oa(D[D[D[i+8>>2]+64>>2]>>2]+(a+c|0)|0,d,c);break b}c=B[k+24|0];d=na((c|0)>=0?c:-1);a=D[i+16>>2];if(D[a+80>>2]){h=D[D[a>>2]>>2]+D[a+48>>2]|0}else{h=0}if(!b){break b}if((c|0)>0){k=c&-4;m=c&3;e=c-1>>>0<3;while(1){a=0;g=0;if(!e){while(1){j=h+(f<<2)|0;B[a+d|0]=D[j>>2];B[(a|1)+d|0]=D[j+4>>2];B[(a|2)+d|0]=D[j+8>>2];B[(a|3)+d|0]=D[j+12>>2];a=a+4|0;f=f+4|0;g=g+4|0;if((k|0)!=(g|0)){continue}break}}g=0;if(m){while(1){B[a+d|0]=D[h+(f<<2)>>2];a=a+1|0;f=f+1|0;g=g+1|0;if((m|0)!=(g|0)){continue}break}}oa(D[D[D[i+8>>2]+64>>2]>>2]+n|0,d,c);n=c+n|0;l=l+1|0;if((l|0)!=(b|0)){continue}break}break b}oa(D[D[k+64>>2]>>2],d,c);if((b|0)==1){break b}e=b-1|0;h=e&1;a=0;if((b|0)!=2){b=e&-2;while(1){a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);f=f+2|0;if((b|0)!=(f|0)){continue}break}}if(!h){break b}oa(D[D[D[i+8>>2]+64>>2]>>2]+(a+c|0)|0,d,c);break b}e=B[k+24|0];a=e+e|0;d=na(a>>>0<e>>>0?-1:a);a=D[i+16>>2];if(D[a+80>>2]){h=D[D[a>>2]>>2]+D[a+48>>2]|0}else{h=0}if(!b){break b}c=e<<1;if((e|0)>0){k=e&-4;m=e&3;e=e-1>>>0<3;while(1){a=0;g=0;if(!e){while(1){l=a<<1;j=h+(f<<2)|0;C[l+d>>1]=D[j>>2];C[(l|2)+d>>1]=D[j+4>>2];C[(l|4)+d>>1]=D[j+8>>2];C[(l|6)+d>>1]=D[j+12>>2];a=a+4|0;f=f+4|0;g=g+4|0;if((k|0)!=(g|0)){continue}break}}g=0;if(m){while(1){C[(a<<1)+d>>1]=D[h+(f<<2)>>2];a=a+1|0;f=f+1|0;g=g+1|0;if((m|0)!=(g|0)){continue}break}}oa(D[D[D[i+8>>2]+64>>2]>>2]+o|0,d,c);o=c+o|0;n=n+1|0;if((n|0)!=(b|0)){continue}break}break b}oa(D[D[k+64>>2]>>2],d,c);if((b|0)==1){break b}e=b-1|0;h=e&1;a=0;if((b|0)!=2){b=e&-2;while(1){a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);f=f+2|0;if((b|0)!=(f|0)){continue}break}}if(!h){break b}oa(D[D[D[i+8>>2]+64>>2]>>2]+(a+c|0)|0,d,c);break b}e=B[k+24|0];a=e+e|0;d=na(a>>>0<e>>>0?-1:a);a=D[i+16>>2];if(D[a+80>>2]){h=D[D[a>>2]>>2]+D[a+48>>2]|0}else{h=0}if(!b){break b}c=e<<1;if((e|0)>0){k=e&-4;m=e&3;e=e-1>>>0<3;while(1){a=0;g=0;if(!e){while(1){l=a<<1;j=h+(f<<2)|0;C[l+d>>1]=D[j>>2];C[(l|2)+d>>1]=D[j+4>>2];C[(l|4)+d>>1]=D[j+8>>2];C[(l|6)+d>>1]=D[j+12>>2];a=a+4|0;f=f+4|0;g=g+4|0;if((k|0)!=(g|0)){continue}break}}g=0;if(m){while(1){C[(a<<1)+d>>1]=D[h+(f<<2)>>2];a=a+1|0;f=f+1|0;g=g+1|0;if((m|0)!=(g|0)){continue}break}}oa(D[D[D[i+8>>2]+64>>2]>>2]+o|0,d,c);o=c+o|0;n=n+1|0;if((n|0)!=(b|0)){continue}break}break b}oa(D[D[k+64>>2]>>2],d,c);if((b|0)==1){break b}e=b-1|0;h=e&1;a=0;if((b|0)!=2){b=e&-2;while(1){a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);f=f+2|0;if((b|0)!=(f|0)){continue}break}}if(!h){break b}oa(D[D[D[i+8>>2]+64>>2]>>2]+(a+c|0)|0,d,c);break b}e=B[k+24|0];c=e<<2;d=na((e|0)!=(e&1073741823)?-1:c);a=D[i+16>>2];if(D[a+80>>2]){h=D[D[a>>2]>>2]+D[a+48>>2]|0}else{h=0}if(!b){break b}if((e|0)>0){k=e&-4;m=e&3;e=e-1>>>0<3;while(1){a=0;g=0;if(!e){while(1){l=a<<2;j=h+(f<<2)|0;D[l+d>>2]=D[j>>2];D[(l|4)+d>>2]=D[j+4>>2];D[(l|8)+d>>2]=D[j+8>>2];D[(l|12)+d>>2]=D[j+12>>2];a=a+4|0;f=f+4|0;g=g+4|0;if((k|0)!=(g|0)){continue}break}}g=0;if(m){while(1){D[(a<<2)+d>>2]=D[h+(f<<2)>>2];a=a+1|0;f=f+1|0;g=g+1|0;if((m|0)!=(g|0)){continue}break}}oa(D[D[D[i+8>>2]+64>>2]>>2]+o|0,d,c);o=c+o|0;n=n+1|0;if((n|0)!=(b|0)){continue}break}break b}oa(D[D[k+64>>2]>>2],d,c);if((b|0)==1){break b}e=b-1|0;h=e&1;a=0;if((b|0)!=2){b=e&-2;while(1){a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);f=f+2|0;if((b|0)!=(f|0)){continue}break}}if(!h){break b}oa(D[D[D[i+8>>2]+64>>2]>>2]+(a+c|0)|0,d,c);break b}e=B[k+24|0];c=e<<2;d=na((e|0)!=(e&1073741823)?-1:c);a=D[i+16>>2];if(D[a+80>>2]){h=D[D[a>>2]>>2]+D[a+48>>2]|0}else{h=0}if(!b){break b}if((e|0)>0){k=e&-4;m=e&3;e=e-1>>>0<3;while(1){a=0;g=0;if(!e){while(1){l=a<<2;j=h+(f<<2)|0;D[l+d>>2]=D[j>>2];D[(l|4)+d>>2]=D[j+4>>2];D[(l|8)+d>>2]=D[j+8>>2];D[(l|12)+d>>2]=D[j+12>>2];a=a+4|0;f=f+4|0;g=g+4|0;if((k|0)!=(g|0)){continue}break}}g=0;if(m){while(1){D[(a<<2)+d>>2]=D[h+(f<<2)>>2];a=a+1|0;f=f+1|0;g=g+1|0;if((m|0)!=(g|0)){continue}break}}oa(D[D[D[i+8>>2]+64>>2]>>2]+o|0,d,c);o=c+o|0;n=n+1|0;if((n|0)!=(b|0)){continue}break}break b}oa(D[D[k+64>>2]>>2],d,c);if((b|0)==1){break b}e=b-1|0;h=e&1;a=0;if((b|0)!=2){b=e&-2;while(1){a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);a=a+c|0;oa(a+D[D[D[i+8>>2]+64>>2]>>2]|0,d,c);f=f+2|0;if((b|0)!=(f|0)){continue}break}}if(!h){break b}oa(D[D[D[i+8>>2]+64>>2]>>2]+(a+c|0)|0,d,c)}ma(d);d=1}return d|0}function Bd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;h=$-96|0;$=h;r=D[a+4>>2];d=D[r+32>>2];e=D[d+8>>2];n=D[d+12>>2];f=n;c=D[d+20>>2];p=D[d+16>>2];a:{if((f|0)<=(c|0)&p>>>0>=e>>>0|(c|0)>(f|0)){break a}k=D[d>>2];q=E[k+p|0];g=p+1|0;f=g?c:c+1|0;D[d+16>>2]=g;D[d+20>>2]=f;if(e>>>0<=g>>>0&(f|0)>=(n|0)|(f|0)>(n|0)){break a}l=E[g+k|0];g=p+2|0;f=g>>>0<2?c+1|0:c;m=g;D[d+16>>2]=g;D[d+20>>2]=f;i=q<<24>>24;b:{if((i|0)>=0){g=D[a+216>>2];if(q>>>0>=(D[a+220>>2]-g|0)/144>>>0){break a}o=g+J(q,144)|0;if(D[o>>2]<0){break b}break a}if(D[a+212>>2]>=0){break a}o=a+212|0}D[o>>2]=b;c:{d:{g=F[r+36>>1];e:{if(((g<<8|g>>>8)&65535)>>>0>=258){if(e>>>0<=m>>>0&(f|0)>=(n|0)|(f|0)>(n|0)){break a}g=E[k+m|0];f=p+3|0;c=f>>>0<3?c+1|0:c;D[d+16>>2]=f;D[d+20>>2]=c;c=g>>>0>1;if(c){break a}c=c?0:g;if(!l){break e}if(c){break a}break d}if(l){break d}c=0}if((i|0)<0){f=a+184|0}else{d=D[a+216>>2]+J(q,144)|0;B[d+100|0]=0;f=d+104|0}d=f;f:{if((c|0)==1){e=$-112|0;$=e;m=D[D[a+4>>2]+44>>2];c=na(120);D[c>>2]=8956;D[c+4>>2]=0;D[c+116>>2]=0;D[c+112>>2]=d;D[c+108>>2]=m;D[c+12>>2]=0;D[c+16>>2]=0;D[c+20>>2]=0;D[c+24>>2]=0;D[c+28>>2]=0;D[c+32>>2]=0;D[c+36>>2]=0;D[c+40>>2]=0;D[c+44>>2]=0;D[c+48>>2]=0;D[c+52>>2]=0;D[c+56>>2]=0;D[c+60>>2]=0;D[c+8>>2]=9168;f=c- -64|0;D[f>>2]=0;D[f+4>>2]=0;D[c+72>>2]=0;D[c+76>>2]=0;D[c+80>>2]=0;D[c+84>>2]=0;D[c+88>>2]=0;D[c+104>>2]=0;D[c+96>>2]=0;D[c+100>>2]=0;l=D[a+8>>2];D[e+48>>2]=0;D[e+52>>2]=0;D[e+40>>2]=0;D[e+44>>2]=0;i=e+32|0;f=i;D[f>>2]=0;D[f+4>>2]=0;D[e+24>>2]=0;D[e+28>>2]=0;D[e+16>>2]=0;D[e+20>>2]=0;f=e- -64|0;D[f>>2]=0;D[f+4>>2]=0;D[e+72>>2]=0;D[e+76>>2]=0;D[e+80>>2]=0;D[e+84>>2]=0;D[e+88>>2]=0;D[e+104>>2]=0;D[e+56>>2]=0;D[e+60>>2]=0;D[e+8>>2]=9168;D[e+96>>2]=0;D[e+100>>2]=0;D[e+12>>2]=l;g=D[l>>2];f=D[l+4>>2];B[e+111|0]=0;n=i;i=e+111|0;Ea(n,(f-g>>2>>>0)/3|0,i);f=D[e+12>>2];g=D[f+28>>2];f=D[f+24>>2];B[e+111|0]=0;Ea(e+44|0,g-f>>2,i);D[e+28>>2]=c;D[e+24>>2]=m;D[e+20>>2]=d;D[e+16>>2]=l;d=c+8|0;f=e+8|0;hc(d,f);g:{if((d|0)==(f|0)){D[c+92>>2]=D[f+84>>2];break g}fb(c+56|0,D[f+48>>2],D[f+52>>2]);fb(c+68|0,D[f+60>>2],D[f- -64>>2]);fb(c+80|0,D[f+72>>2],D[f+76>>2]);D[c+92>>2]=D[f+84>>2];h:{i=D[f+92>>2];l=D[f+88>>2];g=i-l|0;m=g>>2;o=D[c+104>>2];k=D[c+96>>2];if(m>>>0<=o-k>>2>>>0){d=D[c+100>>2]-k|0;f=d>>2;g=f>>>0<m>>>0?d+l|0:i;d=g-l|0;if(d){Na(k,l,d)}if(f>>>0<m>>>0){d=D[c+100>>2];f=i-g|0;if((f|0)>0){d=oa(d,g,f)+f|0}D[c+100>>2]=d;break h}D[c+100>>2]=d+k;break h}if(k){D[c+100>>2]=k;ma(k);D[c+104>>2]=0;D[c+96>>2]=0;D[c+100>>2]=0;o=0}i:{if((g|0)<0){break i}d=o>>1;d=o>>2>>>0<536870911?d>>>0<m>>>0?m:d:1073741823;if(d>>>0>=1073741824){break i}f=d<<2;d=na(f);D[c+96>>2]=d;D[c+100>>2]=d;D[c+104>>2]=d+f;if(g){d=oa(d,l,g)+g|0}D[c+100>>2]=d;break h}qa();T()}}D[e+8>>2]=9168;d=D[e+96>>2];if(d){D[e+100>>2]=d;ma(d)}d=D[e+80>>2];if(d){D[e+84>>2]=d;ma(d)}d=D[e+68>>2];if(d){D[e+72>>2]=d;ma(d)}d=D[e+56>>2];if(d){D[e+60>>2]=d;ma(d)}D[e+8>>2]=9404;d=D[e+44>>2];if(d){ma(d)}d=D[e+32>>2];if(d){ma(d)}$=e+112|0;break f}e=$+-64|0;$=e;l=D[D[a+4>>2]+44>>2];c=na(80);D[c>>2]=9424;D[c+4>>2]=0;D[c+76>>2]=0;D[c+72>>2]=d;D[c+68>>2]=l;D[c+8>>2]=9588;D[c+12>>2]=0;D[c+16>>2]=0;D[c+20>>2]=0;D[c+24>>2]=0;D[c+28>>2]=0;D[c+32>>2]=0;D[c+36>>2]=0;D[c+40>>2]=0;D[c+44>>2]=0;D[c+48>>2]=0;D[c+52>>2]=0;D[c- -64>>2]=0;m=c+56|0;f=m;D[f>>2]=0;D[f+4>>2]=0;k=D[a+8>>2];D[e+40>>2]=0;D[e+44>>2]=0;D[e+32>>2]=0;D[e+36>>2]=0;i=e+24|0;f=i;D[f>>2]=0;D[f+4>>2]=0;D[e+16>>2]=0;D[e+20>>2]=0;D[e+8>>2]=0;D[e+12>>2]=0;D[e+56>>2]=0;D[e+48>>2]=0;D[e+52>>2]=0;D[e>>2]=9588;D[e+4>>2]=k;g=D[k>>2];f=D[k+4>>2];B[e+63|0]=0;n=i;i=e+63|0;Ea(n,(f-g>>2>>>0)/3|0,i);f=D[e+4>>2];g=D[f+28>>2];f=D[f+24>>2];B[e+63|0]=0;Ea(e+36|0,g-f>>2,i);D[e+20>>2]=c;D[e+16>>2]=l;D[e+12>>2]=d;D[e+8>>2]=k;hc(c+8|0,e);fb(m,D[e+48>>2],D[e+52>>2]);D[e>>2]=9588;d=D[e+48>>2];if(d){D[e+52>>2]=d;ma(d)}D[e>>2]=9404;d=D[e+36>>2];if(d){ma(d)}d=D[e+24>>2];if(d){ma(d)}$=e- -64|0}if(!c){break a}break c}if((i|0)<0){break a}g=D[r+44>>2];d=D[a+216>>2];c=na(80);D[c+76>>2]=0;D[c+68>>2]=g;D[c+8>>2]=8652;D[c>>2]=9716;D[c+4>>2]=0;f=d+J(q,144)|0;j=f+104|0;D[c+72>>2]=j;D[c- -64>>2]=0;D[c+56>>2]=0;D[c+60>>2]=0;D[c+52>>2]=0;D[c+44>>2]=0;D[c+48>>2]=0;D[c+36>>2]=0;D[c+40>>2]=0;D[c+28>>2]=0;D[c+32>>2]=0;D[c+20>>2]=0;D[c+24>>2]=0;D[c+12>>2]=0;D[c+16>>2]=0;D[h+24>>2]=g;D[h+68>>2]=0;D[h+72>>2]=0;D[h+60>>2]=0;D[h+64>>2]=0;D[h+52>>2]=0;D[h+56>>2]=0;D[h+44>>2]=0;D[h+48>>2]=0;D[h+84>>2]=0;D[h+88>>2]=0;D[h+76>>2]=0;D[h+80>>2]=0;D[h+28>>2]=c;d=D[h+28>>2];D[h+8>>2]=D[h+24>>2];D[h+12>>2]=d;f=f+4|0;D[h+16>>2]=f;D[h+20>>2]=j;D[h+36>>2]=0;D[h+40>>2]=0;D[h+32>>2]=8652;d=D[h+20>>2];D[h>>2]=D[h+16>>2];D[h+4>>2]=d;j=h+32|0;Ad(j,f,h);d=c+8|0;hc(d,j);if((d|0)!=(j|0)){fb(c+56|0,D[j+48>>2],D[j+52>>2])}zd(j)}c=wc(na(64),c);i=D[a+4>>2];a=c;c=b;j:{k:{if((c|0)>=0){f=i+8|0;b=D[i+12>>2];d=D[i+8>>2];g=b-d>>2;l:{if((g|0)>(c|0)){break l}j=c+1|0;if(c>>>0>=g>>>0){Ob(f,j-g|0);break l}if(g>>>0<=j>>>0){break l}d=d+(j<<2)|0;if((d|0)!=(b|0)){while(1){b=b-4|0;j=D[b>>2];D[b>>2]=0;if(j){ba[D[D[j>>2]+4>>2]](j)}if((b|0)!=(d|0)){continue}break}}D[i+12>>2]=d}d=D[f>>2]+(c<<2)|0;b=D[d>>2];D[d>>2]=a;if(b){break k}break j}b=a;if(!a){break j}}ba[D[D[b>>2]+4>>2]](b)}j=(c^-1)>>>31|0}$=h+96|0;return j|0}function Yd(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,C=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,U=0,V=0,W=0,X=0;if((e|0)==2){D[a+8>>2]=2;D[a- -64>>2]=f;d=a+32|0;e=D[d>>2];f=D[a+36>>2]-e|0;g=f>>2;a:{if(g>>>0<=1){sa(d,2-g|0);break a}if((f|0)==8){break a}D[a+36>>2]=e+8}b:{d=D[a+56>>2];e=D[d+4>>2];d=D[d>>2];f=e-d|0;if((f|0)<=0){f=0;break b}if((d|0)!=(e|0)){m=a+60|0;J=f>>2;U=(J|0)>1?J:1;f=1;while(1){j=$-80|0;$=j;e=-1;d=D[(q<<2)+d>>2];g=-1;c:{if((d|0)==-1){break c}e=d+1|0;e=(e>>>0)%3|0?e:d-2|0;g=d-1|0;if((d>>>0)%3|0){break c}g=d+2|0}i=D[m+36>>2];d=D[i>>2];d:{e:{f:{g:{h:{i=D[i+4>>2]-d>>2;h=e<<2;e=D[D[m+32>>2]+28>>2];k=D[h+e>>2];if(i>>>0<=k>>>0){break h}e=D[e+(g<<2)>>2];if(e>>>0>=i>>>0){break h}i:{j:{g=D[d+(e<<2)>>2];i=D[d+(k<<2)>>2];if((g|0)>=(q|0)|(i|0)>=(q|0)){break j}d=(g<<3)+c|0;v=D[d+4>>2];e=(i<<3)+c|0;s=D[e+4>>2];x=D[d>>2];F=D[e>>2];if(!((x|0)!=(F|0)|(s|0)!=(v|0))){D[m+8>>2]=F;D[m+12>>2]=s;break i}d=D[D[m+4>>2]+(q<<2)>>2];D[j+72>>2]=0;D[j+76>>2]=0;e=j- -64|0;D[e>>2]=0;D[e+4>>2]=0;D[j+56>>2]=0;D[j+60>>2]=0;e=D[m>>2];if(!E[e+84|0]){d=D[D[e+68>>2]+(d<<2)>>2]}Fa(e,d,B[e+24|0],j+56|0);d=D[D[m+4>>2]+(i<<2)>>2];D[j+48>>2]=0;D[j+52>>2]=0;D[j+40>>2]=0;D[j+44>>2]=0;D[j+32>>2]=0;D[j+36>>2]=0;e=D[m>>2];if(!E[e+84|0]){d=D[D[e+68>>2]+(d<<2)>>2]}Fa(e,d,B[e+24|0],j+32|0);d=D[D[m+4>>2]+(g<<2)>>2];D[j+24>>2]=0;D[j+28>>2]=0;D[j+16>>2]=0;D[j+20>>2]=0;D[j+8>>2]=0;D[j+12>>2]=0;e=D[m>>2];if(!E[e+84|0]){d=D[D[e+68>>2]+(d<<2)>>2]}Fa(e,d,B[e+24|0],j+8|0);K=D[j+44>>2];d=D[j+16>>2];G=D[j+40>>2];e=G;k=D[j+20>>2]-(K+(d>>>0<e>>>0)|0)|0;o=d-e|0;d=$h(o,k,o,k);e=aa;n=d;L=D[j+36>>2];d=D[j+8>>2];H=D[j+32>>2];g=H;h=D[j+12>>2]-(L+(d>>>0<g>>>0)|0)|0;t=d-g|0;g=$h(t,h,t,h);d=n+g|0;e=aa+e|0;e=d>>>0<g>>>0?e+1|0:e;n=d;M=D[j+52>>2];d=D[j+24>>2];I=D[j+48>>2];g=I;l=D[j+28>>2]-(M+(d>>>0<g>>>0)|0)|0;p=d-g|0;r=$h(p,l,p,l);d=n+r|0;g=aa+e|0;u=d;r=d>>>0<r>>>0?g+1|0:g;if(!(d|r)){break j}n=D[j+64>>2];d=n;O=D[j+68>>2];d=$h(d-G|0,O-((d>>>0<G>>>0)+K|0)|0,o,k);e=aa;g=d;P=D[j+56>>2];d=P;Q=D[j+60>>2];i=$h(d-H|0,Q-((d>>>0<H>>>0)+L|0)|0,t,h);d=g+i|0;g=aa+e|0;g=d>>>0<i>>>0?g+1|0:g;e=d;R=D[j+72>>2];d=R;S=D[j+76>>2];i=$h(d-I|0,S-((d>>>0<I>>>0)+M|0)|0,p,l);d=e+i|0;e=aa+g|0;y=d;w=d>>>0<i>>>0?e+1|0:e;d=l>>31;e=d+p|0;g=d+l|0;g=e>>>0<d>>>0?g+1|0:g;e=e^d;d=d^g;i=d;d=k>>31;z=d+o|0;g=d+k|0;A=d^z;d=d^(d>>>0>z>>>0?g+1|0:g);z=d;N=0;d=h>>31;C=d+t|0;g=d+h|0;g=C>>>0<d>>>0?g+1|0:g;V=e;C=C^d;d=d^g;g=(z|0)==(d|0)&A>>>0>C>>>0|d>>>0<z>>>0;A=g?A:C;d=g?z:d;e=(i|0)==(d|0)&e>>>0>A>>>0|d>>>0<i>>>0;e=bi(-1,2147483647,e?V:A,e?i:d)>>>0<y>>>0;d=aa;if(e&(d|0)<=(w|0)|(d|0)<(w|0)){break d}i=1;d=0;e=n;o=ai($h(o,k,y,w),aa,u,r);k=o+G|0;g=aa+K|0;g=k>>>0<o>>>0?g+1|0:g;g=O-((e>>>0<k>>>0)+g|0)|0;e=e-k|0;g=$h(e,g,e,g);o=aa;e=P;n=g;h=ai($h(t,h,y,w),aa,u,r);k=h+H|0;g=aa+L|0;g=h>>>0>k>>>0?g+1|0:g;g=Q-((e>>>0<k>>>0)+g|0)|0;e=e-k|0;k=$h(e,g,e,g);g=n+k|0;e=aa+o|0;e=g>>>0<k>>>0?e+1|0:e;k=e;e=R;n=g;l=ai($h(p,l,y,w),aa,u,r);h=l+I|0;g=aa+M|0;g=h>>>0<l>>>0?g+1|0:g;g=S-((e>>>0<h>>>0)+g|0)|0;e=e-h|0;h=$h(e,g,e,g);e=n+h|0;g=aa+k|0;k=$h(e,e>>>0<h>>>0?g+1|0:g,u,r);e=aa;h=e;if(!e&k>>>0<=1){break g}l=k;e=h;while(1){g=d<<1|i>>>31;i=i<<1;d=g;o=!e&l>>>0>7|(e|0)!=0;l=(e&3)<<30|l>>>2;e=e>>>2|0;if(o){continue}break}break f}if((i|0)<(q|0)){d=i<<1}else{if((q|0)<=0){D[m+8>>2]=0;D[m+12>>2]=0;break i}d=(q<<1)-2|0}d=(d<<2)+c|0;D[m+8>>2]=D[d>>2];D[m+12>>2]=D[d+4>>2]}N=1;break d}ua();T()}d=h;i=k;if(i-1|0){break e}}while(1){e=bi(k,h,i,d)+i|0;g=d+aa|0;g=e>>>0<i>>>0?g+1|0:g;i=(g&1)<<31|e>>>1;d=g>>>1|0;e=$h(i,d,i,d);g=aa;if((h|0)==(g|0)&e>>>0>k>>>0|g>>>0>h>>>0){continue}break}}k=D[m+20>>2];if(k){e=k-1|0;l=D[D[m+16>>2]+(e>>>3&536870908)>>2];D[m+20>>2]=e;g=s;o=v-g|0;h=g>>31;t=(v>>31)-(h+(g>>>0>v>>>0)|0)|0;g=$h(y,w,o,t);p=aa;s=$h(s,h,u,r);h=s+g|0;g=aa+p|0;g=h>>>0<s>>>0?g+1|0:g;n=h;h=F;p=x-h|0;s=h>>31;v=(x>>31)-(s+(h>>>0>x>>>0)|0)|0;h=$h(i,d,p,v);x=h;e=l>>>e&1;l=e?0-h|0:h;h=n+l|0;n=g;g=aa;g=n+(e?0-(g+((x|0)!=0)|0)|0:g)|0;W=m,X=ai(h,h>>>0<l>>>0?g+1|0:g,u,r),D[W+12>>2]=X;g=$h(p,v,y,w);l=aa;p=$h(u,r,F,s);h=p+g|0;g=aa+l|0;g=h>>>0<p>>>0?g+1|0:g;n=h;d=$h(i,d,o,t);h=e?d:0-d|0;i=n+h|0;n=g;g=aa;e=n+(e?g:0-(((d|0)!=0)+g|0)|0)|0;W=m,X=ai(i,i>>>0<h>>>0?e+1|0:e,u,r),D[W+8>>2]=X}N=(k|0)!=0}$=j+80|0;if(!N){break b}k:{if(D[a+8>>2]<=0){break k}g=D[a+32>>2];d=0;while(1){e=d<<2;f=D[(e+a|0)+68>>2];i=D[a+16>>2];l:{if((f|0)>(i|0)){D[e+g>>2]=i;break l}e=e+g|0;i=D[a+12>>2];if((i|0)>(f|0)){D[e>>2]=i;break l}D[e>>2]=f}d=d+1|0;f=D[a+8>>2];if((d|0)<(f|0)){continue}break}e=0;if((f|0)<=0){break k}d=q<<3;i=d+c|0;k=b+d|0;while(1){f=e<<2;d=f+i|0;f=D[f+k>>2]+D[f+g>>2]|0;D[d>>2]=f;m:{if((f|0)>D[a+16>>2]){f=f-D[a+20>>2]|0}else{if((f|0)>=D[a+12>>2]){break m}f=f+D[a+20>>2]|0}D[d>>2]=f}e=e+1|0;if((e|0)<D[a+8>>2]){continue}break}}q=q+1|0;f=(J|0)>(q|0);if((q|0)==(U|0)){break b}e=D[a+56>>2];d=D[e>>2];if(D[e+4>>2]-d>>2>>>0>q>>>0){continue}break}}ua();T()}a=f^1}else{a=0}return a&1}function Sh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,C=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,U=0,V=0,W=0;if((e|0)==2){D[a+8>>2]=2;D[a- -64>>2]=f;d=a+32|0;e=D[d>>2];f=D[a+36>>2]-e|0;g=f>>2;a:{if(g>>>0<=1){sa(d,2-g|0);break a}if((f|0)==8){break a}D[a+36>>2]=e+8}b:{d=D[a+56>>2];e=D[d+4>>2];d=D[d>>2];f=e-d|0;if((f|0)<=0){f=0;break b}if((d|0)!=(e|0)){m=a+60|0;I=f>>2;U=(I|0)>1?I:1;f=1;while(1){i=$-80|0;$=i;e=-1;c:{d:{g=D[(p<<2)+d>>2];if((g|0)==-1){break d}h=D[m+32>>2];d=g+1|0;d=(d>>>0)%3|0?d:g-2|0;if((d|0)!=-1){e=D[D[h>>2]+(d<<2)>>2]}d=-1;g=g+((g>>>0)%3|0?-1:2)|0;if((g|0)!=-1){d=D[D[h>>2]+(g<<2)>>2]}h=D[m+36>>2];g=D[h>>2];h=D[h+4>>2]-g>>2;if(h>>>0<=e>>>0|d>>>0>=h>>>0){break d}h=D[g+(e<<2)>>2];e:{f:{g:{h:{i:{j:{g=D[g+(d<<2)>>2];if((g|0)>=(p|0)|(h|0)>=(p|0)){break j}d=(g<<3)+c|0;w=D[d+4>>2];e=(h<<3)+c|0;r=D[e+4>>2];t=D[d>>2];F=D[e>>2];if(!((t|0)!=(F|0)|(r|0)!=(w|0))){D[m+8>>2]=F;D[m+12>>2]=r;break i}d=D[D[m+4>>2]+(p<<2)>>2];D[i+72>>2]=0;D[i+76>>2]=0;e=i- -64|0;D[e>>2]=0;D[e+4>>2]=0;D[i+56>>2]=0;D[i+60>>2]=0;e=D[m>>2];if(!E[e+84|0]){d=D[D[e+68>>2]+(d<<2)>>2]}Fa(e,d,B[e+24|0],i+56|0);d=D[D[m+4>>2]+(h<<2)>>2];D[i+48>>2]=0;D[i+52>>2]=0;D[i+40>>2]=0;D[i+44>>2]=0;D[i+32>>2]=0;D[i+36>>2]=0;e=D[m>>2];if(!E[e+84|0]){d=D[D[e+68>>2]+(d<<2)>>2]}Fa(e,d,B[e+24|0],i+32|0);e=D[D[m+4>>2]+(g<<2)>>2];D[i+24>>2]=0;D[i+28>>2]=0;D[i+16>>2]=0;D[i+20>>2]=0;D[i+8>>2]=0;D[i+12>>2]=0;d=D[m>>2];if(!E[d+84|0]){e=D[D[d+68>>2]+(e<<2)>>2]}Fa(d,e,B[d+24|0],i+8|0);J=D[i+44>>2];d=D[i+16>>2];x=D[i+40>>2];e=x;k=D[i+20>>2]-(J+(d>>>0<e>>>0)|0)|0;n=d-e|0;d=$h(n,k,n,k);e=aa;o=d;K=D[i+36>>2];d=D[i+8>>2];G=D[i+32>>2];g=G;j=D[i+12>>2]-(K+(d>>>0<g>>>0)|0)|0;u=d-g|0;g=$h(u,j,u,j);d=o+g|0;e=aa+e|0;e=d>>>0<g>>>0?e+1|0:e;o=d;L=D[i+52>>2];d=D[i+24>>2];H=D[i+48>>2];g=H;l=D[i+28>>2]-(L+(d>>>0<g>>>0)|0)|0;s=d-g|0;q=$h(s,l,s,l);d=o+q|0;g=aa+e|0;v=d;q=d>>>0<q>>>0?g+1|0:g;if(!(d|q)){break j}N=D[i+64>>2];d=N;O=D[i+68>>2];d=$h(d-x|0,O-((d>>>0<x>>>0)+J|0)|0,n,k);e=aa;g=d;P=D[i+56>>2];d=P;Q=D[i+60>>2];h=$h(d-G|0,Q-((d>>>0<G>>>0)+K|0)|0,u,j);d=g+h|0;g=aa+e|0;g=d>>>0<h>>>0?g+1|0:g;e=d;R=D[i+72>>2];d=R;S=D[i+76>>2];h=$h(d-H|0,S-((d>>>0<H>>>0)+L|0)|0,s,l);d=e+h|0;e=aa+g|0;z=d;y=d>>>0<h>>>0?e+1|0:e;d=l>>31;e=d+s|0;g=d+l|0;g=e>>>0<d>>>0?g+1|0:g;h=e^d;d=d^g;M=d;d=k>>31;g=d+n|0;e=d+k|0;A=g^d;d=d^(g>>>0<d>>>0?e+1|0:e);o=d;e=0;d=j>>31;C=d+u|0;g=d+j|0;g=C>>>0<d>>>0?g+1|0:g;C=C^d;d=d^g;g=(o|0)==(d|0)&A>>>0>C>>>0|d>>>0<o>>>0;A=g?A:C;d=g?o:d;g=(M|0)==(d|0)&h>>>0>A>>>0|d>>>0<M>>>0;g=bi(-1,2147483647,g?h:A,g?M:d)>>>0<z>>>0;d=aa;if(g&(d|0)<=(y|0)|(d|0)<(y|0)){break e}h=1;d=0;e=N;n=ai($h(n,k,z,y),aa,v,q);k=n+x|0;g=aa+J|0;g=k>>>0<n>>>0?g+1|0:g;g=O-((e>>>0<k>>>0)+g|0)|0;e=e-k|0;g=$h(e,g,e,g);n=aa;e=P;o=g;j=ai($h(u,j,z,y),aa,v,q);k=j+G|0;g=aa+K|0;g=j>>>0>k>>>0?g+1|0:g;g=Q-((e>>>0<k>>>0)+g|0)|0;e=e-k|0;k=$h(e,g,e,g);g=o+k|0;e=aa+n|0;e=g>>>0<k>>>0?e+1|0:e;k=e;e=R;o=g;l=ai($h(s,l,z,y),aa,v,q);j=l+H|0;g=aa+L|0;g=j>>>0<l>>>0?g+1|0:g;g=S-((e>>>0<j>>>0)+g|0)|0;e=e-j|0;j=$h(e,g,e,g);e=o+j|0;g=aa+k|0;k=$h(e,e>>>0<j>>>0?g+1|0:g,v,q);e=aa;j=e;if(!e&k>>>0<=1){break h}l=k;e=j;while(1){g=d<<1|h>>>31;h=h<<1;d=g;n=!e&l>>>0>7|(e|0)!=0;l=(e&3)<<30|l>>>2;e=e>>>2|0;if(n){continue}break}break g}if((h|0)<(p|0)){d=h<<1}else{if((p|0)<=0){D[m+8>>2]=0;D[m+12>>2]=0;break i}d=(p<<1)-2|0}d=(d<<2)+c|0;D[m+8>>2]=D[d>>2];D[m+12>>2]=D[d+4>>2]}e=1;break e}d=j;h=k;if(h-1|0){break f}}while(1){g=bi(k,j,h,d)+h|0;e=d+aa|0;e=g>>>0<h>>>0?e+1|0:e;h=(e&1)<<31|g>>>1;d=e>>>1|0;e=$h(h,d,h,d);g=aa;if((j|0)==(g|0)&e>>>0>k>>>0|g>>>0>j>>>0){continue}break}}k=D[m+20>>2];if(k){e=k-1|0;l=D[D[m+16>>2]+(e>>>3&536870908)>>2];D[m+20>>2]=e;g=r;n=w-g|0;j=g>>31;u=(w>>31)-(j+(g>>>0>w>>>0)|0)|0;g=$h(z,y,n,u);s=aa;r=$h(r,j,v,q);j=r+g|0;g=aa+s|0;g=j>>>0<r>>>0?g+1|0:g;o=j;j=F;s=t-j|0;r=j>>31;w=(t>>31)-(r+(j>>>0>t>>>0)|0)|0;j=$h(h,d,s,w);t=j;j=l>>>e&1;e=j;x=e?0-t|0:t;l=o+x|0;o=g;g=aa;e=o+(e?0-(g+((t|0)!=0)|0)|0:g)|0;V=m,W=ai(l,l>>>0<x>>>0?e+1|0:e,v,q),D[V+12>>2]=W;e=$h(s,w,z,y);g=aa;l=$h(v,q,F,r);e=l+e|0;g=aa+g|0;g=e>>>0<l>>>0?g+1|0:g;d=$h(h,d,n,u);l=j?d:0-d|0;h=l+e|0;e=aa;e=(j?e:0-(((d|0)!=0)+e|0)|0)+g|0;V=m,W=ai(h,h>>>0<l>>>0?e+1|0:e,v,q),D[V+8>>2]=W}e=(k|0)!=0}$=i+80|0;break c}ua();T()}if(!e){break b}k:{if(D[a+8>>2]<=0){break k}g=D[a+32>>2];d=0;while(1){e=d<<2;f=D[(e+a|0)+68>>2];h=D[a+16>>2];l:{if((f|0)>(h|0)){D[e+g>>2]=h;break l}e=e+g|0;h=D[a+12>>2];if((h|0)>(f|0)){D[e>>2]=h;break l}D[e>>2]=f}d=d+1|0;f=D[a+8>>2];if((d|0)<(f|0)){continue}break}e=0;if((f|0)<=0){break k}d=p<<3;h=d+c|0;k=b+d|0;while(1){f=e<<2;d=f+h|0;f=D[f+k>>2]+D[f+g>>2]|0;D[d>>2]=f;m:{if((f|0)>D[a+16>>2]){f=f-D[a+20>>2]|0}else{if((f|0)>=D[a+12>>2]){break m}f=f+D[a+20>>2]|0}D[d>>2]=f}e=e+1|0;if((e|0)<D[a+8>>2]){continue}break}}p=p+1|0;f=(I|0)>(p|0);if((p|0)==(U|0)){break b}e=D[a+56>>2];d=D[e>>2];if(D[e+4>>2]-d>>2>>>0>p>>>0){continue}break}}ua();T()}a=f^1}else{a=0}return a&1}function Hc(a){var b=0,c=0,d=0,e=0,f=0,g=0;e=$-16|0;$=e;D[e+12>>2]=a;a:{if(a>>>0<=211){d=D[Gc(10384,10576,e+12|0)>>2];break a}if(a>>>0>=4294967292){Sb();T()}f=(a>>>0)/210|0;d=J(f,210);D[e+8>>2]=a-d;g=Gc(10576,10768,e+8|0)-10576>>2;while(1){d=D[(g<<2)+10576>>2]+d|0;a=5;b:{while(1){c:{if((a|0)==47){a=211;while(1){b=(d>>>0)/(a>>>0)|0;if(b>>>0<a>>>0){break b}if((J(a,b)|0)==(d|0)){break c}b=a+10|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+12|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+16|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+18|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+22|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+28|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+30|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+36|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+40|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+42|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+46|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+52|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+58|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+60|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+66|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+70|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+72|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+78|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+82|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+88|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+96|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+100|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+102|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+106|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+108|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+112|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+120|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+126|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+130|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+136|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+138|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+142|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+148|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+150|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+156|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+162|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+166|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+168|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+172|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+178|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+180|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+186|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+190|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+192|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+196|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+198|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}if((J(b,c)|0)==(d|0)){break c}b=a+208|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}a=a+210|0;if((J(b,c)|0)!=(d|0)){continue}break}break c}b=D[(a<<2)+10384>>2];c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break b}a=a+1|0;if((J(b,c)|0)!=(d|0)){continue}}break}d=g+1|0;a=(d|0)==48;g=a?0:d;f=a+f|0;d=J(f,210);continue}break}D[e+12>>2]=d}$=e+16|0;return d}function Db(a,b,c,d){var e=0,f=0,g=0,h=0,i=K(0),j=0;a:{if(!d){break a}b:{c:{switch(D[a+28>>2]-1|0){case 0:d:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break d}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){B[b+d|0]=E[f|0];b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break d}f=f+1|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 1:e:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break e}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=B[f|0];if((e|0)<0){break a}B[b+d|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break e}f=f+1|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 2:f:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break f}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=F[f>>1];if((e-128&65535)>>>0<65280){break a}B[b+d|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break f}f=f+2|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 3:g:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break g}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=F[f>>1];if(e>>>0>127){break a}B[b+d|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break g}f=f+2|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 4:h:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break h}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=D[f>>2];if(e-128>>>0<4294967040){break a}B[b+d|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break h}f=f+4|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 5:i:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break i}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=D[f>>2];if(e>>>0>127){break a}B[b+d|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break i}f=f+4|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 6:j:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break j}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){h=D[f>>2];e=D[f+4>>2]-(h>>>0<128)|0;if((e|0)==-1&h-128>>>0<4294967040|(e|0)!=-1){break a}B[b+d|0]=h;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break j}f=f+8|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 7:k:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break k}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=D[f+4>>2];h=D[f>>2];if(!e&h>>>0>127|e){break a}B[b+d|0]=h;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break k}f=f+8|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 8:l:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break l}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){h=b+d|0;i=H[f>>2];m:{if(K(L(i))<K(2147483648)){e=~~i;break m}e=-2147483648}B[h|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break l}f=f+4|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 9:n:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break n}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){h=b+d|0;j=I[f>>3];o:{if(L(j)<2147483648){e=~~j;break o}e=-2147483648}B[h|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break n}f=f+8|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 10:break c;default:break a}}p:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break p}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){B[b+d|0]=E[f|0];b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break p}f=f+1|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)<=(e|0)){break a}}pa(d+e|0,0,c-e|0)}return g}function Cb(a,b,c,d){var e=0,f=0,g=0,h=0,i=K(0),j=0;a:{if(!d){break a}b:{c:{switch(D[a+28>>2]-1|0){case 0:d:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break d}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=B[f|0];if((e|0)<0){break a}B[b+d|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break d}f=f+1|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 1:e:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break e}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){B[b+d|0]=E[f|0];b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break e}f=f+1|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 2:f:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break f}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=F[f>>1];if(e>>>0>255){break a}B[b+d|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break f}f=f+2|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 3:g:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break g}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=F[f>>1];if(e>>>0>255){break a}B[b+d|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break g}f=f+2|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 4:h:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break h}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=D[f>>2];if(e>>>0>255){break a}B[b+d|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break h}f=f+4|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 5:i:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break i}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=D[f>>2];if(e>>>0>255){break a}B[b+d|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break i}f=f+4|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 6:j:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break j}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=D[f+4>>2];h=D[f>>2];if(!e&h>>>0>255|e){break a}B[b+d|0]=h;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break j}f=f+8|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 7:k:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break k}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){e=D[f+4>>2];h=D[f>>2];if(!e&h>>>0>255|e){break a}B[b+d|0]=h;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break k}f=f+8|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 8:l:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break l}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){h=b+d|0;i=H[f>>2];m:{if(i<K(4294967296)&i>=K(0)){e=~~i>>>0;break m}e=0}B[h|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break l}f=f+4|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 9:n:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break n}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){h=b+d|0;j=I[f>>3];o:{if(j<4294967296&j>=0){e=~~j>>>0;break o}e=0}B[h|0]=e;b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break n}f=f+8|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)>(e|0)){break b}break a;case 10:break c;default:break a}}p:{e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)<=0){break p}b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;f=D[a>>2];e=D[f>>2];if((b|0)>=(D[f+4>>2]-e|0)){break a}f=b+e|0;b=0;while(1){B[b+d|0]=E[f|0];b=b+1|0;e=B[a+24|0];if((b|0)>=(((c|0)<(e|0)?c:e)|0)){break p}f=f+1|0;if(f>>>0<G[D[a>>2]+4>>2]){continue}break}break a}g=1;if((c|0)<=(e|0)){break a}}pa(d+e|0,0,c-e|0)}return g}function ld(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;a:{b:{c:{d:{e:{if(D[a+92>>2]==D[a+88>>2]){break e}c=D[a+52>>2];f:{if((c|0)!=D[a+56>>2]){D[c>>2]=b;D[a+52>>2]=c+4;break f}g=D[a+48>>2];h=c-g|0;d=h>>2;e=d+1|0;if(e>>>0>=1073741824){break b}c=h>>1;f=d>>>0<536870911?c>>>0<e>>>0?e:c:1073741823;if(f){if(f>>>0>=1073741824){break a}c=na(f<<2)}else{c=0}e=c+(d<<2)|0;D[e>>2]=b;if((h|0)>0){oa(c,g,h)}D[a+56>>2]=c+(f<<2);D[a+52>>2]=e+4;D[a+48>>2]=c;if(!g){break f}ma(g)}D[a+84>>2]=0;e=-1;c=-1;g:{if((b|0)==-1){break g}f=D[a+4>>2];c=b+1|0;c=(c>>>0)%3|0?c:b-2|0;if((c|0)!=-1){e=D[D[f>>2]+(c<<2)>>2]}h:{if((b>>>0)%3|0){d=b-1|0;break h}d=b+2|0;c=-1;if((d|0)==-1){break g}}c=D[D[f>>2]+(d<<2)>>2]}j=c>>>3&536870908;d=D[a+36>>2];g=d+(e>>>3&536870908)|0;h=D[g>>2];f=1<<e;if(!(h&f)){D[g>>2]=f|h;f=a+8|0;if((b|0)!=-1){d=b+1|0;d=(d>>>0)%3|0?d:b-2|0}else{d=-1}Ia(f,e,d);d=D[a+36>>2]}f=d+j|0;d=D[f>>2];e=1<<c;if(!(d&e)){D[f>>2]=d|e;d=a+8|0;e=-1;i:{if((b|0)==-1){break i}e=b-1|0;if((b>>>0)%3|0){break i}e=b+2|0}Ia(d,c,e)}e=-1;e=(b|0)!=-1?D[D[D[a+4>>2]>>2]+(b<<2)>>2]:e;f=D[a+36>>2]+(e>>>3&536870908)|0;d=D[f>>2];c=1<<e;if(!(d&c)){D[f>>2]=c|d;Ia(a+8|0,e,b)}d=D[a+84>>2];if((d|0)>2){break e}while(1){e=J(d,12)+a|0;b=D[e+52>>2];if((b|0)==D[e+48>>2]){d=d+1|0;if((d|0)!=3){continue}break e}c=b-4|0;b=D[c>>2];D[e+52>>2]=c;D[a+84>>2]=d;if((b|0)==-1){break e}e=D[a+24>>2];c=(b>>>0)/3|0;j:{if(D[e+(c>>>3&268435452)>>2]>>>c&1){break j}k:{while(1){l=(b>>>0)/3|0;c=(l>>>3&268435452)+e|0;D[c>>2]=D[c>>2]|1<<l;e=-1;l:{m:{n:{o:{p:{q:{r:{s:{e=(b|0)!=-1?D[D[D[a+4>>2]>>2]+(b<<2)>>2]:e;f=D[a+36>>2]+(e>>>3&536870908)|0;d=D[f>>2];c=1<<e;if(!(d&c)){D[f>>2]=c|d;h=D[(D[D[a+16>>2]+96>>2]+J(l,12)|0)+((b>>>0)%3<<2)>>2];k=D[D[a+20>>2]+4>>2];c=D[k+4>>2];t:{if((c|0)!=D[k+8>>2]){D[c>>2]=h;D[k+4>>2]=c+4;break t}i=D[k>>2];j=c-i|0;f=j>>2;d=f+1|0;if(d>>>0>=1073741824){break s}c=j>>1;g=f>>>0<536870911?c>>>0<d>>>0?d:c:1073741823;if(g){if(g>>>0>=1073741824){break a}c=na(g<<2)}else{c=0}d=c+(f<<2)|0;D[d>>2]=h;if((j|0)>0){oa(c,i,j)}D[k+8>>2]=c+(g<<2);D[k+4>>2]=d+4;D[k>>2]=c;if(!i){break t}ma(i)}i=D[a+12>>2];c=D[i+4>>2];u:{if((c|0)!=D[i+8>>2]){D[c>>2]=b;D[i+4>>2]=c+4;break u}j=D[i>>2];g=c-j|0;f=g>>2;d=f+1|0;if(d>>>0>=1073741824){break r}c=g>>1;h=f>>>0<536870911?c>>>0<d>>>0?d:c:1073741823;if(h){if(h>>>0>=1073741824){break a}c=na(h<<2)}else{c=0}d=c+(f<<2)|0;D[d>>2]=b;if((g|0)>0){oa(c,j,g)}D[i+8>>2]=c+(h<<2);D[i+4>>2]=d+4;D[i>>2]=c;if(!j){break u}ma(j)}c=D[a+12>>2];D[D[c+12>>2]+(e<<2)>>2]=D[c+24>>2];D[c+24>>2]=D[c+24>>2]+1}if((b|0)==-1){break k}g=D[a+4>>2];e=-1;c=b+1|0;c=(c>>>0)%3|0?c:b-2|0;if((c|0)!=-1){e=D[D[g+12>>2]+(c<<2)>>2]}v:{w:{if((J(l,3)|0)!=(b|0)){f=b-1|0;break w}f=b+2|0;b=-1;if((f|0)==-1){break v}}b=D[D[g+12>>2]+(f<<2)>>2]}h=(b|0)==-1;f=(b>>>0)/3|0;d=(e>>>0)/3|0;c=(e|0)==-1;if(!c){c=c?-1:d;c=D[D[a+24>>2]+(c>>>3&536870908)>>2]&1<<c;if(h){break q}l=(c|0)!=0;break p}l=1;if(!h){break p}break k}qa();T()}qa();T()}if(!c){break o}break k}c=h?-1:f;x:{if(D[D[a+24>>2]+(c>>>3&536870908)>>2]>>>c&1){break x}f=0;c=D[D[g>>2]+(b<<2)>>2];if(!(D[D[a+36>>2]+(c>>>3&536870908)>>2]>>>c&1)){c=D[a+88>>2]+(c<<2)|0;d=D[c>>2];D[c>>2]=d+1;f=(d|0)<=0?2:1}if(D[a+84>>2]>=(f|0)?l:0){break m}k=J(f,12)+a|0;c=D[k+52>>2];y:{if((c|0)!=D[k+56>>2]){D[c>>2]=b;D[k+52>>2]=c+4;break y}i=D[k+48>>2];j=c-i|0;h=j>>2;d=h+1|0;if(d>>>0>=1073741824){break d}c=j>>1;g=h>>>0<536870911?c>>>0<d>>>0?d:c:1073741823;if(g){if(g>>>0>=1073741824){break a}c=na(g<<2)}else{c=0}d=c+(h<<2)|0;D[d>>2]=b;if((j|0)>0){oa(c,i,j)}D[k+48>>2]=c;D[k+52>>2]=d+4;D[k+56>>2]=c+(g<<2);if(!i){break y}ma(i)}if(D[a+84>>2]<=(f|0)){break x}D[a+84>>2]=f}if(l){break k}b=-1;if((e|0)==-1){break n}}b=D[D[D[a+4>>2]>>2]+(e<<2)>>2]}f=0;if(!(D[D[a+36>>2]+(b>>>3&536870908)>>2]>>>b&1)){b=D[a+88>>2]+(b<<2)|0;c=D[b>>2];D[b>>2]=c+1;f=(c|0)<=0?2:1}if(D[a+84>>2]<(f|0)){break l}b=e}e=D[a+24>>2];continue}break}i=J(f,12)+a|0;b=D[i+52>>2];z:{if((b|0)!=D[i+56>>2]){D[b>>2]=e;D[i+52>>2]=b+4;break z}j=D[i+48>>2];g=b-j|0;d=g>>2;c=d+1|0;if(c>>>0>=1073741824){break c}b=g>>1;h=d>>>0<536870911?b>>>0<c>>>0?c:b:1073741823;if(h){if(h>>>0>=1073741824){break a}b=na(h<<2)}else{b=0}c=b+(d<<2)|0;D[c>>2]=e;if((g|0)>0){oa(b,j,g)}D[i+48>>2]=b;D[i+52>>2]=c+4;D[i+56>>2]=b+(h<<2);if(!j){break z}ma(j)}d=D[a+84>>2];if((d|0)<=(f|0)){break j}D[a+84>>2]=f;d=f;break j}d=D[a+84>>2]}if((d|0)<3){continue}break}}return 1}qa();T()}qa();T()}qa();T()}ra(1326);T()}function sd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;h=$-48|0;$=h;c=D[D[a+4>>2]+44>>2];d=D[a+8>>2];f=D[d>>2];d=D[d+4>>2];D[h+40>>2]=0;D[h+32>>2]=0;D[h+36>>2]=0;d=(d-f>>2>>>0)/3|0;j=D[c+96>>2];f=(D[c+100>>2]-j|0)/12|0;a:{if(d>>>0>f>>>0){gc(c+96|0,d-f|0,h+32|0);break a}if(d>>>0>=f>>>0){break a}D[c+100>>2]=j+J(d,12)}b:{if(D[a+216>>2]==D[a+220>>2]){k=D[a+4>>2];i=D[k+44>>2];d=D[i+100>>2];if((d|0)!=D[i+96>>2]){c=0;while(1){g=D[a+8>>2];e=J(c,3);c:{d:{if((e|0)==-1){l=D[(D[g>>2]+(e<<2)|0)+4>>2];j=-1;e=1;break d}l=-1;j=D[D[g>>2]+(e<<2)>>2];f=e+1|0;if((f|0)==-1){e=0;break d}l=D[D[g>>2]+(f<<2)>>2];e=e+2|0;f=-1;if((e|0)==-1){break c}}f=D[D[g>>2]+(e<<2)>>2]}g=f;f=c+1|0;e=d;d=D[i+96>>2];e=(e-d|0)/12|0;if(e>>>0<=c>>>0){D[h+40>>2]=0;D[h+32>>2]=0;D[h+36>>2]=0;gc(i+96|0,f-e|0,h+32|0);k=D[a+4>>2];d=D[i+96>>2]}c=J(c,12)+d|0;D[c+8>>2]=g;D[c+4>>2]=l;D[c>>2]=j;c=f;i=D[k+44>>2];d=D[i+100>>2];if(c>>>0<(d-D[i+96>>2]|0)/12>>>0){continue}break}}D[D[k+4>>2]+80>>2]=b;c=1;break b}D[h+24>>2]=0;D[h+16>>2]=0;D[h+20>>2]=0;k=D[a+8>>2];b=D[k>>2];c=D[k+4>>2];D[h+8>>2]=0;D[h>>2]=0;D[h+4>>2]=0;e:{f:{g:{h:{i:{j:{k:{b=c-b|0;if(b){if((b|0)<0){break k}n=na(b);D[h>>2]=n;D[h+8>>2]=(b>>2<<2)+n;u=h,v=pa(n,0,b)+b|0,D[u+4>>2]=v}c=D[k+24>>2];if((D[k+28>>2]-c|0)<=0){d=0;b=0;break f}d=0;f=0;b=0;while(1){j=D[(o<<2)+c>>2];l:{if((j|0)==-1){break l}m:{if(D[D[a+120>>2]+(o>>>3&536870908)>>2]>>>o&1){break m}q=D[a+216>>2];c=D[a+220>>2]-q|0;if(!c){break m}c=(c|0)/144|0;r=c>>>0>1?c:1;l=0;c=(j>>>0)%3|0;g=j+2|0;s=(c|0)!=0|(g|0)!=-1;t=c?j-1|0:g;while(1){i=j<<2;e=q+J(l,144)|0;c=D[i+D[D[e+68>>2]>>2]>>2];n:{if(!(D[D[e+16>>2]+(c>>>3&536870908)>>2]>>>c&1)){break n}c=-1;o:{if(!s){break o}g=D[D[k+12>>2]+(t<<2)>>2];c=-1;if((g|0)==-1){break o}c=g-1|0;if((g>>>0)%3|0){break o}c=g+2|0}if((j|0)==(c|0)){break n}e=D[e+32>>2];g=D[e+i>>2];while(1){m=0;if((c|0)==-1){break e}if(D[e+(c<<2)>>2]!=(g|0)){j=c;break m}p:{q:{if((c>>>0)%3|0){i=c-1|0;break q}i=c+2|0;m=-1;if((i|0)==-1){break p}}c=D[D[k+12>>2]+(i<<2)>>2];m=-1;if((c|0)==-1){break p}m=c-1|0;if((c>>>0)%3|0){break p}m=c+2|0}c=m;if((j|0)!=(c|0)){continue}break}}l=l+1|0;if((r|0)!=(l|0)){continue}break}}c=b-f|0;g=c>>2;D[(j<<2)+n>>2]=g;r:{if(b>>>0<p>>>0){D[b>>2]=j;b=b+4|0;D[h+20>>2]=b;break r}b=g+1|0;if(b>>>0>=1073741824){break j}d=p-f|0;e=d>>1;b=d>>2>>>0<536870911?b>>>0>e>>>0?b:e:1073741823;if(b){if(b>>>0>=1073741824){break i}d=na(b<<2)}else{d=0}g=d+(g<<2)|0;D[g>>2]=j;p=(b<<2)+d|0;b=g+4|0;if((c|0)>0){oa(d,f,c)}D[h+24>>2]=p;D[h+20>>2]=b;D[h+16>>2]=d;if(f){ma(f);k=D[a+8>>2]}f=d}if((j|0)==-1){break l}s:{if((j>>>0)%3|0){c=j-1|0;break s}c=j+2|0;if((c|0)==-1){break l}}c=D[D[k+12>>2]+(c<<2)>>2];if((c|0)==-1){break l}c=c+((c>>>0)%3|0?-1:2)|0;if((c|0)==-1){break l}e=j;if((c|0)==(e|0)){break l}while(1){g=c;t:{u:{i=D[a+216>>2];c=D[a+220>>2]-i|0;if(!c){break u}c=(c|0)/144|0;l=c>>>0>1?c:1;c=0;while(1){q=D[(i+J(c,144)|0)+32>>2];r=g<<2;if(D[q+r>>2]==D[q+(e<<2)>>2]){c=c+1|0;if((l|0)!=(c|0)){continue}break u}break}c=b-d|0;e=c>>2;D[n+r>>2]=e;if(b>>>0<p>>>0){D[b>>2]=g;b=b+4|0;D[h+20>>2]=b;f=d;break t}b=e+1|0;if(b>>>0>=1073741824){break h}f=p-d|0;i=f>>1;b=f>>2>>>0<536870911?b>>>0>i>>>0?b:i:1073741823;if(b){if(b>>>0>=1073741824){break g}f=na(b<<2)}else{f=0}e=f+(e<<2)|0;D[e>>2]=g;p=(b<<2)+f|0;b=e+4|0;if((c|0)>0){oa(f,d,c)}D[h+24>>2]=p;D[h+20>>2]=b;D[h+16>>2]=f;if(!d){d=f;break t}ma(d);k=D[a+8>>2];d=f;break t}D[(g<<2)+n>>2]=D[(e<<2)+n>>2]}if((g|0)==-1){break l}v:{if((g>>>0)%3|0){c=g-1|0;break v}c=g+2|0;if((c|0)==-1){break l}}c=D[D[k+12>>2]+(c<<2)>>2];if((c|0)==-1){break l}c=c+((c>>>0)%3|0?-1:2)|0;if((c|0)==-1){break l}e=g;if((c|0)!=(j|0)){continue}break}}o=o+1|0;c=D[k+24>>2];if((o|0)<D[k+28>>2]-c>>2){continue}break}break f}qa();T()}qa();T()}ra(1326);T()}qa();T()}ra(1326);T()}o=D[a+4>>2];i=D[o+44>>2];l=D[i+100>>2];if((l|0)!=D[i+96>>2]){c=0;while(1){f=c+1|0;g=J(c,12);j=g+n|0;e=D[j+8>>2];k=D[j+4>>2];j=D[j>>2];m=c;c=D[i+96>>2];l=(l-c|0)/12|0;if(m>>>0>=l>>>0){D[h+40>>2]=0;D[h+32>>2]=0;D[h+36>>2]=0;gc(i+96|0,f-l|0,h+32|0);o=D[a+4>>2];c=D[i+96>>2]}c=c+g|0;D[c+8>>2]=e;D[c+4>>2]=k;D[c>>2]=j;c=f;i=D[o+44>>2];l=D[i+100>>2];if(c>>>0<(l-D[i+96>>2]|0)/12>>>0){continue}break}}D[D[o+4>>2]+80>>2]=b-d>>2;m=1}c=m;if(n){ma(n)}if(!d){break b}D[h+20>>2]=d;ma(d)}$=h+48|0;return c}
function Fa(a,b,c,d){var e=0,f=0,g=0,h=0,i=K(0),j=0,k=0;a:{if(!d){break a}b:{c:{switch(D[a+28>>2]-1|0){case 0:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;h=D[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(f<<3)+d|0;g=B[b|0];D[e>>2]=g;D[e+4>>2]=g>>31;b=b+1|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break b}break a;case 1:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;h=D[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(f<<3)+d|0;D[e>>2]=E[b|0];D[e+4>>2]=0;b=b+1|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break b}break a;case 2:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;h=D[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(f<<3)+d|0;g=C[b>>1];D[e>>2]=g;D[e+4>>2]=g>>31;b=b+2|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break b}break a;case 3:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;h=D[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(f<<3)+d|0;D[e>>2]=F[b>>1];D[e+4>>2]=0;b=b+2|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break b}break a;case 4:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;h=D[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(f<<3)+d|0;g=D[b>>2];D[e>>2]=g;D[e+4>>2]=g>>31;b=b+4|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break b}break a;case 5:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;h=D[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(f<<3)+d|0;D[e>>2]=D[b>>2];D[e+4>>2]=0;b=b+4|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break b}break a;case 6:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;h=D[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}g=D[b+4>>2];e=(f<<3)+d|0;D[e>>2]=D[b>>2];D[e+4>>2]=g;b=b+8|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break b}break a;case 7:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;h=D[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=D[b>>2];g=D[b+4>>2];if((g|0)<0){break a}k=(f<<3)+d|0;D[k>>2]=e;D[k+4>>2]=g;b=b+8|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break b}break a;case 8:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;k=D[e+4>>2];while(1){if(b>>>0>=k>>>0){break a}e=(f<<3)+d|0;i=H[b>>2];d:{if(K(L(i))<K(0x8000000000000000)){g=K(L(i))>=K(1)?~~(i>K(0)?K(N(K(P(K(i*K(2.3283064365386963e-10)))),K(4294967296))):K(Q(K(K(i-K(~~i>>>0>>>0))*K(2.3283064365386963e-10)))))>>>0:0;h=~~i>>>0;break d}g=-2147483648;h=0}D[e>>2]=h;D[e+4>>2]=g;b=b+4|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break b}break a;case 9:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;k=D[e+4>>2];while(1){if(b>>>0>=k>>>0){break a}e=(f<<3)+d|0;j=I[b>>3];e:{if(L(j)<0x8000000000000000){g=L(j)>=1?~~(j>0?N(P(j*2.3283064365386963e-10),4294967295):Q((j-+(~~j>>>0>>>0))*2.3283064365386963e-10))>>>0:0;h=~~j>>>0;break e}g=-2147483648;h=0}D[e>>2]=h;D[e+4>>2]=g;b=b+8|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break b}break a;case 10:break c;default:break a}}e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];g=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=g+b|0;h=D[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(f<<3)+d|0;D[e>>2]=E[b|0];D[e+4>>2]=0;b=b+1|0;f=f+1|0;e=B[a+24|0];if((f|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)<=(e|0)){break a}}pa((e<<3)+d|0,0,c-e<<3)}}function Cg(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;d=$-32|0;$=d;a:{if(!$a(1,d+28|0,D[a+32>>2])){break a}if(!$a(1,d+24|0,D[a+32>>2])){break a}k=D[d+28>>2];if(k>>>0>1431655765){break a}e=D[a+32>>2];j=D[e+8>>2];b=j;c=D[e+16>>2];f=b-c|0;g=D[e+12>>2];n=b>>>0<c>>>0;b=D[e+20>>2];f=ai(f,g-(n+b|0)|0,3,0);if(!aa&f>>>0<k>>>0){break a}o=D[d+24>>2];f=$h(k,0,3,0);if(!aa&f>>>0<o>>>0|((b|0)>=(g|0)&c>>>0>=j>>>0|(b|0)>(g|0))){break a}g=E[c+D[e>>2]|0];j=c+1|0;f=j?b:b+1|0;D[e+16>>2]=j;D[e+20>>2]=f;b:{if(!g){e=0;b=$-32|0;$=b;D[b+24>>2]=0;D[b+16>>2]=0;D[b+20>>2]=0;c:{d:{f=J(k,3);if(f){if(f>>>0>=1073741824){break d}c=J(k,12);e=na(c);D[b+16>>2]=e;pa(e,0,c)}f=jc(f,1,D[a+32>>2],e);e:{f:{if(!(!f|!k)){g=0;c=0;while(1){n=h;j=(c<<2)+e|0;h=D[j>>2];i=h>>>1|0;h=n+(h&1?0-i|0:i)|0;D[b>>2]=h;i=D[j+4>>2];l=i>>>1|0;h=h+(i&1?0-l|0:l)|0;D[b+4>>2]=h;j=D[j+8>>2];i=j>>>1|0;h=h+(j&1?0-i|0:i)|0;D[b+8>>2]=h;nb(D[a+44>>2]+96|0,b);c=c+3|0;g=g+1|0;if((g|0)!=(k|0)){continue}break}break f}if(!e){break e}}ma(e)}$=b+32|0;break c}qa();T()}if(!f){break a}break b}g:{if(o>>>0<=255){if(!k){break b}D[d+16>>2]=0;D[d+8>>2]=0;D[d+12>>2]=0;c=D[e+12>>2];b=c;h=D[e+8>>2];if((f|0)>=(b|0)&j>>>0>=h>>>0|(b|0)<(f|0)){break g}while(1){g=D[e>>2];l=E[g+j|0];b=f;i=j+1|0;b=i?b:b+1|0;D[e+16>>2]=i;D[e+20>>2]=b;D[d+8>>2]=l;if(h>>>0<=i>>>0&(b|0)>=(c|0)|(b|0)>(c|0)){break g}l=E[g+i|0];b=f;i=j+2|0;b=i>>>0<2?b+1|0:b;D[e+16>>2]=i;D[e+20>>2]=b;D[d+12>>2]=l;if(h>>>0<=i>>>0&(b|0)>=(c|0)|(b|0)>(c|0)){break g}b=E[g+i|0];c=j+3|0;f=c>>>0<3?f+1|0:f;D[e+16>>2]=c;D[e+20>>2]=f;D[d+16>>2]=b;nb(D[a+44>>2]+96|0,d+8|0);m=m+1|0;if((k|0)==(m|0)){break b}e=D[a+32>>2];b=e;j=D[b+16>>2];f=D[b+20>>2];D[d+16>>2]=0;D[d+8>>2]=0;D[d+12>>2]=0;h=D[b+8>>2];c=D[b+12>>2];b=c;if(j>>>0<h>>>0&(f|0)<=(b|0)|(b|0)>(f|0)){continue}break}break g}if(o>>>0<=65535){if(!k){break b}D[d+16>>2]=0;D[d+8>>2]=0;D[d+12>>2]=0;g=D[e+12>>2];c=c+3|0;b=c>>>0<3?b+1|0:b;i=D[e+8>>2];h=c;c=b;if(i>>>0<h>>>0&(b|0)>=(g|0)|(b|0)>(g|0)){break g}while(1){l=D[e>>2];b=l+j|0;b=E[b|0]|E[b+1|0]<<8;D[e+16>>2]=h;D[e+20>>2]=c;D[d+8>>2]=b;b=f;c=j+4|0;b=c>>>0<4?b+1|0:b;if(c>>>0>i>>>0&(b|0)>=(g|0)|(b|0)>(g|0)){break g}h=h+l|0;h=E[h|0]|E[h+1|0]<<8;D[e+16>>2]=c;D[e+20>>2]=b;D[d+12>>2]=h;b=f;f=j+6|0;b=f>>>0<6?b+1|0:b;if(f>>>0>i>>>0&(b|0)>=(g|0)|(b|0)>(g|0)){break g}c=c+l|0;c=E[c|0]|E[c+1|0]<<8;D[e+16>>2]=f;D[e+20>>2]=b;D[d+16>>2]=c;nb(D[a+44>>2]+96|0,d+8|0);m=m+1|0;if((k|0)==(m|0)){break b}e=D[a+32>>2];b=e;j=D[b+16>>2];f=D[b+20>>2];D[d+16>>2]=0;D[d+8>>2]=0;D[d+12>>2]=0;g=D[b+12>>2];i=D[b+8>>2];b=f;c=j+2|0;b=c>>>0<2?b+1|0:b;h=c;c=b;if((b|0)<=(g|0)&h>>>0<=i>>>0|(b|0)<(g|0)){continue}break}break g}h:{if(G[D[a+44>>2]+80>>2]>2097151){break h}g=F[a+36>>1];if(((g<<8|g>>>8)&65535)>>>0<514){break h}if(!k){break b}D[d+16>>2]=0;D[d+8>>2]=0;D[d+12>>2]=0;if(!$a(1,d+4|0,e)){break g}while(1){D[d+8>>2]=D[d+4>>2];if(!$a(1,d+4|0,D[a+32>>2])){break g}D[d+12>>2]=D[d+4>>2];if(!$a(1,d+4|0,D[a+32>>2])){break g}D[d+16>>2]=D[d+4>>2];nb(D[a+44>>2]+96|0,d+8|0);m=m+1|0;if((k|0)==(m|0)){break b}f=D[a+32>>2];D[d+16>>2]=0;D[d+8>>2]=0;D[d+12>>2]=0;if($a(1,d+4|0,f)){continue}break}break g}if(!k){break b}D[d+16>>2]=0;D[d+8>>2]=0;D[d+12>>2]=0;g=D[e+12>>2];c=c+5|0;b=c>>>0<5?b+1|0:b;i=D[e+8>>2];h=c;c=b;if(i>>>0<h>>>0&(b|0)>=(g|0)|(b|0)>(g|0)){break g}while(1){l=D[e>>2];b=l+j|0;b=E[b|0]|E[b+1|0]<<8|(E[b+2|0]<<16|E[b+3|0]<<24);D[e+16>>2]=h;D[e+20>>2]=c;D[d+8>>2]=b;b=f;c=j+8|0;b=c>>>0<8?b+1|0:b;n=c;if(c>>>0>i>>>0&(b|0)>=(g|0)|(b|0)>(g|0)){break g}c=h+l|0;c=E[c|0]|E[c+1|0]<<8|(E[c+2|0]<<16|E[c+3|0]<<24);D[e+16>>2]=n;D[e+20>>2]=b;D[d+12>>2]=c;b=j+12|0;f=b>>>0<12?f+1|0:f;c=b;if(b>>>0>i>>>0&(f|0)>=(g|0)|(f|0)>(g|0)){break g}b=l+n|0;b=E[b|0]|E[b+1|0]<<8|(E[b+2|0]<<16|E[b+3|0]<<24);D[e+16>>2]=c;D[e+20>>2]=f;D[d+16>>2]=b;nb(D[a+44>>2]+96|0,d+8|0);m=m+1|0;if((k|0)==(m|0)){break b}e=D[a+32>>2];b=e;j=D[b+16>>2];f=D[b+20>>2];D[d+16>>2]=0;D[d+8>>2]=0;D[d+12>>2]=0;g=D[b+12>>2];i=D[b+8>>2];b=f;c=j+4|0;b=c>>>0<4?b+1|0:b;h=c;c=b;if((b|0)<=(g|0)&h>>>0<=i>>>0|(b|0)<(g|0)){continue}break}}m=0;break a}D[D[a+4>>2]+80>>2]=o;m=1}$=d+32|0;return m|0}function Lb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0;f=$-96|0;$=f;e=D[a+16>>2];B[f+92|0]=1;D[f+88>>2]=b;D[f+84>>2]=b;D[f+80>>2]=e;m=D[a+20>>2];d=D[m>>2];a:{b:{e=D[D[e+28>>2]+(b<<2)>>2];if(e>>>0<D[m+4>>2]-d>>2>>>0){d=D[D[a+8>>2]+(D[d+(e<<2)>>2]<<2)>>2];e=D[a+4>>2];if(!E[e+84|0]){d=D[D[e+68>>2]+(d<<2)>>2]}D[f+72>>2]=0;D[f+76>>2]=0;m=f- -64|0;D[m>>2]=0;D[m+4>>2]=0;D[f+56>>2]=0;D[f+60>>2]=0;Fa(e,d,B[e+24|0],f+56|0);e=b+1|0;d=(e>>>0)%3|0;if(b>>>0<=e>>>0){m=d?e:b-2|0;h=((b>>>0)%3|0?-1:2)+b|0;while(1){d=m;e=h;c:{if(!D[a+28>>2]){break c}e=b+1|0;d=(e>>>0)%3|0?e:b-2|0;e=b-1|0;if((b>>>0)%3|0){break c}e=b+2|0}k=D[a+20>>2];b=D[k>>2];d=D[D[D[a+16>>2]+28>>2]+(d<<2)>>2];if(d>>>0>=D[k+4>>2]-b>>2>>>0){break b}d=D[D[a+8>>2]+(D[b+(d<<2)>>2]<<2)>>2];b=D[a+4>>2];if(!E[b+84|0]){d=D[D[b+68>>2]+(d<<2)>>2]}D[f+48>>2]=0;D[f+52>>2]=0;D[f+40>>2]=0;D[f+44>>2]=0;D[f+32>>2]=0;D[f+36>>2]=0;Fa(b,d,B[b+24|0],f+32|0);d=D[a+20>>2];b=D[d>>2];e=D[D[D[a+16>>2]+28>>2]+(e<<2)>>2];if(e>>>0>=D[d+4>>2]-b>>2>>>0){break a}d=D[D[a+8>>2]+(D[b+(e<<2)>>2]<<2)>>2];b=D[a+4>>2];if(!E[b+84|0]){d=D[D[b+68>>2]+(d<<2)>>2]}D[f+24>>2]=0;D[f+28>>2]=0;D[f+16>>2]=0;D[f+20>>2]=0;D[f+8>>2]=0;D[f+12>>2]=0;Fa(b,d,B[b+24|0],f+8|0);e=D[f+8>>2];b=D[f+56>>2];d=e-b|0;k=D[f+60>>2];n=D[f+12>>2]-(k+(b>>>0>e>>>0)|0)|0;i=D[f+40>>2];e=D[f+64>>2];s=i-e|0;t=D[f+68>>2];i=D[f+44>>2]-(t+(e>>>0>i>>>0)|0)|0;u=$h(d,n,s,i);v=j-u|0;g=g-(aa+(j>>>0<u>>>0)|0)|0;w=v;j=D[f+16>>2];u=j-e|0;t=D[f+20>>2]-((e>>>0>j>>>0)+t|0)|0;j=D[f+32>>2];v=j-b|0;k=D[f+36>>2]-((b>>>0>j>>>0)+k|0)|0;e=$h(u,t,v,k);j=w+e|0;b=aa+g|0;b=e>>>0>j>>>0?b+1|0:b;g=b;w=o;p=n;b=D[f+48>>2];e=D[f+72>>2];n=b-e|0;o=D[f+76>>2];x=D[f+52>>2]-(o+(b>>>0<e>>>0)|0)|0;p=$h(d,p,n,x);d=w+p|0;b=aa+l|0;b=d>>>0<p>>>0?b+1|0:b;l=D[f+24>>2];p=l-e|0;e=D[f+28>>2]-((e>>>0>l>>>0)+o|0)|0;l=$h(p,e,v,k);o=d-l|0;l=b-(aa+(d>>>0<l>>>0)|0)|0;b=$h(u,t,n,x);d=q-b|0;b=r-(aa+(b>>>0>q>>>0)|0)|0;r=$h(p,e,s,i);q=r+d|0;b=aa+b|0;b=q>>>0<r>>>0?b+1|0:b;r=b;b=D[f+88>>2];e=D[f+80>>2];d:{if(E[f+92|0]){e:{f:{g:{h:{if((b|0)==-1){break h}d=b+1|0;b=(d>>>0)%3|0?d:b-2|0;if((b|0)==-1|D[D[e>>2]+(b>>>3&536870908)>>2]>>>b&1){break h}b=D[D[D[e+64>>2]+12>>2]+(b<<2)>>2];if((b|0)!=-1){break g}}D[f+88>>2]=-1;break f}d=b+1|0;b=(d>>>0)%3|0?d:b-2|0;D[f+88>>2]=b;if((b|0)!=-1){break e}}b=D[f+84>>2];d=-1;i:{if((b|0)==-1){break i}j:{if((b>>>0)%3|0){b=b-1|0;break j}b=b+2|0;d=-1;if((b|0)==-1){break i}}d=-1;if(D[D[e>>2]+(b>>>3&536870908)>>2]>>>b&1){break i}b=D[D[D[e+64>>2]+12>>2]+(b<<2)>>2];d=-1;if((b|0)==-1){break i}d=b-1|0;if((b>>>0)%3|0){break i}d=b+2|0}B[f+92|0]=0;D[f+88>>2]=d;break d}if((b|0)!=D[f+84>>2]){break d}D[f+88>>2]=-1;break d}d=-1;k:{if((b|0)==-1){break k}l:{if((b>>>0)%3|0){b=b-1|0;break l}b=b+2|0;d=-1;if((b|0)==-1){break k}}d=-1;if(D[D[e>>2]+(b>>>3&536870908)>>2]>>>b&1){break k}b=D[D[D[e+64>>2]+12>>2]+(b<<2)>>2];d=-1;if((b|0)==-1){break k}d=b-1|0;if((b>>>0)%3|0){break k}d=b+2|0}D[f+88>>2]=d}b=D[f+88>>2];if((b|0)!=-1){continue}break}}b=r>>31;d=b+q|0;e=b;b=b+r|0;k=d^e;h=e^(d>>>0<e>>>0?b+1|0:b);n=-1;d=2147483647;b=l>>31;i=b;e=b+o|0;b=b+l|0;b=e>>>0<i>>>0?b+1|0:b;e=e^i;b=b^i;i=b;s=e^-1;b=b^2147483647;m=g;m:{n:{if(!D[a+28>>2]){if((b|0)==(h|0)&k>>>0>s>>>0|b>>>0<h>>>0){break m}b=h+i|0;a=e+k|0;b=a>>>0<e>>>0?b+1|0:b;e=a;a=b;b=g>>31;d=b+j|0;h=g;g=b;b=h+b|0;b=d>>>0<g>>>0?b+1|0:b;h=d^g;d=h+e|0;g=b^g;b=d;g=g^2147483647;a=(g|0)==(a|0)&(h^-1)>>>0<e>>>0|a>>>0>g>>>0;g=!(a&0);a=a?-1:b;if(g&(a|0)<=536870912|(a|0)<536870912){break m}b=0;a=a>>>29|0;break n}o:{if((b|0)==(h|0)&k>>>0>s>>>0|b>>>0<h>>>0){break o}b=h+i|0;a=e+k|0;b=a>>>0<e>>>0?b+1|0:b;e=b;h=g;b=g>>31;g=b+j|0;i=h;h=b;b=i+b|0;b=g>>>0<h>>>0?b+1|0:b;g=g^h;b=b^h;h=b^2147483647;if((h|0)==(e|0)&(g^-1)>>>0<a>>>0|e>>>0>h>>>0){break o}b=b+e|0;a=a+g|0;b=a>>>0<g>>>0?b+1|0:b;n=a;d=b;if(!b&a>>>0<536870913){break m}}b=d>>>29|0;a=(d&536870911)<<3|n>>>29}j=ai(j,m,a,b);o=ai(o,l,a,b);q=ai(q,r,a,b)}D[c+8>>2]=j;D[c+4>>2]=o;D[c>>2]=q;$=f+96|0;return}ua();T()}ua();T()}ua();T()}function Bb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=K(0),k=0;a:{b:{if(!d){break b}c:{d:{switch(D[a+28>>2]-1|0){case 0:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}C[(g<<1)+d>>1]=B[b|0];b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 1:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}C[(g<<1)+d>>1]=E[b|0];b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 2:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}C[(g<<1)+d>>1]=F[b>>1];b=b+2|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 3:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=C[b>>1];if((e|0)<0){break b}C[(g<<1)+d>>1]=e;b=b+2|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}h=1;if((c|0)>(e|0)){break c}break b;case 4:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b>>2];if(e-32768>>>0<4294901760){break a}C[(g<<1)+d>>1]=e;b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 5:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b>>2];if(e>>>0>32767){break a}C[(g<<1)+d>>1]=e;b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 6:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}i=D[b>>2];e=D[b+4>>2]-(i>>>0<32768)|0;if((e|0)==-1&i-32768>>>0<4294901760|(e|0)!=-1){break a}C[(g<<1)+d>>1]=i;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 7:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b+4>>2];i=D[b>>2];if(!e&i>>>0>32767|e){break a}C[(g<<1)+d>>1]=i;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 8:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}i=(g<<1)+d|0;j=H[b>>2];e:{if(K(L(j))<K(2147483648)){e=~~j;break e}e=-2147483648}C[i>>1]=e;b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 9:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}i=(g<<1)+d|0;k=I[b>>3];f:{if(L(k)<2147483648){e=~~k;break f}e=-2147483648}C[i>>1]=e;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 10:break d;default:break b}}h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}C[(g<<1)+d>>1]=E[b|0];b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)<=(e|0)){break b}}pa((e<<1)+d|0,0,c-e<<1)}return h}return 0}function Ab(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=K(0),k=0;a:{b:{if(!d){break b}c:{d:{switch(D[a+28>>2]-1|0){case 0:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=B[b|0];if((e|0)<0){break b}C[(g<<1)+d>>1]=e&255;b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}h=1;if((c|0)>(e|0)){break c}break b;case 1:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}C[(g<<1)+d>>1]=E[b|0];b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 2:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=C[b>>1];if((e|0)<0){break b}C[(g<<1)+d>>1]=e;b=b+2|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}h=1;if((c|0)>(e|0)){break c}break b;case 3:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}C[(g<<1)+d>>1]=F[b>>1];b=b+2|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 4:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b>>2];if(e>>>0>65535){break a}C[(g<<1)+d>>1]=e;b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 5:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b>>2];if(e>>>0>65535){break a}C[(g<<1)+d>>1]=e;b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 6:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b+4>>2];i=D[b>>2];if(!e&i>>>0>65535|e){break a}C[(g<<1)+d>>1]=i;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 7:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b+4>>2];i=D[b>>2];if(!e&i>>>0>65535|e){break a}C[(g<<1)+d>>1]=i;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 8:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}i=(g<<1)+d|0;j=H[b>>2];e:{if(j<K(4294967296)&j>=K(0)){e=~~j>>>0;break e}e=0}C[i>>1]=e;b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 9:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}i=(g<<1)+d|0;k=I[b>>3];f:{if(k<4294967296&k>=0){e=~~k>>>0;break f}e=0}C[i>>1]=e;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 10:break d;default:break b}}h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}C[(g<<1)+d>>1]=E[b|0];b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)<=(e|0)){break b}}pa((e<<1)+d|0,0,c-e<<1)}return h}return 0}function ec(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0;e=$-48|0;$=e;d=F[5069]|F[5070]<<16;f=F[5067]|F[5068]<<16;C[e+38>>1]=f;C[e+40>>1]=f>>>16;C[e+42>>1]=d;C[e+44>>1]=d>>>16;d=D[2533];D[e+32>>2]=D[2532];D[e+36>>2]=d;d=D[2531];D[e+24>>2]=D[2530];D[e+28>>2]=d;d=D[2529];D[e+16>>2]=D[2528];D[e+20>>2]=d;f=D[b+12>>2];d=D[b+20>>2];g=D[b+16>>2];h=g+5|0;d=h>>>0<5?d+1|0:d;a:{b:{if(h>>>0>G[b+8>>2]&(d|0)>=(f|0)|(d|0)>(f|0)){b=za(e+16|0);if(b>>>0>=4294967280){break a}c:{d:{if(b>>>0>=11){d=b+16&-16;c=na(d);D[e+8>>2]=d|-2147483648;D[e>>2]=c;D[e+4>>2]=b;break d}B[e+11|0]=b;c=e;if(!b){break c}}oa(c,e+16|0,b)}B[b+c|0]=0;D[a>>2]=-2;a=a+4|0;if(B[e+11|0]>=0){b=D[e+4>>2];D[a>>2]=D[e>>2];D[a+4>>2]=b;D[a+8>>2]=D[e+8>>2];break b}b=a;a=D[e>>2];ta(b,a,D[e+4>>2]);ma(a);break b}d=g+D[b>>2]|0;f=E[d|0]|E[d+1|0]<<8|(E[d+2|0]<<16|E[d+3|0]<<24);B[c|0]=f;B[c+1|0]=f>>>8;B[c+2|0]=f>>>16;B[c+3|0]=f>>>24;B[c+4|0]=E[d+4|0];d=D[b+20>>2];f=D[b+16>>2]+5|0;d=f>>>0<5?d+1|0:d;D[b+16>>2]=f;D[b+20>>2]=d;if(va(c,1394,5)){b=na(32);B[b+16|0]=E[1638];c=E[1634]|E[1635]<<8|(E[1636]<<16|E[1637]<<24);d=E[1630]|E[1631]<<8|(E[1632]<<16|E[1633]<<24);B[b+8|0]=d;B[b+9|0]=d>>>8;B[b+10|0]=d>>>16;B[b+11|0]=d>>>24;B[b+12|0]=c;B[b+13|0]=c>>>8;B[b+14|0]=c>>>16;B[b+15|0]=c>>>24;c=E[1626]|E[1627]<<8|(E[1628]<<16|E[1629]<<24);d=E[1622]|E[1623]<<8|(E[1624]<<16|E[1625]<<24);B[b|0]=d;B[b+1|0]=d>>>8;B[b+2|0]=d>>>16;B[b+3|0]=d>>>24;B[b+4|0]=c;B[b+5|0]=c>>>8;B[b+6|0]=c>>>16;B[b+7|0]=c>>>24;B[b+17|0]=0;D[a>>2]=-1;ta(a+4|0,b,17);ma(b);break b}g=D[b+12>>2];if((g|0)<=(d|0)&G[b+8>>2]<=f>>>0|(d|0)>(g|0)){b=za(e+16|0);if(b>>>0>=4294967280){break a}e:{f:{if(b>>>0>=11){d=b+16&-16;c=na(d);D[e+8>>2]=d|-2147483648;D[e>>2]=c;D[e+4>>2]=b;break f}B[e+11|0]=b;c=e;if(!b){break e}}oa(c,e+16|0,b)}B[b+c|0]=0;D[a>>2]=-2;a=a+4|0;if(B[e+11|0]>=0){b=D[e+4>>2];D[a>>2]=D[e>>2];D[a+4>>2]=b;D[a+8>>2]=D[e+8>>2];break b}b=a;a=D[e>>2];ta(b,a,D[e+4>>2]);ma(a);break b}B[c+5|0]=E[f+D[b>>2]|0];d=D[b+20>>2];f=D[b+16>>2]+1|0;d=f?d:d+1|0;D[b+16>>2]=f;D[b+20>>2]=d;g=D[b+12>>2];if((g|0)<=(d|0)&G[b+8>>2]<=f>>>0|(d|0)>(g|0)){b=za(e+16|0);if(b>>>0>=4294967280){break a}g:{h:{if(b>>>0>=11){d=b+16&-16;c=na(d);D[e+8>>2]=d|-2147483648;D[e>>2]=c;D[e+4>>2]=b;break h}B[e+11|0]=b;c=e;if(!b){break g}}oa(c,e+16|0,b)}B[b+c|0]=0;D[a>>2]=-2;a=a+4|0;if(B[e+11|0]>=0){b=D[e+4>>2];D[a>>2]=D[e>>2];D[a+4>>2]=b;D[a+8>>2]=D[e+8>>2];break b}b=a;a=D[e>>2];ta(b,a,D[e+4>>2]);ma(a);break b}B[c+6|0]=E[f+D[b>>2]|0];d=D[b+20>>2];f=D[b+16>>2]+1|0;d=f?d:d+1|0;D[b+16>>2]=f;D[b+20>>2]=d;g=D[b+12>>2];if((g|0)<=(d|0)&G[b+8>>2]<=f>>>0|(d|0)>(g|0)){b=za(e+16|0);if(b>>>0>=4294967280){break a}i:{j:{if(b>>>0>=11){d=b+16&-16;c=na(d);D[e+8>>2]=d|-2147483648;D[e>>2]=c;D[e+4>>2]=b;break j}B[e+11|0]=b;c=e;if(!b){break i}}oa(c,e+16|0,b)}B[b+c|0]=0;D[a>>2]=-2;a=a+4|0;if(B[e+11|0]>=0){b=D[e+4>>2];D[a>>2]=D[e>>2];D[a+4>>2]=b;D[a+8>>2]=D[e+8>>2];break b}b=a;a=D[e>>2];ta(b,a,D[e+4>>2]);ma(a);break b}B[c+7|0]=E[f+D[b>>2]|0];d=D[b+20>>2];f=D[b+16>>2]+1|0;d=f?d:d+1|0;D[b+16>>2]=f;D[b+20>>2]=d;g=D[b+12>>2];if((g|0)<=(d|0)&G[b+8>>2]<=f>>>0|(d|0)>(g|0)){b=Fb(e,e+16|0);D[a>>2]=-2;a=a+4|0;if(B[b+11|0]>=0){b=D[e+4>>2];D[a>>2]=D[e>>2];D[a+4>>2]=b;D[a+8>>2]=D[e+8>>2];break b}ta(a,D[b>>2],D[b+4>>2]);if(B[b+11|0]>=0){break b}ma(D[b>>2]);break b}B[c+8|0]=E[f+D[b>>2]|0];d=D[b+20>>2];f=d;i=D[b+16>>2];g=i+1|0;d=g?d:d+1|0;D[b+16>>2]=g;D[b+20>>2]=d;h=D[b+12>>2];d=f;f=i+3|0;d=f>>>0<3?d+1|0:d;if(f>>>0>G[b+8>>2]&(d|0)>=(h|0)|(d|0)>(h|0)){b=Fb(e,e+16|0);D[a>>2]=-2;a=a+4|0;if(B[b+11|0]>=0){b=D[e+4>>2];D[a>>2]=D[e>>2];D[a+4>>2]=b;D[a+8>>2]=D[e+8>>2];break b}ta(a,D[b>>2],D[b+4>>2]);if(B[b+11|0]>=0){break b}ma(D[b>>2]);break b}f=c;c=g+D[b>>2]|0;C[f+10>>1]=E[c|0]|E[c+1|0]<<8;d=D[b+20>>2];c=D[b+16>>2]+2|0;d=c>>>0<2?d+1|0:d;D[b+16>>2]=c;D[b+20>>2]=d;D[a+8>>2]=0;D[a+12>>2]=0;D[a>>2]=0;D[a+4>>2]=0}$=e+48|0;return}Aa();T()}function zb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=K(0),k=0;a:{b:{if(!d){break b}c:{d:{switch(D[a+28>>2]-1|0){case 0:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=B[b|0];b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 1:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=E[b|0];b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 2:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=C[b>>1];b=b+2|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 3:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=F[b>>1];b=b+2|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 4:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=D[b>>2];b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 5:e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b>>2];if((e|0)<0){break b}D[(g<<2)+d>>2]=e;b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}h=1;if((c|0)>(e|0)){break c}break b;case 6:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b>>2];if((D[b+4>>2]-(e>>>0<2147483648)|0)!=-1){break a}D[(g<<2)+d>>2]=e;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 7:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b+4>>2];i=D[b>>2];if(!e&i>>>0>2147483647|e){break a}D[(g<<2)+d>>2]=i;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 8:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}i=(g<<2)+d|0;j=H[b>>2];e:{if(K(L(j))<K(2147483648)){e=~~j;break e}e=-2147483648}D[i>>2]=e;b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 9:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}i=(g<<2)+d|0;k=I[b>>3];f:{if(L(k)<2147483648){e=~~k;break f}e=-2147483648}D[i>>2]=e;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 10:break d;default:break b}}h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=E[b|0];b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)<=(e|0)){break b}}pa((e<<2)+d|0,0,c-e<<2)}return h}return 0}function yb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=K(0),k=0;a:{b:{if(!d){break b}c:{d:{switch(D[a+28>>2]-1|0){case 0:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=B[b|0];b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 1:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=E[b|0];b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 2:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=C[b>>1];b=b+2|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 3:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=F[b>>1];b=b+2|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 4:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=D[b>>2];b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 5:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=D[b>>2];b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 6:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b>>2];if(D[b+4>>2]){break a}D[(g<<2)+d>>2]=e;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 7:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}e=D[b>>2];if(D[b+4>>2]){break a}D[(g<<2)+d>>2]=e;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 8:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}i=(g<<2)+d|0;j=H[b>>2];e:{if(j<K(4294967296)&j>=K(0)){e=~~j>>>0;break e}e=0}D[i>>2]=e;b=b+4|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 9:h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}i=(g<<2)+d|0;k=I[b>>3];f:{if(k<4294967296&k>=0){e=~~k>>>0;break f}e=0}D[i>>2]=e;b=b+8|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)>(e|0)){break c}break b;case 10:break d;default:break b}}h=1;e=B[a+24|0];if((((c|0)<(e|0)?c:e)|0)>0){e=D[a>>2];f=D[e>>2];b=D[a+48>>2]+$h(D[a+40>>2],D[a+44>>2],b,0)|0;b=f+b|0;f=D[e+4>>2];while(1){if(b>>>0>=f>>>0){break a}D[(g<<2)+d>>2]=E[b|0];b=b+1|0;g=g+1|0;e=B[a+24|0];if((g|0)<(((c|0)<(e|0)?c:e)|0)){continue}break}}if((c|0)<=(e|0)){break b}}pa((e<<2)+d|0,0,c-e<<2)}return h}return 0}function oe(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;k=$-16|0;$=k;n=1;o=ba[D[D[a>>2]+24>>2]](a)|0;a:{if((o|0)<=0){n=0;break a}q=a+48|0;while(1){b:{c:{if(!D[(ba[D[D[a>>2]+28>>2]](a)|0)+40>>2]){break c}e=l<<2;b=D[e+D[a+36>>2]>>2];d=D[b+8>>2];g=cb(b);if(!g){break c}b=D[(ba[D[D[a>>2]+28>>2]](a)|0)+40>>2];h=D[d+56>>2];c=na(32);D[k>>2]=c;D[k+4>>2]=24;D[k+8>>2]=-2147483616;B[c+24|0]=0;f=E[1308]|E[1309]<<8|(E[1310]<<16|E[1311]<<24);d=E[1304]|E[1305]<<8|(E[1306]<<16|E[1307]<<24);B[c+16|0]=d;B[c+17|0]=d>>>8;B[c+18|0]=d>>>16;B[c+19|0]=d>>>24;B[c+20|0]=f;B[c+21|0]=f>>>8;B[c+22|0]=f>>>16;B[c+23|0]=f>>>24;f=E[1300]|E[1301]<<8|(E[1302]<<16|E[1303]<<24);d=E[1296]|E[1297]<<8|(E[1298]<<16|E[1299]<<24);B[c+8|0]=d;B[c+9|0]=d>>>8;B[c+10|0]=d>>>16;B[c+11|0]=d>>>24;B[c+12|0]=f;B[c+13|0]=f>>>8;B[c+14|0]=f>>>16;B[c+15|0]=f>>>24;f=E[1292]|E[1293]<<8|(E[1294]<<16|E[1295]<<24);d=E[1288]|E[1289]<<8|(E[1290]<<16|E[1291]<<24);B[c|0]=d;B[c+1|0]=d>>>8;B[c+2|0]=d>>>16;B[c+3|0]=d>>>24;B[c+4|0]=f;B[c+5|0]=f>>>8;B[c+6|0]=f>>>16;B[c+7|0]=f>>>24;f=b+16|0;j=f;i=D[f>>2];d:{if(!i){break d}while(1){d=(h|0)>D[i+16>>2];j=d?j:i;i=D[(d<<2)+i>>2];if(i){continue}break}if((f|0)==(j|0)|(h|0)<D[j+16>>2]){break d}i=D[j+24>>2];if(!i){break d}d=j+20|0;while(1){f=E[i+27|0];h=f<<24>>24<0;m=h?D[i+20>>2]:f;p=m>>>0<24;e:{f:{j=p?m:24;g:{if(j){f=i+16|0;h=h?D[f>>2]:f;f=va(c,h,j);h:{if(!f){if(m>>>0<=24){break h}break e}if((f|0)<0){break e}}f=va(h,c,j);if(!f){break g}if((f|0)<0){break f}b=d;break d}if(m>>>0>24){break e}}if(p){break f}b=d;break d}i=i+4|0}i=D[i>>2];if(i){continue}break}}i=0;d=b+4|0;b=_a(b,k);i:{if((d|0)==(b|0)){break i}d=B[b+39|0]<0?D[b+28>>2]:b+28|0;j=0;f=0;while(1){b=d;d=b+1|0;c=B[b|0];if((c|0)==32|c-9>>>0<5){continue}break}j:{k:{l:{c=B[b|0];switch(c-43|0){case 0:break k;case 2:break l;default:break j}}f=1}c=B[d|0];b=d}if(c-48>>>0<10){while(1){j=(J(j,10)-B[b|0]|0)+48|0;d=B[b+1|0];b=b+1|0;if(d-48>>>0<10){continue}break}}b=f?j:0-j|0;if((b|0)==-1){break i}i=(b|0)!=0}if(B[k+11|0]<0){ma(D[k>>2])}if(!i){break c}e=D[D[D[a+36>>2]+e>>2]+8>>2];if(!D[e+64>>2]){b=na(32);c=b;D[b+16>>2]=0;D[b+20>>2]=0;D[b+8>>2]=0;D[b>>2]=0;D[b+4>>2]=0;D[b+24>>2]=0;D[b+28>>2]=0;d=D[e+64>>2];D[e+64>>2]=b;if(d){b=D[d>>2];if(b){D[d+4>>2]=b;ma(b)}ma(d);c=D[e+64>>2]}D[e>>2]=c;b=D[c+20>>2];D[e+8>>2]=D[c+16>>2];D[e+12>>2]=b;d=D[c+24>>2];b=D[c+28>>2];D[e+48>>2]=0;D[e+52>>2]=0;D[e+40>>2]=0;D[e+44>>2]=0;D[e+16>>2]=d;D[e+20>>2]=b}m:{B[e+24|0]=E[g+24|0];D[e+28>>2]=D[g+28>>2];B[e+32|0]=E[g+32|0];b=D[g+44>>2];D[e+40>>2]=D[g+40>>2];D[e+44>>2]=b;b=D[g+52>>2];D[e+48>>2]=D[g+48>>2];D[e+52>>2]=b;D[e+56>>2]=D[g+56>>2];b=D[g+12>>2];D[e+8>>2]=D[g+8>>2];D[e+12>>2]=b;b=D[g+20>>2];D[e+16>>2]=D[g+16>>2];D[e+20>>2]=b;D[e+60>>2]=D[g+60>>2];d=D[g>>2];n:{if(!d){D[e>>2]=0;c=1;break n}b=D[e>>2];c=0;if(!b){break n}c=b;b=D[d>>2];id(c,b,D[d+4>>2]-b|0,0);c=1}if(!c){break m}B[e+84|0]=E[g+84|0];D[e+80>>2]=D[g+80>>2];if((e|0)!=(g|0)){fb(e+68|0,D[g+68>>2],D[g+72>>2])}o:{h=D[g+88>>2];p:{if(h){b=na(40);d=D[h>>2];D[b+16>>2]=0;D[b+8>>2]=0;D[b+12>>2]=0;D[b>>2]=d;c=D[h+12>>2]-D[h+8>>2]|0;if(c){if((c|0)<0){break o}d=na(c);D[b+8>>2]=d;D[b+12>>2]=d;D[b+16>>2]=c+d;c=D[h+8>>2];f=D[h+12>>2]-c|0;if((f|0)>0){d=oa(d,c,f)+f|0}D[b+12>>2]=d}d=D[h+36>>2];D[b+32>>2]=D[h+32>>2];D[b+36>>2]=d;d=D[h+28>>2];D[b+24>>2]=D[h+24>>2];D[b+28>>2]=d;c=D[e+88>>2];D[e+88>>2]=b;if(c){break p}break m}c=D[e+88>>2];D[e+88>>2]=0;if(!c){break m}}b=D[c+8>>2];if(b){D[c+12>>2]=b;ma(b)}ma(c);break m}qa();T()}break b}b=D[D[a+36>>2]+(l<<2)>>2];if(!(ba[D[D[b>>2]+24>>2]](b,q)|0)){break a}}l=l+1|0;n=(o|0)>(l|0);if((l|0)!=(o|0)){continue}break}}$=k+16|0;return(n^-1)&1}function ih(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=K(0),f=0,g=0,h=0,i=0,j=0,k=K(0),l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;if(D[c>>2]==D[c+4>>2]){g=D[d+80>>2];v=$-16|0;$=v;j=D[a+4>>2];h=D[d+48>>2];d=D[D[d>>2]>>2];i=B[b+24|0];c=v+8|0;D[c>>2]=1065353216;u=c;H[c>>2]=K(-1<<j^-1)/H[a+20>>2];w=na((i|0)!=(i&1073741823)?-1:i<<2);a:{if(!g|(i|0)<=0){break a}s=d+h|0;y=D[b>>2];c=D[b+48>>2];z=D[b+44>>2];x=D[b+40>>2];if(!E[b+84|0]){n=D[b+68>>2];t=i&-2;j=i&1;b=0;while(1){f=D[y>>2];d=$h(x,z,D[n+(o<<2)>>2],0)+c|0;p=oa(w,f+d|0,x);k=H[u>>2];l=D[a+8>>2];d=0;m=0;if((i|0)!=1){while(1){f=s+(b<<2)|0;q=d<<2;e=K(P(K(K(k*K(H[q+p>>2]-H[l+q>>2]))+K(.5))));b:{if(K(L(e))<K(2147483648)){h=~~e;break b}h=-2147483648}D[f>>2]=h;h=q|4;e=K(P(K(K(k*K(H[h+p>>2]-H[l+h>>2]))+K(.5))));c:{if(K(L(e))<K(2147483648)){h=~~e;break c}h=-2147483648}D[f+4>>2]=h;d=d+2|0;b=b+2|0;m=m+2|0;if((t|0)!=(m|0)){continue}break}}if(j){f=s+(b<<2)|0;d=d<<2;e=K(P(K(K(k*K(H[d+p>>2]-H[d+l>>2]))+K(.5))));d:{if(K(L(e))<K(2147483648)){d=~~e;break d}d=-2147483648}D[f>>2]=d;b=b+1|0}o=o+1|0;if((g|0)!=(o|0)){continue}break}break a}n=i&-2;t=i&1;b=0;while(1){h=D[y>>2];d=$h(x,z,o,f)+c|0;p=oa(w,h+d|0,x);k=H[u>>2];l=D[a+8>>2];d=0;m=0;if((i|0)!=1){while(1){h=s+(b<<2)|0;q=d<<2;e=K(P(K(K(k*K(H[q+p>>2]-H[l+q>>2]))+K(.5))));e:{if(K(L(e))<K(2147483648)){j=~~e;break e}j=-2147483648}D[h>>2]=j;j=q|4;e=K(P(K(K(k*K(H[j+p>>2]-H[l+j>>2]))+K(.5))));f:{if(K(L(e))<K(2147483648)){j=~~e;break f}j=-2147483648}D[h+4>>2]=j;d=d+2|0;b=b+2|0;m=m+2|0;if((n|0)!=(m|0)){continue}break}}if(t){h=s+(b<<2)|0;d=d<<2;e=K(P(K(K(k*K(H[d+p>>2]-H[d+l>>2]))+K(.5))));g:{if(K(L(e))<K(2147483648)){d=~~e;break g}d=-2147483648}D[h>>2]=d;b=b+1|0}d=o+1|0;f=d?f:f+1|0;o=d;if((g|0)!=(d|0)|f){continue}break}}ma(w);$=v+16|0;return 1}v=$-16|0;$=v;u=D[a+4>>2];g=D[d+48>>2];f=D[D[d>>2]>>2];r=B[b+24|0];d=v+8|0;D[d>>2]=1065353216;h=d;H[d>>2]=K(-1<<u^-1)/H[a+20>>2];w=na((r|0)!=(r&1073741823)?-1:r<<2);y=D[c>>2];c=D[c+4>>2]-y|0;h:{if(!c|(r|0)<=0){break h}s=f+g|0;z=D[b>>2];d=D[b+48>>2];x=D[b+44>>2];o=D[b+40>>2];c=c>>2;q=c>>>0>1?c:1;if(E[b+84|0]){j=r&-2;u=r&1;b=0;while(1){f=D[z>>2];c=$h(o,x,D[y+(m<<2)>>2],0)+d|0;l=oa(w,f+c|0,o);k=H[h>>2];n=D[a+8>>2];c=0;i=0;if((r|0)!=1){while(1){f=s+(b<<2)|0;t=c<<2;e=K(P(K(K(k*K(H[t+l>>2]-H[n+t>>2]))+K(.5))));i:{if(K(L(e))<K(2147483648)){g=~~e;break i}g=-2147483648}D[f>>2]=g;g=t|4;e=K(P(K(K(k*K(H[g+l>>2]-H[n+g>>2]))+K(.5))));j:{if(K(L(e))<K(2147483648)){g=~~e;break j}g=-2147483648}D[f+4>>2]=g;c=c+2|0;b=b+2|0;i=i+2|0;if((j|0)!=(i|0)){continue}break}}if(u){f=s+(b<<2)|0;c=c<<2;e=K(P(K(K(k*K(H[c+l>>2]-H[c+n>>2]))+K(.5))));k:{if(K(L(e))<K(2147483648)){c=~~e;break k}c=-2147483648}D[f>>2]=c;b=b+1|0}m=m+1|0;if((q|0)!=(m|0)){continue}break}break h}t=D[b+68>>2];j=r&-2;u=r&1;b=0;while(1){f=D[z>>2];c=$h(o,x,D[t+(D[y+(m<<2)>>2]<<2)>>2],0)+d|0;p=oa(w,f+c|0,o);k=H[h>>2];l=D[a+8>>2];c=0;i=0;if((r|0)!=1){while(1){f=s+(b<<2)|0;n=c<<2;e=K(P(K(K(k*K(H[n+p>>2]-H[l+n>>2]))+K(.5))));l:{if(K(L(e))<K(2147483648)){g=~~e;break l}g=-2147483648}D[f>>2]=g;g=n|4;e=K(P(K(K(k*K(H[g+p>>2]-H[l+g>>2]))+K(.5))));m:{if(K(L(e))<K(2147483648)){g=~~e;break m}g=-2147483648}D[f+4>>2]=g;c=c+2|0;b=b+2|0;i=i+2|0;if((j|0)!=(i|0)){continue}break}}if(u){f=s+(b<<2)|0;c=c<<2;e=K(P(K(K(k*K(H[c+p>>2]-H[c+l>>2]))+K(.5))));n:{if(K(L(e))<K(2147483648)){c=~~e;break n}c=-2147483648}D[f>>2]=c;b=b+1|0}m=m+1|0;if((q|0)!=(m|0)){continue}break}}ma(w);$=v+16|0;return 1}function nh(a){a=a|0;var b=0,c=0,d=0,e=0,f=0;c=D[a+32>>2];e=D[c+16>>2];d=D[c+12>>2];b=D[c+20>>2];if(G[c+8>>2]>e>>>0&(d|0)>=(b|0)|(b|0)<(d|0)){f=E[e+D[c>>2]|0];d=e+1|0;b=d?b:b+1|0;D[c+16>>2]=d;D[c+20>>2]=b;c=D[a+48>>2];D[a+48>>2]=0;if(c){ba[D[D[c>>2]+4>>2]](c)}a:{b:{c:{d:{switch(f|0){case 0:c=na(384);D[c>>2]=8312;pa(c+4|0,0,80);D[c+96>>2]=0;D[c+100>>2]=0;D[c+92>>2]=-1;D[c+84>>2]=-1;D[c+88>>2]=-1;D[c+104>>2]=0;D[c+108>>2]=0;D[c+112>>2]=0;D[c+116>>2]=0;D[c+120>>2]=0;D[c+124>>2]=0;D[c+128>>2]=0;D[c+132>>2]=0;D[c+136>>2]=0;D[c+140>>2]=0;D[c+144>>2]=0;D[c+148>>2]=0;D[c+156>>2]=0;D[c+160>>2]=0;D[c+152>>2]=1065353216;D[c+164>>2]=0;D[c+168>>2]=0;D[c+172>>2]=0;D[c+176>>2]=0;D[c+180>>2]=0;D[c+184>>2]=0;D[c+188>>2]=0;D[c+192>>2]=0;D[c+196>>2]=0;D[c+200>>2]=0;D[c+204>>2]=0;D[c+208>>2]=0;D[c+212>>2]=-1;D[c+216>>2]=0;D[c+220>>2]=0;D[c+224>>2]=0;b=c+232|0;C[b+38>>1]=0;D[b>>2]=0;D[b+8>>2]=0;D[b+12>>2]=0;D[b+16>>2]=0;D[b+20>>2]=0;D[b+24>>2]=0;D[b+28>>2]=0;B[b+29|0]=0;B[b+30|0]=0;B[b+31|0]=0;B[b+32|0]=0;B[b+33|0]=0;B[b+34|0]=0;B[b+35|0]=0;B[b+36|0]=0;b=c+272|0;C[b+38>>1]=0;D[b>>2]=0;D[b+8>>2]=0;D[b+12>>2]=0;D[b+16>>2]=0;D[b+20>>2]=0;D[b+24>>2]=0;D[b+28>>2]=0;B[b+29|0]=0;B[b+30|0]=0;B[b+31|0]=0;B[b+32|0]=0;B[b+33|0]=0;B[b+34|0]=0;B[b+35|0]=0;B[b+36|0]=0;b=c+312|0;D[b>>2]=0;D[b+4>>2]=0;B[b+5|0]=0;B[b+6|0]=0;B[b+7|0]=0;B[b+8|0]=0;B[b+9|0]=0;B[b+10|0]=0;B[b+11|0]=0;B[b+12|0]=0;b=c+328|0;C[b+38>>1]=0;D[b>>2]=0;D[b+8>>2]=0;D[b+12>>2]=0;D[b+16>>2]=0;D[b+20>>2]=0;D[b+24>>2]=0;D[b+28>>2]=0;B[b+29|0]=0;B[b+30|0]=0;B[b+31|0]=0;B[b+32|0]=0;B[b+33|0]=0;B[b+34|0]=0;B[b+35|0]=0;B[b+36|0]=0;D[c+376>>2]=0;D[c+368>>2]=0;D[c+372>>2]=0;break c;case 2:break d;default:break b}}c=na(440);D[c>>2]=8364;pa(c+4|0,0,80);D[c+96>>2]=0;D[c+100>>2]=0;D[c+92>>2]=-1;D[c+84>>2]=-1;D[c+88>>2]=-1;D[c+104>>2]=0;D[c+108>>2]=0;D[c+112>>2]=0;D[c+116>>2]=0;D[c+120>>2]=0;D[c+124>>2]=0;D[c+128>>2]=0;D[c+132>>2]=0;D[c+136>>2]=0;D[c+140>>2]=0;D[c+144>>2]=0;D[c+148>>2]=0;D[c+156>>2]=0;D[c+160>>2]=0;D[c+152>>2]=1065353216;D[c+164>>2]=0;D[c+168>>2]=0;D[c+172>>2]=0;D[c+176>>2]=0;D[c+180>>2]=0;D[c+184>>2]=0;D[c+188>>2]=0;D[c+192>>2]=0;D[c+196>>2]=0;D[c+200>>2]=0;D[c+204>>2]=0;D[c+208>>2]=0;D[c+212>>2]=-1;D[c+216>>2]=0;D[c+220>>2]=0;D[c+224>>2]=0;b=c+232|0;C[b+38>>1]=0;D[b>>2]=0;D[b+8>>2]=0;D[b+12>>2]=0;D[b+16>>2]=0;D[b+20>>2]=0;D[b+24>>2]=0;D[b+28>>2]=0;B[b+29|0]=0;B[b+30|0]=0;B[b+31|0]=0;B[b+32|0]=0;B[b+33|0]=0;B[b+34|0]=0;B[b+35|0]=0;B[b+36|0]=0;b=c+272|0;C[b+38>>1]=0;D[b>>2]=0;D[b+8>>2]=0;D[b+12>>2]=0;D[b+16>>2]=0;D[b+20>>2]=0;D[b+24>>2]=0;D[b+28>>2]=0;B[b+29|0]=0;B[b+30|0]=0;B[b+31|0]=0;B[b+32|0]=0;B[b+33|0]=0;B[b+34|0]=0;B[b+35|0]=0;B[b+36|0]=0;b=c+312|0;D[b>>2]=0;D[b+4>>2]=0;B[b+5|0]=0;B[b+6|0]=0;B[b+7|0]=0;B[b+8|0]=0;B[b+9|0]=0;B[b+10|0]=0;B[b+11|0]=0;B[b+12|0]=0;b=c+328|0;C[b+38>>1]=0;D[b>>2]=0;D[b+8>>2]=0;D[b+12>>2]=0;D[b+16>>2]=0;D[b+20>>2]=0;D[b+24>>2]=0;D[b+28>>2]=0;B[b+29|0]=0;B[b+30|0]=0;B[b+31|0]=0;B[b+32|0]=0;B[b+33|0]=0;B[b+34|0]=0;B[b+35|0]=0;B[b+36|0]=0;D[c+392>>2]=0;D[c+396>>2]=0;D[c+384>>2]=0;D[c+388>>2]=0;D[c+376>>2]=0;D[c+380>>2]=0;D[c+368>>2]=0;D[c+372>>2]=0;D[c+416>>2]=0;D[c+420>>2]=0;D[c+408>>2]=2;D[c+412>>2]=7;D[c+400>>2]=-1;D[c+404>>2]=-1;D[c+424>>2]=0;D[c+428>>2]=0;D[c+432>>2]=0;D[c+436>>2]=0}b=D[a+48>>2];D[a+48>>2]=c;if(!b){break a}ba[D[D[b>>2]+4>>2]](b)}c=D[a+48>>2];if(c){break a}return 0}a=ba[D[D[c>>2]+8>>2]](c,a)|0}else{a=0}return a|0}function Gd(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;e=$+-64|0;$=e;D[e+48>>2]=0;D[e+40>>2]=0;D[e+44>>2]=0;D[e+32>>2]=0;D[e+36>>2]=0;D[e+24>>2]=0;D[e+28>>2]=0;D[e+16>>2]=0;D[e+20>>2]=0;D[e+8>>2]=0;D[e+12>>2]=0;D[e>>2]=0;D[e+4>>2]=0;h=b;a:{b:{if(!F[b+38>>1]){break b}if(!Sa(1,e+12|0,h)){break b}l=D[e+12>>2];b=D[e>>2];d=D[e+4>>2]-b>>2;c:{if(l>>>0>d>>>0){sa(e,l-d|0);l=D[e+12>>2];break c}if(d>>>0<=l>>>0){break c}D[e+4>>2]=b+(l<<2)}f=1;if(!l){break a}p=D[h+8>>2];q=D[h+12>>2];o=D[e>>2];while(1){d=D[h+20>>2];j=D[h+16>>2];f=0;if((q|0)<=(d|0)&p>>>0<=j>>>0|(d|0)>(q|0)){break a}m=D[h>>2];r=E[m+j|0];b=j+1|0;d=b?d:d+1|0;f=b;D[h+16>>2]=b;D[h+20>>2]=d;b=r>>>2|0;i=0;d:{e:{f:{g:{n=r&3;switch(n|0){case 3:break g;case 0:break e;default:break f}}b=b+g|0;f=0;if(b>>>0>=l>>>0){break a}pa(o+(g<<2)|0,0,(r&252)+4|0);g=b;break d}while(1){if((d|0)>=(q|0)&f>>>0>=p>>>0|(d|0)>(q|0)){break b}j=E[f+m|0];f=f+1|0;d=f?d:d+1|0;D[h+16>>2]=f;D[h+20>>2]=d;b=j<<(i<<3|6)|b;i=i+1|0;if((n|0)!=(i|0)){continue}break}}D[o+(g<<2)>>2]=b}g=g+1|0;l=D[e+12>>2];if(g>>>0<l>>>0){continue}break}p=e+16|0;m=D[e>>2];f=D[e+16>>2];d=D[e+20>>2]-f|0;b=d>>2;h:{if(b>>>0<=1048575){sa(p,1048576-b|0);break h}if((d|0)==4194304){break h}D[e+20>>2]=f+4194304}d=e+28|0;g=D[d>>2];b=D[e+32>>2]-g>>3;i:{if(b>>>0<l>>>0){bb(d,l-b|0);g=D[d>>2];break i}if(b>>>0>l>>>0){D[e+32>>2]=(l<<3)+g}if(!l){break b}}i=0;d=0;while(1){n=(i<<2)+m|0;j=D[n>>2];f=(i<<3)+g|0;b=d;D[f+4>>2]=d;D[f>>2]=j;o=D[n>>2];d=o+d|0;if(d>>>0>1048576){break b}j:{if(b>>>0>=d>>>0){break j}n=D[p>>2];j=0;f=o&7;if(f){while(1){D[n+(b<<2)>>2]=i;b=b+1|0;j=j+1|0;if((f|0)!=(j|0)){continue}break}}if(o-1>>>0<=6){break j}while(1){f=n+(b<<2)|0;D[f>>2]=i;D[f+28>>2]=i;D[f+24>>2]=i;D[f+20>>2]=i;D[f+16>>2]=i;D[f+12>>2]=i;D[f+8>>2]=i;D[f+4>>2]=i;b=b+8|0;if((d|0)!=(b|0)){continue}break}}i=i+1|0;if((l|0)!=(i|0)){continue}break}k=(d|0)==1048576}f=k}k:{if(!f|(D[e+12>>2]?0:a)){break k}if(!Oa(1,e+56|0,h)){break k}d=D[h+8>>2];b=D[h+16>>2];j=d-b|0;g=D[e+60>>2];f=D[h+20>>2];d=D[h+12>>2]-(f+(b>>>0>d>>>0)|0)|0;k=D[e+56>>2];if((g|0)==(d|0)&j>>>0<k>>>0|d>>>0<g>>>0){break k}d=f+g|0;j=b+k|0;d=j>>>0<b>>>0?d+1|0:d;D[h+16>>2]=j;D[h+20>>2]=d;if((k|0)<=0){break k}m=b+D[h>>2]|0;D[e+40>>2]=m;b=k-1|0;f=m+b|0;d=E[f|0];l:{if(d>>>0<=63){D[e+44>>2]=b;d=E[f|0]&63;break l}m:{switch((d>>>6|0)-1|0){case 0:if(k>>>0<2){break k}b=k-2|0;D[e+44>>2]=b;d=(k+m|0)-2|0;d=E[d+1|0]<<8&16128|E[d|0];break l;case 1:if(k>>>0<3){break k}b=k-3|0;D[e+44>>2]=b;d=(k+m|0)-3|0;d=E[d+2|0]<<16&4128768|E[d+1|0]<<8|E[d|0];break l;default:break m}}b=k-4|0;D[e+44>>2]=b;d=(k+m|0)-4|0;d=E[d+2|0]<<16|E[d+3|0]<<24&1056964608|E[d+1|0]<<8|E[d|0]}g=d+4194304|0;D[e+48>>2]=g;if(g>>>0>1073741823){break k}if(!a){s=1;break k}n=D[e+28>>2];d=0;j=D[e+16>>2];while(1){n:{if(g>>>0>4194303){break n}while(1){if((b|0)<=0){break n}b=b-1|0;D[e+44>>2]=b;g=E[b+m|0]|g<<8;D[e+48>>2]=g;if(g>>>0<4194304){continue}break}}f=g&1048575;k=D[j+(f<<2)>>2];h=n+(k<<3)|0;g=(J(D[h>>2],g>>>20|0)+f|0)-D[h+4>>2]|0;D[e+48>>2]=g;D[(d<<2)+c>>2]=k;s=1;d=d+1|0;if((d|0)!=(a|0)){continue}break}}a=D[e+28>>2];if(a){D[e+32>>2]=a;ma(a)}a=D[e+16>>2];if(a){D[e+20>>2]=a;ma(a)}a=D[e>>2];if(a){D[e+4>>2]=a;ma(a)}$=e- -64|0;return s}function wd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;c=D[a+4>>2];d=D[a>>2];f=(c-d|0)/144|0;if(f>>>0<b>>>0){d=a;g=b-f|0;c=D[a+8>>2];a=D[a+4>>2];a:{if(g>>>0<=(c-a|0)/144>>>0){if(g){b=J(g,144)+a|0;while(1){D[a>>2]=-1;Uc(a+4|0);D[a+104>>2]=0;D[a+108>>2]=0;B[a+100|0]=1;D[a+112>>2]=0;D[a+116>>2]=0;D[a+120>>2]=0;D[a+124>>2]=0;D[a+128>>2]=0;D[a+132>>2]=0;D[a+136>>2]=0;D[a+140>>2]=0;a=a+144|0;if((b|0)!=(a|0)){continue}break}a=b}D[d+4>>2]=a;break a}b:{c:{d:{b=a;a=D[d>>2];f=(b-a|0)/144|0;h=f+g|0;if(h>>>0<29826162){b=(c-a|0)/144|0;a=b<<1;e=b>>>0<14913080?a>>>0<h>>>0?h:a:29826161;if(e){if(e>>>0>=29826162){break d}c=na(J(e,144))}else{c=0}b=c+J(f,144)|0;h=b+J(g,144)|0;a=b;while(1){D[a>>2]=-1;Uc(a+4|0);D[a+104>>2]=0;D[a+108>>2]=0;B[a+100|0]=1;D[a+112>>2]=0;D[a+116>>2]=0;D[a+120>>2]=0;D[a+124>>2]=0;D[a+128>>2]=0;D[a+132>>2]=0;D[a+136>>2]=0;D[a+140>>2]=0;a=a+144|0;if((h|0)!=(a|0)){continue}break}f=c+J(e,144)|0;a=D[d+4>>2];e=D[d>>2];if((a|0)==(e|0)){break c}while(1){b=b-144|0;a=a-144|0;D[b>>2]=D[a>>2];D[b+4>>2]=D[a+4>>2];D[b+8>>2]=D[a+8>>2];D[b+12>>2]=D[a+12>>2];D[a+12>>2]=0;D[a+4>>2]=0;D[a+8>>2]=0;D[b+16>>2]=D[a+16>>2];D[b+20>>2]=D[a+20>>2];D[b+24>>2]=D[a+24>>2];D[a+24>>2]=0;D[a+16>>2]=0;D[a+20>>2]=0;c=E[a+28|0];D[b+40>>2]=0;D[b+32>>2]=0;D[b+36>>2]=0;B[b+28|0]=c;D[b+32>>2]=D[a+32>>2];D[b+36>>2]=D[a+36>>2];D[b+40>>2]=D[a+40>>2];D[a+40>>2]=0;D[a+32>>2]=0;D[a+36>>2]=0;D[b+52>>2]=0;D[b+44>>2]=0;D[b+48>>2]=0;D[b+44>>2]=D[a+44>>2];D[b+48>>2]=D[a+48>>2];D[b+52>>2]=D[a+52>>2];D[a+52>>2]=0;D[a+44>>2]=0;D[a+48>>2]=0;c=b- -64|0;D[c>>2]=0;D[b+56>>2]=0;D[b+60>>2]=0;D[b+56>>2]=D[a+56>>2];D[b+60>>2]=D[a+60>>2];g=c;c=a- -64|0;D[g>>2]=D[c>>2];D[c>>2]=0;D[a+56>>2]=0;D[a+60>>2]=0;D[b+68>>2]=D[a+68>>2];c=D[a+72>>2];D[b+84>>2]=0;D[b+76>>2]=0;D[b+80>>2]=0;D[b+72>>2]=c;D[b+76>>2]=D[a+76>>2];D[b+80>>2]=D[a+80>>2];D[b+84>>2]=D[a+84>>2];D[a+84>>2]=0;D[a+76>>2]=0;D[a+80>>2]=0;D[b+96>>2]=0;D[b+88>>2]=0;D[b+92>>2]=0;D[b+88>>2]=D[a+88>>2];D[b+92>>2]=D[a+92>>2];D[b+96>>2]=D[a+96>>2];D[a+96>>2]=0;D[a+88>>2]=0;D[a+92>>2]=0;c=E[a+100|0];D[b+112>>2]=0;D[b+104>>2]=0;D[b+108>>2]=0;B[b+100|0]=c;D[b+104>>2]=D[a+104>>2];D[b+108>>2]=D[a+108>>2];D[b+112>>2]=D[a+112>>2];D[a+112>>2]=0;D[a+104>>2]=0;D[a+108>>2]=0;D[b+124>>2]=0;D[b+116>>2]=0;D[b+120>>2]=0;D[b+116>>2]=D[a+116>>2];D[b+120>>2]=D[a+120>>2];D[b+124>>2]=D[a+124>>2];D[a+124>>2]=0;D[a+116>>2]=0;D[a+120>>2]=0;c=D[a+128>>2];D[b+140>>2]=0;D[b+132>>2]=0;D[b+136>>2]=0;D[b+128>>2]=c;D[b+132>>2]=D[a+132>>2];D[b+136>>2]=D[a+136>>2];D[b+140>>2]=D[a+140>>2];D[a+140>>2]=0;D[a+132>>2]=0;D[a+136>>2]=0;if((a|0)!=(e|0)){continue}break}D[d+8>>2]=f;a=D[d+4>>2];D[d+4>>2]=h;e=D[d>>2];D[d>>2]=b;if((a|0)==(e|0)){break b}while(1){b=D[a-12>>2];if(b){D[a-8>>2]=b;ma(b)}b=D[a-28>>2];if(b){D[a-24>>2]=b;ma(b)}b=D[a-40>>2];if(b){D[a-36>>2]=b;ma(b)}ob(a-140|0);a=a-144|0;if((e|0)!=(a|0)){continue}break}break b}qa();T()}ra(1326);T()}D[d+8>>2]=f;D[d+4>>2]=h;D[d>>2]=b}if(e){ma(e)}}return}if(b>>>0<f>>>0){d=d+J(b,144)|0;if((d|0)!=(c|0)){while(1){b=D[c-12>>2];if(b){D[c-8>>2]=b;ma(b)}b=D[c-28>>2];if(b){D[c-24>>2]=b;ma(b)}b=D[c-40>>2];if(b){D[c-36>>2]=b;ma(b)}ob(c-140|0);c=c-144|0;if((d|0)!=(c|0)){continue}break}}D[a+4>>2]=d}}function Jb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0;e=$-96|0;$=e;d=D[a+16>>2];B[e+92|0]=1;D[e+88>>2]=b;D[e+84>>2]=b;D[e+80>>2]=d;a:{if((b|0)==-1){break a}m=D[a+20>>2];f=D[m>>2];d=D[D[d>>2]+(b<<2)>>2];if(d>>>0>=D[m+4>>2]-f>>2>>>0){break a}d=D[D[a+8>>2]+(D[f+(d<<2)>>2]<<2)>>2];f=D[a+4>>2];if(!E[f+84|0]){d=D[D[f+68>>2]+(d<<2)>>2]}D[e+72>>2]=0;D[e+76>>2]=0;m=e- -64|0;D[m>>2]=0;D[m+4>>2]=0;D[e+56>>2]=0;D[e+60>>2]=0;Fa(f,d,B[f+24|0],e+56|0);d=b+1|0;m=(d>>>0)%3|0?d:b-2|0;h=((b>>>0)%3|0?-1:2)+b|0;b:{c:{while(1){f=m;d=h;d:{if(!D[a+28>>2]){break d}d=b+1|0;f=(d>>>0)%3|0?d:b-2|0;d=b-1|0;if((b>>>0)%3|0){break d}d=b+2|0}if((f|0)==-1){break b}k=D[a+20>>2];b=D[k>>2];f=D[D[D[a+16>>2]>>2]+(f<<2)>>2];if(f>>>0>=D[k+4>>2]-b>>2>>>0){break b}f=D[D[a+8>>2]+(D[(f<<2)+b>>2]<<2)>>2];b=D[a+4>>2];if(!E[b+84|0]){f=D[D[b+68>>2]+(f<<2)>>2]}D[e+48>>2]=0;D[e+52>>2]=0;D[e+40>>2]=0;D[e+44>>2]=0;D[e+32>>2]=0;D[e+36>>2]=0;Fa(b,f,B[b+24|0],e+32|0);if((d|0)==-1){break c}f=D[a+20>>2];b=D[f>>2];d=D[D[D[a+16>>2]>>2]+(d<<2)>>2];if(d>>>0>=D[f+4>>2]-b>>2>>>0){break c}f=D[D[a+8>>2]+(D[b+(d<<2)>>2]<<2)>>2];b=D[a+4>>2];if(!E[b+84|0]){f=D[D[b+68>>2]+(f<<2)>>2]}D[e+24>>2]=0;D[e+28>>2]=0;D[e+16>>2]=0;D[e+20>>2]=0;D[e+8>>2]=0;D[e+12>>2]=0;Fa(b,f,B[b+24|0],e+8|0);d=D[e+8>>2];b=D[e+56>>2];f=d-b|0;k=D[e+60>>2];n=D[e+12>>2]-(k+(b>>>0>d>>>0)|0)|0;i=D[e+40>>2];d=D[e+64>>2];s=i-d|0;t=D[e+68>>2];i=D[e+44>>2]-(t+(d>>>0>i>>>0)|0)|0;u=$h(f,n,s,i);v=j-u|0;g=g-(aa+(j>>>0<u>>>0)|0)|0;w=v;j=D[e+16>>2];u=j-d|0;t=D[e+20>>2]-((d>>>0>j>>>0)+t|0)|0;j=D[e+32>>2];v=j-b|0;k=D[e+36>>2]-((b>>>0>j>>>0)+k|0)|0;d=$h(u,t,v,k);j=w+d|0;b=aa+g|0;b=d>>>0>j>>>0?b+1|0:b;g=b;w=o;p=n;b=D[e+48>>2];d=D[e+72>>2];n=b-d|0;o=D[e+76>>2];x=D[e+52>>2]-(o+(b>>>0<d>>>0)|0)|0;p=$h(f,p,n,x);f=w+p|0;b=aa+l|0;b=f>>>0<p>>>0?b+1|0:b;l=D[e+24>>2];p=l-d|0;d=D[e+28>>2]-((d>>>0>l>>>0)+o|0)|0;l=$h(p,d,v,k);o=f-l|0;l=b-(aa+(f>>>0<l>>>0)|0)|0;b=$h(u,t,n,x);f=q-b|0;b=r-(aa+(b>>>0>q>>>0)|0)|0;r=$h(p,d,s,i);q=r+f|0;b=aa+b|0;b=q>>>0<r>>>0?b+1|0:b;r=b;kc(e+80|0);b=D[e+88>>2];if((b|0)!=-1){continue}break}b=r>>31;f=b+q|0;d=b;b=b+r|0;k=f^d;h=d^(f>>>0<d>>>0?b+1|0:b);n=-1;f=2147483647;b=l>>31;i=b;d=b+o|0;b=b+l|0;b=d>>>0<i>>>0?b+1|0:b;d=d^i;b=b^i;i=b;s=d^-1;b=b^2147483647;m=g;e:{f:{if(!D[a+28>>2]){if((b|0)==(h|0)&k>>>0>s>>>0|b>>>0<h>>>0){break e}b=h+i|0;a=d+k|0;b=a>>>0<d>>>0?b+1|0:b;d=a;a=b;b=g>>31;f=b+j|0;h=g;g=b;b=h+b|0;b=f>>>0<g>>>0?b+1|0:b;h=f^g;f=h+d|0;g=b^g;b=f;g=g^2147483647;a=(g|0)==(a|0)&(h^-1)>>>0<d>>>0|a>>>0>g>>>0;g=!(a&0);a=a?-1:b;if(g&(a|0)<=536870912|(a|0)<536870912){break e}b=0;a=a>>>29|0;break f}g:{if((b|0)==(h|0)&k>>>0>s>>>0|b>>>0<h>>>0){break g}b=h+i|0;a=d+k|0;b=a>>>0<d>>>0?b+1|0:b;d=b;h=g;b=g>>31;g=b+j|0;i=h;h=b;b=i+b|0;b=g>>>0<h>>>0?b+1|0:b;g=g^h;b=b^h;h=b^2147483647;if((h|0)==(d|0)&(g^-1)>>>0<a>>>0|d>>>0>h>>>0){break g}b=b+d|0;a=a+g|0;b=a>>>0<g>>>0?b+1|0:b;n=a;f=b;if(!b&a>>>0<536870913){break e}}b=f>>>29|0;a=(f&536870911)<<3|n>>>29}j=ai(j,m,a,b);o=ai(o,l,a,b);q=ai(q,r,a,b)}D[c+8>>2]=j;D[c+4>>2]=o;D[c>>2]=q;$=e+96|0;return}ua();T()}ua();T()}ua();T()}function Ic(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;a:{if((b|0)<0){break a}d=D[a+12>>2];c=D[a+8>>2];if(d-c>>2>>>0<=b>>>0){break a}e=c+(b<<2)|0;f=D[e>>2];i=D[f+60>>2];g=D[f+56>>2];c=e+4|0;b:{if((c|0)!=(d|0)){while(1){h=D[c>>2];D[c>>2]=0;D[e>>2]=h;if(f){ya(f)}e=e+4|0;c=c+4|0;if((c|0)!=(d|0)){f=D[e>>2];continue}break}d=D[a+12>>2];if((e|0)==(d|0)){break b}}while(1){d=d-4|0;c=D[d>>2];D[d>>2]=0;if(c){ya(c)}if((d|0)!=(e|0)){continue}break}}D[a+12>>2]=e;f=D[a+4>>2];c:{if(!f|(i|0)<0){break c}d=D[f+24>>2];c=D[f+28>>2];if((d|0)==(c|0)){break c}while(1){if((i|0)==D[D[d>>2]+24>>2]){e=d+4|0;i=D[f+28>>2];d:{if((e|0)!=(i|0)){while(1){h=D[e>>2];D[e>>2]=0;c=D[d>>2];D[d>>2]=h;if(c){Ca(c+12|0,D[c+16>>2]);Ba(c,D[c+4>>2]);ma(c)}d=d+4|0;e=e+4|0;if((i|0)!=(e|0)){continue}break}e=D[f+28>>2];if((e|0)==(d|0)){break d}}while(1){e=e-4|0;c=D[e>>2];D[e>>2]=0;if(c){Ca(c+12|0,D[c+16>>2]);Ba(c,D[c+4>>2]);ma(c)}if((d|0)!=(e|0)){continue}break}}D[f+28>>2]=d;break c}d=d+4|0;if((c|0)!=(d|0)){continue}break}}e:{if((g|0)>4){break e}f:{e=J(g,12)+a|0;d=D[e+20>>2];c=D[e+24>>2];if((d|0)==(c|0)){break f}while(1){if(D[d>>2]==(b|0)){break f}d=d+4|0;if((c|0)!=(d|0)){continue}break}break e}if((d|0)==(c|0)){break e}f=d+4|0;c=c-f|0;if(c){Na(d,f,c)}D[e+24>>2]=d+c}e=D[a+20>>2];d=D[a+24>>2]-e|0;g:{if(!d){break g}c=d>>2;f=c>>>0>1?c:1;i=f&1;d=0;if(c>>>0>=2){f=f&-2;c=0;while(1){g=d<<2;h=g+e|0;j=D[h>>2];if((j|0)>(b|0)){D[h>>2]=j-1}g=e+(g|4)|0;h=D[g>>2];if((h|0)>(b|0)){D[g>>2]=h-1}d=d+2|0;c=c+2|0;if((f|0)!=(c|0)){continue}break}}if(!i){break g}d=e+(d<<2)|0;c=D[d>>2];if((c|0)<=(b|0)){break g}D[d>>2]=c-1}e=D[a+32>>2];d=D[a+36>>2]-e|0;h:{if(!d){break h}c=d>>2;f=c>>>0>1?c:1;i=f&1;d=0;if(c>>>0>=2){f=f&-2;c=0;while(1){g=d<<2;h=g+e|0;j=D[h>>2];if((j|0)>(b|0)){D[h>>2]=j-1}g=e+(g|4)|0;h=D[g>>2];if((h|0)>(b|0)){D[g>>2]=h-1}d=d+2|0;c=c+2|0;if((f|0)!=(c|0)){continue}break}}if(!i){break h}d=e+(d<<2)|0;c=D[d>>2];if((c|0)<=(b|0)){break h}D[d>>2]=c-1}e=D[a+44>>2];d=D[a+48>>2]-e|0;i:{if(!d){break i}c=d>>2;f=c>>>0>1?c:1;i=f&1;d=0;if(c>>>0>=2){f=f&-2;c=0;while(1){g=d<<2;h=g+e|0;j=D[h>>2];if((j|0)>(b|0)){D[h>>2]=j-1}g=e+(g|4)|0;h=D[g>>2];if((h|0)>(b|0)){D[g>>2]=h-1}d=d+2|0;c=c+2|0;if((f|0)!=(c|0)){continue}break}}if(!i){break i}d=e+(d<<2)|0;c=D[d>>2];if((c|0)<=(b|0)){break i}D[d>>2]=c-1}e=D[a+56>>2];d=D[a+60>>2]-e|0;j:{if(!d){break j}c=d>>2;f=c>>>0>1?c:1;i=f&1;d=0;if(c>>>0>=2){f=f&-2;c=0;while(1){g=d<<2;h=g+e|0;j=D[h>>2];if((j|0)>(b|0)){D[h>>2]=j-1}g=e+(g|4)|0;h=D[g>>2];if((h|0)>(b|0)){D[g>>2]=h-1}d=d+2|0;c=c+2|0;if((f|0)!=(c|0)){continue}break}}if(!i){break j}d=e+(d<<2)|0;c=D[d>>2];if((c|0)<=(b|0)){break j}D[d>>2]=c-1}c=D[a+72>>2];a=D[a+68>>2];d=c-a|0;if(!d){break a}c=d>>2;e=c>>>0>1?c:1;f=e&1;d=0;if(c>>>0>=2){e=e&-2;c=0;while(1){i=d<<2;g=i+a|0;h=D[g>>2];if((h|0)>(b|0)){D[g>>2]=h-1}i=a+(i|4)|0;g=D[i>>2];if((g|0)>(b|0)){D[i>>2]=g-1}d=d+2|0;c=c+2|0;if((e|0)!=(c|0)){continue}break}}if(!f){break a}c=b;a=a+(d<<2)|0;b=D[a>>2];if((c|0)>=(b|0)){break a}D[a>>2]=b-1}}function ma(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;a:{if(!a){break a}d=a-8|0;b=D[a-4>>2];a=b&-8;f=d+a|0;b:{if(b&1){break b}if(!(b&3)){break a}b=D[d>>2];d=d-b|0;if(d>>>0<G[2885]){break a}a=a+b|0;if(D[2886]!=(d|0)){if(b>>>0<=255){e=D[d+8>>2];b=b>>>3|0;c=D[d+12>>2];if((c|0)==(e|0)){i=11524,j=D[2881]&di(b),D[i>>2]=j;break b}D[e+12>>2]=c;D[c+8>>2]=e;break b}h=D[d+24>>2];b=D[d+12>>2];c:{if((d|0)!=(b|0)){c=D[d+8>>2];D[c+12>>2]=b;D[b+8>>2]=c;break c}d:{e=d+20|0;c=D[e>>2];if(c){break d}e=d+16|0;c=D[e>>2];if(c){break d}b=0;break c}while(1){g=e;b=c;e=b+20|0;c=D[e>>2];if(c){continue}e=b+16|0;c=D[b+16>>2];if(c){continue}break}D[g>>2]=0}if(!h){break b}e=D[d+28>>2];c=(e<<2)+11828|0;e:{if(D[c>>2]==(d|0)){D[c>>2]=b;if(b){break e}i=11528,j=D[2882]&di(e),D[i>>2]=j;break b}D[h+(D[h+16>>2]==(d|0)?16:20)>>2]=b;if(!b){break b}}D[b+24>>2]=h;c=D[d+16>>2];if(c){D[b+16>>2]=c;D[c+24>>2]=b}c=D[d+20>>2];if(!c){break b}D[b+20>>2]=c;D[c+24>>2]=b;break b}b=D[f+4>>2];if((b&3)!=3){break b}D[2883]=a;D[f+4>>2]=b&-2;D[d+4>>2]=a|1;D[a+d>>2]=a;return}if(d>>>0>=f>>>0){break a}b=D[f+4>>2];if(!(b&1)){break a}f:{if(!(b&2)){if(D[2887]==(f|0)){D[2887]=d;a=D[2884]+a|0;D[2884]=a;D[d+4>>2]=a|1;if(D[2886]!=(d|0)){break a}D[2883]=0;D[2886]=0;return}if(D[2886]==(f|0)){D[2886]=d;a=D[2883]+a|0;D[2883]=a;D[d+4>>2]=a|1;D[a+d>>2]=a;return}a=(b&-8)+a|0;g:{if(b>>>0<=255){e=D[f+8>>2];b=b>>>3|0;c=D[f+12>>2];if((c|0)==(e|0)){i=11524,j=D[2881]&di(b),D[i>>2]=j;break g}D[e+12>>2]=c;D[c+8>>2]=e;break g}h=D[f+24>>2];b=D[f+12>>2];h:{if((f|0)!=(b|0)){c=D[f+8>>2];D[c+12>>2]=b;D[b+8>>2]=c;break h}i:{e=f+20|0;c=D[e>>2];if(c){break i}e=f+16|0;c=D[e>>2];if(c){break i}b=0;break h}while(1){g=e;b=c;e=b+20|0;c=D[e>>2];if(c){continue}e=b+16|0;c=D[b+16>>2];if(c){continue}break}D[g>>2]=0}if(!h){break g}e=D[f+28>>2];c=(e<<2)+11828|0;j:{if(D[c>>2]==(f|0)){D[c>>2]=b;if(b){break j}i=11528,j=D[2882]&di(e),D[i>>2]=j;break g}D[h+(D[h+16>>2]==(f|0)?16:20)>>2]=b;if(!b){break g}}D[b+24>>2]=h;c=D[f+16>>2];if(c){D[b+16>>2]=c;D[c+24>>2]=b}c=D[f+20>>2];if(!c){break g}D[b+20>>2]=c;D[c+24>>2]=b}D[d+4>>2]=a|1;D[a+d>>2]=a;if(D[2886]!=(d|0)){break f}D[2883]=a;return}D[f+4>>2]=b&-2;D[d+4>>2]=a|1;D[a+d>>2]=a}if(a>>>0<=255){a=a>>>3|0;b=(a<<3)+11564|0;c=D[2881];a=1<<a;k:{if(!(c&a)){D[2881]=a|c;a=b;break k}a=D[b+8>>2]}D[b+8>>2]=d;D[a+12>>2]=d;D[d+12>>2]=b;D[d+8>>2]=a;return}e=31;D[d+16>>2]=0;D[d+20>>2]=0;if(a>>>0<=16777215){b=a>>>8|0;g=b+1048320>>>16&8;b=b<<g;e=b+520192>>>16&4;b=b<<e;c=b+245760>>>16&2;b=(b<<c>>>15|0)-(c|(e|g))|0;e=(b<<1|a>>>b+21&1)+28|0}D[d+28>>2]=e;g=(e<<2)+11828|0;l:{m:{c=D[2882];b=1<<e;n:{if(!(c&b)){D[2882]=b|c;D[g>>2]=d;D[d+24>>2]=g;break n}e=a<<((e|0)==31?0:25-(e>>>1|0)|0);b=D[g>>2];while(1){c=b;if((D[b+4>>2]&-8)==(a|0)){break m}b=e>>>29|0;e=e<<1;g=c+(b&4)|0;b=D[g+16>>2];if(b){continue}break}D[g+16>>2]=d;D[d+24>>2]=c}D[d+12>>2]=d;D[d+8>>2]=d;break l}a=D[c+8>>2];D[a+12>>2]=d;D[c+8>>2]=d;D[d+24>>2]=0;D[d+12>>2]=c;D[d+8>>2]=a}a=D[2889]-1|0;D[2889]=a?a:-1}}function qd(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=K(0),l=0,m=0,n=K(0);j=D[c>>2];a:{b:{f=D[b+4>>2];if(!f){break b}g=ci(f);c:{if(g>>>0>=2){e=j;if(e>>>0>=f>>>0){e=(j>>>0)%(f>>>0)|0}c=D[D[b>>2]+(e<<2)>>2];if(!c){break b}if(g>>>0<=1){break c}while(1){c=D[c>>2];if(!c){break b}g=D[c+4>>2];if((g|0)!=(j|0)){if(f>>>0<=g>>>0){g=(g>>>0)%(f>>>0)|0}if((e|0)!=(g|0)){break b}}if(D[c+8>>2]!=(j|0)){continue}break}b=0;break a}e=f-1&j;c=D[D[b>>2]+(e<<2)>>2];if(!c){break b}}g=f-1|0;while(1){c=D[c>>2];if(!c){break b}h=D[c+4>>2];if((h|0)!=(j|0)&(g&h)!=(e|0)){break b}if(D[c+8>>2]!=(j|0)){continue}break}b=0;break a}c=na(16);d=D[D[d>>2]>>2];D[c+12>>2]=0;D[c+8>>2]=d;D[c+4>>2]=j;D[c>>2]=0;n=K(D[b+12>>2]+1>>>0);k=H[b+16>>2];d:{if(!(f?n>K(k*K(f>>>0)):1)){break d}g=(f-1&f)!=0|f>>>0<3|f<<1;e=2;k=K(Q(K(n/k)));e:{if(k<K(4294967296)&k>=K(0)){d=~~k>>>0;break e}d=0}d=d>>>0>g>>>0?d:g;f:{if((d|0)==1){break f}if(!(d&d-1)){e=d;break f}e=Hc(d);f=D[b+4>>2]}g:{if(e>>>0<=f>>>0){if(e>>>0>=f>>>0){break g}g=f>>>0<3;k=K(Q(K(K(G[b+12>>2])/H[b+16>>2])));h:{if(k<K(4294967296)&k>=K(0)){d=~~k>>>0;break h}d=0}i:{j:{if(g){break j}if(ci(f)>>>0>1){break j}d=d>>>0<2?d:1<<32-M(d-1|0);break i}d=Hc(d)}e=d>>>0>e>>>0?d:e;if(e>>>0>=f>>>0){break g}}f=0;h=e;k:{l:{m:{n:{if(e){if(h>>>0>=1073741824){break n}e=na(h<<2);d=D[b>>2];D[b>>2]=e;if(d){ma(d)}D[b+4>>2]=h;e=0;if(h-1>>>0>=3){g=h&-4;while(1){d=e<<2;D[d+D[b>>2]>>2]=0;D[D[b>>2]+(d|4)>>2]=0;D[D[b>>2]+(d|8)>>2]=0;D[D[b>>2]+(d|12)>>2]=0;e=e+4|0;f=f+4|0;if((g|0)!=(f|0)){continue}break}}d=h&3;if(d){while(1){D[D[b>>2]+(e<<2)>>2]=0;e=e+1|0;i=i+1|0;if((d|0)!=(i|0)){continue}break}}f=D[b+8>>2];if(!f){break k}d=b+8|0;g=D[f+4>>2];e=ci(h);if(e>>>0<2){break m}g=g>>>0>=h>>>0?(g>>>0)%(h>>>0)|0:g;D[D[b>>2]+(g<<2)>>2]=d;d=D[f>>2];if(!d){break k}if(e>>>0<=1){break l}while(1){i=D[d+4>>2];if(h>>>0<=i>>>0){i=(i>>>0)%(h>>>0)|0}o:{if((g|0)==(i|0)){f=d;break o}e=d;l=i<<2;m=l+D[b>>2]|0;if(!D[m>>2]){D[m>>2]=f;f=d;g=i;break o}while(1){i=e;e=D[e>>2];if(D[d+8>>2]==D[e+8>>2]?e:0){continue}break}D[f>>2]=e;D[i>>2]=D[D[l+D[b>>2]>>2]>>2];D[D[l+D[b>>2]>>2]>>2]=d}d=D[f>>2];if(d){continue}break}break k}d=D[b>>2];D[b>>2]=0;if(d){ma(d)}D[b+4>>2]=0;break k}ra(1326);T()}g=h-1&g;D[D[b>>2]+(g<<2)>>2]=d;d=D[f>>2];if(!d){break k}}l=h-1|0;while(1){h=l&D[d+4>>2];p:{if((h|0)==(g|0)){f=d;break p}e=d;i=h<<2;m=i+D[b>>2]|0;if(D[m>>2]){while(1){h=e;e=D[e>>2];if(D[d+8>>2]==D[e+8>>2]?e:0){continue}break}D[f>>2]=e;D[h>>2]=D[D[i+D[b>>2]>>2]>>2];D[D[i+D[b>>2]>>2]>>2]=d;break p}D[m>>2]=f;f=d;g=h}d=D[f>>2];if(d){continue}break}}}f=D[b+4>>2];d=f-1|0;if(!(d&f)){e=d&j;break d}if(f>>>0>j>>>0){e=j;break d}e=(j>>>0)%(f>>>0)|0}e=D[b>>2]+(e<<2)|0;d=D[e>>2];q:{if(!d){D[c>>2]=D[b+8>>2];D[b+8>>2]=c;D[e>>2]=b+8;d=D[c>>2];if(!d){break q}d=D[d+4>>2];e=f-1|0;r:{if(!(e&f)){d=d&e;break r}if(d>>>0<f>>>0){break r}d=(d>>>0)%(f>>>0)|0}D[D[b>>2]+(d<<2)>>2]=c;break q}D[c>>2]=D[d>>2];D[d>>2]=c}D[b+12>>2]=D[b+12>>2]+1;b=1}B[a+4|0]=b;D[a>>2]=c}function Yh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;D[a+8>>2]=e;d=a+32|0;k=D[d>>2];f=D[a+36>>2]-k>>2;a:{if(f>>>0<e>>>0){sa(d,e-f|0);k=D[d>>2];d=D[a+8>>2];break a}if(e>>>0<f>>>0){D[a+36>>2]=(e<<2)+k}d=e}q=D[a+52>>2];n=D[a+48>>2];f=0;m=(e&1073741823)!=(e|0)?-1:e<<2;m=pa(na(m),0,m);b:{if((d|0)<=0){break b}while(1){d=f<<2;g=D[d+m>>2];i=D[a+16>>2];c:{if((g|0)>(i|0)){D[d+k>>2]=i;break c}d=d+k|0;i=D[a+12>>2];if((i|0)>(g|0)){D[d>>2]=i;break c}D[d>>2]=g}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}if((d|0)<=0){break b}f=0;while(1){g=f<<2;d=g+c|0;g=D[b+g>>2]+D[g+k>>2]|0;D[d>>2]=g;d:{if((g|0)>D[a+16>>2]){h=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break d}h=g+D[a+20>>2]|0}D[d>>2]=h}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}}f=D[a+56>>2];o=D[f>>2];f=D[f+4>>2]-o|0;if((f|0)>=5){f=f>>2;r=(f|0)>2?f:2;s=f>>>0>1?f:1;t=e&-2;u=e&1;k=1;while(1){e:{f:{if((k|0)!=(s|0)){p=J(e,k);f=D[(k<<2)+o>>2];if((f|0)==-1){break f}f=D[D[n+12>>2]+(f<<2)>>2];if((f|0)==-1){break f}i=D[q>>2];g=D[n>>2];j=D[i+(D[g+(f<<2)>>2]<<2)>>2];h=f+1|0;h=(h>>>0)%3|0?h:f-2|0;if((h|0)!=-1){h=D[g+(h<<2)>>2]}else{h=-1}g:{h:{if((f>>>0)%3|0){f=f-1|0;break h}f=f+2|0;l=-1;if((f|0)==-1){break g}}l=D[g+(f<<2)>>2]}if((j|0)>=(k|0)){break f}f=D[(h<<2)+i>>2];if((f|0)>=(k|0)){break f}g=D[i+(l<<2)>>2];if((g|0)>=(k|0)){break f}i:{if((e|0)<=0){break i}g=J(e,g);i=J(e,f);j=J(e,j);f=0;l=0;if((e|0)!=1){while(1){D[m+(f<<2)>>2]=(D[(f+g<<2)+c>>2]+D[(f+i<<2)+c>>2]|0)-D[(f+j<<2)+c>>2];h=f|1;D[m+(h<<2)>>2]=(D[(g+h<<2)+c>>2]+D[(i+h<<2)+c>>2]|0)-D[(h+j<<2)+c>>2];f=f+2|0;l=l+2|0;if((t|0)!=(l|0)){continue}break}}if(!u){break i}D[m+(f<<2)>>2]=(D[(f+g<<2)+c>>2]+D[(f+i<<2)+c>>2]|0)-D[(f+j<<2)+c>>2]}if((d|0)<=0){break e}i=D[a+32>>2];f=0;while(1){d=f<<2;g=D[d+m>>2];j=D[a+16>>2];j:{if((g|0)>(j|0)){D[d+i>>2]=j;break j}d=d+i|0;j=D[a+12>>2];if((j|0)>(g|0)){D[d>>2]=j;break j}D[d>>2]=g}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}f=0;if((d|0)<=0){break e}d=p<<2;j=d+c|0;h=b+d|0;while(1){g=f<<2;d=g+j|0;g=D[g+h>>2]+D[g+i>>2]|0;D[d>>2]=g;k:{if((g|0)>D[a+16>>2]){l=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break k}l=g+D[a+20>>2]|0}D[d>>2]=l}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}break e}ua();T()}if((d|0)<=0){break e}j=(J(k-1|0,e)<<2)+c|0;i=D[a+32>>2];f=0;while(1){d=f<<2;g=D[d+j>>2];h=D[a+16>>2];l:{if((g|0)>(h|0)){D[d+i>>2]=h;break l}d=d+i|0;h=D[a+12>>2];if((h|0)>(g|0)){D[d>>2]=h;break l}D[d>>2]=g}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}f=0;if((d|0)<=0){break e}d=p<<2;j=d+c|0;h=b+d|0;while(1){g=f<<2;d=g+j|0;g=D[g+h>>2]+D[g+i>>2]|0;D[d>>2]=g;m:{if((g|0)>D[a+16>>2]){l=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break m}l=g+D[a+20>>2]|0}D[d>>2]=l}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}}k=k+1|0;if((r|0)!=(k|0)){continue}break}}ma(m);return 1}function Sc(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;D[a+56>>2]=D[a+52>>2];D[a+44>>2]=D[a+40>>2];a:{b:{c:{e=D[a+64>>2];c=D[e+24>>2];if((c|0)!=D[e+28>>2]){while(1){d=b;i=D[(k<<2)+c>>2];d:{if((i|0)==-1){break d}b=D[a+56>>2];e:{if((b|0)!=D[a+60>>2]){D[b>>2]=d;D[a+56>>2]=b+4;break e}c=D[a+52>>2];e=b-c|0;g=e>>2;b=g+1|0;if(b>>>0>=1073741824){break c}f=e>>1;f=g>>>0<536870911?b>>>0>f>>>0?b:f:1073741823;if(f){if(f>>>0>=1073741824){break a}b=na(f<<2)}else{b=0}g=b+(g<<2)|0;D[g>>2]=d;if((e|0)>0){oa(b,c,e)}D[a+60>>2]=b+(f<<2);D[a+56>>2]=g+4;D[a+52>>2]=b;if(!c){break e}ma(c)}f:{if(!(D[D[a+12>>2]+(k>>>3&536870908)>>2]>>>k&1)){break f}b=i+1|0;b=(b>>>0)%3|0?b:i-2|0;if((b|0)==-1){break f}e=D[a>>2];if(D[e+(b>>>3&536870908)>>2]>>>b&1){break f}b=D[D[D[a+64>>2]+12>>2]+(b<<2)>>2];if((b|0)==-1){break f}c=b+1|0;c=(c>>>0)%3|0?c:b-2|0;if((c|0)==-1){break f}f=D[a+64>>2];while(1){i=c;b=c+1|0;b=(b>>>0)%3|0?b:c-2|0;if((b|0)==-1|D[e+(b>>>3&536870908)>>2]>>>b&1){break f}b=D[D[f+12>>2]+(b<<2)>>2];if((b|0)==-1){break f}c=b+1|0;c=(c>>>0)%3|0?c:b-2|0;if((c|0)!=-1){continue}break}}D[D[a+28>>2]+(i<<2)>>2]=d;b=D[a+44>>2];g:{if((b|0)!=D[a+48>>2]){D[b>>2]=i;D[a+44>>2]=b+4;break g}c=D[a+40>>2];e=b-c|0;g=e>>2;b=g+1|0;if(b>>>0>=1073741824){break b}f=e>>1;f=g>>>0<536870911?b>>>0>f>>>0?b:f:1073741823;if(f){if(f>>>0>=1073741824){break a}b=na(f<<2)}else{b=0}g=b+(g<<2)|0;D[g>>2]=i;if((e|0)>0){oa(b,c,e)}D[a+48>>2]=b+(f<<2);D[a+44>>2]=g+4;D[a+40>>2]=b;if(!c){break g}ma(c)}b=d+1|0;e=D[a+64>>2];h:{if((i>>>0)%3|0){c=i-1|0;break h}c=i+2|0;if((c|0)==-1){break d}}c=D[D[e+12>>2]+(c<<2)>>2];if((c|0)==-1){break d}c=c+((c>>>0)%3|0?-1:2)|0;if((c|0)==-1|(c|0)==(i|0)){break d}while(1){e=c+1|0;e=(e>>>0)%3|0?e:c-2|0;if(D[D[a>>2]+(e>>>3&536870908)>>2]>>>e&1){d=D[a+56>>2];i:{if((d|0)!=D[a+60>>2]){D[d>>2]=b;D[a+56>>2]=d+4;break i}e=D[a+52>>2];f=d-e|0;h=f>>2;d=h+1|0;if(d>>>0>=1073741824){break c}g=f>>1;g=h>>>0<536870911?d>>>0>g>>>0?d:g:1073741823;if(g){if(g>>>0>=1073741824){break a}d=na(g<<2)}else{d=0}h=d+(h<<2)|0;D[h>>2]=b;if((f|0)>0){oa(d,e,f)}D[a+60>>2]=d+(g<<2);D[a+56>>2]=h+4;D[a+52>>2]=d;if(!e){break i}ma(e)}e=b+1|0;d=D[a+44>>2];j:{if((d|0)!=D[a+48>>2]){D[d>>2]=c;D[a+44>>2]=d+4;break j}f=D[a+40>>2];g=d-f|0;j=g>>2;d=j+1|0;if(d>>>0>=1073741824){break b}h=g>>1;h=j>>>0<536870911?d>>>0>h>>>0?d:h:1073741823;if(h){if(h>>>0>=1073741824){break a}d=na(h<<2)}else{d=0}j=d+(j<<2)|0;D[j>>2]=c;if((g|0)>0){oa(d,f,g)}D[a+48>>2]=d+(h<<2);D[a+44>>2]=j+4;D[a+40>>2]=d;if(!f){break j}ma(f)}d=b;b=e}D[D[a+28>>2]+(c<<2)>>2]=d;e=D[a+64>>2];k:{if((c>>>0)%3|0){c=c-1|0;break k}c=c+2|0;if((c|0)==-1){break d}}c=D[D[e+12>>2]+(c<<2)>>2];if((c|0)==-1){break d}c=c+((c>>>0)%3|0?-1:2)|0;if((c|0)==-1){break d}if((c|0)!=(i|0)){continue}break}}k=k+1|0;c=D[e+24>>2];if(k>>>0<D[e+28>>2]-c>>2>>>0){continue}break}}return}qa();T()}qa();T()}ra(1326);T()}function ce(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;D[a+8>>2]=e;d=a+32|0;k=D[d>>2];f=D[a+36>>2]-k>>2;a:{if(f>>>0<e>>>0){sa(d,e-f|0);k=D[d>>2];d=D[a+8>>2];break a}if(e>>>0<f>>>0){D[a+36>>2]=(e<<2)+k}d=e}q=D[a+52>>2];m=D[a+48>>2];f=0;l=(e&1073741823)!=(e|0)?-1:e<<2;l=pa(na(l),0,l);b:{if((d|0)<=0){break b}while(1){d=f<<2;g=D[d+l>>2];h=D[a+16>>2];c:{if((g|0)>(h|0)){D[d+k>>2]=h;break c}d=d+k|0;h=D[a+12>>2];if((h|0)>(g|0)){D[d>>2]=h;break c}D[d>>2]=g}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}if((d|0)<=0){break b}f=0;while(1){g=f<<2;d=g+c|0;g=D[b+g>>2]+D[g+k>>2]|0;D[d>>2]=g;d:{if((g|0)>D[a+16>>2]){g=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break d}g=g+D[a+20>>2]|0}D[d>>2]=g}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}}f=D[a+56>>2];o=D[f>>2];f=D[f+4>>2]-o|0;if((f|0)>=5){f=f>>2;r=(f|0)>2?f:2;s=f>>>0>1?f:1;t=e&-2;u=e&1;k=1;while(1){e:{f:{if((k|0)!=(s|0)){p=J(e,k);f=D[(k<<2)+o>>2];if((f|0)==-1|D[D[m>>2]+(f>>>3&536870908)>>2]>>>f&1){break f}f=D[D[D[m+64>>2]+12>>2]+(f<<2)>>2];if((f|0)==-1){break f}h=D[q>>2];g=D[m+28>>2];j=D[h+(D[g+(f<<2)>>2]<<2)>>2];if((j|0)>=(k|0)){break f}i=f+1|0;i=D[h+(D[g+(((i>>>0)%3|0?i:f-2|0)<<2)>>2]<<2)>>2];if((i|0)>=(k|0)){break f}f=D[h+(D[g+(f+((f>>>0)%3|0?-1:2)<<2)>>2]<<2)>>2];if((f|0)>=(k|0)){break f}g:{if((e|0)<=0){break g}g=J(e,f);h=J(e,i);j=J(e,j);f=0;n=0;if((e|0)!=1){while(1){D[l+(f<<2)>>2]=(D[(f+g<<2)+c>>2]+D[(f+h<<2)+c>>2]|0)-D[(f+j<<2)+c>>2];i=f|1;D[l+(i<<2)>>2]=(D[(g+i<<2)+c>>2]+D[(h+i<<2)+c>>2]|0)-D[(j+i<<2)+c>>2];f=f+2|0;n=n+2|0;if((t|0)!=(n|0)){continue}break}}if(!u){break g}D[l+(f<<2)>>2]=(D[(f+g<<2)+c>>2]+D[(f+h<<2)+c>>2]|0)-D[(f+j<<2)+c>>2]}if((d|0)<=0){break e}h=D[a+32>>2];f=0;while(1){d=f<<2;g=D[d+l>>2];j=D[a+16>>2];h:{if((g|0)>(j|0)){D[d+h>>2]=j;break h}d=d+h|0;j=D[a+12>>2];if((j|0)>(g|0)){D[d>>2]=j;break h}D[d>>2]=g}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}f=0;if((d|0)<=0){break e}d=p<<2;j=d+c|0;i=b+d|0;while(1){g=f<<2;d=g+j|0;g=D[g+i>>2]+D[g+h>>2]|0;D[d>>2]=g;i:{if((g|0)>D[a+16>>2]){g=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break i}g=g+D[a+20>>2]|0}D[d>>2]=g}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}break e}ua();T()}if((d|0)<=0){break e}j=(J(k-1|0,e)<<2)+c|0;h=D[a+32>>2];f=0;while(1){d=f<<2;g=D[d+j>>2];i=D[a+16>>2];j:{if((g|0)>(i|0)){D[d+h>>2]=i;break j}d=d+h|0;i=D[a+12>>2];if((i|0)>(g|0)){D[d>>2]=i;break j}D[d>>2]=g}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}f=0;if((d|0)<=0){break e}d=p<<2;j=d+c|0;i=b+d|0;while(1){g=f<<2;d=g+j|0;g=D[g+i>>2]+D[g+h>>2]|0;D[d>>2]=g;k:{if((g|0)>D[a+16>>2]){g=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break k}g=g+D[a+20>>2]|0}D[d>>2]=g}d=D[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}}k=k+1|0;if((r|0)!=(k|0)){continue}break}}ma(l);return 1}function jd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;if((b|0)==-1){return 1}c=(b>>>0)/3|0;if(!(D[D[a+24>>2]+(c>>>3&268435452)>>2]>>>c&1)){d=D[a+48>>2];D[a+52>>2]=d;a:{if((d|0)!=D[a+56>>2]){D[d>>2]=b;D[a+52>>2]=d+4;break a}e=na(4);D[e>>2]=b;f=e+4|0;D[a+56>>2]=f;D[a+52>>2]=f;D[a+48>>2]=e;if(!d){break a}ma(d)}f=D[D[a+4>>2]+28>>2];d=b+1|0;e=(d>>>0)%3|0?d:b-2|0;d=D[f+(e<<2)>>2];if((d|0)==-1){return 0}g=(b-J(c,3)|0?-1:2)+b|0;c=D[f+(g<<2)>>2];if((c|0)==-1){return 0}b=D[a+36>>2];f=b+(d>>>3&536870908)|0;h=D[f>>2];i=1<<d;if(!(h&i)){D[f>>2]=h|i;Ia(a+8|0,d,e);b=D[a+36>>2]}b=(c>>>3&536870908)+b|0;d=D[b>>2];e=1<<c;if(!(d&e)){D[b>>2]=d|e;Ia(a+8|0,c,g)}c=D[a+52>>2];if((c|0)==D[a+48>>2]){return 1}k=a+8|0;while(1){c=c-4|0;b=D[c>>2];d=(b>>>0)/3|0;b:{c:{if((b|0)==-1){break c}e=D[a+24>>2]+(d>>>3&268435452)|0;f=D[e>>2];d=1<<d;if(f&d){break c}D[e>>2]=d|f;e=D[a+4>>2];d=D[D[e+28>>2]+(b<<2)>>2];if((d|0)==-1){return 0}while(1){c=b;d:{e:{f=D[a+36>>2]+(d>>>3&536870908)|0;g=D[f>>2];h=1<<d;if(g&h){break e}f:{b=D[D[e+40>>2]+(d<<2)>>2];g:{if((b|0)==-1){break g}i=b+1|0;b=(i>>>0)%3|0?i:b-2|0;if((b|0)==-1|D[D[e>>2]+(b>>>3&536870908)>>2]>>>b&1){break g}b=D[D[D[e+64>>2]+12>>2]+(b<<2)>>2];if((b|0)!=-1){break f}}D[f>>2]=g|h;Ia(k,d,c);break e}D[f>>2]=g|h;Ia(k,d,c);d=b+1|0;if((((d>>>0)%3|0?d:b-2|0)|0)==-1){break e}b=-1;e=D[a+4>>2];h:{if((c|0)==-1){break h}d=c+1|0;d=(d>>>0)%3|0?d:c-2|0;if((d|0)==-1|D[D[e>>2]+(d>>>3&536870908)>>2]>>>d&1){break h}b=D[D[D[e+64>>2]+12>>2]+(d<<2)>>2]}d=(b>>>0)/3|0;h=1<<d;c=D[a+24>>2];i=d>>>5|0;f=D[c+(i<<2)>>2];break d}i:{j:{if((c|0)==-1){break j}d=-1;b=c+1|0;b=(b>>>0)%3|0?b:c-2|0;e=D[a+4>>2];if(!((b|0)==-1|D[D[e>>2]+(b>>>3&536870908)>>2]>>>b&1)){d=D[D[D[e+64>>2]+12>>2]+(b<<2)>>2]}k:{l:{if((c>>>0)%3|0){c=c-1|0;break l}c=c+2|0;b=-1;if((c|0)==-1){break k}}b=-1;if(D[D[e>>2]+(c>>>3&536870908)>>2]>>>c&1){break k}b=D[D[D[e+64>>2]+12>>2]+(c<<2)>>2]}j=(b|0)==-1;g=j?-1:(b>>>0)/3|0;f=(d>>>0)/3|0;h=(d|0)==-1;if(!h){c=D[a+24>>2];h=h?-1:f;i=h>>>5|0;f=D[c+(i<<2)>>2];h=1<<h;if(!(f&h)){break i}}if(j){break j}h=1<<g;c=D[a+24>>2];i=g>>>5|0;f=D[c+(i<<2)>>2];if(!(h&f)){break d}}c=D[a+52>>2]-4|0;D[a+52>>2]=c;break b}if(j){b=d;break d}if(D[(g>>>3&536870908)+c>>2]>>>g&1){b=d;break d}c=D[a+52>>2];D[c-4>>2]=b;if((c|0)!=D[a+56>>2]){D[c>>2]=d;c=c+4|0;break c}m:{e=D[a+48>>2];f=c-e|0;c=f>>2;b=c+1|0;if(b>>>0<1073741824){g=f>>1;g=c>>>0<536870911?b>>>0>g>>>0?b:g:1073741823;if(g){if(g>>>0>=1073741824){break m}b=na(g<<2)}else{b=0}c=b+(c<<2)|0;D[c>>2]=d;c=c+4|0;if((f|0)>0){oa(b,e,f)}D[a+56>>2]=b+(g<<2);D[a+52>>2]=c;D[a+48>>2]=b;if(!e){break b}ma(e);c=D[a+52>>2];break b}qa();T()}ra(1326);T()}D[(i<<2)+c>>2]=f|h;d=D[D[e+28>>2]+(b<<2)>>2];if((d|0)!=-1){continue}break}return 0}D[a+52>>2]=c}if(D[a+48>>2]!=(c|0)){continue}break}}return 1}function Tb(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;i=J(b,12)+a|0;D[i+12>>2]=D[i+8>>2];l=(c|0)==-1?-1:(c>>>0)/3|0;n=1;j=c;a:{b:{c:{while(1){d:{o=e&1;if(o){if((j|0)==-1){break d}if((Qc(a,((j>>>0)%3|0?-1:2)+j|0)|0)==-1){break a}c=j+1|0;d=(c>>>0)%3|0?c:j-2|0;if((d|0)==-1){break a}c=d+1|0;c=(c>>>0)%3|0?c:d-2|0;if((c|0)==-1){break a}d=D[D[D[a+4>>2]+12>>2]+(c<<2)>>2];if((d|0)==-1){break a}c=d+1|0;c=(c>>>0)%3|0?c:d-2|0;if((c|0)==-1){break a}l=(c>>>0)/3|0}k=1<<l;g=D[a+56>>2]+(l>>>3&536870908)|0;m=D[g>>2];e:{if(k&m){break e}e=0;while(1){D[g>>2]=m|k;d=D[i+12>>2];f:{if((d|0)!=D[i+16>>2]){D[d>>2]=l;D[i+12>>2]=d+4;break f}g=D[i+8>>2];k=d-g|0;f=k>>2;h=f+1|0;if(h>>>0>=1073741824){break c}d=k>>1;m=f>>>0<536870911?d>>>0<h>>>0?h:d:1073741823;if(m){if(m>>>0>=1073741824){break b}d=na(m<<2)}else{d=0}h=d+(f<<2)|0;D[h>>2]=l;if((k|0)>0){oa(d,g,k)}D[i+8>>2]=d;D[i+12>>2]=h+4;D[i+16>>2]=d+(m<<2);if(!g){break f}ma(g)}h=e+1|0;g:{h:{i:{if(!e){break i}if(h&1){if((c|0)==-1){c=-1;break g}d=c+1|0;c=(d>>>0)%3|0?d:c-2|0;break i}j=o?c:j;if((c|0)==-1){c=-1;break g}if((c>>>0)%3|0){e=c-1|0;break h}c=c+2|0}d=c;c=-1;e=d;if((d|0)==-1){break g}}c=D[D[D[a+4>>2]+12>>2]+(e<<2)>>2];g=-1;m=-1;d=e+1|0;f=(d>>>0)%3|0?d:e-2|0;if((f|0)>=0){d=(f>>>0)/3|0;m=D[(D[D[a>>2]+96>>2]+J(d,12)|0)+(f-J(d,3)<<2)>>2]}j:{if((c|0)==-1){k=1;break j}k=0;f=((c>>>0)%3|0?-1:2)+c|0;if((f|0)<0){break j}d=(f>>>0)/3|0;g=D[(D[D[a>>2]+96>>2]+J(d,12)|0)+(f-J(d,3)<<2)>>2]}if((g|0)!=(m|0)){c=-1;break g}k:{l:{e=((e>>>0)%3|0?-1:2)+e|0;m:{n:{if((e|0)>=0){d=(e>>>0)/3|0;g=D[(D[D[a>>2]+96>>2]+J(d,12)|0)+(e-J(d,3)<<2)>>2];e=-1;if(!k){break n}break m}g=-1;if(k){break l}}d=c+1|0;e=(d>>>0)%3|0?d:c-2|0;if((e|0)<0){e=-1;break m}d=(e>>>0)/3|0;e=D[(D[D[a>>2]+96>>2]+J(d,12)|0)+(e-J(d,3)<<2)>>2]}if((e|0)!=(g|0)){c=-1;break g}if((c|0)!=-1){break k}c=-1;break g}if((c|0)!=-1){break k}c=-1;break g}e=h;l=(c>>>0)/3|0;g=D[a+56>>2]+(l>>>3&268435452)|0;m=D[g>>2];k=1<<l;if(!(m&k)){continue}}break}if(!o|!(h&1)){break e}f=D[i+12>>2]-4|0;h=D[f>>2];e=D[a+56>>2]+(h>>>3&536870908)|0;d=D[e>>2];p=e,q=di(h)&d,D[p>>2]=q;D[i+12>>2]=f}e=1;d=n;n=0;if(d){continue}break a}break}j=-1;Qc(a,-1);break a}qa();T()}ra(1326);T()}D[((b<<2)+a|0)+44>>2]=j;f=D[i+8>>2];b=D[i+12>>2]-f|0;o:{if(!b){break o}j=D[a+56>>2];b=b>>2;a=b>>>0>1?b:1;h=a&1;c=0;if(b>>>0>=2){n=a&-2;l=0;while(1){e=c<<2;d=D[e+f>>2];b=j+(d>>>3&536870908)|0;a=D[b>>2];p=b,q=di(d)&a,D[p>>2]=q;d=D[f+(e|4)>>2];b=j+(d>>>3&536870908)|0;a=D[b>>2];p=b,q=di(d)&a,D[p>>2]=q;c=c+2|0;l=l+2|0;if((n|0)!=(l|0)){continue}break}}if(!h){break o}c=D[f+(c<<2)>>2];b=j+(c>>>3&536870908)|0;a=D[b>>2];p=b,q=di(c)&a,D[p>>2]=q}}function kd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;if((b|0)==-1){return 1}d=(b>>>0)/3|0;if(!(D[D[a+24>>2]+(d>>>3&268435452)>>2]>>>d&1)){c=D[a+48>>2];D[a+52>>2]=c;a:{if((c|0)!=D[a+56>>2]){D[c>>2]=b;D[a+52>>2]=c+4;break a}e=na(4);D[e>>2]=b;f=e+4|0;D[a+56>>2]=f;D[a+52>>2]=f;D[a+48>>2]=e;if(!c){break a}ma(c)}e=-1;f=D[a+4>>2];c=b+1|0;g=(c>>>0)%3|0?c:b-2|0;if((g|0)!=-1){e=D[D[f>>2]+(g<<2)>>2]}b:{h=b-J(d,3)|0;if(h){c=b-1|0;break b}c=b+2|0;if((c|0)!=-1){break b}return 0}if((e|0)==-1){return 0}d=D[D[f>>2]+(c<<2)>>2];if((d|0)==-1){return 0}c=D[a+36>>2];f=c+(e>>>3&536870908)|0;i=D[f>>2];j=1<<e;if(!(i&j)){D[f>>2]=i|j;Ia(a+8|0,e,g);c=D[a+36>>2]}c=(d>>>3&536870908)+c|0;e=D[c>>2];f=1<<d;if(!(e&f)){D[c>>2]=e|f;Ia(a+8|0,d,(h?-1:2)+b|0)}c=D[a+52>>2];if((c|0)==D[a+48>>2]){return 1}j=a+8|0;while(1){c=c-4|0;b=D[c>>2];d=(b>>>0)/3|0;c:{d:{if((b|0)==-1){break d}e=D[a+24>>2]+(d>>>3&268435452)|0;f=D[e>>2];d=1<<d;if(f&d){break d}D[e>>2]=d|f;while(1){d=D[a+4>>2];c=D[D[d>>2]+(b<<2)>>2];if((c|0)==-1){return 0}e:{f:{e=D[a+36>>2]+(c>>>3&536870908)|0;f=D[e>>2];g=1<<c;if(f&g){break f}g:{h=D[D[d+24>>2]+(c<<2)>>2];h:{if((h|0)==-1){break h}i=h+1|0;h=(i>>>0)%3|0?i:h-2|0;if((h|0)==-1){break h}d=D[D[d+12>>2]+(h<<2)>>2];if((d|0)!=-1){break g}}D[e>>2]=f|g;Ia(j,c,b);break f}D[e>>2]=f|g;Ia(j,c,b);c=d+1|0;if((((c>>>0)%3|0?c:d-2|0)|0)==-1){break f}d=b-2|0;c=b+1|0;b=-1;c=(c>>>0)%3|0?c:d;if((c|0)!=-1){b=D[D[D[a+4>>2]+12>>2]+(c<<2)>>2]}c=(b>>>0)/3|0;g=1<<c;e=D[a+24>>2];h=c>>>5|0;d=D[e+(h<<2)>>2];break e}c=-1;f=D[a+4>>2];d=b+1|0;d=(d>>>0)%3|0?d:b-2|0;if((d|0)!=-1){c=D[D[f+12>>2]+(d<<2)>>2]}i:{j:{if((b>>>0)%3|0){e=b-1|0;break j}e=b+2|0;b=-1;if((e|0)==-1){break i}}b=D[D[f+12>>2]+(e<<2)>>2]}i=(b|0)==-1;f=i?-1:(b>>>0)/3|0;d=(c>>>0)/3|0;k:{g=(c|0)==-1;if(!g){e=D[a+24>>2];g=g?-1:d;h=g>>>5|0;d=D[e+(h<<2)>>2];g=1<<g;if(!(d&g)){break k}}if(!i){g=1<<f;e=D[a+24>>2];h=f>>>5|0;d=D[e+(h<<2)>>2];if(!(g&d)){break e}}c=D[a+52>>2]-4|0;D[a+52>>2]=c;break c}if(i){b=c;break e}if(D[(f>>>3&536870908)+e>>2]>>>f&1){b=c;break e}d=D[a+52>>2];D[d-4>>2]=b;if((d|0)!=D[a+56>>2]){D[d>>2]=c;c=d+4|0;break d}l:{b=d;d=D[a+48>>2];e=b-d|0;g=e>>2;b=g+1|0;if(b>>>0<1073741824){f=e>>1;f=g>>>0<536870911?b>>>0>f>>>0?b:f:1073741823;if(f){if(f>>>0>=1073741824){break l}b=na(f<<2)}else{b=0}g=b+(g<<2)|0;D[g>>2]=c;c=g+4|0;if((e|0)>0){oa(b,d,e)}D[a+56>>2]=b+(f<<2);D[a+52>>2]=c;D[a+48>>2]=b;if(!d){break c}ma(d);c=D[a+52>>2];break c}qa();T()}ra(1326);T()}D[(h<<2)+e>>2]=d|g;if((b|0)!=-1){continue}break}return 0}D[a+52>>2]=c}if(D[a+48>>2]!=(c|0)){continue}break}}return 1}function vc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;a:{n=ba[D[D[a>>2]+44>>2]](a)|0;if((n|0)<=0){break a}e=D[b+4>>2]-D[b>>2]>>2;g=$+-64|0;$=g;d=lb(g);f=J(D[2549],n);$b(d,D[D[a+8>>2]+56>>2],n<<24>>24,5,0,f,f>>31);f=na(96);d=Zb(f,d);B[d+84|0]=1;D[d+72>>2]=D[d+68>>2];Yb(d,e);D[d+60>>2]=D[D[a+8>>2]+60>>2];d=D[a+16>>2];D[a+16>>2]=f;if(d){ya(d)}$=g- -64|0;g=D[a+16>>2];if(!D[g+80>>2]){break a}i=D[D[g>>2]>>2];if(!i){break a}h=D[c+12>>2];d=D[c+20>>2];j=D[c+8>>2];f=D[c+16>>2];if((h|0)<=(d|0)&j>>>0<=f>>>0|(d|0)>(h|0)){break a}k=J(e,n);i=i+D[g+48>>2]|0;l=D[c>>2];m=E[l+f|0];g=f+1|0;e=g?d:d+1|0;D[c+16>>2]=g;D[c+20>>2]=e;b:{c:{d:{if(m){if(jc(k,n,c,i)){break d}break a}if((e|0)>=(h|0)&g>>>0>=j>>>0|(e|0)>(h|0)){break a}g=E[g+l|0];e=f+2|0;d=e>>>0<2?d+1|0:d;D[c+16>>2]=e;D[c+20>>2]=d;d=D[D[a+16>>2]+64>>2];d=D[d+4>>2]-D[d>>2]|0;e:{if((g|0)==D[2549]){e=d;d=k<<2;if(e>>>0<d>>>0){break a}h=D[c+8>>2];g=D[c+12>>2];e=D[c+20>>2];j=D[c+16>>2];f=d+j|0;e=f>>>0<d>>>0?e+1|0:e;if((e|0)<=(g|0)&f>>>0<=h>>>0|(e|0)<(g|0)){break e}break a}if(d>>>0<J(g,k)>>>0){break a}h=D[c+12>>2];e=D[c+20>>2];l=D[c+8>>2];f=l;j=D[c+16>>2];d=g;p=f-j>>>0<$h(d,0,k,0)>>>0;f=h-(e+(f>>>0<j>>>0)|0)|0;m=aa;if(p&(f|0)<=(m|0)|(f|0)<(m|0)){break a}f=1;if(!k){break c}f=0;m=l;l=d+j|0;e=l>>>0<d>>>0?e+1|0:e;if(m>>>0<l>>>0&(e|0)>=(h|0)|(e|0)>(h|0)){break b}while(1){oa(i+(o<<2)|0,j+D[c>>2]|0,g);e=D[c+20>>2];h=d+D[c+16>>2]|0;e=h>>>0<d>>>0?e+1|0:e;j=h;D[c+16>>2]=h;D[c+20>>2]=e;o=o+1|0;if((k|0)==(o|0)){break d}m=D[c+8>>2];h=D[c+12>>2];l=d+j|0;e=l>>>0<d>>>0?e+1|0:e;if((e|0)<=(h|0)&l>>>0<=m>>>0|(e|0)<(h|0)){continue}break}break b}oa(i,j+D[c>>2]|0,d);e=d;g=d+D[c+16>>2]|0;d=D[c+20>>2];D[c+16>>2]=g;D[c+20>>2]=e>>>0>g>>>0?d+1|0:d}f=1;if(!k){break c}d=D[a+20>>2];if(d){f=0;if(ba[D[D[d>>2]+32>>2]](d)|0){break c}}d=0;f=0;f:{if((k|0)<=0){break f}if((k|0)!=1){h=k&-2;while(1){e=d<<2;g=D[e+i>>2];D[e+i>>2]=0-(g&1)^g>>>1;g=e|4;e=D[g+i>>2];D[g+i>>2]=0-(e&1)^e>>>1;d=d+2|0;f=f+2|0;if((h|0)!=(f|0)){continue}break}}if(!(k&1)){break f}e=d<<2;d=D[e+i>>2];D[e+i>>2]=0-(d&1)^d>>>1}f=0}d=D[a+20>>2];g:{if(!d){break g}if(!(ba[D[D[d>>2]+40>>2]](d,c)|0)){break a}if(f){break g}f=0;a=D[a+20>>2];if(!(ba[D[D[a>>2]+44>>2]](a,i,i,k,n,D[b>>2])|0)){break b}}f=1}return f|0}return 0}function pc(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;h=$-32|0;$=h;a:{b:{if(!jb(1,h+28|0,b)){break b}c:{d=D[h+28>>2];if(d){ib(a+60|0,d);c=h+8|0;D[c>>2]=0;D[c+4>>2]=0;B[c+5|0]=0;B[c+6|0]=0;B[c+7|0]=0;B[c+8|0]=0;B[c+9|0]=0;B[c+10|0]=0;B[c+11|0]=0;B[c+12|0]=0;if(!Ka(c,b)){break c}while(1){f=1<<e;i=Ga(c);g=D[a+60>>2]+(e>>>3&536870908)|0;if(i){f=f|D[g>>2]}else{f=D[g>>2]&(f^-1)}D[g>>2]=f;e=e+1|0;if((d|0)!=(e|0)){continue}break}}if(!jb(1,h+28|0,b)){break b}d=D[h+28>>2];if(d){e=0;ib(a+72|0,d);c=h+8|0;D[c>>2]=0;D[c+4>>2]=0;B[c+5|0]=0;B[c+6|0]=0;B[c+7|0]=0;B[c+8|0]=0;B[c+9|0]=0;B[c+10|0]=0;B[c+11|0]=0;B[c+12|0]=0;if(!Ka(c,b)){break c}while(1){f=1<<e;i=Ga(c);g=D[a+72>>2]+(e>>>3&536870908)|0;if(i){f=f|D[g>>2]}else{f=D[g>>2]&(f^-1)}D[g>>2]=f;e=e+1|0;if((d|0)!=(e|0)){continue}break}}if(!jb(1,h+28|0,b)){break b}d=D[h+28>>2];if(d){e=0;ib(a+84|0,d);c=h+8|0;D[c>>2]=0;D[c+4>>2]=0;B[c+5|0]=0;B[c+6|0]=0;B[c+7|0]=0;B[c+8|0]=0;B[c+9|0]=0;B[c+10|0]=0;B[c+11|0]=0;B[c+12|0]=0;if(!Ka(c,b)){break c}while(1){f=1<<e;i=Ga(c);g=D[a+84>>2]+(e>>>3&536870908)|0;if(i){f=f|D[g>>2]}else{f=D[g>>2]&(f^-1)}D[g>>2]=f;e=e+1|0;if((d|0)!=(e|0)){continue}break}}if(!jb(1,h+28|0,b)){break b}d=D[h+28>>2];if(d){e=0;ib(a+96|0,d);c=h+8|0;D[c>>2]=0;D[c+4>>2]=0;B[c+5|0]=0;B[c+6|0]=0;B[c+7|0]=0;B[c+8|0]=0;B[c+9|0]=0;B[c+10|0]=0;B[c+11|0]=0;B[c+12|0]=0;if(!Ka(c,b)){break c}while(1){f=1<<e;i=Ga(c);g=D[a+96>>2]+(e>>>3&536870908)|0;if(i){f=f|D[g>>2]}else{f=D[g>>2]&(f^-1)}D[g>>2]=f;e=e+1|0;if((d|0)!=(e|0)){continue}break}}e=0;d=D[b+12>>2];f=d;c=D[b+20>>2];g=c;i=D[b+16>>2];j=i+4|0;c=j>>>0<4?c+1|0:c;k=D[b+8>>2];if(k>>>0<j>>>0&(c|0)>=(d|0)|(c|0)>(d|0)){break a}l=D[b>>2];d=l+i|0;d=E[d|0]|E[d+1|0]<<8|(E[d+2|0]<<16|E[d+3|0]<<24);D[b+16>>2]=j;D[b+20>>2]=c;c=g;g=i+8|0;c=g>>>0<8?c+1|0:c;i=g;g=c;if(i>>>0>k>>>0&(c|0)>=(f|0)|(c|0)>(f|0)){break a}c=j+l|0;c=E[c|0]|E[c+1|0]<<8|(E[c+2|0]<<16|E[c+3|0]<<24);D[b+16>>2]=i;D[b+20>>2]=g;if((c|0)<(d|0)){break a}D[a+16>>2]=c;D[a+12>>2]=d;b=(c>>31)-((d>>31)+(c>>>0<d>>>0)|0)|0;c=c-d|0;if(!b&c>>>0>2147483646|b){break a}e=1;b=c+1|0;D[a+20>>2]=b;c=b>>>1|0;D[a+24>>2]=c;D[a+28>>2]=0-c;if(b&1){break a}D[a+24>>2]=c-1;break a}}e=0}$=h+32|0;return e|0}function Zh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;g=$-32|0;$=g;D[a+68>>2]=f;e=D[a+56>>2];d=D[e>>2];f=D[e+4>>2];D[g+24>>2]=0;D[g+16>>2]=0;D[g+20>>2]=0;a:{f=f-d|0;if((f|0)<=0){break a}d=D[e>>2];if((d|0)!=D[e+4>>2]){m=a+112|0;n=a+60|0;e=f>>>2|0;o=e>>>0>1?e:1;while(1){Lb(n,D[(k<<2)+d>>2],g+16|0);e=D[g+20>>2];d=e>>31;f=D[g+16>>2];h=f>>31;j=D[g+24>>2];i=j>>31;i=i^i+j;h=i+((d^d+e)+(h^f+h)|0)|0;d=0;d=h>>>0<i>>>0?1:d;b:{if(!(d|h)){D[g+16>>2]=D[a+108>>2];break b}i=D[a+108>>2];l=i>>31;f=ai($h(i,l,f,f>>31),aa,h,d);D[g+16>>2]=f;d=ai($h(i,l,e,e>>31),aa,h,d);D[g+20>>2]=d;e=d;d=d>>31;e=e+d^d;d=f>>31;d=e+(d+f^d)|0;if((j|0)>=0){D[g+24>>2]=i-d;break b}D[g+24>>2]=d-i}d=Ga(m);f=D[g+16>>2];c:{if(d){D[g+24>>2]=0-D[g+24>>2];e=0-D[g+20>>2]|0;D[g+20>>2]=e;f=0-f|0;D[g+16>>2]=f;break c}e=D[g+20>>2]}d:{if((f|0)>=0){d=D[a+108>>2];f=d+D[g+24>>2]|0;d=d+e|0;break d}e:{if((e|0)<0){f=D[g+24>>2];d=f>>31;d=d^d+f;break e}f=D[g+24>>2];d=f>>31;d=D[a+100>>2]-(d^d+f)|0}if((f|0)<0){f=e;e=e>>31;f=f+e^e;break d}f=e;e=e>>31;f=D[a+100>>2]-(f+e^e)|0}e=D[a+100>>2];f:{if(!(d|f)){f=e;d=f;break f}if(!((e|0)!=(f|0)|d)){d=f;break f}h=(d|0)!=(e|0);if(!(f|h)){f=d;break f}g:{if(d){break g}j=D[a+108>>2];if((j|0)>=(f|0)){break g}f=(j<<1)-f|0;d=0;break f}h:{if(h){break h}h=D[a+108>>2];if((h|0)<=(f|0)){break h}f=(h<<1)-f|0;break f}i:{if((e|0)!=(f|0)){break i}e=D[a+108>>2];if((e|0)<=(d|0)){break i}d=(e<<1)-d|0;break f}if(f){break f}f=0;e=D[a+108>>2];if((e|0)>=(d|0)){break f}d=(e<<1)-d|0}D[g+12>>2]=f;D[g+8>>2]=d;j:{if(D[a+8>>2]<=0){break j}h=D[a+32>>2];f=0;while(1){e=D[a+16>>2];k:{if((e|0)<(d|0)){D[h+(f<<2)>>2]=e;break k}e=h+(f<<2)|0;j=D[a+12>>2];if((j|0)>(d|0)){D[e>>2]=j;break k}D[e>>2]=d}f=f+1|0;e=D[a+8>>2];if((f|0)<(e|0)){d=D[(g+8|0)+(f<<2)>>2];continue}break}d=0;if((e|0)<=0){break j}e=k<<3;j=e+c|0;i=b+e|0;while(1){f=d<<2;e=f+j|0;f=D[f+i>>2]+D[f+h>>2]|0;D[e>>2]=f;l:{if((f|0)>D[a+16>>2]){f=f-D[a+20>>2]|0}else{if((f|0)>=D[a+12>>2]){break l}f=f+D[a+20>>2]|0}D[e>>2]=f}d=d+1|0;if((d|0)<D[a+8>>2]){continue}break}}k=k+1|0;if((o|0)==(k|0)){break a}e=D[a+56>>2];d=D[e>>2];if(D[e+4>>2]-d>>2>>>0>k>>>0){continue}break}}ua();T()}$=g+32|0;return 1}function Ph(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;g=$-32|0;$=g;D[a+68>>2]=f;e=D[a+56>>2];d=D[e>>2];f=D[e+4>>2];D[g+24>>2]=0;D[g+16>>2]=0;D[g+20>>2]=0;a:{f=f-d|0;if((f|0)<=0){break a}d=D[e>>2];if((d|0)!=D[e+4>>2]){m=a+112|0;n=a+60|0;e=f>>>2|0;o=e>>>0>1?e:1;while(1){Jb(n,D[(k<<2)+d>>2],g+16|0);e=D[g+20>>2];d=e>>31;f=D[g+16>>2];h=f>>31;j=D[g+24>>2];i=j>>31;i=i^i+j;h=i+((d^d+e)+(h^f+h)|0)|0;d=0;d=h>>>0<i>>>0?1:d;b:{if(!(d|h)){D[g+16>>2]=D[a+108>>2];break b}i=D[a+108>>2];l=i>>31;f=ai($h(i,l,f,f>>31),aa,h,d);D[g+16>>2]=f;d=ai($h(i,l,e,e>>31),aa,h,d);D[g+20>>2]=d;e=d;d=d>>31;e=e+d^d;d=f>>31;d=e+(d+f^d)|0;if((j|0)>=0){D[g+24>>2]=i-d;break b}D[g+24>>2]=d-i}d=Ga(m);f=D[g+16>>2];c:{if(d){D[g+24>>2]=0-D[g+24>>2];e=0-D[g+20>>2]|0;D[g+20>>2]=e;f=0-f|0;D[g+16>>2]=f;break c}e=D[g+20>>2]}d:{if((f|0)>=0){d=D[a+108>>2];f=d+D[g+24>>2]|0;d=d+e|0;break d}e:{if((e|0)<0){f=D[g+24>>2];d=f>>31;d=d^d+f;break e}f=D[g+24>>2];d=f>>31;d=D[a+100>>2]-(d^d+f)|0}if((f|0)<0){f=e;e=e>>31;f=f+e^e;break d}f=e;e=e>>31;f=D[a+100>>2]-(f+e^e)|0}e=D[a+100>>2];f:{if(!(d|f)){f=e;d=f;break f}if(!((e|0)!=(f|0)|d)){d=f;break f}h=(d|0)!=(e|0);if(!(f|h)){f=d;break f}g:{if(d){break g}j=D[a+108>>2];if((j|0)>=(f|0)){break g}f=(j<<1)-f|0;d=0;break f}h:{if(h){break h}h=D[a+108>>2];if((h|0)<=(f|0)){break h}f=(h<<1)-f|0;break f}i:{if((e|0)!=(f|0)){break i}e=D[a+108>>2];if((e|0)<=(d|0)){break i}d=(e<<1)-d|0;break f}if(f){break f}f=0;e=D[a+108>>2];if((e|0)>=(d|0)){break f}d=(e<<1)-d|0}D[g+12>>2]=f;D[g+8>>2]=d;j:{if(D[a+8>>2]<=0){break j}h=D[a+32>>2];f=0;while(1){e=D[a+16>>2];k:{if((e|0)<(d|0)){D[h+(f<<2)>>2]=e;break k}e=h+(f<<2)|0;j=D[a+12>>2];if((j|0)>(d|0)){D[e>>2]=j;break k}D[e>>2]=d}f=f+1|0;e=D[a+8>>2];if((f|0)<(e|0)){d=D[(g+8|0)+(f<<2)>>2];continue}break}d=0;if((e|0)<=0){break j}e=k<<3;j=e+c|0;i=b+e|0;while(1){f=d<<2;e=f+j|0;f=D[f+i>>2]+D[f+h>>2]|0;D[e>>2]=f;l:{if((f|0)>D[a+16>>2]){f=f-D[a+20>>2]|0}else{if((f|0)>=D[a+12>>2]){break l}f=f+D[a+20>>2]|0}D[e>>2]=f}d=d+1|0;if((d|0)<D[a+8>>2]){continue}break}}k=k+1|0;if((o|0)==(k|0)){break a}e=D[a+56>>2];d=D[e>>2];if(D[e+4>>2]-d>>2>>>0>k>>>0){continue}break}}ua();T()}$=g+32|0;return 1}function Fh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;h=$-48|0;$=h;d=D[a+8>>2];if(d-31>>>0>=4294967267){D[a+76>>2]=d;e=-1<<d;d=-2-e|0;D[a+84>>2]=d;D[a+80>>2]=e^-1;D[a+92>>2]=(d|0)/2;H[a+88>>2]=K(2)/K(d|0)}D[a+52>>2]=f;e=D[a+40>>2];d=D[e>>2];g=D[e+4>>2];f=0;D[h+16>>2]=0;D[h+8>>2]=0;D[h+12>>2]=0;a:{g=g-d|0;if((g|0)<=0){break a}d=D[e>>2];if((d|0)!=D[e+4>>2]){m=a+8|0;n=a+96|0;o=a+44|0;e=g>>>2|0;p=e>>>0>1?e:1;while(1){Lb(o,D[(f<<2)+d>>2],h+8|0);e=D[h+12>>2];d=e>>31;g=D[h+8>>2];i=g>>31;k=D[h+16>>2];j=k>>31;j=j^j+k;i=j+((d^d+e)+(i^g+i)|0)|0;d=0;d=i>>>0<j>>>0?1:d;b:{if(!(d|i)){D[h+8>>2]=D[a+92>>2];break b}j=D[a+92>>2];l=j>>31;g=ai($h(j,l,g,g>>31),aa,i,d);D[h+8>>2]=g;d=ai($h(j,l,e,e>>31),aa,i,d);D[h+12>>2]=d;e=d;d=d>>31;e=e+d^d;d=g>>31;d=e+(d+g^d)|0;if((k|0)>=0){D[h+16>>2]=j-d;break b}D[h+16>>2]=d-j}d=Ga(n);e=D[h+8>>2];c:{if(d){D[h+16>>2]=0-D[h+16>>2];g=0-D[h+12>>2]|0;D[h+12>>2]=g;e=0-e|0;D[h+8>>2]=e;break c}g=D[h+12>>2]}d:{if((e|0)>=0){e=D[a+92>>2];d=e+D[h+16>>2]|0;e=e+g|0;break d}e:{if((g|0)<0){d=D[h+16>>2];e=d>>31;e=e^d+e;break e}d=D[h+16>>2];e=d>>31;e=D[a+84>>2]-(e^d+e)|0}if((d|0)<0){d=g>>31;d=d+g^d;break d}d=g>>31;d=D[a+84>>2]-(d+g^d)|0}g=D[a+84>>2];f:{if(!(d|e)){d=g;e=d;break f}if(!((d|0)!=(g|0)|e)){e=d;break f}i=(e|0)!=(g|0);if(!(d|i)){d=e;break f}g:{if(e){break g}k=D[a+92>>2];if((k|0)>=(d|0)){break g}d=(k<<1)-d|0;e=0;break f}h:{if(i){break h}i=D[a+92>>2];if((i|0)<=(d|0)){break h}d=(i<<1)-d|0;break f}i:{if((d|0)!=(g|0)){break i}g=D[a+92>>2];if((g|0)<=(e|0)){break i}e=(g<<1)-e|0;break f}if(d){break f}d=0;g=D[a+92>>2];if((g|0)>=(e|0)){break f}e=(g<<1)-e|0}g=f<<3;i=g+b|0;k=D[i+4>>2];i=D[i>>2];D[h+36>>2]=d;D[h+32>>2]=e;D[h+24>>2]=i;D[h+28>>2]=k;Ib(h+40|0,m,h+32|0,h+24|0);d=c+g|0;D[d>>2]=D[h+40>>2];D[d+4>>2]=D[h+44>>2];f=f+1|0;if((p|0)==(f|0)){break a}e=D[a+40>>2];d=D[e>>2];if(D[e+4>>2]-d>>2>>>0>f>>>0){continue}break}}ua();T()}$=h+48|0;return 1}function Bh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;h=$-48|0;$=h;d=D[a+8>>2];if(d-31>>>0>=4294967267){D[a+76>>2]=d;e=-1<<d;d=-2-e|0;D[a+84>>2]=d;D[a+80>>2]=e^-1;D[a+92>>2]=(d|0)/2;H[a+88>>2]=K(2)/K(d|0)}D[a+52>>2]=f;e=D[a+40>>2];d=D[e>>2];g=D[e+4>>2];f=0;D[h+16>>2]=0;D[h+8>>2]=0;D[h+12>>2]=0;a:{g=g-d|0;if((g|0)<=0){break a}d=D[e>>2];if((d|0)!=D[e+4>>2]){m=a+8|0;n=a+96|0;o=a+44|0;e=g>>>2|0;p=e>>>0>1?e:1;while(1){Jb(o,D[(f<<2)+d>>2],h+8|0);e=D[h+12>>2];d=e>>31;g=D[h+8>>2];i=g>>31;k=D[h+16>>2];j=k>>31;j=j^j+k;i=j+((d^d+e)+(i^g+i)|0)|0;d=0;d=i>>>0<j>>>0?1:d;b:{if(!(d|i)){D[h+8>>2]=D[a+92>>2];break b}j=D[a+92>>2];l=j>>31;g=ai($h(j,l,g,g>>31),aa,i,d);D[h+8>>2]=g;d=ai($h(j,l,e,e>>31),aa,i,d);D[h+12>>2]=d;e=d;d=d>>31;e=e+d^d;d=g>>31;d=e+(d+g^d)|0;if((k|0)>=0){D[h+16>>2]=j-d;break b}D[h+16>>2]=d-j}d=Ga(n);e=D[h+8>>2];c:{if(d){D[h+16>>2]=0-D[h+16>>2];g=0-D[h+12>>2]|0;D[h+12>>2]=g;e=0-e|0;D[h+8>>2]=e;break c}g=D[h+12>>2]}d:{if((e|0)>=0){e=D[a+92>>2];d=e+D[h+16>>2]|0;e=e+g|0;break d}e:{if((g|0)<0){d=D[h+16>>2];e=d>>31;e=e^d+e;break e}d=D[h+16>>2];e=d>>31;e=D[a+84>>2]-(e^d+e)|0}if((d|0)<0){d=g>>31;d=d+g^d;break d}d=g>>31;d=D[a+84>>2]-(d+g^d)|0}g=D[a+84>>2];f:{if(!(d|e)){d=g;e=d;break f}if(!((d|0)!=(g|0)|e)){e=d;break f}i=(e|0)!=(g|0);if(!(d|i)){d=e;break f}g:{if(e){break g}k=D[a+92>>2];if((k|0)>=(d|0)){break g}d=(k<<1)-d|0;e=0;break f}h:{if(i){break h}i=D[a+92>>2];if((i|0)<=(d|0)){break h}d=(i<<1)-d|0;break f}i:{if((d|0)!=(g|0)){break i}g=D[a+92>>2];if((g|0)<=(e|0)){break i}e=(g<<1)-e|0;break f}if(d){break f}d=0;g=D[a+92>>2];if((g|0)>=(e|0)){break f}e=(g<<1)-e|0}g=f<<3;i=g+b|0;k=D[i+4>>2];i=D[i>>2];D[h+36>>2]=d;D[h+32>>2]=e;D[h+24>>2]=i;D[h+28>>2]=k;Ib(h+40|0,m,h+32|0,h+24|0);d=c+g|0;D[d>>2]=D[h+40>>2];D[d+4>>2]=D[h+44>>2];f=f+1|0;if((p|0)==(f|0)){break a}e=D[a+40>>2];d=D[e>>2];if(D[e+4>>2]-d>>2>>>0>f>>>0){continue}break}}ua();T()}$=h+48|0;return 1}function Kh(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;e=$-32|0;$=e;a:{if((c|0)!=3){break a}c=D[a+4>>2];a=D[a+12>>2];D[e+24>>2]=-1;D[e+16>>2]=-1;D[e+20>>2]=1065353216;D[e+8>>2]=-1;D[e+12>>2]=-1;if((b|0)==-2){break a}k=D[D[D[c+4>>2]+8>>2]+(a<<2)>>2];if((ba[D[D[c>>2]+8>>2]](c)|0)==1){j=D[D[D[c+4>>2]+8>>2]+(a<<2)>>2];b:{c:{if((ba[D[D[c>>2]+8>>2]](c)|0)!=1|b-1>>>0>5){break c}h=ba[D[D[c>>2]+36>>2]](c)|0;f=ba[D[D[c>>2]+44>>2]](c,a)|0;if(!h|!f){break c}g=f+12|0;i=ba[D[D[c>>2]+40>>2]](c,a)|0;c=D[c+44>>2];if(i){if((b|0)!=6){break c}a=na(112);D[a+4>>2]=j;b=D[e+12>>2];D[a+8>>2]=D[e+8>>2];D[a+12>>2]=b;b=D[e+20>>2];D[a+16>>2]=D[e+16>>2];D[a+20>>2]=b;D[a+24>>2]=D[e+24>>2];D[a+40>>2]=f;D[a+36>>2]=g;D[a+32>>2]=i;D[a+28>>2]=c;D[a+68>>2]=f;D[a- -64>>2]=g;D[a+60>>2]=i;D[a+56>>2]=c;D[a+48>>2]=0;D[a+52>>2]=0;D[a>>2]=5956;D[a+88>>2]=1065353216;D[a+92>>2]=-1;D[a+80>>2]=-1;D[a+84>>2]=-1;D[a+72>>2]=1;D[a+76>>2]=-1;D[a+44>>2]=6520;b=a+96|0;D[b>>2]=0;D[b+4>>2]=0;B[b+5|0]=0;B[b+6|0]=0;B[b+7|0]=0;B[b+8|0]=0;B[b+9|0]=0;B[b+10|0]=0;B[b+11|0]=0;B[b+12|0]=0;break b}if((b|0)!=6){break c}d=na(112);D[d+4>>2]=j;a=D[e+12>>2];D[d+8>>2]=D[e+8>>2];D[d+12>>2]=a;a=D[e+20>>2];D[d+16>>2]=D[e+16>>2];D[d+20>>2]=a;D[d+24>>2]=D[e+24>>2];D[d+40>>2]=f;D[d+36>>2]=g;D[d+32>>2]=h;D[d+28>>2]=c;D[d+68>>2]=f;D[d- -64>>2]=g;D[d+60>>2]=h;D[d+56>>2]=c;D[d+48>>2]=0;D[d+52>>2]=0;D[d>>2]=6960;D[d+88>>2]=1065353216;D[d+92>>2]=-1;D[d+80>>2]=-1;D[d+84>>2]=-1;D[d+72>>2]=1;D[d+76>>2]=-1;D[d+44>>2]=7380;a=d+96|0;D[a>>2]=0;D[a+4>>2]=0;B[a+5|0]=0;B[a+6|0]=0;B[a+7|0]=0;B[a+8|0]=0;B[a+9|0]=0;B[a+10|0]=0;B[a+11|0]=0;B[a+12|0]=0}a=d}d=a;if(a){break a}}d=na(28);D[d+4>>2]=k;a=D[e+12>>2];D[d+8>>2]=D[e+8>>2];D[d+12>>2]=a;a=D[e+20>>2];D[d+16>>2]=D[e+16>>2];D[d+20>>2]=a;D[d+24>>2]=D[e+24>>2];D[d>>2]=7792}$=e+32|0;return d|0}function jf(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;f=$-32|0;$=f;e=f+8|0;c=$-80|0;$=c;a=D[b+36>>2];D[c+72>>2]=D[b+32>>2];D[c+76>>2]=a;d=D[b+28>>2];a=c- -64|0;D[a>>2]=D[b+24>>2];D[a+4>>2]=d;a=D[b+20>>2];D[c+56>>2]=D[b+16>>2];D[c+60>>2]=a;a=D[b+12>>2];D[c+48>>2]=D[b+8>>2];D[c+52>>2]=a;a=D[b+4>>2];D[c+40>>2]=D[b>>2];D[c+44>>2]=a;ec(c+8|0,c+40|0,c+24|0);a=D[c+8>>2];a:{if(a){D[e>>2]=a;a=e+4|0;if(B[c+23|0]>=0){b=c+8|4;e=D[b+4>>2];D[a>>2]=D[b>>2];D[a+4>>2]=e;D[a+8>>2]=D[b+8>>2];break a}ta(a,D[c+12>>2],D[c+16>>2]);if(B[c+23|0]>=0){break a}ma(D[c+12>>2]);break a}if(B[c+23|0]<0){ma(D[c+12>>2])}a=E[c+31|0];if(a>>>0>=2){a=na(32);b=E[1619]|E[1620]<<8;B[a+24|0]=b;B[a+25|0]=b>>>8;b=E[1615]|E[1616]<<8|(E[1617]<<16|E[1618]<<24);d=E[1611]|E[1612]<<8|(E[1613]<<16|E[1614]<<24);B[a+16|0]=d;B[a+17|0]=d>>>8;B[a+18|0]=d>>>16;B[a+19|0]=d>>>24;B[a+20|0]=b;B[a+21|0]=b>>>8;B[a+22|0]=b>>>16;B[a+23|0]=b>>>24;b=E[1607]|E[1608]<<8|(E[1609]<<16|E[1610]<<24);d=E[1603]|E[1604]<<8|(E[1605]<<16|E[1606]<<24);B[a+8|0]=d;B[a+9|0]=d>>>8;B[a+10|0]=d>>>16;B[a+11|0]=d>>>24;B[a+12|0]=b;B[a+13|0]=b>>>8;B[a+14|0]=b>>>16;B[a+15|0]=b>>>24;b=E[1599]|E[1600]<<8|(E[1601]<<16|E[1602]<<24);d=E[1595]|E[1596]<<8|(E[1597]<<16|E[1598]<<24);B[a|0]=d;B[a+1|0]=d>>>8;B[a+2|0]=d>>>16;B[a+3|0]=d>>>24;B[a+4|0]=b;B[a+5|0]=b>>>8;B[a+6|0]=b>>>16;B[a+7|0]=b>>>24;B[a+26|0]=0;D[c+8>>2]=-1;b=c+8|4;ta(b,a,26);d=B[c+23|0];D[e>>2]=D[c+8>>2];e=e+4|0;b:{if((d|0)>=0){d=D[b+4>>2];D[e>>2]=D[b>>2];D[e+4>>2]=d;D[e+8>>2]=D[b+8>>2];break b}ta(e,D[c+12>>2],D[c+16>>2])}if(B[c+23|0]<0){ma(D[c+12>>2])}ma(a);break a}D[e>>2]=0;D[e+4>>2]=0;D[e+16>>2]=a;D[e+8>>2]=0;D[e+12>>2]=0}$=c+80|0;a=D[f+24>>2];if(B[f+23|0]<0){ma(D[f+12>>2])}$=f+32|0;return a|0}function Jf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;i=c;a:{d=a;if(D[d+12>>2]==(b|0)){break a}a=b;b=D[d+4>>2];c=D[d>>2];if((b|0)!=(c|0)){while(1){e=b-12|0;if(B[b-1|0]<0){ma(D[e>>2])}b=e;if((c|0)!=(b|0)){continue}break}}D[d+12>>2]=a;D[d+4>>2]=c;b=D[a>>2];j=a+4|0;if((b|0)==(j|0)){break a}while(1){b:{if(D[d+8>>2]!=(c|0)){c:{if(B[b+27|0]>=0){a=D[b+20>>2];D[c>>2]=D[b+16>>2];D[c+4>>2]=a;D[c+8>>2]=D[b+24>>2];break c}ta(c,D[b+16>>2],D[b+20>>2])}D[d+4>>2]=c+12;break b}g=0;d:{e:{f:{e=D[d+4>>2];a=D[d>>2];f=(e-a|0)/12|0;c=f+1|0;if(c>>>0<357913942){h=(D[d+8>>2]-a|0)/12|0;k=h<<1;c=h>>>0<178956970?c>>>0>k>>>0?c:k:357913941;if(c){if(c>>>0>=357913942){break f}g=na(J(c,12))}h=J(c,12);c=J(f,12)+g|0;g:{if(B[b+27|0]>=0){f=D[b+20>>2];D[c>>2]=D[b+16>>2];D[c+4>>2]=f;D[c+8>>2]=D[b+24>>2];break g}ta(c,D[b+16>>2],D[b+20>>2]);e=D[d+4>>2];a=D[d>>2]}g=g+h|0;f=c+12|0;if((a|0)==(e|0)){break e}while(1){e=e-12|0;h=D[e+4>>2];c=c-12|0;D[c>>2]=D[e>>2];D[c+4>>2]=h;D[c+8>>2]=D[e+8>>2];D[e>>2]=0;D[e+4>>2]=0;D[e+8>>2]=0;if((a|0)!=(e|0)){continue}break}D[d+8>>2]=g;a=D[d+4>>2];D[d+4>>2]=f;e=D[d>>2];D[d>>2]=c;if((a|0)==(e|0)){break d}while(1){c=a-12|0;if(B[a-1|0]<0){ma(D[c>>2])}a=c;if((c|0)!=(e|0)){continue}break}break d}qa();T()}ra(1326);T()}D[d+8>>2]=g;D[d+4>>2]=f;D[d>>2]=c}if(e){ma(e)}}c=D[b+4>>2];h:{if(!c){a=D[b+8>>2];if(D[a>>2]==(b|0)){break h}b=b+8|0;while(1){c=D[b>>2];b=c+8|0;a=D[c+8>>2];if((c|0)!=D[a>>2]){continue}break}break h}while(1){a=c;c=D[c>>2];if(c){continue}break}}if((a|0)==(j|0)){break a}c=D[d+4>>2];b=a;continue}}c=0;i:{if((i|0)<0){break i}a=D[d>>2];if((D[d+4>>2]-a|0)/12>>>0<=i>>>0){break i}a=a+J(i,12)|0;c=B[a+11|0]<0?D[a>>2]:a}return c|0}function ic(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;a:{if(!F[b+38>>1]){break a}if(!Sa(1,a+12|0,b)){break a}f=D[a+12>>2];c=D[a>>2];d=D[a+4>>2]-c>>2;b:{if(f>>>0>d>>>0){sa(a,f-d|0);f=D[a+12>>2];break b}if(d>>>0<=f>>>0){break b}D[a+4>>2]=c+(f<<2)}if(!f){return 1}j=D[b+8>>2];i=D[b+12>>2];l=D[a>>2];while(1){d=D[b+20>>2];c=D[b+16>>2];if((i|0)<=(d|0)&j>>>0<=c>>>0|(d|0)>(i|0)){return 0}m=D[b>>2];k=E[m+c|0];c=c+1|0;d=c?d:d+1|0;h=c;D[b+16>>2]=c;D[b+20>>2]=d;c=k>>>2|0;e=0;c:{d:{e:{f:{n=k&3;switch(n|0){case 3:break f;case 0:break d;default:break e}}d=c+g|0;if(d>>>0>=f>>>0){return 0}pa(l+(g<<2)|0,0,(k&252)+4|0);g=d;break c}while(1){if((d|0)>=(i|0)&h>>>0>=j>>>0|(d|0)>(i|0)){break a}f=E[h+m|0];h=h+1|0;d=h?d:d+1|0;D[b+16>>2]=h;D[b+20>>2]=d;c=f<<(e<<3|6)|c;e=e+1|0;if((n|0)!=(e|0)){continue}break}}D[l+(g<<2)>>2]=c}f=D[a+12>>2];g=g+1|0;if(f>>>0>g>>>0){continue}break}d=a+16|0;k=D[a>>2];b=D[a+16>>2];c=D[a+20>>2]-b|0;g=c>>2;g:{if(g>>>0<=4095){sa(d,4096-g|0);break g}if((c|0)==16384){break g}D[a+20>>2]=b+16384}b=a+28|0;g=D[b>>2];c=D[a+32>>2]-g>>3;h:{if(c>>>0<f>>>0){bb(b,f-c|0);g=D[b>>2];break h}if(c>>>0>f>>>0){D[a+32>>2]=(f<<3)+g}if(!f){break a}}e=0;a=0;while(1){b=k+(e<<2)|0;i=D[b>>2];c=a;h=(e<<3)+g|0;D[h+4>>2]=a;D[h>>2]=i;i=D[b>>2];a=i+a|0;if(a>>>0>4096){break a}i:{if(a>>>0<=c>>>0){break i}h=D[d>>2];b=0;j=i&7;if(j){while(1){D[h+(c<<2)>>2]=e;c=c+1|0;b=b+1|0;if((j|0)!=(b|0)){continue}break}}if(i-1>>>0<=6){break i}while(1){b=h+(c<<2)|0;D[b>>2]=e;D[b+28>>2]=e;D[b+24>>2]=e;D[b+20>>2]=e;D[b+16>>2]=e;D[b+12>>2]=e;D[b+8>>2]=e;D[b+4>>2]=e;c=c+8|0;if((c|0)!=(a|0)){continue}break}}e=e+1|0;if((f|0)!=(e|0)){continue}break}o=(a|0)==4096}return o}function Jc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;k=$-16|0;$=k;D[k+8>>2]=c;e=D[a+12>>2];d=D[a+8>>2];f=e-d>>2;a:{if((f|0)>(b|0)){break a}h=b+1|0;if(h>>>0>f>>>0){f=h-f|0;g=D[a+16>>2];d=D[a+12>>2];if(f>>>0<=g-d>>2>>>0){if(f){e=d;d=f<<2;d=pa(e,0,d)+d|0}D[a+12>>2]=d;break a}b:{c:{d:{h=D[a+8>>2];j=d-h>>2;e=j+f|0;if(e>>>0<1073741824){g=g-h|0;l=g>>1;g=g>>2>>>0<536870911?e>>>0>l>>>0?e:l:1073741823;if(g){if(g>>>0>=1073741824){break d}i=na(g<<2)}e=(j<<2)+i|0;j=f<<2;f=pa(e,0,j);j=f+j|0;g=(g<<2)+i|0;if((d|0)==(h|0)){break c}while(1){d=d-4|0;f=D[d>>2];D[d>>2]=0;e=e-4|0;D[e>>2]=f;if((d|0)!=(h|0)){continue}break}D[a+16>>2]=g;f=D[a+12>>2];D[a+12>>2]=j;d=D[a+8>>2];D[a+8>>2]=e;if((d|0)==(f|0)){break b}while(1){f=f-4|0;e=D[f>>2];D[f>>2]=0;if(e){ya(e)}if((d|0)!=(f|0)){continue}break}break b}qa();T()}ra(1326);T()}D[a+16>>2]=g;D[a+12>>2]=j;D[a+8>>2]=f}if(d){ma(d)}break a}if(f>>>0<=h>>>0){break a}d=d+(h<<2)|0;if((d|0)!=(e|0)){while(1){e=e-4|0;c=D[e>>2];D[e>>2]=0;if(c){ya(c)}if((d|0)!=(e|0)){continue}break}c=D[k+8>>2]}D[a+12>>2]=d}e:{f:{d=D[c+56>>2];g:{if((d|0)>4){break g}e=J(d,12)+a|0;d=D[e+24>>2];if((d|0)!=D[e+28>>2]){D[d>>2]=b;D[e+24>>2]=d+4;break g}f=D[e+20>>2];h=d-f|0;i=h>>2;d=i+1|0;if(d>>>0>=1073741824){break f}g=h>>1;g=i>>>0<536870911?d>>>0>g>>>0?d:g:1073741823;if(g){if(g>>>0>=1073741824){break e}d=na(g<<2)}else{d=0}i=d+(i<<2)|0;D[i>>2]=b;if((h|0)>0){oa(d,f,h)}D[e+20>>2]=d;D[e+24>>2]=i+4;D[e+28>>2]=d+(g<<2);if(!f){break g}ma(f)}D[c+60>>2]=b;a=D[a+8>>2];D[k+8>>2]=0;b=a+(b<<2)|0;a=D[b>>2];D[b>>2]=c;if(a){ya(a)}a=D[k+8>>2];D[k+8>>2]=0;if(a){ya(a)}$=k+16|0;return}qa();T()}ra(1326);T()}function rf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;e=$-16|0;$=e;k=D[b+80>>2];n=E[c+24|0];b=n<<24>>24;f=J(k,b);a:{b:{c:{a=D[c+28>>2];d:{if(!(!((a|0)==1|(a|0)==2)|!E[c+84|0])){a=D[c+48>>2];c=D[D[c>>2]>>2];b=0;D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;if(f){if((f|0)<0){break c}g=na(f);b=oa(g,a+c|0,f)+f|0}a=D[d>>2];if(a){D[d+4>>2]=a;ma(a)}D[d+8>>2]=b;D[d+4>>2]=b;D[d>>2]=g;a=1;break d}D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;if(b){if((b|0)<0){break c}g=na(b);D[e>>2]=g;a=b+g|0;D[e+8>>2]=a;pa(g,0,b);D[e+4>>2]=a}a=D[d+4>>2];i=D[d>>2];j=a-i|0;e:{if(j>>>0<f>>>0){l=f-j|0;m=D[d+8>>2];if(l>>>0<=m-a>>>0){if(l){a=pa(a,0,l)+l|0}D[d+4>>2]=a;break e}if((f|0)<0){break b}a=m-i|0;m=a<<1;a=a>>>0<1073741823?f>>>0>m>>>0?f:m:2147483647;if(a){h=na(a)}pa(h+j|0,0,l);if((j|0)>0){oa(h,i,j)}D[d+8>>2]=a+h;D[d+4>>2]=f+h;D[d>>2]=h;if(!i){break e}ma(i);break e}if(f>>>0>=j>>>0){break e}D[d+4>>2]=f+i}f:{if(!k){b=0;break f}if(!b){a=0;b=1;while(1){if(!Db(c,E[c+84|0]?a:D[D[c+68>>2]+(a<<2)>>2],B[c+24|0],g)){break f}a=a+1|0;b=k>>>0>a>>>0;if((a|0)!=(k|0)){continue}break}break f}a=b-1|0;i=a&-2;j=a&1;a=0;b=1;h=0;while(1){f=D[e>>2];if(Db(c,E[c+84|0]?h:D[D[c+68>>2]+(h<<2)>>2],B[c+24|0],f)){B[D[d>>2]+a|0]=E[f|0];b=1;a=a+1|0;g=0;g:{h:{switch(n-1|0){default:while(1){B[D[d>>2]+a|0]=E[D[e>>2]+b|0];B[(D[d>>2]+a|0)+1|0]=E[(D[e>>2]+b|0)+1|0];b=b+2|0;a=a+2|0;g=g+2|0;if((i|0)!=(g|0)){continue}break};break;case 0:break g;case 1:break h}}if(!j){break g}B[D[d>>2]+a|0]=E[D[e>>2]+b|0];a=a+1|0}h=h+1|0;b=k>>>0>h>>>0;if((h|0)!=(k|0)){continue}}break}g=D[e>>2]}if(g){ma(g)}a=!b}$=e+16|0;a=a&1;break a}qa();T()}qa();T()}return a|0}function qf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;e=$-16|0;$=e;k=D[b+80>>2];n=E[c+24|0];b=n<<24>>24;f=J(k,b);a:{b:{c:{a=D[c+28>>2];d:{if(!(!((a|0)==1|(a|0)==2)|!E[c+84|0])){a=D[c+48>>2];c=D[D[c>>2]>>2];b=0;D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;if(f){if((f|0)<0){break c}g=na(f);b=oa(g,a+c|0,f)+f|0}a=D[d>>2];if(a){D[d+4>>2]=a;ma(a)}D[d+8>>2]=b;D[d+4>>2]=b;D[d>>2]=g;a=1;break d}D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;if(b){if((b|0)<0){break c}g=na(b);D[e>>2]=g;a=b+g|0;D[e+8>>2]=a;pa(g,0,b);D[e+4>>2]=a}a=D[d+4>>2];i=D[d>>2];j=a-i|0;e:{if(j>>>0<f>>>0){l=f-j|0;m=D[d+8>>2];if(l>>>0<=m-a>>>0){if(l){a=pa(a,0,l)+l|0}D[d+4>>2]=a;break e}if((f|0)<0){break b}a=m-i|0;m=a<<1;a=a>>>0<1073741823?f>>>0>m>>>0?f:m:2147483647;if(a){h=na(a)}pa(h+j|0,0,l);if((j|0)>0){oa(h,i,j)}D[d+8>>2]=a+h;D[d+4>>2]=f+h;D[d>>2]=h;if(!i){break e}ma(i);break e}if(f>>>0>=j>>>0){break e}D[d+4>>2]=f+i}f:{if(!k){b=0;break f}if(!b){a=0;b=1;while(1){if(!Cb(c,E[c+84|0]?a:D[D[c+68>>2]+(a<<2)>>2],B[c+24|0],g)){break f}a=a+1|0;b=k>>>0>a>>>0;if((a|0)!=(k|0)){continue}break}break f}a=b-1|0;i=a&-2;j=a&1;a=0;b=1;h=0;while(1){f=D[e>>2];if(Cb(c,E[c+84|0]?h:D[D[c+68>>2]+(h<<2)>>2],B[c+24|0],f)){B[D[d>>2]+a|0]=E[f|0];b=1;a=a+1|0;g=0;g:{h:{switch(n-1|0){default:while(1){B[D[d>>2]+a|0]=E[D[e>>2]+b|0];B[(D[d>>2]+a|0)+1|0]=E[(D[e>>2]+b|0)+1|0];b=b+2|0;a=a+2|0;g=g+2|0;if((i|0)!=(g|0)){continue}break};break;case 0:break g;case 1:break h}}if(!j){break g}B[D[d>>2]+a|0]=E[D[e>>2]+b|0];a=a+1|0}h=h+1|0;b=k>>>0>h>>>0;if((h|0)!=(k|0)){continue}}break}g=D[e>>2]}if(g){ma(g)}a=!b}$=e+16|0;a=a&1;break a}qa();T()}qa();T()}return a|0}function Zc(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;g=$-80|0;$=g;a:{if(!Vb(1,g+76|0,b)){break a}h=D[g+76>>2];if(!h){break a}d=D[b+8>>2];c=D[b+16>>2];d=$h(d-c|0,D[b+12>>2]-(D[b+20>>2]+(c>>>0>d>>>0)|0)|0,5,0);c=aa;if(d>>>0<h>>>0&(c|0)<=0|(c|0)<0){break a}c=D[a+4>>2];d=D[a+8>>2]-c>>2;b:{if(d>>>0<h>>>0){sa(a+4|0,h-d|0);break b}if(d>>>0<=h>>>0){break b}D[a+8>>2]=c+(h<<2)}r=a+16|0;j=D[a+32>>2];k=1;while(1){c:{e=D[b+12>>2];c=e;d=D[b+20>>2];p=D[b+8>>2];m=D[b+16>>2];if((c|0)<=(d|0)&p>>>0<=m>>>0|(c|0)<(d|0)){break c}q=D[b>>2];o=E[q+m|0];c=d;f=m+1|0;c=f?c:c+1|0;D[b+16>>2]=f;D[b+20>>2]=c;if((c|0)>=(e|0)&f>>>0>=p>>>0|(c|0)>(e|0)){break c}f=E[f+q|0];c=d;i=m+2|0;c=i>>>0<2?c+1|0:c;D[b+16>>2]=i;D[b+20>>2]=c;if((c|0)>=(e|0)&i>>>0>=p>>>0|(c|0)>(e|0)){break c}i=E[i+q|0];c=d;n=m+3|0;c=n>>>0<3?c+1|0:c;D[b+16>>2]=n;D[b+20>>2]=c;if((c|0)>=(e|0)&n>>>0>=p>>>0|(c|0)>(e|0)){break c}e=E[n+q|0];c=d;d=m+4|0;c=d>>>0<4?c+1|0:c;D[b+16>>2]=d;D[b+20>>2]=c;if(!i|((f-12&255)>>>0<245|o>>>0>4)){break c}c=lb(g+8|0);n=i<<24>>24;e=(e|0)!=0;d=f-1|0;if(d>>>0<=10){d=D[(d<<2)+10180>>2]}else{d=-1}d=J(d,i);$b(c,o,n,f,e,d,d>>31);if(!Vb(1,g+4|0,b)){break c}f=D[g+4>>2];D[g+68>>2]=f;d=Zb(na(96),c);ba[D[D[j>>2]+8>>2]](j,D[j+12>>2]-D[j+8>>2]>>2,d);d=(D[j+12>>2]-D[j+8>>2]>>2)-1|0;o=d<<2;D[D[o+D[j+8>>2]>>2]+60>>2]=f;D[D[a+4>>2]+(l<<2)>>2]=d;k=D[a+16>>2];c=D[a+20>>2]-k>>2;d:{if((c|0)>(d|0)){break d}D[g>>2]=-1;d=d+1|0;if(d>>>0>c>>>0){xa(r,d-c|0,g);k=D[r>>2];break d}if(c>>>0<=d>>>0){break d}D[a+20>>2]=(d<<2)+k}D[k+o>>2]=l;l=l+1|0;k=l>>>0<h>>>0;if((h|0)!=(l|0)){continue}}break}l=!k}$=g+80|0;return l&1}function td(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;i=$-16|0;$=i;D[i>>2]=b;g=-1;a:{if((b|0)==-1){D[i+4>>2]=-1;break a}c=b+1|0;D[i+4>>2]=(c>>>0)%3|0?c:b-2|0;if((b>>>0)%3|0){g=b-1|0;break a}g=b+2|0}D[i+8>>2]=g;m=(b|0)==-1?-1:(b>>>0)/3|0;b:{c:{d:{e:{while(1){f:{g:{if((b|0)!=-1){c=D[D[D[a+8>>2]+12>>2]+(b<<2)>>2];if((c|0)!=-1){break g}}g=0;c=D[a+216>>2];if((c|0)==D[a+220>>2]){break f}while(1){f=J(g,144)+c|0;c=D[f+136>>2];d=D[f+140>>2];h:{if(c>>>0<d>>>0){D[c>>2]=b;D[f+136>>2]=c+4;break h}h=D[f+132>>2];j=c-h|0;e=j>>2;c=e+1|0;if(c>>>0>=1073741824){break e}k=e<<2;d=d-h|0;e=d>>1;d=d>>2>>>0<536870911?c>>>0>e>>>0?c:e:1073741823;if(d){if(d>>>0>=1073741824){break d}c=na(d<<2)}else{c=0}e=k+c|0;D[e>>2]=b;if((j|0)>0){oa(c,h,j)}D[f+132>>2]=c;D[f+136>>2]=e+4;D[f+140>>2]=c+(d<<2);if(!h){break h}ma(h)}g=g+1|0;c=D[a+216>>2];if(g>>>0<(D[a+220>>2]-c|0)/144>>>0){continue}break}break f}if((c>>>0)/3>>>0<m>>>0){break f}g=0;if(D[a+220>>2]==D[a+216>>2]){break f}while(1){i:{if(!Ga(D[a+368>>2]+(g<<4)|0)){break i}f=D[a+216>>2]+J(g,144)|0;c=D[f+136>>2];d=D[f+140>>2];if(c>>>0<d>>>0){D[c>>2]=b;D[f+136>>2]=c+4;break i}h=D[f+132>>2];j=c-h|0;e=j>>2;c=e+1|0;if(c>>>0>=1073741824){break c}k=e<<2;d=d-h|0;e=d>>1;d=d>>2>>>0<536870911?c>>>0>e>>>0?c:e:1073741823;if(d){if(d>>>0>=1073741824){break b}c=na(d<<2)}else{c=0}e=k+c|0;D[e>>2]=b;if((j|0)>0){oa(c,h,j)}D[f+132>>2]=c;D[f+136>>2]=e+4;D[f+140>>2]=c+(d<<2);if(!h){break i}ma(h)}g=g+1|0;if(g>>>0<(D[a+220>>2]-D[a+216>>2]|0)/144>>>0){continue}break}}l=l+1|0;if((l|0)!=3){b=D[(l<<2)+i>>2];continue}break}$=i+16|0;return 1}qa();T()}ra(1326);T()}qa();T()}ra(1326);T()}function vd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;i=$-16|0;$=i;k=-1;a:{b:{c:{if(!Da(1,i+12|0,b)){break c}d=D[i+12>>2];if(d){c=D[a+8>>2];if(d>>>0>(D[c+4>>2]-D[c>>2]>>2>>>0)/3>>>0){break c}while(1){if(!Da(1,i+8|0,b)){break c}c=D[i+8>>2];if(!Da(1,i+8|0,b)){break c}f=c+f|0;c=D[i+8>>2];if(f>>>0<c>>>0){break c}g=f-c|0;c=D[a+40>>2];d:{if((c|0)!=D[a+44>>2]){D[c+4>>2]=f;D[c>>2]=g;D[a+40>>2]=c+12;break d}e=c;c=D[a+36>>2];j=e-c|0;h=(j|0)/12|0;e=h+1|0;if(e>>>0>=357913942){break b}l=h<<1;e=h>>>0<178956970?e>>>0>l>>>0?e:l:357913941;if(e>>>0>=357913942){break a}e=J(e,12);l=na(e);h=l+J(h,12)|0;D[h+4>>2]=f;D[h>>2]=g;g=h+J((j|0)/-12|0,12)|0;if((j|0)>0){oa(g,c,j)}D[a+44>>2]=e+l;D[a+40>>2]=h+12;D[a+36>>2]=g;if(!c){break d}ma(c)}m=m+1|0;if((d|0)!=(m|0)){continue}break}f=0;cc(b,0,0);h=d>>>0>1?d:1;while(1){d=E[b+36|0];c=F[D[a+4>>2]+36>>1];e:{f:{if(((c<<8|c>>>8)&65535)>>>0<=513){if(!d){break e}g=0;c=D[b+32>>2];j=c>>>3|0;k=D[b+24>>2];d=j+k|0;e=D[b+28>>2];g:{if(d>>>0>=e>>>0){d=c;break g}g=E[d|0];d=c+1|0;D[b+32>>2]=d;j=d>>>3|0;g=g>>>(c&7)&1}if(e>>>0>j+k>>>0){break f}break e}if(!d){break e}g=0;d=D[b+32>>2];c=D[b+24>>2]+(d>>>3|0)|0;if(c>>>0>=G[b+28>>2]){break e}g=E[c|0]>>>(d&7)&1}D[b+32>>2]=d+1}d=D[a+36>>2]+J(f,12)|0;B[d+8|0]=E[d+8|0]&254|g&1;f=f+1|0;if((h|0)!=(f|0)){continue}break}B[b+36|0]=0;c=D[b+20>>2];a=0;d=D[b+32>>2]+7|0;a=d>>>0<7?1:a;f=a<<29|d>>>3;d=f+D[b+16>>2]|0;a=(a>>>3|0)+c|0;D[b+16>>2]=d;D[b+20>>2]=d>>>0<f>>>0?a+1|0:a}k=D[b+16>>2]}$=i+16|0;return k}qa();T()}ra(1326);T()}function gc(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;e=D[a+8>>2];g=D[a+4>>2];if((e-g|0)/12>>>0>=b>>>0){a:{if(!b){break a}d=g;e=J(b,12)-12|0;f=((e>>>0)/12|0)+1&3;if(f){while(1){j=D[c+4>>2];D[d>>2]=D[c>>2];D[d+4>>2]=j;D[d+8>>2]=D[c+8>>2];d=d+12|0;h=h+1|0;if((f|0)!=(h|0)){continue}break}}g=J(b,12)+g|0;if(e>>>0<36){break a}while(1){b=D[c+4>>2];D[d>>2]=D[c>>2];D[d+4>>2]=b;D[d+8>>2]=D[c+8>>2];D[d+20>>2]=D[c+8>>2];b=D[c+4>>2];D[d+12>>2]=D[c>>2];D[d+16>>2]=b;D[d+32>>2]=D[c+8>>2];b=D[c+4>>2];D[d+24>>2]=D[c>>2];D[d+28>>2]=b;b=D[c+4>>2];D[d+36>>2]=D[c>>2];D[d+40>>2]=b;D[d+44>>2]=D[c+8>>2];d=d+48|0;if((g|0)!=(d|0)){continue}break}}D[a+4>>2]=g;return}b:{f=D[a>>2];i=(g-f|0)/12|0;d=i+b|0;if(d>>>0<357913942){e=(e-f|0)/12|0;f=e<<1;f=e>>>0<178956970?d>>>0>f>>>0?d:f:357913941;if(f){if(f>>>0>=357913942){break b}j=na(J(f,12))}e=J(i,12)+j|0;d=e;b=J(b,12);i=b-12|0;k=((i>>>0)/12|0)+1&3;if(k){d=e;while(1){l=D[c+4>>2];D[d>>2]=D[c>>2];D[d+4>>2]=l;D[d+8>>2]=D[c+8>>2];d=d+12|0;h=h+1|0;if((k|0)!=(h|0)){continue}break}}h=b+e|0;if(i>>>0>=36){while(1){b=D[c+4>>2];D[d>>2]=D[c>>2];D[d+4>>2]=b;D[d+8>>2]=D[c+8>>2];D[d+20>>2]=D[c+8>>2];b=D[c+4>>2];D[d+12>>2]=D[c>>2];D[d+16>>2]=b;D[d+32>>2]=D[c+8>>2];b=D[c+4>>2];D[d+24>>2]=D[c>>2];D[d+28>>2]=b;b=D[c+4>>2];D[d+36>>2]=D[c>>2];D[d+40>>2]=b;D[d+44>>2]=D[c+8>>2];d=d+48|0;if((h|0)!=(d|0)){continue}break}}b=D[a>>2];c=g-b|0;d=e+J((c|0)/-12|0,12)|0;if((c|0)>0){oa(d,b,c)}D[a+8>>2]=J(f,12)+j;D[a+4>>2]=h;D[a>>2]=d;if(b){ma(b)}return}qa();T()}ra(1326);T()}function sf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;a=0;h=$-16|0;$=h;k=D[b+80>>2];e=B[c+24|0];D[h+8>>2]=0;D[h>>2]=0;D[h+4>>2]=0;a:{b:{if(e){if((e|0)<0){break b}b=e<<2;a=na(b);D[h>>2]=a;g=a+b|0;D[h+8>>2]=g;i=b-4|0;l=(i>>>2|0)+1&7;c:{if(!l){b=a;break c}b=a;while(1){D[b>>2]=-1073741824;b=b+4|0;f=f+1|0;if((l|0)!=(f|0)){continue}break}}if(i>>>0>=28){while(1){D[b+24>>2]=-1073741824;D[b+28>>2]=-1073741824;D[b+16>>2]=-1073741824;D[b+20>>2]=-1073741824;D[b+8>>2]=-1073741824;D[b+12>>2]=-1073741824;D[b>>2]=-1073741824;D[b+4>>2]=-1073741824;b=b+32|0;if((g|0)!=(b|0)){continue}break}}D[h+4>>2]=g}b=J(e,k);g=D[d>>2];f=D[d+4>>2]-g>>2;d:{if(b>>>0>f>>>0){sa(d,b-f|0);break d}if(b>>>0>=f>>>0){break d}D[d+4>>2]=g+(b<<2)}e:{if(!k){break e}j=1;if((e|0)<=0){b=0;while(1){if(!mb(c,E[c+84|0]?b:D[D[c+68>>2]+(b<<2)>>2],B[c+24|0],a)){break e}b=b+1|0;j=k>>>0>b>>>0;if((b|0)!=(k|0)){continue}break}break e}p=e&-4;l=e&3;f=0;q=e-1>>>0<3;while(1){if(mb(c,E[c+84|0]?m:D[D[c+68>>2]+(m<<2)>>2],B[c+24|0],a)){o=D[d>>2];n=0;e=D[h>>2];b=0;j=0;if(!q){while(1){g=o+(f<<2)|0;i=b<<2;H[g>>2]=H[i+e>>2];H[g+4>>2]=H[(i|4)+e>>2];H[g+8>>2]=H[(i|8)+e>>2];H[g+12>>2]=H[(i|12)+e>>2];b=b+4|0;f=f+4|0;j=j+4|0;if((p|0)!=(j|0)){continue}break}}if(l){while(1){H[o+(f<<2)>>2]=H[(b<<2)+e>>2];b=b+1|0;f=f+1|0;n=n+1|0;if((l|0)!=(n|0)){continue}break}}m=m+1|0;j=m>>>0<k>>>0;if((k|0)!=(m|0)){continue}}break}a=D[h>>2]}if(a){ma(a)}$=h+16|0;a=(j^-1)&1;break a}qa();T()}return a|0}function Ib(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;k=D[b+16>>2];g=D[c+4>>2]-k|0;e=g;f=D[c>>2]-k|0;D[c>>2]=f;D[c+4>>2]=e;j=D[b+16>>2];e=e>>31;h=e+g^e;e=f>>31;l=(j|0)>=(h+(e+f^e)|0);a:{if(l){e=g;break a}b:{c:{if((f|0)>=0){i=1;h=1;if((g|0)>=0){break b}e=1;i=-1;h=-1;if(f){break c}break b}e=-1;i=-1;h=-1;if((g|0)<=0){break b}}i=(g|0)<=0?-1:1;h=e}e=f<<1;f=J(h,j);e=e-f|0;h=(J(i,h)|0)>=0;i=J(i,j);e=((h?0-e|0:e)+i|0)/2|0;D[c+4>>2]=e;j=f;f=(g<<1)-i|0;f=(j+(h?0-f|0:f)|0)/2|0;D[c>>2]=f}d:{e:{f:{g:{h:{i:{j:{if(f){if((f|0)<0){break j}if((e|0)>=0){break i}break f}if(e){break h}i=1;g=0;e=0;h=0;break d}i=1;if((e|0)>0){break g}h=(e|0)>0?253:0;g=e;e=f;break d}g=0-e|0;e=0-f|0;h=254;break e}if((e|0)<=0){break f}}e=0-e|0;g=f;h=253;break e}g=0-f|0;h=255}D[c>>2]=e;D[c+4>>2]=g;i=0}c=D[d+4>>2]+g|0;f=D[d>>2]+e|0;g=D[b+16>>2];k:{if((f|0)>(g|0)){f=f-D[b+4>>2]|0;break k}if((0-g|0)<=(f|0)){break k}f=D[b+4>>2]+f|0}l:{if((c|0)>(g|0)){c=c-D[b+4>>2]|0;break l}if((0-g|0)<=(c|0)){break l}c=D[b+4>>2]+c|0}m:{if(i){b=c;break m}b=c;n:{switch((h&3)-1|0){case 0:b=0-f|0;f=c;break m;case 1:b=0-c|0;f=0-f|0;break m;case 2:break n;default:break m}}b=f;f=0-c|0}o:{if(l){c=b;break o}p:{q:{if((f|0)>=0){c=1;e=1;if((b|0)>=0){break p}d=1;c=-1;e=-1;if(f){break q}break p}d=-1;c=-1;e=-1;if((b|0)<=0){break p}}c=(b|0)<=0?-1:1;e=d}d=f<<1;f=J(e,g);d=d-f|0;D[a>>2]=d;j=0-d|0;h=d;d=(J(c,e)|0)>=0;e=J(c,g);c=((d?j:h)+e|0)/2|0;b=(b<<1)-e|0;f=(f+(d?0-b|0:b)|0)/2|0}D[a>>2]=f+k;D[a+4>>2]=c+k}function rb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0;h=d-c|0;if((h|0)<=0){return}a:{f=D[a+8>>2];i=D[a+4>>2];if((h|0)<=(f-i|0)){j=i-b|0;if((j|0)>=(h|0)){g=i;f=d;break a}g=i;f=c+j|0;if((f|0)!=(d|0)){e=f;while(1){B[g|0]=E[e|0];g=g+1|0;e=e+1|0;if((e|0)!=(d|0)){continue}break}}D[a+4>>2]=g;if((j|0)>0){break a}return}e=D[a>>2];d=h+(i-e|0)|0;if((d|0)>=0){g=b-e|0;f=f-e|0;j=f<<1;f=f>>>0<1073741823?d>>>0>j>>>0?d:j:2147483647;if(f){d=na(f)}else{d=0}c=oa(g+d|0,c,h);if((g|0)>0){oa(d,e,g)}c=c+h|0;if((b|0)!=(i|0)){g=(b^-1)+i|0;h=i-b&7;if(h){e=0;while(1){B[c|0]=E[b|0];c=c+1|0;b=b+1|0;e=e+1|0;if((h|0)!=(e|0)){continue}break}}if(g>>>0>=7){while(1){B[c|0]=E[b|0];B[c+1|0]=E[b+1|0];B[c+2|0]=E[b+2|0];B[c+3|0]=E[b+3|0];B[c+4|0]=E[b+4|0];B[c+5|0]=E[b+5|0];B[c+6|0]=E[b+6|0];B[c+7|0]=E[b+7|0];c=c+8|0;b=b+8|0;if((i|0)!=(b|0)){continue}break}}e=D[a>>2]}D[a+8>>2]=d+f;D[a+4>>2]=c;D[a>>2]=d;if(e){ma(e)}return}qa();T()}e=g;d=e-h|0;if(i>>>0>d>>>0){while(1){B[e|0]=E[d|0];e=e+1|0;d=d+1|0;if(i>>>0>d>>>0){continue}break}}D[a+4>>2]=e;a=g-(b+h|0)|0;if(a){Na(g-a|0,b,a)}if((c|0)==(f|0)){return}a=(c^-1)+f|0;g=f-c&7;b:{if(!g){e=b;break b}d=0;e=b;while(1){B[e|0]=E[c|0];e=e+1|0;c=c+1|0;d=d+1|0;if((g|0)!=(d|0)){continue}break}}if(a>>>0<7){return}while(1){B[e|0]=E[c|0];B[e+1|0]=E[c+1|0];B[e+2|0]=E[c+2|0];B[e+3|0]=E[c+3|0];B[e+4|0]=E[c+4|0];B[e+5|0]=E[c+5|0];B[e+6|0]=E[c+6|0];B[e+7|0]=E[c+7|0];e=e+8|0;c=c+8|0;if((f|0)!=(c|0)){continue}break}}function mf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;e=$-16|0;$=e;i=D[b+80>>2];a=B[c+24|0];f=J(i,a);a:{b:{b=D[c+28>>2];c:{if(!(!((b|0)==5|(b|0)==6)|!E[c+84|0])){h=D[c+48>>2];i=D[D[c>>2]>>2];b=0;D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;c=0;a=f<<2;if(a){if((a|0)<0){break b}c=na(a);b=oa(c,h+i|0,a);j=b+a|0;b=b+(a>>2<<2)|0}a=D[d>>2];if(a){D[d+4>>2]=a;ma(a)}D[d+8>>2]=b;D[d+4>>2]=j;D[d>>2]=c;a=1;break c}D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;if(a){if((a|0)<0){break b}b=a<<2;h=na(b);D[e>>2]=h;g=b+h|0;D[e+8>>2]=g;pa(h,0,b);D[e+4>>2]=g}g=D[d>>2];b=D[d+4>>2]-g>>2;d:{if(b>>>0<f>>>0){sa(d,f-b|0);break d}if(b>>>0<=f>>>0){break d}D[d+4>>2]=g+(f<<2)}e:{if(!i){b=0;break e}if(!a){a=0;b=1;while(1){if(!yb(c,E[c+84|0]?a:D[D[c+68>>2]+(a<<2)>>2],B[c+24|0],h)){break e}a=a+1|0;b=i>>>0>a>>>0;if((a|0)!=(i|0)){continue}break}break e}p=a&-4;n=a&3;q=a-1>>>0<3;b=1;f=0;while(1){if(yb(c,E[c+84|0]?f:D[D[c+68>>2]+(f<<2)>>2],B[c+24|0],h)){o=D[d>>2];m=0;g=D[e>>2];a=0;b=0;if(!q){while(1){k=(j<<2)+o|0;l=a<<2;D[k>>2]=D[g+l>>2];D[k+4>>2]=D[g+(l|4)>>2];D[k+8>>2]=D[g+(l|8)>>2];D[k+12>>2]=D[g+(l|12)>>2];a=a+4|0;j=j+4|0;b=b+4|0;if((p|0)!=(b|0)){continue}break}}if(n){while(1){D[(j<<2)+o>>2]=D[g+(a<<2)>>2];a=a+1|0;j=j+1|0;m=m+1|0;if((m|0)!=(n|0)){continue}break}}f=f+1|0;b=i>>>0>f>>>0;if((f|0)!=(i|0)){continue}}break}h=D[e>>2]}if(h){ma(h)}a=b^1}$=e+16|0;a=a&1;break a}qa();T()}return a|0}function _c(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;e=$-16|0;$=e;i=D[b+80>>2];a=B[c+24|0];f=J(i,a);a:{b:{b=D[c+28>>2];c:{if(!(!((b|0)==5|(b|0)==6)|!E[c+84|0])){h=D[c+48>>2];i=D[D[c>>2]>>2];b=0;D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;c=0;a=f<<2;if(a){if((a|0)<0){break b}c=na(a);b=oa(c,h+i|0,a);j=b+a|0;b=b+(a>>2<<2)|0}a=D[d>>2];if(a){D[d+4>>2]=a;ma(a)}D[d+8>>2]=b;D[d+4>>2]=j;D[d>>2]=c;a=1;break c}D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;if(a){if((a|0)<0){break b}b=a<<2;h=na(b);D[e>>2]=h;g=b+h|0;D[e+8>>2]=g;pa(h,0,b);D[e+4>>2]=g}g=D[d>>2];b=D[d+4>>2]-g>>2;d:{if(b>>>0<f>>>0){sa(d,f-b|0);break d}if(b>>>0<=f>>>0){break d}D[d+4>>2]=g+(f<<2)}e:{if(!i){b=0;break e}if(!a){a=0;b=1;while(1){if(!zb(c,E[c+84|0]?a:D[D[c+68>>2]+(a<<2)>>2],B[c+24|0],h)){break e}a=a+1|0;b=i>>>0>a>>>0;if((a|0)!=(i|0)){continue}break}break e}p=a&-4;n=a&3;q=a-1>>>0<3;b=1;f=0;while(1){if(zb(c,E[c+84|0]?f:D[D[c+68>>2]+(f<<2)>>2],B[c+24|0],h)){o=D[d>>2];m=0;g=D[e>>2];a=0;b=0;if(!q){while(1){k=(j<<2)+o|0;l=a<<2;D[k>>2]=D[g+l>>2];D[k+4>>2]=D[g+(l|4)>>2];D[k+8>>2]=D[g+(l|8)>>2];D[k+12>>2]=D[g+(l|12)>>2];a=a+4|0;j=j+4|0;b=b+4|0;if((p|0)!=(b|0)){continue}break}}if(n){while(1){D[(j<<2)+o>>2]=D[g+(a<<2)>>2];a=a+1|0;j=j+1|0;m=m+1|0;if((m|0)!=(n|0)){continue}break}}f=f+1|0;b=i>>>0>f>>>0;if((f|0)!=(i|0)){continue}}break}h=D[e>>2]}if(h){ma(h)}a=b^1}$=e+16|0;a=a&1;break a}qa();T()}return a|0}function pf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;e=$-16|0;$=e;i=D[b+80>>2];a=B[c+24|0];f=J(i,a);a:{b:{b=D[c+28>>2];c:{if(!(!((b|0)==3|(b|0)==4)|!E[c+84|0])){h=D[c+48>>2];i=D[D[c>>2]>>2];b=0;D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;c=0;a=f<<1;if(a){if((a|0)<0){break b}c=na(a);b=oa(c,h+i|0,a);j=b+a|0;b=b+(a>>1<<1)|0}a=D[d>>2];if(a){D[d+4>>2]=a;ma(a)}D[d+8>>2]=b;D[d+4>>2]=j;D[d>>2]=c;a=1;break c}D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;if(a){if((a|0)<0){break b}b=a<<1;h=na(b);D[e>>2]=h;g=b+h|0;D[e+8>>2]=g;pa(h,0,b);D[e+4>>2]=g}g=D[d>>2];b=D[d+4>>2]-g>>1;d:{if(b>>>0<f>>>0){gd(d,f-b|0);break d}if(b>>>0<=f>>>0){break d}D[d+4>>2]=g+(f<<1)}e:{if(!i){b=0;break e}if(!a){a=0;b=1;while(1){if(!Bb(c,E[c+84|0]?a:D[D[c+68>>2]+(a<<2)>>2],B[c+24|0],h)){break e}a=a+1|0;b=i>>>0>a>>>0;if((a|0)!=(i|0)){continue}break}break e}p=a&-4;n=a&3;q=a-1>>>0<3;b=1;f=0;while(1){if(Bb(c,E[c+84|0]?f:D[D[c+68>>2]+(f<<2)>>2],B[c+24|0],h)){o=D[d>>2];m=0;g=D[e>>2];a=0;b=0;if(!q){while(1){k=(j<<1)+o|0;l=a<<1;C[k>>1]=F[g+l>>1];C[k+2>>1]=F[g+(l|2)>>1];C[k+4>>1]=F[g+(l|4)>>1];C[k+6>>1]=F[g+(l|6)>>1];a=a+4|0;j=j+4|0;b=b+4|0;if((p|0)!=(b|0)){continue}break}}if(n){while(1){C[(j<<1)+o>>1]=F[g+(a<<1)>>1];a=a+1|0;j=j+1|0;m=m+1|0;if((m|0)!=(n|0)){continue}break}}f=f+1|0;b=i>>>0>f>>>0;if((f|0)!=(i|0)){continue}}break}h=D[e>>2]}if(h){ma(h)}a=b^1}$=e+16|0;a=a&1;break a}qa();T()}return a|0}function nf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;e=$-16|0;$=e;i=D[b+80>>2];a=B[c+24|0];f=J(i,a);a:{b:{b=D[c+28>>2];c:{if(!(!((b|0)==3|(b|0)==4)|!E[c+84|0])){h=D[c+48>>2];i=D[D[c>>2]>>2];b=0;D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;c=0;a=f<<1;if(a){if((a|0)<0){break b}c=na(a);b=oa(c,h+i|0,a);j=b+a|0;b=b+(a>>1<<1)|0}a=D[d>>2];if(a){D[d+4>>2]=a;ma(a)}D[d+8>>2]=b;D[d+4>>2]=j;D[d>>2]=c;a=1;break c}D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;if(a){if((a|0)<0){break b}b=a<<1;h=na(b);D[e>>2]=h;g=b+h|0;D[e+8>>2]=g;pa(h,0,b);D[e+4>>2]=g}g=D[d>>2];b=D[d+4>>2]-g>>1;d:{if(b>>>0<f>>>0){gd(d,f-b|0);break d}if(b>>>0<=f>>>0){break d}D[d+4>>2]=g+(f<<1)}e:{if(!i){b=0;break e}if(!a){a=0;b=1;while(1){if(!Ab(c,E[c+84|0]?a:D[D[c+68>>2]+(a<<2)>>2],B[c+24|0],h)){break e}a=a+1|0;b=i>>>0>a>>>0;if((a|0)!=(i|0)){continue}break}break e}p=a&-4;n=a&3;q=a-1>>>0<3;b=1;f=0;while(1){if(Ab(c,E[c+84|0]?f:D[D[c+68>>2]+(f<<2)>>2],B[c+24|0],h)){o=D[d>>2];m=0;g=D[e>>2];a=0;b=0;if(!q){while(1){k=(j<<1)+o|0;l=a<<1;C[k>>1]=F[g+l>>1];C[k+2>>1]=F[g+(l|2)>>1];C[k+4>>1]=F[g+(l|4)>>1];C[k+6>>1]=F[g+(l|6)>>1];a=a+4|0;j=j+4|0;b=b+4|0;if((p|0)!=(b|0)){continue}break}}if(n){while(1){C[(j<<1)+o>>1]=F[g+(a<<1)>>1];a=a+1|0;j=j+1|0;m=m+1|0;if((m|0)!=(n|0)){continue}break}}f=f+1|0;b=i>>>0>f>>>0;if((f|0)!=(i|0)){continue}}break}h=D[e>>2]}if(h){ma(h)}a=b^1}$=e+16|0;a=a&1;break a}qa();T()}return a|0}function Oh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;D[a+8>>2]=e;f=a+32|0;i=D[f>>2];h=D[a+36>>2]-i>>2;a:{if(h>>>0<e>>>0){sa(f,e-h|0);i=D[f>>2];f=D[a+8>>2];break a}if(e>>>0<h>>>0){D[a+36>>2]=(e<<2)+i}f=e}h=0;g=(e&1073741823)!=(e|0)?-1:e<<2;m=pa(na(g),0,g);b:{if((f|0)<=0){break b}while(1){f=h<<2;g=D[f+m>>2];j=D[a+16>>2];c:{if((g|0)>(j|0)){D[f+i>>2]=j;break c}f=f+i|0;j=D[a+12>>2];if((j|0)>(g|0)){D[f>>2]=j;break c}D[f>>2]=g}f=D[a+8>>2];h=h+1|0;if((f|0)>(h|0)){continue}break}if((f|0)<=0){break b}h=0;while(1){g=h<<2;f=g+c|0;g=D[b+g>>2]+D[g+i>>2]|0;D[f>>2]=g;d:{if((g|0)>D[a+16>>2]){g=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break d}g=g+D[a+20>>2]|0}D[f>>2]=g}f=D[a+8>>2];h=h+1|0;if((f|0)>(h|0)){continue}break}}if(!((d|0)<=(e|0)|(f|0)<=0)){o=0-e<<2;i=e;while(1){e:{if((f|0)<=0){break e}l=i<<2;n=l+c|0;p=n+o|0;j=D[a+32>>2];h=0;while(1){f=h<<2;g=D[f+p>>2];k=D[a+16>>2];f:{if((g|0)>(k|0)){D[f+j>>2]=k;break f}f=f+j|0;k=D[a+12>>2];if((k|0)>(g|0)){D[f>>2]=k;break f}D[f>>2]=g}f=D[a+8>>2];h=h+1|0;if((f|0)>(h|0)){continue}break}h=0;if((f|0)<=0){break e}l=b+l|0;while(1){g=h<<2;f=g+n|0;g=D[g+l>>2]+D[g+j>>2]|0;D[f>>2]=g;g:{if((g|0)>D[a+16>>2]){g=g-D[a+20>>2]|0}else{if((g|0)>=D[a+12>>2]){break g}g=g+D[a+20>>2]|0}D[f>>2]=g}f=D[a+8>>2];h=h+1|0;if((f|0)>(h|0)){continue}break}}i=e+i|0;if((i|0)<(d|0)){continue}break}}ma(m);return 1}function md(a){a=a|0;var b=0,c=0,d=0,e=0,f=0;D[a>>2]=8364;e=a+232|0;b=D[e+196>>2];if(b){D[e+200>>2]=b;ma(b)}c=D[e+184>>2];if(c){b=D[e+188>>2];if((c|0)==(b|0)){b=c}else{while(1){d=b-12|0;f=D[d>>2];if(f){D[b-8>>2]=f;ma(f)}b=d;if((c|0)!=(b|0)){continue}break}b=D[e+184>>2]}D[e+188>>2]=c;ma(b)}b=D[e+156>>2];if(b){D[e+160>>2]=b;ma(b)}c=D[e+136>>2];D[e+136>>2]=0;if(c){d=c-4|0;b=D[d>>2];if(b){b=c+(b<<4)|0;while(1){b=b-16|0;if((c|0)!=(b|0)){continue}break}}ma(d)}c=D[a+216>>2];if(c){b=D[a+220>>2];if((c|0)==(b|0)){b=c}else{while(1){d=D[b-12>>2];if(d){D[b-8>>2]=d;ma(d)}d=D[b-28>>2];if(d){D[b-24>>2]=d;ma(d)}d=D[b-40>>2];if(d){D[b-36>>2]=d;ma(d)}ob(b-140|0);b=b-144|0;if((c|0)!=(b|0)){continue}break}b=D[a+216>>2]}D[a+220>>2]=c;ma(b)}b=D[a+196>>2];if(b){D[a+200>>2]=b;ma(b)}b=D[a+184>>2];if(b){D[a+188>>2]=b;ma(b)}b=D[a+172>>2];if(b){D[a+176>>2]=b;ma(b)}b=D[a+160>>2];if(b){D[a+164>>2]=b;ma(b)}b=D[a+144>>2];if(b){while(1){c=D[b>>2];ma(b);b=c;if(b){continue}break}}b=D[a+136>>2];D[a+136>>2]=0;if(b){ma(b)}b=D[a+120>>2];if(b){ma(b)}b=D[a+108>>2];if(b){ma(b)}b=D[a+96>>2];if(b){ma(b)}b=D[a+72>>2];if(b){D[a+76>>2]=b;ma(b)}b=D[a+60>>2];if(b){ma(b)}b=D[a+48>>2];if(b){D[a+52>>2]=b;ma(b)}b=D[a+36>>2];if(b){D[a+40>>2]=b;ma(b)}b=D[a+24>>2];if(b){D[a+28>>2]=b;ma(b)}b=D[a+12>>2];if(b){D[a+16>>2]=b;ma(b)}b=D[a+8>>2];D[a+8>>2]=0;if(b){ab(b)}return a|0}function lc(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;j=$-16|0;$=j;c=D[b+20>>2];d=D[b+16>>2];g=d+4|0;c=g>>>0<4?c+1|0:c;e=D[b+12>>2];a:{if((e|0)<=(c|0)&g>>>0>G[b+8>>2]|(c|0)>(e|0)){break a}d=d+D[b>>2]|0;d=E[d|0]|E[d+1|0]<<8|(E[d+2|0]<<16|E[d+3|0]<<24);D[b+16>>2]=g;D[b+20>>2]=c;if((d|0)<0){break a}ib(a+76|0,d);c=j;D[c>>2]=0;D[c+4>>2]=0;B[c+5|0]=0;B[c+6|0]=0;B[c+7|0]=0;B[c+8|0]=0;B[c+9|0]=0;B[c+10|0]=0;B[c+11|0]=0;B[c+12|0]=0;b:{if(!Ka(c,b)){break b}if(d){e=1;while(1){f=1<<h;i=Ga(c);g=D[a+76>>2]+(h>>>3&536870908)|0;e=e^i;if(e&1){f=D[g>>2]&(f^-1)}else{f=f|D[g>>2]}e=e^1;D[g>>2]=f;h=h+1|0;if((d|0)!=(h|0)){continue}break}}h=0;d=D[b+12>>2];g=d;c=D[b+20>>2];e=c;f=D[b+16>>2];i=f+4|0;c=i>>>0<4?c+1|0:c;k=D[b+8>>2];if(k>>>0<i>>>0&(c|0)>=(d|0)|(c|0)>(d|0)){break b}l=D[b>>2];d=l+f|0;d=E[d|0]|E[d+1|0]<<8|(E[d+2|0]<<16|E[d+3|0]<<24);D[b+16>>2]=i;D[b+20>>2]=c;c=e;e=f+8|0;c=e>>>0<8?c+1|0:c;f=e;e=c;if(f>>>0>k>>>0&(c|0)>=(g|0)|(c|0)>(g|0)){break b}c=i+l|0;c=E[c|0]|E[c+1|0]<<8|(E[c+2|0]<<16|E[c+3|0]<<24);D[b+16>>2]=f;D[b+20>>2]=e;if((c|0)<(d|0)){break b}D[a+16>>2]=c;D[a+12>>2]=d;b=(c>>31)-((d>>31)+(c>>>0<d>>>0)|0)|0;c=c-d|0;if(!b&c>>>0>2147483646|b){break b}h=1;b=c+1|0;D[a+20>>2]=b;c=b>>>1|0;D[a+24>>2]=c;D[a+28>>2]=0-c;if(b&1){break b}D[a+24>>2]=c-1}}$=j+16|0;return h|0}function fc(a,b){var c=0,d=0,e=0,f=0,g=0;f=-1;d=-1;a:{if((b|0)==-1){break a}d=b+1|0;f=(d>>>0)%3|0?d:b-2|0;d=b-1|0;if((b>>>0)%3|0){break a}d=b+2|0}b:{c:{d:{switch(D[a+168>>2]){case 0:case 1:e=D[a+148>>2];c=1;b=D[a+156>>2];g=b+(((f|0)!=-1?D[D[e>>2]+(f<<2)>>2]:-1)<<2)|0;D[g>>2]=D[g>>2]+1;b=(((d|0)!=-1?D[D[e>>2]+(d<<2)>>2]:-1)<<2)+b|0;break c;case 5:e=D[a+148>>2];c=-1;c=((b|0)!=-1?D[D[e>>2]+(b<<2)>>2]:c)<<2;b=D[a+156>>2];c=c+b|0;D[c>>2]=D[c>>2]+1;c=(((f|0)!=-1?D[D[e>>2]+(f<<2)>>2]:-1)<<2)+b|0;D[c>>2]=D[c>>2]+1;c=2;b=(((d|0)!=-1?D[D[e>>2]+(d<<2)>>2]:-1)<<2)+b|0;break c;case 3:e=D[a+148>>2];c=-1;c=((b|0)!=-1?D[D[e>>2]+(b<<2)>>2]:c)<<2;b=D[a+156>>2];c=c+b|0;D[c>>2]=D[c>>2]+1;c=(((f|0)!=-1?D[D[e>>2]+(f<<2)>>2]:-1)<<2)+b|0;D[c>>2]=D[c>>2]+2;c=1;b=(((d|0)!=-1?D[D[e>>2]+(d<<2)>>2]:-1)<<2)+b|0;break c;case 7:break d;default:break b}}e=D[a+148>>2];c=-1;c=((b|0)!=-1?D[D[e>>2]+(b<<2)>>2]:c)<<2;b=D[a+156>>2];c=c+b|0;D[c>>2]=D[c>>2]+2;c=(((f|0)!=-1?D[D[e>>2]+(f<<2)>>2]:-1)<<2)+b|0;D[c>>2]=D[c>>2]+2;c=2;b=(((d|0)!=-1?D[D[e>>2]+(d<<2)>>2]:-1)<<2)+b|0}D[b>>2]=D[b>>2]+c}c=a;d=D[a+180>>2];b=D[D[a+156>>2]+(((f|0)!=-1?D[D[D[a+148>>2]>>2]+(f<<2)>>2]:-1)<<2)>>2];a=D[a+176>>2];D[c+172>>2]=(a|0)>(b|0)?0:((b|0)>(d|0)?d:b)-a|0}function Ub(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;d=D[a+8>>2];e=D[a>>2];if(d-e>>2>>>0>=b>>>0){g=D[a+4>>2];h=g-e>>2;f=b>>>0>h>>>0?h:b;a:{if(!f){break a}k=f-1|0;i=f&7;b:{if(!i){d=e;break b}d=e;while(1){D[d>>2]=D[c>>2];f=f-1|0;d=d+4|0;j=j+1|0;if((j|0)!=(i|0)){continue}break}}if(k>>>0<7){break a}while(1){D[d>>2]=D[c>>2];D[d+4>>2]=D[c>>2];D[d+8>>2]=D[c>>2];D[d+12>>2]=D[c>>2];D[d+16>>2]=D[c>>2];D[d+20>>2]=D[c>>2];D[d+24>>2]=D[c>>2];D[d+28>>2]=D[c>>2];d=d+32|0;f=f-8|0;if(f){continue}break}}if(b>>>0>h>>>0){d=a;a=b-h|0;if(a){a=(a<<2)+g|0;while(1){D[g>>2]=D[c>>2];g=g+4|0;if((a|0)!=(g|0)){continue}break}}else{a=g}D[d+4>>2]=a;return}D[a+4>>2]=e+(b<<2);return}if(e){D[a+4>>2]=e;ma(e);D[a+8>>2]=0;D[a>>2]=0;D[a+4>>2]=0;d=0}c:{if(b>>>0>=1073741824){break c}e=d>>1;d=d>>2>>>0<536870911?b>>>0>e>>>0?b:e:1073741823;if(d>>>0>=1073741824){break c}d=d<<2;e=na(d);D[a>>2]=e;D[a+8>>2]=d+e;c=D[c>>2];d=e;b=b<<2;g=b-4|0;h=(g>>>2|0)+1&7;if(h){while(1){D[d>>2]=c;d=d+4|0;f=f+1|0;if((h|0)!=(f|0)){continue}break}}b=b+e|0;if(g>>>0>=28){while(1){D[d+28>>2]=c;D[d+24>>2]=c;D[d+20>>2]=c;D[d+16>>2]=c;D[d+12>>2]=c;D[d+8>>2]=c;D[d+4>>2]=c;D[d>>2]=c;d=d+32|0;if((b|0)!=(d|0)){continue}break}}D[a+4>>2]=b;return}qa();T()}function wg(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;a:{b=D[a+32>>2];f=D[b+8>>2];g=D[b+12>>2];d=D[b+20>>2];h=f;f=D[b+16>>2];e=0;b:{if((g|0)<=(d|0)&h>>>0<=f>>>0|(d|0)>(g|0)){break b}h=E[f+D[b>>2]|0];g=f+1|0;d=g?d:d+1|0;D[b+16>>2]=g;D[b+20>>2]=d;c:{if(!h){break c}while(1){if(ba[D[D[a>>2]+16>>2]](a,c)|0){c=c+1|0;if((h|0)!=(c|0)){continue}break c}break}return 0}c=D[a+8>>2];d=D[a+12>>2];if((c|0)!=(d|0)){while(1){b=D[c>>2];if(!(ba[D[D[b>>2]+8>>2]](b,a,D[a+4>>2])|0)){break a}c=c+4|0;if((d|0)!=(c|0)){continue}break}}d:{if(!h){break d}c=0;while(1){b=D[D[a+8>>2]+(c<<2)>>2];if(!(ba[D[D[b>>2]+12>>2]](b,D[a+32>>2])|0)){break a}c=c+1|0;if((h|0)!=(c|0)){continue}break}if(!h){break d}f=a+20|0;while(1){c=0;g=i<<2;b=D[g+D[a+8>>2]>>2];d=ba[D[D[b>>2]+24>>2]](b)|0;if((d|0)>0){while(1){b=D[D[a+8>>2]+g>>2];k=ba[D[D[b>>2]+20>>2]](b,c)|0;e=D[a+20>>2];j=D[a+24>>2]-e>>2;e:{if(k>>>0<j>>>0){break e}b=k+1|0;if(b>>>0>j>>>0){sa(f,b-j|0);e=D[f>>2];break e}if(b>>>0>=j>>>0){break e}D[a+24>>2]=(b<<2)+e}D[(k<<2)+e>>2]=i;c=c+1|0;if((d|0)!=(c|0)){continue}break}}i=i+1|0;if((h|0)!=(i|0)){continue}break}}e=0;if(!(ba[D[D[a>>2]+28>>2]](a)|0)){break b}e=ba[D[D[a>>2]+32>>2]](a)|0}return e|0}return 0}function Se(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;Jc(a,b,c);c=D[a+84>>2];d=D[a+88>>2]-c>>2;a:{if((d|0)>(b|0)){break a}b=b+1|0;if(b>>>0>d>>>0){b:{d=b-d|0;e=D[a+92>>2];c=D[a+88>>2];if(d>>>0<=e-c>>2>>>0){c:{if(!d){break c}b=c;f=(d<<2)-4|0;g=(f>>>2|0)+1&7;if(g){while(1){D[b>>2]=1;b=b+4|0;h=h+1|0;if((g|0)!=(h|0)){continue}break}}c=(d<<2)+c|0;if(f>>>0<28){break c}while(1){D[b+24>>2]=1;D[b+28>>2]=1;D[b+16>>2]=1;D[b+20>>2]=1;D[b+8>>2]=1;D[b+12>>2]=1;D[b>>2]=1;D[b+4>>2]=1;b=b+32|0;if((c|0)!=(b|0)){continue}break}}D[a+88>>2]=c;break b}d:{f=D[a+84>>2];j=c-f|0;c=j>>2;b=c+d|0;if(b>>>0<1073741824){e=e-f|0;i=e>>1;e=e>>2>>>0<536870911?b>>>0>i>>>0?b:i:1073741823;if(e){if(e>>>0>=1073741824){break d}g=na(e<<2)}c=(c<<2)+g|0;b=c;d=d<<2;i=d-4|0;k=(i>>>2|0)+1&7;if(k){b=c;while(1){D[b>>2]=1;b=b+4|0;h=h+1|0;if((k|0)!=(h|0)){continue}break}}c=c+d|0;if(i>>>0>=28){while(1){D[b+24>>2]=1;D[b+28>>2]=1;D[b+16>>2]=1;D[b+20>>2]=1;D[b+8>>2]=1;D[b+12>>2]=1;D[b>>2]=1;D[b+4>>2]=1;b=b+32|0;if((c|0)!=(b|0)){continue}break}}if((j|0)>0){oa(g,f,j)}D[a+92>>2]=(e<<2)+g;D[a+88>>2]=c;D[a+84>>2]=g;if(f){ma(f)}break b}qa();T()}ra(1326);T()}return}if(b>>>0>=d>>>0){break a}D[a+88>>2]=c+(b<<2)}}function xa(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;f=D[a+8>>2];e=D[a+4>>2];if(f-e>>2>>>0>=b>>>0){a:{if(!b){break a}d=e;g=(b<<2)-4|0;h=(g>>>2|0)+1&7;if(h){while(1){D[d>>2]=D[c>>2];d=d+4|0;i=i+1|0;if((h|0)!=(i|0)){continue}break}}e=(b<<2)+e|0;if(g>>>0<28){break a}while(1){D[d>>2]=D[c>>2];D[d+4>>2]=D[c>>2];D[d+8>>2]=D[c>>2];D[d+12>>2]=D[c>>2];D[d+16>>2]=D[c>>2];D[d+20>>2]=D[c>>2];D[d+24>>2]=D[c>>2];D[d+28>>2]=D[c>>2];d=d+32|0;if((e|0)!=(d|0)){continue}break}}D[a+4>>2]=e;return}b:{g=D[a>>2];k=e-g|0;e=k>>2;d=e+b|0;if(d>>>0<1073741824){f=f-g|0;j=f>>1;f=f>>2>>>0<536870911?d>>>0>j>>>0?d:j:1073741823;if(f){if(f>>>0>=1073741824){break b}h=na(f<<2)}e=(e<<2)+h|0;d=e;b=b<<2;j=b-4|0;l=(j>>>2|0)+1&7;if(l){d=e;while(1){D[d>>2]=D[c>>2];d=d+4|0;i=i+1|0;if((l|0)!=(i|0)){continue}break}}b=b+e|0;if(j>>>0>=28){while(1){D[d>>2]=D[c>>2];D[d+4>>2]=D[c>>2];D[d+8>>2]=D[c>>2];D[d+12>>2]=D[c>>2];D[d+16>>2]=D[c>>2];D[d+20>>2]=D[c>>2];D[d+24>>2]=D[c>>2];D[d+28>>2]=D[c>>2];d=d+32|0;if((b|0)!=(d|0)){continue}break}}if((k|0)>0){oa(h,g,k)}D[a+8>>2]=(f<<2)+h;D[a+4>>2]=b;D[a>>2]=h;if(g){ma(g)}return}qa();T()}ra(1326);T()}function oa(a,b,c){var d=0,e=0,f=0;if(c>>>0>=512){X(a|0,b|0,c|0)|0;return a}e=a+c|0;a:{if(!((a^b)&3)){b:{if(!(a&3)){c=a;break b}if(!c){c=a;break b}c=a;while(1){B[c|0]=E[b|0];b=b+1|0;c=c+1|0;if(!(c&3)){break b}if(c>>>0<e>>>0){continue}break}}d=e&-4;c:{if(d>>>0<64){break c}f=d+-64|0;if(f>>>0<c>>>0){break c}while(1){D[c>>2]=D[b>>2];D[c+4>>2]=D[b+4>>2];D[c+8>>2]=D[b+8>>2];D[c+12>>2]=D[b+12>>2];D[c+16>>2]=D[b+16>>2];D[c+20>>2]=D[b+20>>2];D[c+24>>2]=D[b+24>>2];D[c+28>>2]=D[b+28>>2];D[c+32>>2]=D[b+32>>2];D[c+36>>2]=D[b+36>>2];D[c+40>>2]=D[b+40>>2];D[c+44>>2]=D[b+44>>2];D[c+48>>2]=D[b+48>>2];D[c+52>>2]=D[b+52>>2];D[c+56>>2]=D[b+56>>2];D[c+60>>2]=D[b+60>>2];b=b- -64|0;c=c- -64|0;if(f>>>0>=c>>>0){continue}break}}if(c>>>0>=d>>>0){break a}while(1){D[c>>2]=D[b>>2];b=b+4|0;c=c+4|0;if(d>>>0>c>>>0){continue}break}break a}if(e>>>0<4){c=a;break a}d=e-4|0;if(d>>>0<a>>>0){c=a;break a}c=a;while(1){B[c|0]=E[b|0];B[c+1|0]=E[b+1|0];B[c+2|0]=E[b+2|0];B[c+3|0]=E[b+3|0];b=b+4|0;c=c+4|0;if(d>>>0>=c>>>0){continue}break}}if(c>>>0<e>>>0){while(1){B[c|0]=E[b|0];b=b+1|0;c=c+1|0;if((e|0)!=(c|0)){continue}break}}return a}function nd(a){a=a|0;var b=0,c=0,d=0,e=0;D[a>>2]=8312;e=D[a+368>>2];D[a+368>>2]=0;if(e){d=e-4|0;b=D[d>>2];if(b){c=(b<<4)+e|0;while(1){c=c-16|0;if((e|0)!=(c|0)){continue}break}}ma(d)}d=D[a+216>>2];if(d){c=D[a+220>>2];if((d|0)==(c|0)){b=d}else{while(1){b=D[c-12>>2];if(b){D[c-8>>2]=b;ma(b)}b=D[c-28>>2];if(b){D[c-24>>2]=b;ma(b)}b=D[c-40>>2];if(b){D[c-36>>2]=b;ma(b)}ob(c-140|0);c=c-144|0;if((d|0)!=(c|0)){continue}break}b=D[a+216>>2]}D[a+220>>2]=d;ma(b)}b=D[a+196>>2];if(b){D[a+200>>2]=b;ma(b)}b=D[a+184>>2];if(b){D[a+188>>2]=b;ma(b)}b=D[a+172>>2];if(b){D[a+176>>2]=b;ma(b)}b=D[a+160>>2];if(b){D[a+164>>2]=b;ma(b)}c=D[a+144>>2];if(c){while(1){b=D[c>>2];ma(c);c=b;if(b){continue}break}}b=D[a+136>>2];D[a+136>>2]=0;if(b){ma(b)}b=D[a+120>>2];if(b){ma(b)}b=D[a+108>>2];if(b){ma(b)}b=D[a+96>>2];if(b){ma(b)}b=D[a+72>>2];if(b){D[a+76>>2]=b;ma(b)}b=D[a+60>>2];if(b){ma(b)}b=D[a+48>>2];if(b){D[a+52>>2]=b;ma(b)}b=D[a+36>>2];if(b){D[a+40>>2]=b;ma(b)}b=D[a+24>>2];if(b){D[a+28>>2]=b;ma(b)}b=D[a+12>>2];if(b){D[a+16>>2]=b;ma(b)}b=D[a+8>>2];D[a+8>>2]=0;if(b){ab(b)}return a|0}function bi(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;g=c;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{k:{if(b){if(!g){break k}if(!d){break j}c=M(d)-M(b)|0;if(c>>>0<=31){break i}break c}if((d|0)==1|d>>>0>1){break c}a=(a>>>0)/(g>>>0)|0;aa=0;break a}if(!a){break h}if(!d){break g}if(d-1&d){break g}a=b>>>ei(d)|0;aa=0;break a}if(!(g-1&g)){break f}h=(M(g)+33|0)-M(b)|0;e=0-h|0;break d}h=c+1|0;e=63-c|0;break d}a=(b>>>0)/(d>>>0)|0;aa=0;break a}c=M(d)-M(b)|0;if(c>>>0<31){break e}break c}if((g|0)==1){break b}c=ei(g);d=c&31;if((c&63)>>>0>=32){c=0;a=b>>>d|0}else{c=b>>>d|0;a=((1<<d)-1&b)<<32-d|a>>>d}aa=c;break a}h=c+1|0;e=63-c|0}c=h&63;f=c&31;if(c>>>0>=32){c=0;j=b>>>f|0}else{c=b>>>f|0;j=((1<<f)-1&b)<<32-f|a>>>f}f=c;c=e&63;e=c&31;if(c>>>0>=32){c=a<<e;a=0}else{c=(1<<e)-1&a>>>32-e|b<<e;a=a<<e}b=c;if(h){c=d-1|0;e=g-1|0;l=(e|0)!=-1?c+1|0:c;while(1){c=j<<1|b>>>31;f=f<<1|j>>>31;i=l-(f+(c>>>0>e>>>0)|0)>>31;k=g&i;j=c-k|0;f=f-((d&i)+(c>>>0<k>>>0)|0)|0;b=b<<1|a>>>31;a=m|a<<1;i=i&1;m=i;h=h-1|0;if(h){continue}break}}aa=b<<1|a>>>31;a=i|a<<1;break a}a=0;b=0}aa=b}return a}function pb(a,b){var c=0,d=0,e=0;c=(a|0)==(b|0);B[b+12|0]=c;a:{if(c){break a}while(1){d=D[b+8>>2];if(E[d+12|0]){break a}b:{c=D[d+8>>2];e=D[c>>2];c:{if((d|0)==(e|0)){e=D[c+4>>2];if(!(!e|E[e+12|0])){break b}d:{if(D[d>>2]==(b|0)){b=d;break d}b=D[d+4>>2];a=D[b>>2];D[d+4>>2]=a;if(a){D[a+8>>2]=d;c=D[d+8>>2]}D[b+8>>2]=c;a=D[d+8>>2];D[(((d|0)!=D[a>>2])<<2)+a>>2]=b;D[b>>2]=d;D[d+8>>2]=b;c=D[b+8>>2]}B[b+12|0]=1;B[c+12|0]=0;a=D[c>>2];b=D[a+4>>2];D[c>>2]=b;if(b){D[b+8>>2]=c}D[a+8>>2]=D[c+8>>2];b=D[c+8>>2];D[((D[b>>2]!=(c|0))<<2)+b>>2]=a;D[a+4>>2]=c;b=c+8|0;break c}if(!(E[e+12|0]|!e)){break b}e:{if(D[d>>2]!=(b|0)){b=d;break e}a=D[b+4>>2];D[d>>2]=a;if(a){D[a+8>>2]=d;c=D[d+8>>2]}D[b+8>>2]=c;a=D[d+8>>2];D[(((d|0)!=D[a>>2])<<2)+a>>2]=b;D[b+4>>2]=d;D[d+8>>2]=b;c=D[b+8>>2]}B[b+12|0]=1;B[c+12|0]=0;a=D[c+4>>2];b=D[a>>2];D[c+4>>2]=b;if(b){D[b+8>>2]=c}D[a+8>>2]=D[c+8>>2];b=D[c+8>>2];D[((D[b>>2]!=(c|0))<<2)+b>>2]=a;D[a>>2]=c;b=c+8|0}D[b>>2]=a;break a}B[d+12|0]=1;d=(a|0)==(c|0);B[c+12|0]=d;B[e+12|0]=1;b=c;if(!d){continue}break}}}function yd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=K(0),j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;k=$-16|0;$=k;if(D[c+28>>2]==9){d=D[a+4>>2];g=B[c+24|0];e=g<<2;f=na((g&1073741823)!=(g|0)?-1:e);l=k+8|0;D[l>>2]=1065353216;i=H[a+20>>2];d=-1<<d^-1;if((d|0)>0){H[l>>2]=i/K(d|0)}o=(d|0)>0;a:{if(!o){break a}j=D[c+80>>2];if(!j){break a}d=0;if((g|0)<=0){if((j|0)!=1){a=j&-2;b=0;while(1){oa(D[D[c+64>>2]>>2]+d|0,f,e);d=d+e|0;oa(d+D[D[c+64>>2]>>2]|0,f,e);d=d+e|0;b=b+2|0;if((a|0)!=(b|0)){continue}break}}if(!(j&1)){break a}oa(D[D[c+64>>2]>>2]+d|0,f,e);break a}p=D[D[b>>2]>>2]+D[b+48>>2]|0;t=g&-2;u=g&1;while(1){m=D[a+8>>2];i=H[l>>2];b=0;n=0;if((g|0)!=1){while(1){h=b<<2;q=(d<<2)+p|0;H[h+f>>2]=K(i*K(D[q>>2]))+H[h+m>>2];h=h|4;H[h+f>>2]=K(i*K(D[q+4>>2]))+H[h+m>>2];b=b+2|0;d=d+2|0;n=n+2|0;if((t|0)!=(n|0)){continue}break}}if(u){b=b<<2;H[b+f>>2]=K(i*K(D[(d<<2)+p>>2]))+H[b+m>>2];d=d+1|0}oa(D[D[c+64>>2]>>2]+r|0,f,e);r=e+r|0;s=s+1|0;if((s|0)!=(j|0)){continue}break}}ma(f)}$=k+16|0;return o|0}function te(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;if(Zc(a,b)){i=a+36|0;g=ba[D[D[a>>2]+24>>2]](a)|0;d=D[a+40>>2];e=D[a+36>>2];c=d-e>>2;a:{if(g>>>0>c>>>0){Ob(i,g-c|0);break a}if(c>>>0<=g>>>0){break a}e=e+(g<<2)|0;if((e|0)!=(d|0)){while(1){d=d-4|0;c=D[d>>2];D[d>>2]=0;if(c){ba[D[D[c>>2]+4>>2]](c)}if((d|0)!=(e|0)){continue}break}}D[a+40>>2]=e}b:{if((g|0)<=0){e=0;break b}e=1;c=D[b+20>>2];d=D[b+12>>2];f=D[b+16>>2];if((c|0)>=(d|0)&f>>>0>=G[b+8>>2]|(c|0)>(d|0)){break b}d=0;while(1){h=E[f+D[b>>2]|0];f=f+1|0;c=f?c:c+1|0;D[b+16>>2]=f;D[b+20>>2]=c;f=ba[D[D[a>>2]+48>>2]](a,h)|0;h=d<<2;j=h+D[a+36>>2]|0;c=D[j>>2];D[j>>2]=f;if(c){ba[D[D[c>>2]+4>>2]](c)}c=D[D[i>>2]+h>>2];if(!c){break b}if(!(l=c,m=ba[D[D[a>>2]+28>>2]](a)|0,n=ba[D[D[a>>2]+20>>2]](a,d)|0,k=D[D[c>>2]+8>>2],ba[k](l|0,m|0,n|0)|0)){break b}d=d+1|0;e=(g|0)>(d|0);if((d|0)==(g|0)){break b}f=D[b+16>>2];c=D[b+20>>2];h=D[b+12>>2];if(f>>>0<G[b+8>>2]&(c|0)<=(h|0)|(c|0)<(h|0)){continue}break}}a=!e}else{a=0}return a|0}function xh(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;c=D[b+88>>2];if(!(!c|D[c>>2]!=1)){d=D[c+8>>2];D[a+4>>2]=E[d|0]|E[d+1|0]<<8|(E[d+2|0]<<16|E[d+3|0]<<24);d=B[b+24|0];e=D[a+8>>2];f=D[a+12>>2]-e>>2;a:{if(d>>>0>f>>>0){sa(a+8|0,d-f|0);d=B[b+24|0];e=D[a+8>>2];break a}if(d>>>0>=f>>>0){break a}D[a+12>>2]=(d<<2)+e}k=1;f=D[c+8>>2];b:{if((d|0)<=0){b=4;break b}h=d&3;c:{if(d-1>>>0<3){b=4;d=0;break c}l=d&-4;d=0;b=4;while(1){g=d<<2;c=b+f|0;D[g+e>>2]=E[c|0]|E[c+1|0]<<8|(E[c+2|0]<<16|E[c+3|0]<<24);D[(g|4)+e>>2]=E[c+4|0]|E[c+5|0]<<8|(E[c+6|0]<<16|E[c+7|0]<<24);D[(g|8)+e>>2]=E[c+8|0]|E[c+9|0]<<8|(E[c+10|0]<<16|E[c+11|0]<<24);D[(g|12)+e>>2]=E[c+12|0]|E[c+13|0]<<8|(E[c+14|0]<<16|E[c+15|0]<<24);d=d+4|0;b=b+16|0;i=i+4|0;if((l|0)!=(i|0)){continue}break}}if(!h){break b}while(1){c=b+f|0;D[(d<<2)+e>>2]=E[c|0]|E[c+1|0]<<8|(E[c+2|0]<<16|E[c+3|0]<<24);d=d+1|0;b=b+4|0;j=j+1|0;if((j|0)!=(h|0)){continue}break}}c=a;a=b+f|0;D[c+20>>2]=E[a|0]|E[a+1|0]<<8|(E[a+2|0]<<16|E[a+3|0]<<24)}return k|0}function Ia(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0;d=(c>>>0)/3|0;g=D[(D[D[a+8>>2]+96>>2]+J(d,12)|0)+(c-J(d,3)<<2)>>2];e=D[D[a+12>>2]+4>>2];d=D[e+4>>2];a:{if((d|0)!=D[e+8>>2]){D[d>>2]=g;D[e+4>>2]=d+4;break a}b:{h=D[e>>2];i=d-h|0;j=i>>2;d=j+1|0;if(d>>>0<1073741824){f=i>>1;f=j>>>0<536870911?d>>>0>f>>>0?d:f:1073741823;if(f){if(f>>>0>=1073741824){break b}d=na(f<<2)}else{d=0}j=d+(j<<2)|0;D[j>>2]=g;if((i|0)>0){oa(d,h,i)}D[e+8>>2]=d+(f<<2);D[e+4>>2]=j+4;D[e>>2]=d;if(h){ma(h)}break a}qa();T()}ra(1326);T()}e=D[a+4>>2];d=D[e+4>>2];c:{d:{e:{if((d|0)!=D[e+8>>2]){D[d>>2]=c;D[e+4>>2]=d+4;break e}h=D[e>>2];i=d-h|0;g=i>>2;d=g+1|0;if(d>>>0>=1073741824){break d}f=i>>1;f=g>>>0<536870911?d>>>0>f>>>0?d:f:1073741823;if(f){if(f>>>0>=1073741824){break c}d=na(f<<2)}else{d=0}g=d+(g<<2)|0;D[g>>2]=c;if((i|0)>0){oa(d,h,i)}D[e+8>>2]=d+(f<<2);D[e+4>>2]=g+4;D[e>>2]=d;if(!h){break e}ma(h)}a=D[a+4>>2];D[D[a+12>>2]+(b<<2)>>2]=D[a+24>>2];D[a+24>>2]=D[a+24>>2]+1;return}qa();T()}ra(1326);T()}function Rf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;h=D[d+80>>2];e=$-48|0;$=e;a=D[a+4>>2];a:{if(a-31>>>0<4294967267){break a}i=D[D[d>>2]>>2]+D[d+48>>2]|0;D[e+16>>2]=a;a=-1<<a;D[e+20>>2]=a^-1;a=-2-a|0;D[e+24>>2]=a;D[e+32>>2]=(a|0)/2;H[e+28>>2]=K(2)/K(a|0);g=D[c>>2];if((g|0)!=D[c+4>>2]){a=0;d=0;while(1){f=D[(d<<2)+g>>2];h=e+36|0;j=D[D[b>>2]>>2];l=D[b+48>>2];g=D[b+44>>2];k=D[b+40>>2];if(!E[b+84|0]){f=D[D[b+68>>2]+(f<<2)>>2]}f=$h(k,g,f,0)+l|0;oa(h,f+j|0,k);Dc(e+16|0,h,e+12|0,e+8|0);g=a<<2;D[g+i>>2]=D[e+12>>2];D[(g|4)+i>>2]=D[e+8>>2];f=1;a=a+2|0;d=d+1|0;g=D[c>>2];if(d>>>0<D[c+4>>2]-g>>2>>>0){continue}break}break a}if(!h){f=1;break a}d=0;a=0;while(1){j=e+36|0;c=D[D[b>>2]>>2];f=D[b+40>>2];g=D[b+48>>2]+$h(f,D[b+44>>2],E[b+84|0]?a:D[D[b+68>>2]+(a<<2)>>2],0)|0;oa(j,c+g|0,f);Dc(e+16|0,j,e+12|0,e+8|0);c=d<<2;D[c+i>>2]=D[e+12>>2];D[(c|4)+i>>2]=D[e+8>>2];d=d+2|0;f=1;a=a+1|0;if((h|0)!=(a|0)){continue}break}}$=e+48|0;return f|0}function ib(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;d=$-16|0;$=d;a:{e=D[a+4>>2];b:{if(e>>>0<b>>>0){f=b-e|0;h=D[a+8>>2];c=h<<5;c:{if(!(f>>>0>c>>>0|e>>>0>c-f>>>0)){D[a+4>>2]=b;g=e&31;b=D[a>>2]+(e>>>3&536870908)|0;break c}D[d+8>>2]=0;D[d>>2]=0;D[d+4>>2]=0;if((b|0)<0){break a}if(c>>>0<=1073741822){c=b+31&-32;b=h<<6;b=b>>>0<c>>>0?c:b}else{b=2147483647}Ta(d,b);e=D[a+4>>2];D[d+4>>2]=e+f;i=D[a>>2];b=D[d>>2];d:{if((e|0)<=0){break d}c=e>>>5|0;h=c<<2;b=Na(b,i,h)+h|0;g=e-(c<<5)|0;e:{if((g|0)<=0){g=0;break e}c=-1>>>32-g|0;D[b>>2]=D[b>>2]&(c^-1)|c&D[i+h>>2]}i=D[a>>2]}D[a>>2]=D[d>>2];D[d>>2]=i;c=D[a+4>>2];D[a+4>>2]=D[d+4>>2];D[d+4>>2]=c;c=D[a+8>>2];D[a+8>>2]=D[d+8>>2];D[d+8>>2]=c;if(!i){break c}ma(i)}if(!f){break b}if(g){c=32-g|0;a=c>>>0>f>>>0?f:c;D[b>>2]=D[b>>2]&(-1<<g&-1>>>c-a^-1);f=f-a|0;b=b+4|0}c=f>>>5<<2;a=pa(b,0,c);b=f&31;if(!b){break b}a=a+c|0;D[a>>2]=D[a>>2]&(-1>>>32-b^-1);break b}D[a+4>>2]=b}$=d+16|0;return}qa();T()}function Qb(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;d=E[a+11|0]>>>7|0?D[a+4>>2]:E[a+11|0];if(d>>>0<b>>>0){h=$-16|0;$=h;f=b-d|0;if(f){b=E[a+11|0]>>>7|0;g=b?D[a+4>>2]:E[a+11|0];i=g+f|0;b=b?(D[a+8>>2]&2147483647)-1|0:10;if(b-g>>>0<f>>>0){a:{d=$-16|0;$=d;c=i-b|0;if(c>>>0<=-17-b>>>0){j=E[a+11|0]>>>7|0?D[a>>2]:a;b:{if(b>>>0<2147483623){D[d+8>>2]=b<<1;D[d+12>>2]=b+c;c=$-16|0;$=c;$=c+16|0;c=d+8|0;e=d+12|0;c=D[(G[e>>2]<G[c>>2]?c:e)>>2];if(c>>>0>=11){e=c+16&-16;c=e-1|0;c=(c|0)==11?e:c}else{c=10}break b}c=-18}e=c+1|0;c=na(e);if(g){Xa(c,j,g)}if((b|0)!=10){ma(j)}D[a>>2]=c;D[a+8>>2]=e|-2147483648;$=d+16|0;break a}Aa();T()}}b=E[a+11|0]>>>7|0?D[a>>2]:a;d=g+b|0;if(f){pa(d,0,f)}c:{if(E[a+11|0]>>>7|0){D[a+4>>2]=i;break c}B[a+11|0]=i}B[h+15|0]=0;B[b+i|0]=E[h+15|0]}$=h+16|0;return}d=$-16|0;$=d;d:{if(E[a+11|0]>>>7|0){f=D[a>>2];B[d+15|0]=0;B[b+f|0]=E[d+15|0];D[a+4>>2]=b;break d}B[d+14|0]=0;B[a+b|0]=E[d+14|0];B[a+11|0]=b}$=d+16|0}function Sg(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;l=D[a+12>>2];c=D[a+108>>2];e=D[c+80>>2];B[b+84|0]=0;f=D[b+68>>2];d=D[b+72>>2]-f>>2;a:{if(d>>>0<e>>>0){xa(b+68|0,e-d|0,9156);c=D[a+108>>2];e=D[c+80>>2];break a}if(e>>>0>=d>>>0){break a}D[b+72>>2]=f+(e<<2)}k=D[c+96>>2];c=D[c+100>>2]-k|0;if(!c){return 1}c=(c|0)/12|0;m=c>>>0>1?c:1;c=0;b:{while(1){if((c|0)==1431655765){break b}d=D[l>>2]+(J(c,3)<<2)|0;h=D[d>>2];if((h|0)==-1){break b}f=J(c,12)+k|0;g=D[f>>2];if(g>>>0>=e>>>0){break b}j=D[D[a+112>>2]+12>>2];i=D[j+(h<<2)>>2];if(i>>>0>=e>>>0){break b}h=D[b+68>>2];D[h+(g<<2)>>2]=i;g=D[d+4>>2];if((g|0)==-1){break b}i=D[f+4>>2];if(i>>>0>=e>>>0){break b}g=D[(g<<2)+j>>2];if(g>>>0>=e>>>0){break b}D[h+(i<<2)>>2]=g;d=D[d+8>>2];if((d|0)==-1){break b}f=D[f+8>>2];if(f>>>0>=e>>>0){break b}d=D[(d<<2)+j>>2];if(d>>>0>=e>>>0){break b}D[h+(f<<2)>>2]=d;c=c+1|0;if((m|0)!=(c|0)){continue}break}return 1}return 0}function Jg(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;l=D[a+12>>2];c=D[a+68>>2];e=D[c+80>>2];B[b+84|0]=0;f=D[b+68>>2];d=D[b+72>>2]-f>>2;a:{if(d>>>0<e>>>0){xa(b+68|0,e-d|0,9156);c=D[a+68>>2];e=D[c+80>>2];break a}if(e>>>0>=d>>>0){break a}D[b+72>>2]=f+(e<<2)}k=D[c+96>>2];c=D[c+100>>2]-k|0;if(!c){return 1}c=(c|0)/12|0;m=c>>>0>1?c:1;c=0;b:{while(1){if((c|0)==1431655765){break b}d=D[l>>2]+(J(c,3)<<2)|0;h=D[d>>2];if((h|0)==-1){break b}f=J(c,12)+k|0;g=D[f>>2];if(g>>>0>=e>>>0){break b}j=D[D[a+72>>2]+12>>2];i=D[j+(h<<2)>>2];if(i>>>0>=e>>>0){break b}h=D[b+68>>2];D[h+(g<<2)>>2]=i;g=D[d+4>>2];if((g|0)==-1){break b}i=D[f+4>>2];if(i>>>0>=e>>>0){break b}g=D[(g<<2)+j>>2];if(g>>>0>=e>>>0){break b}D[h+(i<<2)>>2]=g;d=D[d+8>>2];if((d|0)==-1){break b}f=D[f+8>>2];if(f>>>0>=e>>>0){break b}d=D[(d<<2)+j>>2];if(d>>>0>=e>>>0){break b}D[h+(f<<2)>>2]=d;c=c+1|0;if((m|0)!=(c|0)){continue}break}return 1}return 0}function Rg(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0;g=$-16|0;$=g;b=D[a+4>>2];d=D[b>>2];a:{c=D[a+12>>2];c=D[c+28>>2]-D[c+24>>2]|0;e=c>>2;b:{if(e>>>0<=D[b+8>>2]-d>>2>>>0){break b}if((c|0)<0){break a}f=D[b+4>>2];c=na(c);h=c+(e<<2)|0;e=f-d|0;f=e+c|0;if((e|0)>0){oa(c,d,e)}D[b+8>>2]=h;D[b+4>>2]=f;D[b>>2]=c;if(!d){break b}ma(d)}b=D[a+12>>2];d=D[b+28>>2];b=D[b+24>>2];D[g+12>>2]=0;b=d-b>>2;c=a+96|0;e=D[c>>2];d=D[a+100>>2]-e>>2;c:{if(b>>>0>d>>>0){xa(c,b-d|0,g+12|0);break c}if(b>>>0>=d>>>0){break c}D[a+100>>2]=e+(b<<2)}e=a+8|0;b=D[a+116>>2];d:{if(b){c=D[b>>2];if((c|0)==D[b+4>>2]){d=1;break d}b=0;while(1){d=ld(e,D[(b<<2)+c>>2]);if(!d){break d}f=D[a+116>>2];c=D[f>>2];b=b+1|0;if(b>>>0<D[f+4>>2]-c>>2>>>0){continue}break}break d}d=1;a=D[a+12>>2];a=D[a+4>>2]-D[a>>2]>>2;if(a>>>0<3){break d}a=(a>>>0)/3|0;b=0;while(1){d=ld(e,J(b,3));if(!d){break d}b=b+1|0;if((a|0)!=(b|0)){continue}break}}$=g+16|0;return d|0}ra(1326);T()}function Ka(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;g=$-16|0;$=g;e=D[b+20>>2];c=D[b+12>>2];d=D[b+16>>2];a:{if((e|0)>=(c|0)&d>>>0>=G[b+8>>2]|(c|0)<(e|0)){break a}B[a+12|0]=E[d+D[b>>2]|0];e=D[b+20>>2];c=D[b+16>>2]+1|0;e=c?e:e+1|0;D[b+16>>2]=c;D[b+20>>2]=e;if(!Ld(1,g+12|0,b)){break a}e=D[b+8>>2];h=D[b+16>>2];c=h;d=e-c|0;c=c>>>0>e>>>0;e=D[b+20>>2];f=D[b+12>>2]-(c+e|0)|0;c=D[g+12>>2];if((f|0)<=0&d>>>0<c>>>0|(f|0)<0|(c|0)<=0){break a}d=h+D[b>>2]|0;D[a>>2]=d;k=a;f=c-1|0;i=f+d|0;j=E[i|0];b:{if(j>>>0<=63){D[a+4>>2]=f;a=E[i|0]&63;break b}c:{switch((j>>>6|0)-1|0){case 0:if(c>>>0<2){break a}D[a+4>>2]=c-2;d=(c+d|0)-2|0;a=E[d+1|0]<<8&16128|E[d|0];break b;case 1:break c;default:break a}}if(c>>>0<3){break a}D[a+4>>2]=c-3;d=(c+d|0)-3|0;a=E[d+2|0]<<16&4128768|E[d+1|0]<<8|E[d|0]}a=a+4096|0;D[k+8>>2]=a;if(a>>>0>1048575){break a}a=e;d=c+h|0;a=d>>>0<c>>>0?a+1|0:a;D[b+16>>2]=d;D[b+20>>2]=a;l=1}$=g+16|0;return l}function od(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;f=D[a+12>>2];h=D[a+8>>2];d=f-h>>2;b=B[b+24|0];a:{if(d>>>0<b>>>0){sa(a+8|0,b-d|0);h=D[a+8>>2];f=D[a+12>>2];break a}if(b>>>0>=d>>>0){break a}f=(b<<2)+h|0;D[a+12>>2]=f}b=0;i=D[c+20>>2];e=D[c+16>>2];d=f-h|0;f=d;g=e+d|0;j=D[c+12>>2];i=d>>>0>g>>>0?i+1|0:i;b:{if(g>>>0>G[c+8>>2]&(j|0)<=(i|0)|(i|0)>(j|0)){break b}oa(h,e+D[c>>2]|0,d);d=D[c+20>>2];e=f+D[c+16>>2]|0;d=e>>>0<f>>>0?d+1|0:d;g=e;D[c+16>>2]=e;D[c+20>>2]=d;e=D[c+12>>2];f=g+4|0;d=f>>>0<4?d+1|0:d;if(f>>>0>G[c+8>>2]&(d|0)>=(e|0)|(d|0)>(e|0)){break b}d=g+D[c>>2]|0;D[a+20>>2]=E[d|0]|E[d+1|0]<<8|(E[d+2|0]<<16|E[d+3|0]<<24);d=D[c+20>>2];g=D[c+16>>2];e=g+4|0;f=e>>>0<4?d+1|0:d;j=e;D[c+16>>2]=e;D[c+20>>2]=f;e=D[c+12>>2];if((f|0)>=(e|0)&j>>>0>=G[c+8>>2]|(f|0)>(e|0)){break b}e=E[j+D[c>>2]|0];f=g+5|0;d=f>>>0<5?d+1|0:d;D[c+16>>2]=f;D[c+20>>2]=d;if(e-1>>>0>29){break b}D[a+4>>2]=e;b=1}return b|0}function Tc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;g=$-16|0;$=g;a:{b:{if(b){D[a+88>>2]=0;D[a+92>>2]=0;c=D[a+84>>2];D[a+84>>2]=0;if(c){ma(c)}D[a+76>>2]=0;D[a+80>>2]=0;c=D[a+72>>2];D[a+72>>2]=0;if(c){ma(c)}c=D[b>>2];d=D[b+4>>2];B[g+15|0]=0;Ea(a,d-c>>2,g+15|0);c=D[b+28>>2];d=D[b+24>>2];B[g+14|0]=0;Ea(a+12|0,c-d>>2,g+14|0);Ub(a+28|0,D[b+4>>2]-D[b>>2]>>2,10316);d=D[b+28>>2]-D[b+24>>2]|0;e=d>>2;c=D[a+52>>2];c:{if(e>>>0<=D[a+60>>2]-c>>2>>>0){break c}if((d|0)<0){break b}f=D[a+56>>2];d=na(d);h=d+(e<<2)|0;e=f-c|0;f=e+d|0;if((e|0)>0){oa(d,c,e)}D[a+60>>2]=h;D[a+56>>2]=f;D[a+52>>2]=d;if(!c){break c}ma(c)}d=D[b+28>>2]-D[b+24>>2]|0;e=d>>2;c=D[a+40>>2];d:{if(e>>>0<=D[a+48>>2]-c>>2>>>0){break d}if((d|0)<0){break a}f=D[a+44>>2];d=na(d);h=d+(e<<2)|0;e=f-c|0;f=e+d|0;if((e|0)>0){oa(d,c,e)}D[a+48>>2]=h;D[a+44>>2]=f;D[a+40>>2]=d;if(!c){break d}ma(c)}B[a+24|0]=1;D[a+64>>2]=b}$=g+16|0;return}ra(1326);T()}ra(1326);T()}function Eg(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;h=D[a+12>>2];c=D[a+68>>2];e=D[c+80>>2];B[b+84|0]=0;g=D[b+68>>2];d=D[b+72>>2]-g>>2;a:{if(d>>>0<e>>>0){xa(b+68|0,e-d|0,9156);c=D[a+68>>2];e=D[c+80>>2];break a}if(e>>>0>=d>>>0){break a}D[b+72>>2]=g+(e<<2)}k=D[c+96>>2];c=D[c+100>>2]-k|0;if(!c){return 1}c=(c|0)/12|0;l=c>>>0>1?c:1;m=D[h+28>>2];c=0;b:{while(1){d=(J(c,3)<<2)+m|0;g=D[d>>2];if((g|0)==-1){break b}h=J(c,12)+k|0;i=D[h>>2];if(i>>>0>=e>>>0){break b}f=g<<2;g=D[D[a+72>>2]+12>>2];f=D[f+g>>2];if(f>>>0>=e>>>0){break b}j=i<<2;i=D[b+68>>2];D[j+i>>2]=f;f=D[d+4>>2];if((f|0)==-1){break b}j=D[h+4>>2];if(j>>>0>=e>>>0){break b}f=D[g+(f<<2)>>2];if(f>>>0>=e>>>0){break b}D[i+(j<<2)>>2]=f;d=D[d+8>>2];if((d|0)==-1){break b}h=D[h+8>>2];if(h>>>0>=e>>>0){break b}d=D[g+(d<<2)>>2];if(d>>>0>=e>>>0){break b}D[i+(h<<2)>>2]=d;c=c+1|0;if((l|0)!=(c|0)){continue}break}return 1}return 0}function xc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=K(0),f=K(0),g=K(0),h=K(0),i=K(0),j=0,k=K(0),l=K(0),m=K(0),n=K(0),o=0;a:{if(D[c+28>>2]!=9|E[c+24|0]!=3){break a}a=D[a+4>>2];if(a-31>>>0<4294967267){break a}o=1;j=D[c+80>>2];if(!j){break a}k=K(K(2)/K((1<<a)-2|0));c=D[D[c>>2]>>2]+D[c+48>>2]|0;a=D[D[b>>2]>>2]+D[b+48>>2]|0;b=0;while(1){g=K(0);l=K(0);m=K(0);e=K(K(K(D[a>>2])*k)+K(-1));f=K(K(K(D[a+4>>2])*k)+K(-1));i=K(K(K(1)-K(L(e)))-K(L(f)));h=K(O(K(-i),K(0)));n=K(-h);f=K(f+(f<K(0)?h:n));e=K(e+(e<K(0)?h:n));h=K(K(f*f)+K(K(i*i)+K(e*e)));if(!(+h<1e-6)){g=K(K(1)/K(S(h)));m=K(f*g);l=K(e*g);g=K(i*g)}a=a+8|0;d=(v(m),x(2));B[c+8|0]=d;B[c+9|0]=d>>>8;B[c+10|0]=d>>>16;B[c+11|0]=d>>>24;d=(v(l),x(2));B[c+4|0]=d;B[c+5|0]=d>>>8;B[c+6|0]=d>>>16;B[c+7|0]=d>>>24;d=(v(g),x(2));B[c|0]=d;B[c+1|0]=d>>>8;B[c+2|0]=d>>>16;B[c+3|0]=d>>>24;c=c+12|0;b=b+1|0;if((j|0)!=(b|0)){continue}break}}return o|0}function Dc(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;j=+H[b>>2];k=+H[b+4>>2];l=+H[b+8>>2];g=L(j)+L(k)+L(l);a:{if(!(g>1e-6)){j=1;k=0;e=0;break a}g=1/g;k=g*k;j=g*j;e=g*l<0}h=D[a+16>>2];l=+(h|0);g=P(j*l+.5);b:{if(L(g)<2147483648){m=~~g;break b}m=-2147483648}f=m>>31;i=f+m^f;g=P(k*l+.5);c:{if(L(g)<2147483648){f=~~g;break c}f=-2147483648}b=f>>31;b=h-(i+(f+b^b)|0)|0;i=(b|0)<0?0:b;e=e?0-i|0:i;f=f+(b>>31&((f|0)>0?b:0-b|0))|0;d:{if((m|0)>=0){b=e+h|0;a=D[a+8>>2];e=f+h|0;break d}b=f>>31;b=b+f^b;a=D[a+8>>2];b=(e|0)<0?b:a-b|0;e=(f|0)<0?i:a-i|0}e:{if(!(b|e)){b=a;break e}if(!((a|0)!=(b|0)|e)){b=a;break e}f=(a|0)!=(e|0);if(!(b|f)){b=a;break e}if(!((b|0)<=(h|0)|e)){b=(h<<1)-b|0;a=0;break e}if(!((b|0)>=(h|0)|f)){b=(h<<1)-b|0;break e}if(!((a|0)!=(b|0)|(e|0)>=(h|0))){b=a;a=(h<<1)-e|0;break e}if(b){a=e;break e}b=0;if((e|0)<=(h|0)){a=e;break e}a=(h<<1)-e|0}D[c>>2]=a;D[d>>2]=b}function Rc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;g=D[a>>2];c=g+(b>>>3&536870908)|0;D[c>>2]=D[c>>2]|1<<b;f=D[a+64>>2];e=(b|0)==-1;d=-1;a:{if(e){break a}c=b+1|0;c=(c>>>0)%3|0?c:b-2|0;d=-1;if((c|0)==-1){break a}d=D[D[f>>2]+(c<<2)>>2]}c=D[a+12>>2];h=(d>>>3&536870908)+c|0;D[h>>2]=D[h>>2]|1<<d;b:{c:{if(!e){d:{e:{if((b>>>0)%3|0){e=b-1|0;break e}e=b+2|0;d=-1;if((e|0)==-1){break d}}d=D[D[f>>2]+(e<<2)>>2]}e=(d>>>3&536870908)+c|0;D[e>>2]=D[e>>2]|1<<d;d=-1;b=D[D[f+12>>2]+(b<<2)>>2];if((b|0)==-1){break b}B[a+24|0]=0;a=(b>>>3&536870908)+g|0;D[a>>2]=D[a>>2]|1<<b;a=b+1|0;a=(a>>>0)%3|0?a:b-2|0;if((a|0)!=-1){d=D[D[f>>2]+(a<<2)>>2]}a=c+(d>>>3&536870908)|0;D[a>>2]=D[a>>2]|1<<d;f:{g:{if((b>>>0)%3|0){b=b-1|0;break g}b=b+2|0;a=-1;if((b|0)==-1){break f}}a=D[D[f>>2]+(b<<2)>>2]}b=1<<a;a=c+(a>>>3&536870908)|0;c=D[a>>2];break c}a=c+536870908|0;b=D[c+536870908>>2];c=-2147483648}D[a>>2]=b|c}}function hc(a,b){var c=0,d=0;c=D[b+8>>2];D[a+4>>2]=D[b+4>>2];D[a+8>>2]=c;D[a+20>>2]=D[b+20>>2];c=D[b+16>>2];D[a+12>>2]=D[b+12>>2];D[a+16>>2]=c;a:{b:{if((a|0)!=(b|0)){c=D[b+28>>2];if(c){c:{if(D[a+32>>2]<<5>>>0>=c>>>0){d=D[a+24>>2];break c}d=D[a+24>>2];if(d){ma(d);D[a+32>>2]=0;D[a+24>>2]=0;D[a+28>>2]=0;c=D[b+28>>2]}if((c|0)<0){break b}c=(c-1>>>5|0)+1|0;d=na(c<<2);D[a+32>>2]=c;D[a+28>>2]=0;D[a+24>>2]=d;c=D[b+28>>2]}Na(d,D[b+24>>2],(c-1>>>3&536870908)+4|0);c=D[b+28>>2]}else{c=0}D[a+28>>2]=c;c=D[b+40>>2];if(c){d:{if(D[a+44>>2]<<5>>>0>=c>>>0){d=D[a+36>>2];break d}d=D[a+36>>2];if(d){ma(d);D[a+44>>2]=0;D[a+36>>2]=0;D[a+40>>2]=0;c=D[b+40>>2]}if((c|0)<0){break a}c=(c-1>>>5|0)+1|0;d=na(c<<2);D[a+44>>2]=c;D[a+40>>2]=0;D[a+36>>2]=d;c=D[b+40>>2]}Na(d,D[b+36>>2],(c-1>>>3&536870908)+4|0);b=D[b+40>>2]}else{b=0}D[a+40>>2]=b}return}qa();T()}qa();T()}function Hd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;g=$-16|0;$=g;a:{if(!Oa(1,g+8|0,b)){break a}c=D[b+8>>2];e=D[b+16>>2];h=D[g+12>>2];d=D[b+20>>2];f=D[b+12>>2]-(d+(c>>>0<e>>>0)|0)|0;i=c-e|0;c=D[g+8>>2];if((h|0)==(f|0)&i>>>0<c>>>0|f>>>0<h>>>0){break a}d=d+h|0;f=c+e|0;d=f>>>0<e>>>0?d+1|0:d;D[b+16>>2]=f;D[b+20>>2]=d;if((c|0)<=0){break a}d=e+D[b>>2]|0;D[a+40>>2]=d;h=a;f=c-1|0;e=d+f|0;b=E[e|0];b:{if(b>>>0<=63){D[a+44>>2]=f;a=E[e|0]&63;break b}c:{switch((b>>>6|0)-1|0){case 0:if(c>>>0<2){break a}D[a+44>>2]=c-2;b=(c+d|0)-2|0;a=E[b+1|0]<<8&16128|E[b|0];break b;case 1:if(c>>>0<3){break a}D[a+44>>2]=c-3;b=(c+d|0)-3|0;a=E[b+2|0]<<16&4128768|E[b+1|0]<<8|E[b|0];break b;default:break c}}D[a+44>>2]=c-4;b=(c+d|0)-4|0;a=E[b+2|0]<<16|E[b+3|0]<<24&1056964608|E[b+1|0]<<8|E[b|0]}a=a+16384|0;D[h+48>>2]=a;j=a>>>0<4194304}$=g+16|0;return j}function Ie(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0;f=$+-64|0;$=f;d=1;a:{if(Ja(a,b,0)){break a}d=0;if(!b){break a}d=$+-64|0;$=d;e=D[b>>2];g=D[e-4>>2];h=D[e-8>>2];D[d+20>>2]=0;D[d+16>>2]=11220;D[d+12>>2]=b;D[d+8>>2]=11268;e=0;pa(d+24|0,0,39);b=b+h|0;b:{if(Ja(g,11268,0)){D[d+56>>2]=1;ba[D[D[g>>2]+20>>2]](g,d+8|0,b,b,1,0);e=D[d+32>>2]==1?b:0;break b}ba[D[D[g>>2]+24>>2]](g,d+8|0,b,1,0);c:{switch(D[d+44>>2]){case 0:e=D[d+48>>2]==1?D[d+36>>2]==1?D[d+40>>2]==1?D[d+28>>2]:0:0:0;break b;case 1:break c;default:break b}}if(D[d+32>>2]!=1){if(D[d+48>>2]|D[d+36>>2]!=1|D[d+40>>2]!=1){break b}}e=D[d+24>>2]}$=d- -64|0;d=0;if(!e){break a}b=f+8|0;pa(b|4,0,52);D[f+56>>2]=1;D[f+20>>2]=-1;D[f+16>>2]=a;D[f+8>>2]=e;ba[D[D[e>>2]+28>>2]](e,b,D[c>>2],1);a=D[f+32>>2];if((a|0)==1){D[c>>2]=D[f+24>>2]}d=(a|0)==1}$=f- -64|0;return d|0}function Nf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;a:{a=$-32|0;$=a;e=za(c);if(e>>>0<4294967280){b:{c:{if(e>>>0>=11){g=e+16&-16;f=na(g);D[a+24>>2]=g|-2147483648;D[a+16>>2]=f;D[a+20>>2]=e;break c}B[a+27|0]=e;f=a+16|0;if(!e){break b}}oa(f,c,e)}B[e+f|0]=0;D[a+8>>2]=0;D[a>>2]=0;D[a+4>>2]=0;d:{c=_a(b,a+16|0);if((c|0)==(b+4|0)){break d}b=D[c+28>>2];f=D[c+32>>2];if((b|0)==(f|0)){break d}b=f-b|0;if(b&3){break d}e=b>>>2|0;f=D[a+4>>2];b=D[a>>2];g=f-b>>2;e:{if(e>>>0>g>>>0){sa(a,e-g|0);b=D[a>>2];f=D[a+4>>2];break e}if(e>>>0>=g>>>0){break e}f=(e<<2)+b|0;D[a+4>>2]=f}if((b|0)!=(f|0)){e=b;b=D[c+28>>2];oa(e,b,D[c+32>>2]-b|0);break d}ua();T()}b=D[d>>2];if(b){D[d+4>>2]=b;ma(b)}D[d>>2]=D[a>>2];D[d+4>>2]=D[a+4>>2];D[d+8>>2]=D[a+8>>2];if(B[a+27|0]<0){ma(D[a+16>>2])}$=a+32|0;break a}Aa();T()}}function kc(a){var b=0,c=0,d=0;b=D[a+8>>2];d=D[a>>2];a:{if(E[a+12|0]){b:{c:{d:{e:{if((b|0)==-1){break e}c=b+1|0;b=(c>>>0)%3|0?c:b-2|0;if((b|0)==-1){break e}b=D[D[d+12>>2]+(b<<2)>>2];if((b|0)!=-1){break d}}D[a+8>>2]=-1;break c}c=b+1|0;b=(c>>>0)%3|0?c:b-2|0;D[a+8>>2]=b;if((b|0)!=-1){break b}}c=D[a+4>>2];b=-1;f:{if((c|0)==-1){break f}g:{if((c>>>0)%3|0){c=c-1|0;break g}c=c+2|0;b=-1;if((c|0)==-1){break f}}c=D[D[d+12>>2]+(c<<2)>>2];b=-1;if((c|0)==-1){break f}b=c-1|0;if((c>>>0)%3|0){break f}b=c+2|0}B[a+12|0]=0;D[a+8>>2]=b;return}if((b|0)!=D[a+4>>2]){break a}D[a+8>>2]=-1;return}c=-1;h:{if((b|0)==-1){break h}i:{if((b>>>0)%3|0){b=b-1|0;break i}b=b+2|0;c=-1;if((b|0)==-1){break h}}b=D[D[d+12>>2]+(b<<2)>>2];c=-1;if((b|0)==-1){break h}c=b-1|0;if((b>>>0)%3|0){break h}c=b+2|0}D[a+8>>2]=c}}function Jd(a){var b=0,c=0,d=0;b=na(32);c=E[1619]|E[1620]<<8;B[b+24|0]=c;B[b+25|0]=c>>>8;c=E[1615]|E[1616]<<8|(E[1617]<<16|E[1618]<<24);d=E[1611]|E[1612]<<8|(E[1613]<<16|E[1614]<<24);B[b+16|0]=d;B[b+17|0]=d>>>8;B[b+18|0]=d>>>16;B[b+19|0]=d>>>24;B[b+20|0]=c;B[b+21|0]=c>>>8;B[b+22|0]=c>>>16;B[b+23|0]=c>>>24;c=E[1607]|E[1608]<<8|(E[1609]<<16|E[1610]<<24);d=E[1603]|E[1604]<<8|(E[1605]<<16|E[1606]<<24);B[b+8|0]=d;B[b+9|0]=d>>>8;B[b+10|0]=d>>>16;B[b+11|0]=d>>>24;B[b+12|0]=c;B[b+13|0]=c>>>8;B[b+14|0]=c>>>16;B[b+15|0]=c>>>24;c=E[1599]|E[1600]<<8|(E[1601]<<16|E[1602]<<24);d=E[1595]|E[1596]<<8|(E[1597]<<16|E[1598]<<24);B[b|0]=d;B[b+1|0]=d>>>8;B[b+2|0]=d>>>16;B[b+3|0]=d>>>24;B[b+4|0]=c;B[b+5|0]=c>>>8;B[b+6|0]=c>>>16;B[b+7|0]=c>>>24;B[b+26|0]=0;D[a>>2]=-1;ta(a+4|0,b,26);ma(b)}function Pf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;d=$-16|0;$=d;a:{e=za(c);if(e>>>0<4294967280){b:{c:{if(e>>>0>=11){f=e+16&-16;a=na(f);D[d+8>>2]=f|-2147483648;D[d>>2]=a;D[d+4>>2]=e;break c}B[d+11|0]=e;a=d;if(!e){break b}}oa(a,c,e)}B[a+e|0]=0;c=E[d+11|0];e=c<<24>>24;f=D[d>>2];b=D[b+4>>2];a=0;d:{if(!b){break d}a=c;c=(e|0)<0;a=c?D[d+4>>2]:a;j=c?f:d;while(1){c=E[b+27|0];g=c<<24>>24<0;c=g?D[b+20>>2]:c;k=c>>>0<a>>>0;e:{f:{i=k?c:a;g:{if(i){h=b+16|0;g=g?D[h>>2]:h;h=va(j,g,i);h:{if(!h){if(a>>>0>=c>>>0){break h}break e}if((h|0)<0){break e}}c=va(g,j,i);if(!c){break g}if((c|0)<0){break f}a=1;break d}if(a>>>0<c>>>0){break e}}if(k){break f}a=1;break d}b=b+4|0}b=D[b>>2];if(b){continue}break}a=0}if((e|0)<0){ma(f)}$=d+16|0;break a}Aa();T()}return a|0}function Na(a,b,c){var d=0,e=0;a:{if((a|0)==(b|0)){break a}e=a+c|0;if(b-e>>>0<=0-(c<<1)>>>0){return oa(a,b,c)}d=(a^b)&3;b:{c:{if(a>>>0<b>>>0){if(d){d=a;break b}if(!(a&3)){d=a;break c}d=a;while(1){if(!c){break a}B[d|0]=E[b|0];b=b+1|0;c=c-1|0;d=d+1|0;if(d&3){continue}break}break c}d:{if(d){break d}if(e&3){while(1){if(!c){break a}c=c-1|0;d=c+a|0;B[d|0]=E[b+c|0];if(d&3){continue}break}}if(c>>>0<=3){break d}while(1){c=c-4|0;D[c+a>>2]=D[b+c>>2];if(c>>>0>3){continue}break}}if(!c){break a}while(1){c=c-1|0;B[c+a|0]=E[b+c|0];if(c){continue}break}break a}if(c>>>0<=3){break b}while(1){D[d>>2]=D[b>>2];b=b+4|0;d=d+4|0;c=c-4|0;if(c>>>0>3){continue}break}}if(!c){break a}while(1){B[d|0]=E[b|0];d=d+1|0;b=b+1|0;c=c-1|0;if(c){continue}break}}return a}function Oc(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;d=$-16|0;$=d;g=D[a+24>>2];l=D[a+28>>2];a:{if((g|0)!=(l|0)){while(1){D[d+8>>2]=0;D[d>>2]=0;D[d+4>>2]=0;e=Nc(D[g>>2],b,d);a=E[d+11|0];i=a<<24>>24;j=3;b:{c:{d:{e:{if(!e){break e}j=0;e=E[c+11|0];f=e<<24>>24;k=(i|0)<0?D[d+4>>2]:a;if((k|0)!=(((f|0)<0?D[c+4>>2]:e)|0)){break e}h=(f|0)<0?D[c>>2]:c;f=D[d>>2];e=(i|0)<0;f:{if(!e){if(!i){break f}e=d;if(E[h|0]!=(f&255)){break b}while(1){a=a-1|0;if(!a){break f}f=E[h+1|0];h=h+1|0;e=e+1|0;if((f|0)==E[e|0]){continue}break}break e}if(!k){break f}if(va(e?f:d,h,k)){break d}}m=D[g>>2];j=1}if((i|0)>=0){break c}}ma(D[d>>2])}switch(j|0){case 0:case 3:break b;default:break a}}g=g+4|0;if((l|0)!=(g|0)){continue}break}}m=0}$=d+16|0;return m}function Ob(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;d=D[a+8>>2];c=D[a+4>>2];if(d-c>>2>>>0>=b>>>0){if(b){b=b<<2;c=pa(c,0,b)+b|0}D[a+4>>2]=c;return}a:{b:{c:{g=D[a>>2];f=c-g>>2;e=f+b|0;if(e>>>0<1073741824){d=d-g|0;h=d>>1;e=d>>2>>>0<536870911?e>>>0>h>>>0?e:h:1073741823;if(e){if(e>>>0>=1073741824){break c}i=na(e<<2)}d=(f<<2)+i|0;f=b<<2;b=pa(d,0,f);f=b+f|0;e=(e<<2)+i|0;if((c|0)==(g|0)){break b}while(1){c=c-4|0;b=D[c>>2];D[c>>2]=0;d=d-4|0;D[d>>2]=b;if((c|0)!=(g|0)){continue}break}D[a+8>>2]=e;b=D[a+4>>2];D[a+4>>2]=f;c=D[a>>2];D[a>>2]=d;if((b|0)==(c|0)){break a}while(1){b=b-4|0;a=D[b>>2];D[b>>2]=0;if(a){ba[D[D[a>>2]+4>>2]](a)}if((b|0)!=(c|0)){continue}break}break a}qa();T()}ra(1326);T()}D[a+8>>2]=e;D[a+4>>2]=f;D[a>>2]=b}if(c){ma(c)}}function Sd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;d=D[b+12>>2];g=d;c=D[b+20>>2];e=c;f=D[b+16>>2];h=f+4|0;c=h>>>0<4?c+1|0:c;i=D[b+8>>2];a:{if(i>>>0<h>>>0&(c|0)>=(d|0)|(c|0)>(d|0)){break a}j=D[b>>2];d=j+f|0;d=E[d|0]|E[d+1|0]<<8|(E[d+2|0]<<16|E[d+3|0]<<24);D[b+16>>2]=h;D[b+20>>2]=c;c=e;e=f+8|0;c=e>>>0<8?c+1|0:c;f=e;e=c;if(f>>>0>i>>>0&(c|0)>=(g|0)|(c|0)>(g|0)){break a}c=h+j|0;c=E[c|0]|E[c+1|0]<<8|(E[c+2|0]<<16|E[c+3|0]<<24);D[b+16>>2]=f;D[b+20>>2]=e;if((c|0)<(d|0)){break a}D[a+16>>2]=c;D[a+12>>2]=d;g=(c>>31)-((d>>31)+(c>>>0<d>>>0)|0)|0;c=c-d|0;if(!g&c>>>0>2147483646|g){break a}c=c+1|0;D[a+20>>2]=c;d=c>>>1|0;D[a+24>>2]=d;D[a+28>>2]=0-d;if(!(c&1)){D[a+24>>2]=d-1}k=Ka(a+112|0,b)}return k|0}function pa(a,b,c){var d=0,e=0,f=0;a:{if(!c){break a}B[a|0]=b;e=a+c|0;B[e-1|0]=b;if(c>>>0<3){break a}B[a+2|0]=b;B[a+1|0]=b;B[e-3|0]=b;B[e-2|0]=b;if(c>>>0<7){break a}B[a+3|0]=b;B[e-4|0]=b;if(c>>>0<9){break a}e=0-a&3;f=e+a|0;d=J(b&255,16843009);D[f>>2]=d;b=c-e&-4;c=b+f|0;D[c-4>>2]=d;if(b>>>0<9){break a}D[f+8>>2]=d;D[f+4>>2]=d;D[c-8>>2]=d;D[c-12>>2]=d;if(b>>>0<25){break a}D[f+24>>2]=d;D[f+20>>2]=d;D[f+16>>2]=d;D[f+12>>2]=d;D[c-16>>2]=d;D[c-20>>2]=d;D[c-24>>2]=d;D[c-28>>2]=d;c=b;b=f&4|24;c=c-b|0;if(c>>>0<32){break a}d=$h(d,0,1,1);e=aa;b=b+f|0;while(1){D[b+24>>2]=d;D[b+28>>2]=e;D[b+16>>2]=d;D[b+20>>2]=e;D[b+8>>2]=d;D[b+12>>2]=e;D[b>>2]=d;D[b+4>>2]=e;b=b+32|0;c=c-32|0;if(c>>>0>31){continue}break}}return a}function Qc(a,b){var c=0,d=0,e=0,f=0,g=0;d=-1;f=-1;e=-1;a:{b:{if((b|0)==-1){break b}g=1;f=D[D[D[a+4>>2]+12>>2]+(b<<2)>>2];c=b+1|0;c=(c>>>0)%3|0?c:b-2|0;if((c|0)>=0){e=(c>>>0)/3|0;e=D[(D[D[a>>2]+96>>2]+J(e,12)|0)+(c-J(e,3)<<2)>>2]}c:{if((f|0)==-1){break c}g=0;c=((f>>>0)%3|0?-1:2)+f|0;if((c|0)<0){break c}d=(c>>>0)/3|0;d=D[(D[D[a>>2]+96>>2]+J(d,12)|0)+(c-J(d,3)<<2)>>2]}c=-1;if((d|0)!=(e|0)){break a}e=-1;d:{b=((b>>>0)%3|0?-1:2)+b|0;if((b|0)>=0){d=(b>>>0)/3|0;d=D[(D[D[a>>2]+96>>2]+J(d,12)|0)+(b-J(d,3)<<2)>>2];if(g){break b}break d}d=-1;if(!g){break d}break b}b=f+1|0;b=(b>>>0)%3|0?b:f-2|0;if((b|0)<0){break b}c=D[D[a>>2]+96>>2];a=(b>>>0)/3|0;e=D[(c+J(a,12)|0)+(b-J(a,3)<<2)>>2]}c=(d|0)==(e|0)?f:-1}return c}function de(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;d=D[b+12>>2];h=d;c=D[b+20>>2];e=c;f=D[b+16>>2];g=f+4|0;c=g>>>0<4?c+1|0:c;i=D[b+8>>2];a:{if(i>>>0<g>>>0&(c|0)>=(d|0)|(c|0)>(d|0)){break a}j=D[b>>2];d=j+f|0;d=E[d|0]|E[d+1|0]<<8|(E[d+2|0]<<16|E[d+3|0]<<24);D[b+16>>2]=g;D[b+20>>2]=c;c=e;e=f+8|0;c=e>>>0<8?c+1|0:c;f=e;e=c;if(f>>>0>i>>>0&(c|0)>=(h|0)|(c|0)>(h|0)){break a}c=g+j|0;c=E[c|0]|E[c+1|0]<<8|(E[c+2|0]<<16|E[c+3|0]<<24);D[b+16>>2]=f;D[b+20>>2]=e;if((c|0)<(d|0)){break a}D[a+16>>2]=c;D[a+12>>2]=d;b=(c>>31)-((d>>31)+(c>>>0<d>>>0)|0)|0;c=c-d|0;if(!b&c>>>0>2147483646|b){break a}k=1;b=c+1|0;D[a+20>>2]=b;c=b>>>1|0;D[a+24>>2]=c;D[a+28>>2]=0-c;if(b&1){break a}D[a+24>>2]=c-1}return k|0}function Dg(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0;b=D[a+4>>2];d=D[b>>2];a:{c=D[a+12>>2];c=D[c+56>>2]-D[c+52>>2]|0;e=c>>2;b:{if(e>>>0<=D[b+8>>2]-d>>2>>>0){break b}if((c|0)<0){break a}f=D[b+4>>2];c=na(c);g=c+(e<<2)|0;e=f-d|0;f=e+c|0;if((e|0)>0){oa(c,d,e)}D[b+8>>2]=g;D[b+4>>2]=f;D[b>>2]=c;if(!d){break b}ma(d)}e=a+8|0;b=D[a+76>>2];c:{if(b){d=D[b>>2];if((d|0)==D[b+4>>2]){return 1}b=0;while(1){c=jd(e,D[(b<<2)+d>>2]);if(!c){break c}f=D[a+76>>2];d=D[f>>2];b=b+1|0;if(b>>>0<D[f+4>>2]-d>>2>>>0){continue}break}break c}c=1;a=D[D[a+12>>2]+64>>2];a=D[a+4>>2]-D[a>>2]>>2;if(a>>>0<3){break c}a=(a>>>0)/3|0;b=0;while(1){c=jd(e,J(b,3));if(!c){break c}b=b+1|0;if((a|0)!=(b|0)){continue}break}}return c|0}ra(1326);T()}function Ff(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;f=$+-64|0;$=f;e=f+8|0;C[e+38>>1]=0;D[e>>2]=0;D[e+8>>2]=0;D[e+12>>2]=0;D[e+16>>2]=0;D[e+20>>2]=0;D[e+24>>2]=0;D[e+28>>2]=0;B[e+29|0]=0;B[e+30|0]=0;B[e+31|0]=0;B[e+32|0]=0;B[e+33|0]=0;B[e+34|0]=0;B[e+35|0]=0;B[e+36|0]=0;D[e+16>>2]=0;D[e+20>>2]=0;D[e>>2]=b;D[e+8>>2]=c;D[e+12>>2]=0;b=f+48|0;Kd(b,a,e,d);D[a+24>>2]=D[f+48>>2];c=a+24|0;a:{if((c|0)==(b|0)){break a}e=f+48|4;b=E[f+63|0];d=b<<24>>24;g=a+28|0;if(B[g+11|0]>=0){if((d|0)>=0){a=D[e+4>>2];D[g>>2]=D[e>>2];D[g+4>>2]=a;D[g+8>>2]=D[e+8>>2];break a}sb(g,D[f+52>>2],D[f+56>>2]);break a}a=(d|0)<0;tb(g,a?D[f+52>>2]:e,a?D[f+56>>2]:b)}if(B[f+63|0]<0){ma(D[f+52>>2])}$=f- -64|0;return c|0}function Ig(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0;b=D[a+4>>2];d=D[b>>2];a:{c=D[a+12>>2];c=D[c+28>>2]-D[c+24>>2]|0;e=c>>2;b:{if(e>>>0<=D[b+8>>2]-d>>2>>>0){break b}if((c|0)<0){break a}f=D[b+4>>2];c=na(c);g=c+(e<<2)|0;e=f-d|0;f=e+c|0;if((e|0)>0){oa(c,d,e)}D[b+8>>2]=g;D[b+4>>2]=f;D[b>>2]=c;if(!d){break b}ma(d)}e=a+8|0;b=D[a+76>>2];c:{if(b){d=D[b>>2];if((d|0)==D[b+4>>2]){return 1}b=0;while(1){c=kd(e,D[(b<<2)+d>>2]);if(!c){break c}f=D[a+76>>2];d=D[f>>2];b=b+1|0;if(b>>>0<D[f+4>>2]-d>>2>>>0){continue}break}break c}c=1;a=D[a+12>>2];a=D[a+4>>2]-D[a>>2]>>2;if(a>>>0<3){break c}a=(a>>>0)/3|0;b=0;while(1){c=kd(e,J(b,3));if(!c){break c}b=b+1|0;if((a|0)!=(b|0)){continue}break}}return c|0}ra(1326);T()}function Gf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;d=$+-64|0;$=d;C[d+46>>1]=0;D[d+8>>2]=0;D[d+16>>2]=0;D[d+20>>2]=0;D[d+24>>2]=0;D[d+28>>2]=0;D[d+32>>2]=0;D[d+36>>2]=0;B[d+37|0]=0;B[d+38|0]=0;B[d+39|0]=0;B[d+40|0]=0;B[d+41|0]=0;B[d+42|0]=0;B[d+43|0]=0;B[d+44|0]=0;D[d+24>>2]=0;D[d+28>>2]=0;D[d+8>>2]=b;D[d+16>>2]=c;D[d+20>>2]=0;b=d+48|0;Jd(b);D[a+24>>2]=D[d+48>>2];e=a+24|0;a:{if((b|0)==(e|0)){break a}b=d+48|4;f=E[d+63|0];c=f<<24>>24;a=a+28|0;if(B[a+11|0]>=0){if((c|0)>=0){c=D[b+4>>2];D[a>>2]=D[b>>2];D[a+4>>2]=c;D[a+8>>2]=D[b+8>>2];break a}sb(a,D[d+52>>2],D[d+56>>2]);break a}g=a;a=(c|0)<0;tb(g,a?D[d+52>>2]:b,a?D[d+56>>2]:f)}if(B[d+63|0]<0){ma(D[d+52>>2])}$=d- -64|0;return e|0}function we(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;d=D[b>>2];b=D[b+4>>2];f=D[D[a+8>>2]+40>>2];m=na((f|0)>=0?f:-1);g=1;h=b-d|0;a:{if((h|0)<=0){break a}b=0;g=0;d=0+D[c+20>>2]|0;i=D[c+16>>2];e=f+i|0;d=e>>>0<f>>>0?d+1|0:d;j=e;e=D[c+12>>2];if(j>>>0>G[c+8>>2]&(e|0)<=(d|0)|(d|0)>(e|0)){break a}k=h>>2;g=(k|0)>1?k:1;while(1){b:{e=oa(m,i+D[c>>2]|0,f);D[c+16>>2]=j;D[c+20>>2]=d;oa(D[D[D[a+8>>2]+64>>2]>>2]+b|0,e,f);l=l+1|0;if((g|0)==(l|0)){break b}b=b+f|0;d=n+D[c+20>>2]|0;i=D[c+16>>2];e=f+i|0;d=e>>>0<f>>>0?d+1|0:d;j=e;h=e;e=D[c+12>>2];if((e|0)>=(d|0)&G[c+8>>2]>=h>>>0|(d|0)<(e|0)){continue}}break}g=(l|0)>=(k|0)}ma(m);return g|0}function ne(a,b){a=a|0;b=b|0;a=0;a:{switch(b|0){case 0:a=na(20);D[a+12>>2]=-1;D[a+16>>2]=0;D[a+4>>2]=0;D[a+8>>2]=0;D[a>>2]=1948;return a|0;case 1:a=na(24);D[a+12>>2]=-1;D[a+16>>2]=0;D[a+4>>2]=0;D[a+8>>2]=0;D[a>>2]=1948;D[a+20>>2]=0;D[a>>2]=2164;return a|0;case 2:a=na(48);D[a+12>>2]=-1;D[a+16>>2]=0;D[a+4>>2]=0;D[a+8>>2]=0;D[a>>2]=1948;D[a+20>>2]=0;D[a>>2]=2164;D[a+32>>2]=0;D[a+36>>2]=0;D[a+28>>2]=-1;D[a+24>>2]=1140;D[a>>2]=7976;D[a+40>>2]=0;D[a+44>>2]=0;return a|0;case 3:a=na(32);D[a+12>>2]=-1;D[a+16>>2]=0;D[a+4>>2]=0;D[a+8>>2]=0;D[a>>2]=1948;D[a+20>>2]=0;D[a>>2]=2164;D[a+28>>2]=-1;D[a+24>>2]=1032;D[a>>2]=5840;break;default:break a}}return a|0}function rh(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0;D[b>>2]=1;f=b+8|0;c=D[b+8>>2];d=D[b+12>>2]-c|0;if(d>>>0<=4294967291){Eb(f,d+4|0);c=D[f>>2]}c=c+d|0;d=D[a+4>>2];B[c|0]=d;B[c+1|0]=d>>>8;B[c+2|0]=d>>>16;B[c+3|0]=d>>>24;c=D[a+8>>2];if((c|0)!=D[a+12>>2]){d=0;while(1){g=(d<<2)+c|0;c=D[b+8>>2];e=D[b+12>>2]-c|0;if(e>>>0<=4294967291){Eb(f,e+4|0);c=D[f>>2]}c=c+e|0;e=D[g>>2];B[c|0]=e;B[c+1|0]=e>>>8;B[c+2|0]=e>>>16;B[c+3|0]=e>>>24;d=d+1|0;c=D[a+8>>2];if(d>>>0<D[a+12>>2]-c>>2>>>0){continue}break}}c=D[b+12>>2];b=D[b+8>>2];c=c-b|0;if(c>>>0<=4294967291){Eb(f,c+4|0);b=D[f>>2]}b=b+c|0;a=D[a+20>>2];B[b|0]=a;B[b+1|0]=a>>>8;B[b+2|0]=a>>>16;B[b+3|0]=a>>>24}function Cf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=$-32|0;$=e;a:{b:{f=za(c);if(f>>>0<4294967280){c:{d:{if(f>>>0>=11){g=f+16&-16;a=na(g);D[e+24>>2]=g|-2147483648;D[e+16>>2]=a;D[e+20>>2]=f;break d}B[e+27|0]=f;a=e+16|0;if(!f){break c}}oa(a,c,f)}B[a+f|0]=0;c=za(d);if(c>>>0>=4294967280){break b}e:{f:{if(c>>>0>=11){f=c+16&-16;a=na(f);D[e+8>>2]=f|-2147483648;D[e>>2]=a;D[e+4>>2]=c;break f}B[e+11|0]=c;a=e;if(!c){break e}}oa(a,d,c)}B[a+c|0]=0;c=D[b+4>>2];a=-1;g:{if(!c){break g}c=Oc(c,e+16|0,e);a=-1;if(!c){break g}a=Kc(b,D[c+24>>2])}if(B[e+11|0]<0){ma(D[e>>2])}if(B[e+27|0]<0){ma(D[e+16>>2])}$=e+32|0;break a}Aa();T()}Aa();T()}return a|0}function Ea(a,b,c){var d=0,e=0,f=0,g=0;e=$-16|0;$=e;D[a+4>>2]=0;a:{b:{if(!b){break b}g=D[a+8>>2];d=g<<5;c:{if(d>>>0>=b>>>0){D[a+4>>2]=b;break c}D[e+8>>2]=0;D[e>>2]=0;D[e+4>>2]=0;if((b|0)<0){break a}if(d>>>0<=1073741822){f=b+31&-32;d=g<<6;d=d>>>0<f>>>0?f:d}else{d=2147483647}Ta(e,d);f=D[a>>2];D[a>>2]=D[e>>2];D[e>>2]=f;d=D[a+4>>2];D[a+4>>2]=b;D[e+4>>2]=d;d=D[a+8>>2];D[a+8>>2]=D[e+8>>2];D[e+8>>2]=d;if(!f){break c}ma(f)}f=b>>>5|0;d=f<<2;a=D[a>>2];if(E[c|0]){a=pa(a,255,d);b=b&31;if(!b){break b}a=a+(f<<2)|0;D[a>>2]=D[a>>2]|-1>>>32-b;break b}a=pa(a,0,d);b=b&31;if(!b){break b}a=a+(f<<2)|0;D[a>>2]=D[a>>2]&(-1>>>32-b^-1)}$=e+16|0;return}qa();T()}function ie(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;h=D[c+12>>2];d=h;e=D[c+20>>2];i=D[c+8>>2];f=D[c+16>>2];a:{if((d|0)<=(e|0)&i>>>0<=f>>>0|(d|0)<(e|0)){break a}j=D[c>>2];k=B[j+f|0];d=e;g=f+1|0;d=g?d:d+1|0;D[c+16>>2]=g;D[c+20>>2]=d;b:{if((k|0)==-2){break b}if((d|0)>=(h|0)&g>>>0>=i>>>0|(d|0)>(h|0)){break a}d=B[g+j|0];f=f+2|0;e=f>>>0<2?e+1|0:e;D[c+16>>2]=f;D[c+20>>2]=e;if((d-4&255)>>>0<251){break a}e=ba[D[D[a>>2]+40>>2]](a,k,d)|0;d=D[a+20>>2];D[a+20>>2]=e;if(!d){break b}ba[D[D[d>>2]+4>>2]](d)}d=D[a+20>>2];if(d){if(!(ba[D[D[a>>2]+28>>2]](a,d)|0)){break a}}l=ba[D[D[a>>2]+36>>2]](a,b,c)|0}return l|0}function Ah(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;f=$-32|0;$=f;h=(e&1073741823)!=(e|0)?-1:e<<2;h=pa(na(h),0,h);g=D[b>>2];i=D[b+4>>2];k=D[h+4>>2];D[f+16>>2]=D[h>>2];D[f+20>>2]=k;D[f+8>>2]=g;D[f+12>>2]=i;i=a+8|0;Ib(f+24|0,i,f+16|0,f+8|0);D[c>>2]=D[f+24>>2];D[c+4>>2]=D[f+28>>2];if((d|0)>(e|0)){k=0-e<<2;a=e;while(1){g=a<<2;j=g+b|0;m=D[j>>2];j=D[j+4>>2];g=c+g|0;l=g+k|0;n=D[l+4>>2];D[f+16>>2]=D[l>>2];D[f+20>>2]=n;D[f+8>>2]=m;D[f+12>>2]=j;Ib(f+24|0,i,f+16|0,f+8|0);D[g>>2]=D[f+24>>2];D[g+4>>2]=D[f+28>>2];a=a+e|0;if((d|0)>(a|0)){continue}break}}ma(h);$=f+32|0;return 1}function fb(a,b,c){var d=0,e=0,f=0,g=0,h=0;f=c-b|0;g=f>>2;d=D[a+8>>2];e=D[a>>2];if(g>>>0<=d-e>>2>>>0){f=D[a+4>>2];d=f-e|0;h=d>>2;d=g>>>0>h>>>0?b+d|0:c;if((d|0)!=(b|0)){while(1){D[e>>2]=D[b>>2];e=e+4|0;b=b+4|0;if((d|0)!=(b|0)){continue}break}}if(g>>>0>h>>>0){b=c-d|0;if((b|0)>0){f=oa(f,d,b)+b|0}D[a+4>>2]=f;return}D[a+4>>2]=e;return}if(e){D[a+4>>2]=e;ma(e);D[a+8>>2]=0;D[a>>2]=0;D[a+4>>2]=0;d=0}a:{if((f|0)<0){break a}c=d>>1;c=d>>2>>>0<536870911?c>>>0<g>>>0?g:c:1073741823;if(c>>>0>=1073741824){break a}e=c<<2;c=na(e);D[a>>2]=c;D[a+4>>2]=c;D[a+8>>2]=c+e;if(f){c=oa(c,b,f)+f|0}D[a+4>>2]=c;return}qa();T()}function Md(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;e=D[b+12>>2];c=D[b+20>>2];f=c;g=D[b+16>>2];d=g+4|0;c=d>>>0<4?c+1|0:c;h=D[b+8>>2];i=d;a:{if(h>>>0<d>>>0&(c|0)>=(e|0)|(c|0)>(e|0)){break a}d=g+D[b>>2]|0;d=E[d|0]|E[d+1|0]<<8|(E[d+2|0]<<16|E[d+3|0]<<24);D[b+16>>2]=i;D[b+20>>2]=c;c=f;f=g+8|0;c=f>>>0<8?c+1|0:c;if(f>>>0>h>>>0&(c|0)>=(e|0)|(c|0)>(e|0)){break a}D[b+16>>2]=f;D[b+20>>2]=c;if(!(d&1)){break a}c=M(d)^31;if(c-30>>>0<4294967267){break a}D[a+8>>2]=c+1;e=-2<<c;c=-2-e|0;D[a+16>>2]=c;D[a+12>>2]=e^-1;D[a+24>>2]=(c|0)/2;H[a+20>>2]=K(2)/K(c|0);j=Ka(a+96|0,b)}return j|0}function rd(a){var b=0,c=0,d=0,e=0;c=1;d=D[a+140>>2];a:{if((d|0)<=0){break a}b=d<<4;c=na((d|0)!=(d&268435455)?-1:b|4);D[c>>2]=d;c=c+4|0;d=c+b|0;b=c;while(1){D[b>>2]=0;D[b+4>>2]=0;B[b+5|0]=0;B[b+6|0]=0;B[b+7|0]=0;B[b+8|0]=0;B[b+9|0]=0;B[b+10|0]=0;B[b+11|0]=0;B[b+12|0]=0;b=b+16|0;if((d|0)!=(b|0)){continue}break}e=D[a+136>>2];D[a+136>>2]=c;if(e){d=e-4|0;c=D[d>>2];if(c){b=(c<<4)+e|0;while(1){b=b-16|0;if((e|0)!=(b|0)){continue}break}}ma(d)}c=1;if(D[a+140>>2]<=0){break a}b=0;while(1){c=Ka(D[a+136>>2]+(b<<4)|0,a);if(!c){break a}b=b+1|0;if((b|0)<D[a+140>>2]){continue}break}}return c}function Mf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;a=$-32|0;$=a;D[a+24>>2]=0;D[a+28>>2]=0;a:{d=za(c);if(d>>>0<4294967280){b:{c:{if(d>>>0>=11){f=d+16&-16;e=na(f);D[a+16>>2]=f|-2147483648;D[a+8>>2]=e;D[a+12>>2]=d;break c}B[a+19|0]=d;e=a+8|0;if(!d){break b}}oa(e,c,d)}B[d+e|0]=0;c=b+4|0;b=_a(b,a+8|0);d:{if((c|0)==(b|0)){break d}c=D[b+32>>2];b=D[b+28>>2];if((c-b|0)!=8){break d}c=E[b+4|0]|E[b+5|0]<<8|(E[b+6|0]<<16|E[b+7|0]<<24);D[a+24>>2]=E[b|0]|E[b+1|0]<<8|(E[b+2|0]<<16|E[b+3|0]<<24);D[a+28>>2]=c}g=I[a+24>>3];if(B[a+19|0]<0){ma(D[a+8>>2])}$=a+32|0;break a}Aa();T()}return+g}function Yb(a,b){var c=0,d=0,e=0,f=0,g=0;a:{if(D[a+64>>2]){break a}c=na(32);D[c+16>>2]=0;D[c+20>>2]=0;D[c+8>>2]=0;D[c>>2]=0;D[c+4>>2]=0;D[c+24>>2]=0;D[c+28>>2]=0;d=D[a+64>>2];D[a+64>>2]=c;if(!d){break a}c=D[d>>2];if(c){D[d+4>>2]=c;ma(c)}ma(d)}e=D[a+64>>2];c=D[a+28>>2]-1|0;if(c>>>0<=10){d=D[(c<<2)+10180>>2]}else{d=-1}c=J(d,B[a+24|0]);d=c;g=c>>31;e=id(e,0,$h(c,g,b,0),aa);if(e){c=D[a+64>>2];D[a>>2]=c;f=D[c+20>>2];D[a+8>>2]=D[c+16>>2];D[a+12>>2]=f;f=D[c+24>>2];c=D[c+28>>2];D[a+48>>2]=0;D[a+52>>2]=0;D[a+40>>2]=d;D[a+44>>2]=g;D[a+16>>2]=f;D[a+20>>2]=c;D[a+80>>2]=b}return e}function Eh(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;f=D[b+12>>2];c=D[b+20>>2];e=c;g=D[b+16>>2];d=g+4|0;c=d>>>0<4?c+1|0:c;h=D[b+8>>2];i=d;a:{if(h>>>0<d>>>0&(c|0)>=(f|0)|(c|0)>(f|0)){break a}d=g+D[b>>2]|0;d=E[d|0]|E[d+1|0]<<8|(E[d+2|0]<<16|E[d+3|0]<<24);D[b+16>>2]=i;D[b+20>>2]=c;c=e;e=g+8|0;c=e>>>0<8?c+1|0:c;if(e>>>0>h>>>0&(c|0)>=(f|0)|(c|0)>(f|0)){break a}D[b+16>>2]=e;D[b+20>>2]=c;if(!(d&1)){break a}b=M(d)^31;if(b-30>>>0<4294967267){break a}j=1;D[a+8>>2]=b+1;c=-2<<b;b=-2-c|0;D[a+16>>2]=b;D[a+12>>2]=c^-1;D[a+24>>2]=(b|0)/2;H[a+20>>2]=K(2)/K(b|0)}return j|0}function Id(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0;f=a+4|0;a:{a=D[a+4>>2];if(a){e=E[c+11|0];d=e<<24>>24<0;i=d?D[c>>2]:c;e=d?D[c+4>>2]:e;while(1){c=E[a+27|0];d=c<<24>>24<0;c=d?D[a+20>>2]:c;j=c>>>0<e>>>0;b:{c:{d:{e:{f:{h=j?c:e;g:{if(h){g=a+16|0;d=d?D[g>>2]:g;g=va(i,d,h);if(!g){if(c>>>0>e>>>0){break g}break f}if((g|0)>=0){break f}break g}if(c>>>0<=e>>>0){break e}}c=D[a>>2];if(c){break b}D[b>>2]=a;return a}c=va(d,i,h);if(c){break d}}if(j){break c}break a}if((c|0)>=0){break a}}f=a+4|0;c=D[a+4>>2];if(!c){break a}a=f}f=a;a=c;continue}}D[b>>2]=f;return f}D[b>>2]=a;return f}function Zb(a,b){var c=0;c=D[b+4>>2];D[a>>2]=D[b>>2];D[a+4>>2]=c;c=D[b+60>>2];D[a+56>>2]=D[b+56>>2];D[a+60>>2]=c;c=D[b+52>>2];D[a+48>>2]=D[b+48>>2];D[a+52>>2]=c;c=D[b+44>>2];D[a+40>>2]=D[b+40>>2];D[a+44>>2]=c;c=D[b+36>>2];D[a+32>>2]=D[b+32>>2];D[a+36>>2]=c;c=D[b+28>>2];D[a+24>>2]=D[b+24>>2];D[a+28>>2]=c;c=D[b+20>>2];D[a+16>>2]=D[b+16>>2];D[a+20>>2]=c;c=D[b+12>>2];D[a+8>>2]=D[b+8>>2];D[a+12>>2]=c;D[a+88>>2]=0;D[a+64>>2]=0;D[a+68>>2]=0;D[a+72>>2]=0;D[a+76>>2]=0;B[a+77|0]=0;B[a+78|0]=0;B[a+79|0]=0;B[a+80|0]=0;B[a+81|0]=0;B[a+82|0]=0;B[a+83|0]=0;B[a+84|0]=0;return a}function _a(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;g=a+4|0;a=D[a+4>>2];a:{b:{if(!a){break b}d=E[b+11|0];c=d<<24>>24<0;i=c?D[b>>2]:b;e=c?D[b+4>>2]:d;c=g;while(1){b=E[a+27|0];j=b<<24>>24<0;h=j?D[a+20>>2]:b;f=h>>>0>e>>>0;d=f?e:h;c:{if(d){b=a+16|0;b=va(j?D[b>>2]:b,i,d);if(b){break c}}b=e>>>0>h>>>0?-1:f}c=(b|0)<0?c:a;a=D[(b>>>29&4)+a>>2];if(a){continue}break}if((c|0)==(g|0)){break b}a=E[c+27|0];f=a<<24>>24<0;d:{d=f?D[c+20>>2]:a;b=d>>>0<e>>>0?d:e;if(b){a=c+16|0;a=va(i,f?D[a>>2]:a,b);if(a){break d}}if(d>>>0>e>>>0){break b}break a}if((a|0)>=0){break a}}c=g}return c}function Ee(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(Ja(a,D[b+8>>2],e)){if(!(D[b+28>>2]==1|D[b+4>>2]!=(c|0))){D[b+28>>2]=d}return}a:{if(Ja(a,D[b>>2],e)){if(!(D[b+16>>2]!=(c|0)&D[b+20>>2]!=(c|0))){if((d|0)!=1){break a}D[b+32>>2]=1;return}D[b+32>>2]=d;b:{if(D[b+44>>2]==4){break b}C[b+52>>1]=0;a=D[a+8>>2];ba[D[D[a>>2]+20>>2]](a,b,c,c,1,e);if(E[b+53|0]){D[b+44>>2]=3;if(!E[b+52|0]){break b}break a}D[b+44>>2]=4}D[b+20>>2]=c;D[b+40>>2]=D[b+40>>2]+1;if(D[b+36>>2]!=1|D[b+24>>2]!=2){break a}B[b+54|0]=1;return}a=D[a+8>>2];ba[D[D[a>>2]+24>>2]](a,b,c,d,e)}}function Bg(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0;f=na(64);c=na(12);D[c+8>>2]=D[D[a+4>>2]+80>>2];D[c>>2]=1e4;D[c+4>>2]=0;f=wc(f,c);a:{b:{if((b|0)<0){c=f;break b}h=a+8|0;c=D[a+12>>2];e=D[a+8>>2];g=c-e>>2;c:{if((g|0)>(b|0)){break c}d=b+1|0;if(b>>>0>=g>>>0){Ob(h,d-g|0);break c}if(d>>>0>=g>>>0){break c}e=e+(d<<2)|0;if((e|0)!=(c|0)){while(1){c=c-4|0;d=D[c>>2];D[c>>2]=0;if(d){ba[D[D[d>>2]+4>>2]](d)}if((c|0)!=(e|0)){continue}break}}D[a+12>>2]=e}a=D[h>>2]+(b<<2)|0;c=D[a>>2];D[a>>2]=f;if(!c){break a}}ba[D[D[c>>2]+4>>2]](c)}return(b^-1)>>>31|0}function Vc(a,b,c){var d=0,e=0,f=0,g=0;a:{if(!((b|0)<0|(c|0)<0)){b:{if(b>>>0>1431655765){break b}d=J(b,3);Ub(a,d,10256);Ub(a+12|0,d,10260);d=D[a+24>>2];c:{if(D[a+32>>2]-d>>2>>>0>=c>>>0){break c}if(c>>>0>=1073741824){break a}e=D[a+28>>2];f=c<<2;c=na(f);f=c+f|0;e=e-d|0;g=e+c|0;if((e|0)>0){oa(c,d,e)}D[a+32>>2]=f;D[a+28>>2]=g;D[a+24>>2]=c;if(!d){break c}ma(d)}D[a+80>>2]=0;D[a+84>>2]=0;c=D[a+76>>2];D[a+76>>2]=0;if(c){ma(c)}D[a+68>>2]=0;D[a+72>>2]=0;c=a- -64|0;a=D[c>>2];D[c>>2]=0;if(!a){break b}ma(a)}d=b>>>0<1431655766}return d}ra(1326);T()}function cb(a){var b=0,c=0,d=0,e=0,f=0;d=D[a+8>>2];a:{if(E[d+84|0]){break a}b=D[a+16>>2];if(!b|!E[b+84|0]){break a}c=D[d+72>>2];e=D[d+68>>2];B[b+84|0]=0;c=c-e>>2;f=D[b+68>>2];e=D[b+72>>2]-f>>2;b:{if(c>>>0>e>>>0){xa(b+68|0,c-e|0,2032);d=D[a+8>>2];break b}if(c>>>0>=e>>>0){break b}D[b+72>>2]=f+(c<<2)}b=E[d+84|0];if(b){break a}c=D[d+68>>2];if((c|0)==D[d+72>>2]){break a}e=D[D[a+16>>2]+68>>2];if(!b){b=0;while(1){f=b<<2;D[f+e>>2]=D[c+f>>2];b=b+1|0;c=D[d+68>>2];if(b>>>0<D[d+72>>2]-c>>2>>>0){continue}break}break a}D[e>>2]=0}return D[a+16>>2]}function nb(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;c=D[a+4>>2];if((c|0)!=D[a+8>>2]){d=D[b+4>>2];D[c>>2]=D[b>>2];D[c+4>>2]=d;D[c+8>>2]=D[b+8>>2];D[a+4>>2]=c+12;return}a:{f=D[a>>2];g=c-f|0;d=(g|0)/12|0;c=d+1|0;if(c>>>0<357913942){e=d<<1;e=d>>>0<178956970?c>>>0>e>>>0?c:e:357913941;if(e){if(e>>>0>=357913942){break a}c=na(J(e,12))}else{c=0}d=c+J(d,12)|0;h=D[b+4>>2];D[d>>2]=D[b>>2];D[d+4>>2]=h;D[d+8>>2]=D[b+8>>2];b=d+J((g|0)/-12|0,12)|0;if((g|0)>0){oa(b,f,g)}D[a+8>>2]=c+J(e,12);D[a+4>>2]=d+12;D[a>>2]=b;if(f){ma(f)}return}qa();T()}ra(1326);T()}function Dd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;a:{c=D[a+216>>2];if((c|0)==D[a+220>>2]){break a}while(1){b:{c=D[J(e,144)+c>>2];if((c|0)<0){break b}d=D[a+4>>2];f=D[d+8>>2];if((c|0)>=D[d+12>>2]-f>>2){break b}d=0;c=D[(c<<2)+f>>2];if((ba[D[D[c>>2]+24>>2]](c)|0)<=0){break b}while(1){if((ba[D[D[c>>2]+20>>2]](c,d)|0)!=(b|0)){d=d+1|0;if((ba[D[D[c>>2]+24>>2]](c)|0)>(d|0)){continue}break b}break}a=D[a+216>>2]+J(e,144)|0;e=E[a+100|0]?a+4|0:0;break a}e=e+1|0;c=D[a+216>>2];if(e>>>0<(D[a+220>>2]-c|0)/144>>>0){continue}break}return 0}return e|0}function se(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0;c=D[a+60>>2];a:{if(!c){break a}D[c+4>>2]=a+48;if(!(ba[D[D[c>>2]+12>>2]](c)|0)){break a}b:{c=ba[D[D[a>>2]+24>>2]](a)|0;if((c|0)<=0){break b}while(1){c:{f=D[(ba[D[D[a>>2]+28>>2]](a)|0)+4>>2];g=ba[D[D[a>>2]+20>>2]](a,d)|0;e=D[a+60>>2];if(!(ba[D[D[e>>2]+8>>2]](e,D[D[f+8>>2]+(g<<2)>>2])|0)){break c}d=d+1|0;if((c|0)!=(d|0)){continue}break b}break}return 0}d=0;if(!(ba[D[D[a>>2]+36>>2]](a,b)|0)){break a}if(!(ba[D[D[a>>2]+40>>2]](a,b)|0)){break a}d=ba[D[D[a>>2]+44>>2]](a)|0}return d|0}function Fc(a,b,c,d,e,f,g){var h=0,i=0,j=0;h=$-16|0;$=h;if((b^-1)-17>>>0>=c>>>0){if(E[a+11|0]>>>7|0){j=D[a>>2]}else{j=a}a:{if(b>>>0<2147483623){D[h+8>>2]=b<<1;D[h+12>>2]=b+c;c=$-16|0;$=c;$=c+16|0;c=h+8|0;i=h+12|0;c=D[(G[i>>2]<G[c>>2]?c:i)>>2];if(c>>>0>=11){i=c+16&-16;c=i-1|0;c=(c|0)==11?i:c}else{c=10}break a}c=-18}i=c+1|0;c=na(i);if(f){Xa(c,g,f)}d=d-e|0;if(d){Xa(c+f|0,e+j|0,d)}if((b|0)!=10){ma(j)}D[a>>2]=c;D[a+8>>2]=i|-2147483648;b=a;a=d+f|0;D[b+4>>2]=a;B[h+7|0]=0;B[a+c|0]=E[h+7|0];$=h+16|0;return}Aa();T()}function Cd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;c=D[a+216>>2];if((c|0)!=D[a+220>>2]){while(1){a:{c=D[J(e,144)+c>>2];if((c|0)<0){break a}d=D[a+4>>2];f=D[d+8>>2];if((c|0)>=D[d+12>>2]-f>>2){break a}d=0;c=D[(c<<2)+f>>2];if((ba[D[D[c>>2]+24>>2]](c)|0)<=0){break a}while(1){if((ba[D[D[c>>2]+20>>2]](c,d)|0)!=(b|0)){d=d+1|0;if((ba[D[D[c>>2]+24>>2]](c)|0)>(d|0)){continue}break a}break}return(D[a+216>>2]+J(e,144)|0)+104|0}e=e+1|0;c=D[a+216>>2];if(e>>>0<(D[a+220>>2]-c|0)/144>>>0){continue}break}}return a+184|0}function Ta(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;c=$-16|0;$=c;a:{b:{if(D[a+8>>2]<<5>>>0>=b>>>0){break b}D[c+8>>2]=0;D[c>>2]=0;D[c+4>>2]=0;if((b|0)<0){break a}g=(b-1>>>5|0)+1|0;e=na(g<<2);D[c+8>>2]=g;D[c>>2]=e;f=D[a>>2];b=D[a+4>>2];D[c+4>>2]=b;D[((b>>>0<33?0:b-1>>>5|0)<<2)+e>>2]=0;c:{if((b|0)<=0){break c}h=b>>>5|0;d=h<<2;i=Na(e,f,d);b=b-(h<<5)|0;if((b|0)<=0){break c}d=d+i|0;b=-1>>>32-b|0;D[d>>2]=D[d>>2]&(b^-1)|b&D[(h<<2)+f>>2]}D[a+8>>2]=g;D[a>>2]=e;if(!f){break b}ma(f)}$=c+16|0;return}qa();T()}function xe(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0;if((ba[D[D[b>>2]+20>>2]](b)|0)<=0){return 1}a:{while(1){d=Lc(D[D[a+4>>2]+4>>2],ba[D[D[b>>2]+24>>2]](b,e)|0);if((d|0)==-1){break a}f=D[a+4>>2];c=0;b:{if((d|0)<0){break b}g=D[f+4>>2];if((d|0)>=D[g+12>>2]-D[g+8>>2]>>2){break b}c=D[D[f+8>>2]+(D[D[f+20>>2]+(d<<2)>>2]<<2)>>2];c=ba[D[D[c>>2]+32>>2]](c,d)|0}if(!c){break a}if(!(ba[D[D[b>>2]+28>>2]](b,c)|0)){break a}e=e+1|0;if((ba[D[D[b>>2]+20>>2]](b)|0)>(e|0)){continue}break}return 1}return 0}function Df(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0;a=$-32|0;$=a;a:{d=za(c);if(d>>>0<4294967280){b:{c:{if(d>>>0>=11){f=d+16&-16;e=na(f);D[a+24>>2]=f|-2147483648;D[a+16>>2]=e;D[a+20>>2]=d;break c}B[a+27|0]=d;e=a+16|0;if(!d){break b}}oa(e,c,d)}B[d+e|0]=0;B[a+4|0]=0;D[a>>2]=1701667182;B[a+11|0]=4;d=D[b+4>>2];c=-1;d:{if(!d){break d}d=Oc(d,a,a+16|0);c=-1;if(!d){break d}c=Kc(b,D[d+24>>2])}b=c;if(B[a+11|0]<0){ma(D[a>>2])}if(B[a+27|0]<0){ma(D[a+16>>2])}$=a+32|0;break a}Aa();T()}return b|0}function Of(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0;d=$-16|0;$=d;D[d+12>>2]=0;a:{e=za(c);if(e>>>0<4294967280){b:{c:{if(e>>>0>=11){f=e+16&-16;a=na(f);D[d+8>>2]=f|-2147483648;D[d>>2]=a;D[d+4>>2]=e;break c}B[d+11|0]=e;a=d;if(!e){break b}}oa(a,c,e)}B[a+e|0]=0;a=_a(b,d);d:{if((a|0)==(b+4|0)){break d}b=D[a+32>>2];a=D[a+28>>2];if((b-a|0)!=4){break d}D[d+12>>2]=E[a|0]|E[a+1|0]<<8|(E[a+2|0]<<16|E[a+3|0]<<24)}a=D[d+12>>2];if(B[d+11|0]<0){ma(D[d>>2])}$=d+16|0;break a}Aa();T()}return a|0}function ub(a){a=a|0;var b=0,c=0,d=0;D[a>>2]=10332;b=D[a+68>>2];if(b){D[a+72>>2]=b;ma(b)}b=D[a+56>>2];if(b){D[a+60>>2]=b;ma(b)}b=D[a+44>>2];if(b){D[a+48>>2]=b;ma(b)}b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}b=D[a+20>>2];if(b){D[a+24>>2]=b;ma(b)}d=D[a+8>>2];if(d){c=D[a+12>>2];if((c|0)==(d|0)){b=d}else{while(1){c=c-4|0;b=D[c>>2];D[c>>2]=0;if(b){ya(b)}if((d|0)!=(c|0)){continue}break}b=D[a+8>>2]}D[a+12>>2]=d;ma(b)}b=D[a+4>>2];D[a+4>>2]=0;if(b){dc(b)}return a|0}function sa(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;e=D[a+8>>2];c=D[a+4>>2];if(e-c>>2>>>0>=b>>>0){if(b){b=b<<2;c=pa(c,0,b)+b|0}D[a+4>>2]=c;return}a:{f=D[a>>2];g=c-f|0;h=g>>2;d=h+b|0;if(d>>>0<1073741824){c=0;e=e-f|0;i=e>>1;d=e>>2>>>0<536870911?d>>>0>i>>>0?d:i:1073741823;if(d){if(d>>>0>=1073741824){break a}c=na(d<<2)}b=b<<2;b=pa((h<<2)+c|0,0,b)+b|0;if((g|0)>0){oa(c,f,g)}D[a+8>>2]=(d<<2)+c;D[a+4>>2]=b;D[a>>2]=c;if(f){ma(f)}return}qa();T()}ra(1326);T()}function bb(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;e=D[a+8>>2];c=D[a+4>>2];if(e-c>>3>>>0>=b>>>0){if(b){b=b<<3;c=pa(c,0,b)+b|0}D[a+4>>2]=c;return}a:{f=D[a>>2];g=c-f|0;h=g>>3;d=h+b|0;if(d>>>0<536870912){c=0;e=e-f|0;i=e>>2;d=e>>3>>>0<268435455?d>>>0>i>>>0?d:i:536870911;if(d){if(d>>>0>=536870912){break a}c=na(d<<3)}b=b<<3;b=pa((h<<3)+c|0,0,b)+b|0;if((g|0)>0){oa(c,f,g)}D[a+8>>2]=(d<<3)+c;D[a+4>>2]=b;D[a>>2]=c;if(f){ma(f)}return}qa();T()}ra(1326);T()}function Qa(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;e=D[a>>2];a=D[e+4>>2];c=D[e+8>>2];if(a>>>0<c>>>0){D[a>>2]=D[b>>2];D[e+4>>2]=a+4;return}a:{f=D[e>>2];g=a-f|0;d=g>>2;a=d+1|0;if(a>>>0<1073741824){h=d<<2;c=c-f|0;d=c>>1;c=c>>2>>>0<536870911?a>>>0>d>>>0?a:d:1073741823;if(c){if(c>>>0>=1073741824){break a}a=na(c<<2)}else{a=0}d=h+a|0;D[d>>2]=D[b>>2];if((g|0)>0){oa(a,f,g)}D[e+8>>2]=a+(c<<2);D[e+4>>2]=d+4;D[e>>2]=a;if(f){ma(f)}return}qa();T()}ra(1326);T()}function gb(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;a:{c=D[a+4>>2];e=D[a>>2];f=c-e|0;b:{if(f>>>0<b>>>0){g=b-f|0;d=D[a+8>>2];if(g>>>0<=d-c>>>0){if(g){c=pa(c,0,g)+g|0}D[a+4>>2]=c;return}if((b|0)<0){break a}c=0;d=d-e|0;h=d<<1;d=d>>>0<1073741823?b>>>0>h>>>0?b:h:2147483647;if(d){c=na(d)}pa(c+f|0,0,g);if((f|0)>0){oa(c,e,f)}D[a+8>>2]=c+d;D[a+4>>2]=b+c;D[a>>2]=c;if(!e){break b}ma(e);return}if(b>>>0>=f>>>0){break b}D[a+4>>2]=b+e}return}qa();T()}function me(a){a=a|0;var b=0,c=0,d=0;D[a>>2]=2044;b=D[a+60>>2];D[a+60>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}b=D[a+48>>2];if(b){D[a+52>>2]=b;ma(b)}d=D[a+36>>2];if(d){c=D[a+40>>2];if((c|0)==(d|0)){b=d}else{while(1){c=c-4|0;b=D[c>>2];D[c>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}if((d|0)!=(c|0)){continue}break}b=D[a+36>>2]}D[a+40>>2]=d;ma(b)}D[a>>2]=1804;b=D[a+16>>2];if(b){D[a+20>>2]=b;ma(b)}b=D[a+4>>2];if(b){D[a+8>>2]=b;ma(b)}return a|0}function le(a){a=a|0;var b=0,c=0,d=0;D[a>>2]=2044;b=D[a+60>>2];D[a+60>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}b=D[a+48>>2];if(b){D[a+52>>2]=b;ma(b)}d=D[a+36>>2];if(d){c=D[a+40>>2];if((c|0)==(d|0)){b=d}else{while(1){c=c-4|0;b=D[c>>2];D[c>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}if((d|0)!=(c|0)){continue}break}b=D[a+36>>2]}D[a+40>>2]=d;ma(b)}D[a>>2]=1804;b=D[a+16>>2];if(b){D[a+20>>2]=b;ma(b)}b=D[a+4>>2];if(b){D[a+8>>2]=b;ma(b)}ma(a)}function gd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;e=D[a+8>>2];c=D[a+4>>2];if(e-c>>1>>>0>=b>>>0){if(b){b=b<<1;c=pa(c,0,b)+b|0}D[a+4>>2]=c;return}a:{f=D[a>>2];g=c-f|0;h=g>>1;d=h+b|0;if((d|0)>=0){c=0;e=e-f|0;d=e>>1>>>0<1073741823?d>>>0>e>>>0?d:e:2147483647;if(d){if((d|0)<0){break a}c=na(d<<1)}b=b<<1;b=pa((h<<1)+c|0,0,b)+b|0;if((g|0)>0){oa(c,f,g)}D[a+8>>2]=(d<<1)+c;D[a+4>>2]=b;D[a>>2]=c;if(f){ma(f)}return}qa();T()}ra(1326);T()}function dh(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;d=$-16|0;$=d;e=D[a+4>>2];a:{if((e|0)==-1){break a}c=D[b+20>>2];if(!!D[b+16>>2]&(c|0)>=0|(c|0)>0){break a}rb(b,D[b+4>>2],D[a+8>>2],D[a+12>>2]);c=D[b+20>>2];if(!!D[b+16>>2]&(c|0)>=0|(c|0)>0){break a}c=a+20|0;rb(b,D[b+4>>2],c,c+4|0);c=D[b+20>>2];f=D[b+16>>2];B[d+15|0]=D[a+4>>2];if(!!f&(c|0)>=0|(c|0)>0){break a}rb(b,D[b+4>>2],d+15|0,d+16|0)}$=d+16|0;return(e|0)!=-1|0}
function yg(a){a=a|0;var b=0,c=0,d=0,e=0,f=0;a:{b=D[a+8>>2];b:{if((b|0)<0){break b}c=D[a+4>>2];e=D[c>>2];d=D[c+4>>2]-e>>2;c:{if(d>>>0<b>>>0){sa(c,b-d|0);f=D[a+8>>2];break c}f=b;if(b>>>0>=d>>>0){break c}D[c+4>>2]=e+(b<<2);f=b}d=f;if((d|0)<=0){break b}a=D[a+4>>2];c=D[a>>2];e=D[a+4>>2]-c>>2;a=0;while(1){if((a|0)==(e|0)){break a}D[c+(a<<2)>>2]=a;a=a+1|0;if((d|0)!=(a|0)){continue}break}}return(b^-1)>>>31|0}ua();T()}function Ra(a){var b=0,c=0,d=0,e=0,f=0;b=D[a+4>>2];if((b|0)!=D[a+8>>2]){D[b>>2]=D[2088];D[a+4>>2]=b+4;return}a:{e=D[a>>2];f=b-e|0;d=f>>2;b=d+1|0;if(b>>>0<1073741824){c=f>>1;c=d>>>0<536870911?b>>>0>c>>>0?b:c:1073741823;if(c){if(c>>>0>=1073741824){break a}b=na(c<<2)}else{b=0}d=b+(d<<2)|0;D[d>>2]=D[2088];if((f|0)>0){oa(b,e,f)}D[a+8>>2]=b+(c<<2);D[a+4>>2]=d+4;D[a>>2]=b;if(e){ma(e)}return}qa();T()}ra(1326);T()}function gf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;d=$-16|0;$=d;Kd(d,a,b,c);D[a+24>>2]=D[d>>2];e=a+24|0;a:{if((e|0)==(d|0)){break a}b=d|4;f=E[d+15|0];c=f<<24>>24;a=a+28|0;if(B[a+11|0]>=0){if((c|0)>=0){c=D[b+4>>2];D[a>>2]=D[b>>2];D[a+4>>2]=c;D[a+8>>2]=D[b+8>>2];break a}sb(a,D[d+4>>2],D[d+8>>2]);break a}g=a;a=(c|0)<0;tb(g,a?D[d+4>>2]:b,a?D[d+8>>2]:f)}if(B[d+15|0]<0){ma(D[d+4>>2])}$=d+16|0;return e|0}function tf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;a=$-32|0;$=a;e=B[b+24|0];f=D[2563];D[a+24>>2]=D[2562];D[a+28>>2]=f;f=D[2561];D[a+16>>2]=D[2560];D[a+20>>2]=f;a:{b:{c=mb(b,c,e,a+16|0);if(c){D[a+8>>2]=0;D[a>>2]=0;D[a+4>>2]=0;b=0;if(e){if((e|0)<0){break b}e=e<<2;b=na(e);g=oa(b,a+16|0,e)+e|0}e=D[d>>2];if(e){D[d+4>>2]=e;ma(e)}D[d+8>>2]=g;D[d+4>>2]=g;D[d>>2]=b}$=a+32|0;break a}qa();T()}return c|0}function hf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;b=$-16|0;$=b;Jd(b);D[a+24>>2]=D[b>>2];e=a+24|0;a:{if((e|0)==(b|0)){break a}c=b|4;f=E[b+15|0];d=f<<24>>24;a=a+28|0;if(B[a+11|0]>=0){if((d|0)>=0){d=D[c+4>>2];D[a>>2]=D[c>>2];D[a+4>>2]=d;D[a+8>>2]=D[c+8>>2];break a}sb(a,D[b+4>>2],D[b+8>>2]);break a}g=a;a=(d|0)<0;tb(g,a?D[b+4>>2]:c,a?D[b+8>>2]:f)}if(B[b+15|0]<0){ma(D[b+4>>2])}$=b+16|0;return e|0}function Gb(a,b){var c=0,d=0,e=0,f=0;d=D[a+12>>2];c=D[a+16>>2]-d>>2;a:{if(c>>>0<b>>>0){sa(a+12|0,b-c|0);break a}if(b>>>0>=c>>>0){break a}D[a+16>>2]=d+(b<<2)}b:{c=D[a>>2];c:{if(D[a+8>>2]-c>>2>>>0>=b>>>0){break c}if(b>>>0>=1073741824){break b}d=D[a+4>>2];e=b<<2;b=na(e);e=b+e|0;d=d-c|0;f=d+b|0;if((d|0)>0){oa(b,c,d)}D[a+8>>2]=e;D[a+4>>2]=f;D[a>>2]=b;if(!c){break c}ma(c)}return}ra(1326);T()}function Lf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;d=$-16|0;$=d;a:{e=za(c);if(e>>>0<4294967280){b:{c:{if(e>>>0>=11){g=e+16&-16;f=na(g);D[d+8>>2]=g|-2147483648;D[d>>2]=f;D[d+4>>2]=e;break c}B[d+11|0]=e;f=d;if(!e){break b}}oa(f,c,e)}B[e+f|0]=0;f=a+16|0;c=Nc(b,d,f);b=B[a+27|0];a=D[a+16>>2];if(B[d+11|0]<0){ma(D[d>>2])}$=d+16|0;a=c?(b|0)<0?a:f:0;break a}Aa();T()}return a|0}function mg(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0;d=$+-64|0;$=d;e=ba[D[D[a>>2]+44>>2]](a,b)|0;a=ba[D[D[a>>2]+40>>2]](a,b)|0;f=lb(d);g=D[b+56>>2];h=e<<24>>24;i=a;a=a-1|0;if(a>>>0<=10){a=D[(a<<2)+10180>>2]}else{a=-1}a=J(a,e);$b(f,g,h,i,0,a,a>>31);a=Zb(na(96),f);Yb(a,c);B[a+84|0]=1;D[a+72>>2]=D[a+68>>2];D[a+60>>2]=D[b+60>>2];$=d- -64|0;return a|0}function hd(a,b,c){var d=0,e=0,f=0,g=0;a:{if(a>>>0>10){break a}d=D[c+20>>2];e=D[c+12>>2];f=D[c+16>>2];if((d|0)>=(e|0)&f>>>0>=G[c+8>>2]|(d|0)>(e|0)){break a}e=B[f+D[c>>2]|0];f=f+1|0;d=f?d:d+1|0;D[c+16>>2]=f;D[c+20>>2]=d;b:{if((e|0)<0){if(!hd(a+1|0,b,c)){break a}c=D[b>>2];a=D[b+4>>2]<<7|c>>>25;c=e&127|c<<7;break b}a=0;c=e&255}D[b>>2]=c;D[b+4>>2]=a;g=1}return g}function Oa(a,b,c){var d=0,e=0,f=0,g=0;a:{if(a>>>0>10){break a}d=D[c+20>>2];e=D[c+12>>2];f=D[c+16>>2];if((d|0)>=(e|0)&f>>>0>=G[c+8>>2]|(d|0)>(e|0)){break a}e=B[f+D[c>>2]|0];f=f+1|0;d=f?d:d+1|0;D[c+16>>2]=f;D[c+20>>2]=d;b:{if((e|0)<0){if(!Oa(a+1|0,b,c)){break a}c=D[b>>2];a=D[b+4>>2]<<7|c>>>25;c=e&127|c<<7;break b}a=0;c=e&255}D[b>>2]=c;D[b+4>>2]=a;g=1}return g}function De(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(Ja(a,D[b+8>>2],e)){if(!(D[b+28>>2]==1|D[b+4>>2]!=(c|0))){D[b+28>>2]=d}return}a:{if(!Ja(a,D[b>>2],e)){break a}if(!(D[b+16>>2]!=(c|0)&D[b+20>>2]!=(c|0))){if((d|0)!=1){break a}D[b+32>>2]=1;return}D[b+20>>2]=c;D[b+32>>2]=d;D[b+40>>2]=D[b+40>>2]+1;if(!(D[b+36>>2]!=1|D[b+24>>2]!=2)){B[b+54|0]=1}D[b+44>>2]=4}}function vf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;a:{if(G[b+80>>2]>65535){break a}f=D[b+96>>2];b=D[b+100>>2]-f|0;a=(b|0)/12|0;e=J(a,6);g=(e|0)==(c|0);if(!b|(c|0)!=(e|0)){break a}g=1;e=a>>>0>1?a:1;a=0;while(1){b=J(a,6)+d|0;c=J(a,12)+f|0;C[b>>1]=D[c>>2];C[b+2>>1]=D[c+4>>2];C[b+4>>1]=D[c+8>>2];a=a+1|0;if((e|0)!=(a|0)){continue}break}}return g|0}function kh(a){a=a|0;var b=0,c=0,d=0;D[a>>2]=8204;b=D[a+48>>2];D[a+48>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}D[a>>2]=10064;b=D[a+20>>2];if(b){D[a+24>>2]=b;ma(b)}d=D[a+8>>2];if(d){c=D[a+12>>2];if((c|0)==(d|0)){b=d}else{while(1){c=c-4|0;b=D[c>>2];D[c>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}if((d|0)!=(c|0)){continue}break}b=D[a+8>>2]}D[a+12>>2]=d;ma(b)}return a|0}function jh(a){a=a|0;var b=0,c=0,d=0;D[a>>2]=8204;b=D[a+48>>2];D[a+48>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}D[a>>2]=10064;b=D[a+20>>2];if(b){D[a+24>>2]=b;ma(b)}d=D[a+8>>2];if(d){c=D[a+12>>2];if((c|0)==(d|0)){b=d}else{while(1){c=c-4|0;b=D[c>>2];D[c>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}if((d|0)!=(c|0)){continue}break}b=D[a+8>>2]}D[a+12>>2]=d;ma(b)}ma(a)}function Bc(a,b,c,d){B[a+53|0]=1;a:{if(D[a+4>>2]!=(c|0)){break a}B[a+52|0]=1;c=D[a+16>>2];b:{if(!c){D[a+36>>2]=1;D[a+24>>2]=d;D[a+16>>2]=b;if(D[a+48>>2]!=1){break a}if((d|0)==1){break b}break a}if((b|0)==(c|0)){c=D[a+24>>2];if((c|0)==2){D[a+24>>2]=d;c=d}if(D[a+48>>2]!=1){break a}if((c|0)==1){break b}break a}D[a+36>>2]=D[a+36>>2]+1}B[a+54|0]=1}}function re(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0;e=ba[D[D[a>>2]+24>>2]](a)|0;c=1;a:{if((e|0)<=0){break a}d=D[D[a+36>>2]>>2];f=a+48|0;c=0;if(!(ba[D[D[d>>2]+16>>2]](d,f,b)|0)){break a}d=1;while(1){c=d;if((e|0)!=(c|0)){d=c+1|0;g=D[D[a+36>>2]+(c<<2)>>2];if(ba[D[D[g>>2]+16>>2]](g,f,b)|0){continue}}break}c=(c|0)>=(e|0)}return c|0}function pe(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0;e=ba[D[D[a>>2]+24>>2]](a)|0;c=1;a:{if((e|0)<=0){break a}d=D[D[a+36>>2]>>2];f=a+48|0;c=0;if(!(ba[D[D[d>>2]+20>>2]](d,f,b)|0)){break a}d=1;while(1){c=d;if((e|0)!=(c|0)){d=c+1|0;g=D[D[a+36>>2]+(c<<2)>>2];if(ba[D[D[g>>2]+20>>2]](g,f,b)|0){continue}}break}c=(c|0)>=(e|0)}return c|0}function Ad(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=$-16|0;$=d;D[a+4>>2]=b;b=D[b+64>>2];e=D[b>>2];b=D[b+4>>2];B[d+15|0]=0;Ea(a+24|0,(b-e>>2>>>0)/3|0,d+15|0);b=D[a+4>>2];e=D[b+56>>2];b=D[b+52>>2];B[d+14|0]=0;Ea(a+36|0,e-b>>2,d+14|0);b=D[c+12>>2];D[a+16>>2]=D[c+8>>2];D[a+20>>2]=b;b=D[c+4>>2];D[a+8>>2]=D[c>>2];D[a+12>>2]=b;$=d+16|0}function Uc(a){D[a>>2]=0;D[a+4>>2]=0;D[a+28>>2]=0;D[a+32>>2]=0;B[a+24|0]=1;D[a+16>>2]=0;D[a+20>>2]=0;D[a+8>>2]=0;D[a+12>>2]=0;D[a+36>>2]=0;D[a+40>>2]=0;D[a+44>>2]=0;D[a+48>>2]=0;D[a+52>>2]=0;D[a+56>>2]=0;D[a+60>>2]=0;D[a+64>>2]=0;D[a+72>>2]=0;D[a+76>>2]=0;D[a+80>>2]=0;D[a+84>>2]=0;D[a+88>>2]=0;D[a+92>>2]=0;D[a+68>>2]=a}function Xg(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=$-16|0;$=d;D[a+4>>2]=b;e=D[b>>2];b=D[b+4>>2];B[d+15|0]=0;Ea(a+24|0,(b-e>>2>>>0)/3|0,d+15|0);b=D[a+4>>2];e=D[b+28>>2];b=D[b+24>>2];B[d+14|0]=0;Ea(a+36|0,e-b>>2,d+14|0);b=D[c+12>>2];D[a+16>>2]=D[c+8>>2];D[a+20>>2]=b;b=D[c+4>>2];D[a+8>>2]=D[c>>2];D[a+12>>2]=b;$=d+16|0}function ud(a,b){var c=0,d=0,e=0,f=0,g=0;D[a+144>>2]=b;c=D[(ba[D[D[b>>2]+32>>2]](b)|0)+32>>2];e=D[c>>2]+D[c+16>>2]|0;d=D[(ba[D[D[b>>2]+32>>2]](b)|0)+32>>2];c=D[d+8>>2];d=D[d+16>>2];c=c-d|0;f=a,g=F[D[(ba[D[D[b>>2]+32>>2]](b)|0)+32>>2]+38>>1],C[f+38>>1]=g;D[a>>2]=e;D[a+16>>2]=0;D[a+20>>2]=0;D[a+8>>2]=c;D[a+12>>2]=0}function Wc(a){var b=0;D[a>>2]=0;D[a+4>>2]=0;D[a+56>>2]=0;D[a+48>>2]=0;D[a+52>>2]=0;D[a+40>>2]=0;D[a+44>>2]=0;D[a+32>>2]=0;D[a+36>>2]=0;D[a+24>>2]=0;D[a+28>>2]=0;D[a+16>>2]=0;D[a+20>>2]=0;D[a+8>>2]=0;D[a+12>>2]=0;b=a- -64|0;D[b>>2]=0;D[b+4>>2]=0;D[a+72>>2]=0;D[a+76>>2]=0;D[a+80>>2]=0;D[a+84>>2]=0;D[a+60>>2]=a}function jb(a,b,c){var d=0,e=0,f=0,g=0,h=0;a:{if(a>>>0>5){break a}f=D[c+20>>2];d=f;g=D[c+12>>2];e=D[c+16>>2];if((d|0)>=(g|0)&e>>>0>=G[c+8>>2]|(d|0)>(g|0)){break a}d=E[e+D[c>>2]|0];e=e+1|0;f=e?f:f+1|0;D[c+16>>2]=e;D[c+20>>2]=f;if(d&128){if(!jb(a+1|0,b,c)){break a}d=d&127|D[b>>2]<<7}D[b>>2]=d;h=1}return h}function eb(a,b,c){var d=0,e=0,f=0,g=0,h=0;a:{if(a>>>0>5){break a}f=D[c+20>>2];d=f;g=D[c+12>>2];e=D[c+16>>2];if((d|0)>=(g|0)&e>>>0>=G[c+8>>2]|(d|0)>(g|0)){break a}d=E[e+D[c>>2]|0];e=e+1|0;f=e?f:f+1|0;D[c+16>>2]=e;D[c+20>>2]=f;if(d&128){if(!eb(a+1|0,b,c)){break a}d=d&127|D[b>>2]<<7}D[b>>2]=d;h=1}return h}function Vb(a,b,c){var d=0,e=0,f=0,g=0,h=0;a:{if(a>>>0>5){break a}f=D[c+20>>2];d=f;g=D[c+12>>2];e=D[c+16>>2];if((d|0)>=(g|0)&e>>>0>=G[c+8>>2]|(d|0)>(g|0)){break a}d=E[e+D[c>>2]|0];e=e+1|0;f=e?f:f+1|0;D[c+16>>2]=e;D[c+20>>2]=f;if(d&128){if(!Vb(a+1|0,b,c)){break a}d=d&127|D[b>>2]<<7}D[b>>2]=d;h=1}return h}function Sa(a,b,c){var d=0,e=0,f=0,g=0,h=0;a:{if(a>>>0>5){break a}f=D[c+20>>2];d=f;g=D[c+12>>2];e=D[c+16>>2];if((d|0)>=(g|0)&e>>>0>=G[c+8>>2]|(d|0)>(g|0)){break a}d=E[e+D[c>>2]|0];e=e+1|0;f=e?f:f+1|0;D[c+16>>2]=e;D[c+20>>2]=f;if(d&128){if(!Sa(a+1|0,b,c)){break a}d=d&127|D[b>>2]<<7}D[b>>2]=d;h=1}return h}function Ld(a,b,c){var d=0,e=0,f=0,g=0,h=0;a:{if(a>>>0>5){break a}f=D[c+20>>2];d=f;g=D[c+12>>2];e=D[c+16>>2];if((d|0)>=(g|0)&e>>>0>=G[c+8>>2]|(d|0)>(g|0)){break a}d=E[e+D[c>>2]|0];e=e+1|0;f=e?f:f+1|0;D[c+16>>2]=e;D[c+20>>2]=f;if(d&128){if(!Ld(a+1|0,b,c)){break a}d=d&127|D[b>>2]<<7}D[b>>2]=d;h=1}return h}function Da(a,b,c){var d=0,e=0,f=0,g=0,h=0;a:{if(a>>>0>5){break a}f=D[c+20>>2];d=f;g=D[c+12>>2];e=D[c+16>>2];if((d|0)>=(g|0)&e>>>0>=G[c+8>>2]|(d|0)>(g|0)){break a}d=E[e+D[c>>2]|0];e=e+1|0;f=e?f:f+1|0;D[c+16>>2]=e;D[c+20>>2]=f;if(d&128){if(!Da(a+1|0,b,c)){break a}d=d&127|D[b>>2]<<7}D[b>>2]=d;h=1}return h}function $a(a,b,c){var d=0,e=0,f=0,g=0,h=0;a:{if(a>>>0>5){break a}f=D[c+20>>2];d=f;g=D[c+12>>2];e=D[c+16>>2];if((d|0)>=(g|0)&e>>>0>=G[c+8>>2]|(d|0)>(g|0)){break a}d=E[e+D[c>>2]|0];e=e+1|0;f=e?f:f+1|0;D[c+16>>2]=e;D[c+20>>2]=f;if(d&128){if(!$a(a+1|0,b,c)){break a}d=d&127|D[b>>2]<<7}D[b>>2]=d;h=1}return h}function uf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0;g=D[b+96>>2];b=D[b+100>>2]-g|0;if(!((b|0)!=(c|0)|!b)){a=(c|0)/12|0;h=a>>>0>1?a:1;a=0;while(1){e=J(a,12);f=e+d|0;e=e+g|0;D[f>>2]=D[e>>2];D[f+4>>2]=D[e+4>>2];D[f+8>>2]=D[e+8>>2];a=a+1|0;if((h|0)!=(a|0)){continue}break}}return(b|0)==(c|0)|0}function va(a,b,c){var d=0,e=0;a:{b:{if(c>>>0>=4){if((a|b)&3){break b}while(1){if(D[a>>2]!=D[b>>2]){break b}b=b+4|0;a=a+4|0;c=c-4|0;if(c>>>0>3){continue}break}}if(!c){break a}}while(1){d=E[a|0];e=E[b|0];if((d|0)==(e|0)){b=b+1|0;a=a+1|0;c=c-1|0;if(c){continue}break a}break}return d-e|0}return 0}function yf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=D[b+4>>2];a:{if(!d){break a}b=D[D[D[b+8>>2]+(c<<2)>>2]+60>>2];if((b|0)<0){break a}a=D[d+24>>2];c=D[d+28>>2];if((a|0)==(c|0)){break a}b:{while(1){e=D[a>>2];if((b|0)==D[e+24>>2]){break b}a=a+4|0;if((c|0)!=(a|0)){continue}break}e=0}}return e|0}function dc(a){var b=0,c=0,d=0;if(a){d=D[a+24>>2];if(d){c=D[a+28>>2];if((c|0)==(d|0)){b=d}else{while(1){c=c-4|0;b=D[c>>2];D[c>>2]=0;if(b){Ca(b+12|0,D[b+16>>2]);Ba(b,D[b+4>>2]);ma(b)}if((d|0)!=(c|0)){continue}break}b=D[a+24>>2]}D[a+28>>2]=d;ma(b)}Ca(a+12|0,D[a+16>>2]);Ba(a,D[a+4>>2]);ma(a)}}function ph(a){a=a|0;var b=0,c=0,d=0;D[a>>2]=10064;b=D[a+20>>2];if(b){D[a+24>>2]=b;ma(b)}d=D[a+8>>2];if(d){c=D[a+12>>2];if((c|0)==(d|0)){b=d}else{while(1){c=c-4|0;b=D[c>>2];D[c>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}if((d|0)!=(c|0)){continue}break}b=D[a+8>>2]}D[a+12>>2]=d;ma(b)}return a|0}function Vg(a){a=a|0;var b=0;D[a+8>>2]=9168;D[a>>2]=8956;b=D[a+96>>2];if(b){D[a+100>>2]=b;ma(b)}b=D[a+80>>2];if(b){D[a+84>>2]=b;ma(b)}b=D[a+68>>2];if(b){D[a+72>>2]=b;ma(b)}b=D[a+56>>2];if(b){D[a+60>>2]=b;ma(b)}D[a+8>>2]=9404;b=D[a+44>>2];if(b){ma(b)}b=D[a+32>>2];if(b){ma(b)}return a|0}function za(a){var b=0,c=0,d=0;b=a;a:{if(b&3){while(1){if(!E[b|0]){break a}b=b+1|0;if(b&3){continue}break}}while(1){c=b;b=b+4|0;d=D[c>>2];if(!((d^-1)&d-16843009&-2139062144)){continue}break}if(!(d&255)){return c-a|0}while(1){d=E[c+1|0];b=c+1|0;c=b;if(d){continue}break}}return b-a|0}function Tg(a){a=a|0;var b=0;D[a+8>>2]=9168;D[a>>2]=8956;b=D[a+96>>2];if(b){D[a+100>>2]=b;ma(b)}b=D[a+80>>2];if(b){D[a+84>>2]=b;ma(b)}b=D[a+68>>2];if(b){D[a+72>>2]=b;ma(b)}b=D[a+56>>2];if(b){D[a+60>>2]=b;ma(b)}D[a+8>>2]=9404;b=D[a+44>>2];if(b){ma(b)}b=D[a+32>>2];if(b){ma(b)}ma(a)}function Ag(a){a=a|0;var b=0,c=0,d=0;D[a>>2]=10064;b=D[a+20>>2];if(b){D[a+24>>2]=b;ma(b)}d=D[a+8>>2];if(d){c=D[a+12>>2];if((c|0)==(d|0)){b=d}else{while(1){c=c-4|0;b=D[c>>2];D[c>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}if((d|0)!=(c|0)){continue}break}b=D[a+8>>2]}D[a+12>>2]=d;ma(b)}ma(a)}function rc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0;h=D[c+8>>2];e=D[c+16>>2];g=D[c+12>>2];f=g;d=D[c+20>>2];if(h>>>0>e>>>0&(f|0)>=(d|0)|(d|0)<(f|0)){b=E[e+D[c>>2]|0];i=e+1|0;f=i?d:d+1|0;D[c+16>>2]=i;D[c+20>>2]=f;D[a+4>>2]=b}return e>>>0<h>>>0&(d|0)<=(g|0)|(d|0)<(g|0)}function Ja(a,b,c){var d=0;if(!c){return D[a+4>>2]==D[b+4>>2]}if((a|0)==(b|0)){return 1}d=D[a+4>>2];a=E[d|0];c=D[b+4>>2];b=E[c|0];a:{if(!a|(b|0)!=(a|0)){break a}while(1){b=E[c+1|0];a=E[d+1|0];if(!a){break a}c=c+1|0;d=d+1|0;if((a|0)==(b|0)){continue}break}}return(a|0)==(b|0)}function Wg(a){a=a|0;var b=0;D[a>>2]=9168;b=D[a+88>>2];if(b){D[a+92>>2]=b;ma(b)}b=D[a+72>>2];if(b){D[a+76>>2]=b;ma(b)}b=D[a+60>>2];if(b){D[a- -64>>2]=b;ma(b)}b=D[a+48>>2];if(b){D[a+52>>2]=b;ma(b)}D[a>>2]=9404;b=D[a+36>>2];if(b){ma(b)}b=D[a+24>>2];if(b){ma(b)}return a|0}function Hb(a,b){var c=0,d=0,e=0,f=0;a:{c=D[a>>2];b:{if(D[a+8>>2]-c>>2>>>0>=b>>>0){break b}if(b>>>0>=1073741824){break a}d=D[a+4>>2];e=b<<2;b=na(e);e=b+e|0;d=d-c|0;f=d+b|0;if((d|0)>0){oa(b,c,d)}D[a+8>>2]=e;D[a+4>>2]=f;D[a>>2]=b;if(!c){break b}ma(c)}return}ra(1326);T()}function Af(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;a=D[b+12>>2];b=D[b+8>>2];d=a-b|0;a=0;a:{if(!d){break a}a=d>>2;d=a>>>0>1?a:1;a=0;b:{while(1){e=D[b+(a<<2)>>2];if(D[e+60>>2]==(c|0)){break b}a=a+1|0;if((d|0)!=(a|0)){continue}break}a=0;break a}a=(a|0)==-1?0:e}return a|0}function Qg(a){a=a|0;var b=0;D[a>>2]=9168;b=D[a+88>>2];if(b){D[a+92>>2]=b;ma(b)}b=D[a+72>>2];if(b){D[a+76>>2]=b;ma(b)}b=D[a+60>>2];if(b){D[a- -64>>2]=b;ma(b)}b=D[a+48>>2];if(b){D[a+52>>2]=b;ma(b)}D[a>>2]=9404;b=D[a+36>>2];if(b){ma(b)}b=D[a+24>>2];if(b){ma(b)}ma(a)}function ab(a){var b=0;if(a){b=D[a+76>>2];if(b){D[a+80>>2]=b;ma(b)}b=D[a- -64>>2];if(b){D[a+68>>2]=b;ma(b)}b=D[a+48>>2];if(b){D[a+52>>2]=b;ma(b)}b=D[a+24>>2];if(b){D[a+28>>2]=b;ma(b)}b=D[a+12>>2];if(b){D[a+16>>2]=b;ma(b)}b=D[a>>2];if(b){D[a+4>>2]=b;ma(b)}ma(a)}}function ob(a){var b=0;b=D[a+84>>2];if(b){D[a+88>>2]=b;ma(b)}b=D[a+72>>2];if(b){D[a+76>>2]=b;ma(b)}b=D[a+52>>2];if(b){D[a+56>>2]=b;ma(b)}b=D[a+40>>2];if(b){D[a+44>>2]=b;ma(b)}b=D[a+28>>2];if(b){D[a+32>>2]=b;ma(b)}b=D[a+12>>2];if(b){ma(b)}a=D[a>>2];if(a){ma(a)}}function xd(a){var b=0,c=0,d=0;b=D[a+4>>2];d=D[a>>2];if((b|0)!=(d|0)){while(1){c=D[b-12>>2];if(c){D[b-8>>2]=c;ma(c)}c=D[b-28>>2];if(c){D[b-24>>2]=c;ma(c)}c=D[b-40>>2];if(c){D[b-36>>2]=c;ma(c)}ob(b-140|0);b=b-144|0;if((d|0)!=(b|0)){continue}break}}D[a+4>>2]=d}function ta(a,b,c){var d=0,e=0;a:{b:{if(c>>>0<=10){d=a;B[d+11|0]=c;break b}if(c>>>0>4294967279){break a}if(c>>>0>=11){e=c+16&-16;d=e-1|0;d=(d|0)==11?e:d}else{d=10}e=d+1|0;d=na(e);D[a>>2]=d;D[a+8>>2]=e|-2147483648;D[a+4>>2]=c}Xa(d,b,c+1|0);return}Aa();T()}function Gc(a,b,c){var d=0,e=0,f=0,g=0,h=0;f=$-16|0;$=f;d=$-16|0;$=d;b=b-a>>2;while(1){if(b){D[d+12>>2]=a;e=b>>>1|0;D[d+12>>2]=D[d+12>>2]+(e<<2);h=(e^-1)+b|0;b=e;e=D[d+12>>2];g=G[e>>2]<G[c>>2];b=g?h:b;a=g?e+4|0:a;continue}break}$=d+16|0;$=f+16|0;return a}function fd(a,b){var c=0,d=0;d=na(40);D[d>>2]=-1;c=d+8|0;D[c+16>>2]=0;D[c+20>>2]=0;D[c+8>>2]=0;D[c>>2]=0;D[c+4>>2]=0;D[c+24>>2]=0;D[c+28>>2]=0;ba[D[D[a>>2]+16>>2]](a,d);a=D[b+88>>2];D[b+88>>2]=d;if(a){b=D[a+8>>2];if(b){D[a+12>>2]=b;ma(b)}ma(a)}return 1}function Ga(a){var b=0,c=0,d=0,e=0,f=0;d=E[a+12|0];c=D[a+8>>2];a:{if(c>>>0>4095){break a}b=D[a+4>>2];if((b|0)<=0){break a}b=b-1|0;D[a+4>>2]=b;c=E[b+D[a>>2]|0]|c<<8}d=0-d&255;b=J(d,c>>>8|0);e=c&255;f=e>>>0<d>>>0;D[a+8>>2]=f?b+e|0:c-(b+d|0)|0;return f}function xf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;a=$-16|0;$=a;b=D[b+96>>2];D[a+8>>2]=0;D[a>>2]=0;D[a+4>>2]=0;e=na(12);c=oa(e,b+J(c,12)|0,12);f=c+12|0;b=D[d>>2];if(b){D[d+4>>2]=b;ma(b)}D[d+8>>2]=c+12;D[d+4>>2]=f;D[d>>2]=e;$=a+16|0;return 1}function wc(a,b){D[a+4>>2]=0;D[a+8>>2]=0;D[a>>2]=1804;D[a+12>>2]=0;D[a+16>>2]=0;D[a+20>>2]=0;D[a+24>>2]=0;D[a+28>>2]=0;D[a+32>>2]=0;D[a+36>>2]=0;D[a+40>>2]=0;D[a>>2]=2044;D[a+44>>2]=0;D[a+48>>2]=0;D[a+52>>2]=0;D[a+56>>2]=0;D[a+60>>2]=b;return a}function id(a,b,c,d){a:{if(!b){if((d|0)<0){return 0}gb(a,c);break a}if((d|0)<0){return 0}if(!(!d&D[a+4>>2]-D[a>>2]>>>0>=c>>>0)){gb(a,c)}if(!c){break a}Na(D[a>>2],b,c)}b=D[a+28>>2];c=D[a+24>>2]+1|0;b=c?b:b+1|0;D[a+24>>2]=c;D[a+28>>2]=b;return 1}function If(a){a=a|0;var b=0,c=0,d=0;if(a){if(B[a+27|0]<0){ma(D[a+16>>2])}b=D[a>>2];if(b){c=D[a+4>>2];if((c|0)==(b|0)){d=b}else{while(1){d=c-12|0;if(B[c-1|0]<0){ma(D[d>>2])}c=d;if((c|0)!=(b|0)){continue}break}d=D[a>>2]}D[a+4>>2]=b;ma(d)}ma(a)}}function ug(){var a=0;a=na(40);C[a+38>>1]=0;D[a>>2]=0;D[a+8>>2]=0;D[a+12>>2]=0;D[a+16>>2]=0;D[a+20>>2]=0;D[a+24>>2]=0;D[a+28>>2]=0;B[a+29|0]=0;B[a+30|0]=0;B[a+31|0]=0;B[a+32|0]=0;B[a+33|0]=0;B[a+34|0]=0;B[a+35|0]=0;B[a+36|0]=0;return a|0}function ya(a){a=a|0;var b=0,c=0;if(a){b=D[a+88>>2];D[a+88>>2]=0;if(b){c=D[b+8>>2];if(c){D[b+12>>2]=c;ma(c)}ma(b)}b=D[a+68>>2];if(b){D[a+72>>2]=b;ma(b)}b=D[a+64>>2];D[a+64>>2]=0;if(b){c=D[b>>2];if(c){D[b+4>>2]=c;ma(c)}ma(b)}ma(a)}}function th(a){a=a|0;var b=0;D[a+24>>2]=1140;D[a>>2]=7976;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}D[a>>2]=2164;b=D[a+20>>2];D[a+20>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}D[a>>2]=1948;b=D[a+16>>2];D[a+16>>2]=0;if(b){ya(b)}return a|0}function Fb(a,b){var c=0,d=0,e=0;c=za(b);if(c>>>0<4294967280){a:{b:{if(c>>>0>=11){e=c+16&-16;d=na(e);D[a+8>>2]=e|-2147483648;D[a>>2]=d;D[a+4>>2]=c;break b}B[a+11|0]=c;d=a;if(!c){break a}}oa(d,b,c)}B[c+d|0]=0;return a}Aa();T()}function ai(a,b,c,d){var e=0,f=0,g=0,h=0;f=b^d;g=f>>31;e=b>>31;a=a^e;h=a-e|0;e=(b^e)-((a>>>0<e>>>0)+e|0)|0;a=d>>31;b=c^a;f=f>>31;a=bi(h,e,b-a|0,(a^d)-((a>>>0>b>>>0)+a|0)|0)^f;b=a-f|0;aa=(g^aa)-((a>>>0<f>>>0)+g|0)|0;return b}function sh(a){a=a|0;var b=0;D[a+24>>2]=1140;D[a>>2]=7976;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}D[a>>2]=2164;b=D[a+20>>2];D[a+20>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}D[a>>2]=1948;b=D[a+16>>2];D[a+16>>2]=0;if(b){ya(b)}ma(a)}function tb(a,b,c){var d=0,e=0,f=0;e=$-16|0;$=e;d=D[a+8>>2]&2147483647;a:{if(d>>>0>c>>>0){d=D[a>>2];D[a+4>>2]=c;Xa(d,b,c);B[e+15|0]=0;B[c+d|0]=E[e+15|0];break a}f=a;a=D[a+4>>2];Fc(f,d-1|0,(c-d|0)+1|0,a,a,c,b)}$=e+16|0}function $h(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0;e=c>>>16|0;f=a>>>16|0;j=J(e,f);g=c&65535;h=a&65535;i=J(g,h);f=(i>>>16|0)+J(f,g)|0;e=(f&65535)+J(e,h)|0;aa=(J(b,c)+j|0)+J(a,d)+(f>>>16)+(e>>>16)|0;return i&65535|e<<16}function qe(a,b){a=a|0;b=b|0;var c=0,d=0;c=$-16|0;$=c;a=D[a+4>>2];a:{if((a|0)==-1){break a}B[c+15|0]=a;d=D[b+20>>2];if(!!D[b+16>>2]&(d|0)>=0|(d|0)>0){break a}rb(b,D[b+4>>2],c+15|0,c+16|0)}$=c+16|0;return(a|0)!=-1|0}function pg(){var a=0;a=na(96);lb(a);D[a+64>>2]=0;D[a+68>>2]=0;D[a+88>>2]=0;D[a+72>>2]=0;D[a+76>>2]=0;B[a+77|0]=0;B[a+78|0]=0;B[a+79|0]=0;B[a+80|0]=0;B[a+81|0]=0;B[a+82|0]=0;B[a+83|0]=0;B[a+84|0]=0;return a|0}function Cc(a,b,c){var d=0;d=D[a+16>>2];if(!d){D[a+36>>2]=1;D[a+24>>2]=c;D[a+16>>2]=b;return}a:{if((b|0)==(d|0)){if(D[a+24>>2]!=2){break a}D[a+24>>2]=c;return}B[a+54|0]=1;D[a+24>>2]=2;D[a+36>>2]=D[a+36>>2]+1}}function bh(a,b){a=a|0;b=b|0;var c=0,d=0;D[b>>2]=2;c=D[b+8>>2];d=D[b+12>>2]-c|0;if(d>>>0<=4294967291){Eb(b+8|0,d+4|0);c=D[b+8>>2]}b=c+d|0;a=D[a+4>>2];B[b|0]=a;B[b+1|0]=a>>>8;B[b+2|0]=a>>>16;B[b+3|0]=a>>>24}function vg(a){a=a|0;var b=0,c=0,d=0;b=D[a+8>>2];d=D[a+12>>2];if((b|0)==(d|0)){return 1}while(1){c=D[b>>2];c=ba[D[D[c>>2]+16>>2]](c,D[a+32>>2])|0;if(c){b=b+4|0;if((d|0)!=(b|0)){continue}}break}return c|0}function be(a){a=a|0;var b=0;D[a>>2]=3044;b=D[a+96>>2];if(b){ma(b)}b=D[a+84>>2];if(b){ma(b)}b=D[a+72>>2];if(b){ma(b)}b=D[a+60>>2];if(b){ma(b)}D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}return a|0}function Xh(a){a=a|0;var b=0;D[a>>2]=4608;b=D[a+96>>2];if(b){ma(b)}b=D[a+84>>2];if(b){ma(b)}b=D[a+72>>2];if(b){ma(b)}b=D[a+60>>2];if(b){ma(b)}D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}return a|0}function ra(a){var b=0,c=0,d=0,e=0,f=0;b=_(8)|0;D[b>>2]=11024;D[b>>2]=11068;c=za(a);d=na(c+13|0);D[d+8>>2]=0;D[d+4>>2]=c;D[d>>2]=c;e=b,f=oa(d+12|0,a,c+1|0),D[e+4>>2]=f;D[b>>2]=11116;Z(b|0,11148,13);T()}function ae(a){a=a|0;var b=0;D[a>>2]=3044;b=D[a+96>>2];if(b){ma(b)}b=D[a+84>>2];if(b){ma(b)}b=D[a+72>>2];if(b){ma(b)}b=D[a+60>>2];if(b){ma(b)}D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}ma(a)}function Wh(a){a=a|0;var b=0;D[a>>2]=4608;b=D[a+96>>2];if(b){ma(b)}b=D[a+84>>2];if(b){ma(b)}b=D[a+72>>2];if(b){ma(b)}b=D[a+60>>2];if(b){ma(b)}D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}ma(a)}function Nc(a,b,c){var d=0,e=0;d=a+4|0;a=_a(a,b);if((d|0)==(a|0)){return 0}b=D[a+32>>2];d=D[a+28>>2];if((b|0)!=(d|0)){Qb(c,b-d|0);e=Rb(c);c=D[a+28>>2];oa(e,c,D[a+32>>2]-c|0)}return(b|0)!=(d|0)}function Kc(a,b){var c=0,d=0;c=D[a+8>>2];a=D[a+12>>2]-c|0;if(a){a=a>>2;d=a>>>0>1?a:1;a=0;while(1){if(D[D[(a<<2)+c>>2]+60>>2]==(b|0)){return a}a=a+1|0;if((d|0)!=(a|0)){continue}break}}return-1}function lb(a){D[a+8>>2]=0;D[a+12>>2]=0;D[a>>2]=0;D[a+40>>2]=0;D[a+44>>2]=0;D[a+28>>2]=9;B[a+24|0]=1;D[a+56>>2]=-1;D[a+60>>2]=0;D[a+16>>2]=0;D[a+20>>2]=0;D[a+48>>2]=0;D[a+52>>2]=0;return a}function Re(a,b){a=a|0;b=b|0;var c=0,d=0;Ic(a,b);a:{if((b|0)<0){break a}c=D[a+88>>2];d=D[a+84>>2];if(c-d>>2<=(b|0)){break a}b=d+(b<<2)|0;d=b+4|0;c=c-d|0;if(c){Na(b,d,c)}D[a+88>>2]=b+c}}function Fd(a){D[a+40>>2]=0;D[a+4>>2]=0;D[a+8>>2]=0;D[a>>2]=10064;D[a+12>>2]=0;D[a+16>>2]=0;D[a+20>>2]=0;D[a+24>>2]=0;D[a+28>>2]=0;D[a+32>>2]=0;C[a+36>>1]=0;D[a+44>>2]=0;D[a>>2]=8108}function cc(a,b,c){var d=0;a:{if(b){b=0;if(!hd(1,c,a)){break a}}B[a+36|0]=1;D[a+32>>2]=0;b=D[a+16>>2];c=b+D[a>>2]|0;D[a+24>>2]=c;d=a;a=D[a+8>>2];D[d+28>>2]=c+(a-b|0);b=1}return b}function ke(a,b){a=a|0;b=b|0;var c=0,d=0;d=D[a+16>>2];c=0;a:{if(D[a+20>>2]-d>>2<=(b|0)){break a}b=D[(b<<2)+d>>2];c=0;if((b|0)<0){break a}c=cb(D[D[a+36>>2]+(b<<2)>>2])}return c|0}function Hf(){var a=0,b=0;a=na(40);D[a+4>>2]=0;D[a+8>>2]=0;D[a>>2]=a+4;b=a+16|0;D[b>>2]=0;D[b+4>>2]=0;D[a+24>>2]=0;D[a+28>>2]=0;D[a+12>>2]=b;D[a+32>>2]=0;D[a+36>>2]=0;return a|0}function Wa(a){var b=0,c=0;b=D[2853];c=a+3&-4;a=b+c|0;a:{if(a>>>0<=b>>>0?c:0){break a}if(a>>>0>ca()<<16>>>0){if(!(Y(a|0)|0)){break a}}D[2853]=a;return b}D[2879]=48;return-1}function ze(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=D[a+8>>2];a:{if(B[d+24|0]<=0){break a}if(!Yb(d,D[b+4>>2]-D[b>>2]>>2)){break a}e=ba[D[D[a>>2]+32>>2]](a,b,c)|0}return e|0}function sb(a,b,c){var d=0,e=0;d=$-16|0;$=d;a:{if(c>>>0<=10){B[a+11|0]=c;Xa(a,b,c);B[d+15|0]=0;B[a+c|0]=E[d+15|0];break a}e=a;a=E[a+11|0];Fc(e,10,c-10|0,a,a,c,b)}$=d+16|0}function Nh(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;D[a+4>>2]=b;b=D[D[D[b+4>>2]+8>>2]+(c<<2)>>2];D[a+12>>2]=c;D[a+8>>2]=b;a=D[a+8>>2];if(E[a+24|0]==3){d=D[a+28>>2]==9}return d|0}function Mg(a){a=a|0;var b=0;D[a+8>>2]=9588;D[a>>2]=9424;b=D[a+56>>2];if(b){D[a+60>>2]=b;ma(b)}D[a+8>>2]=9404;b=D[a+44>>2];if(b){ma(b)}b=D[a+32>>2];if(b){ma(b)}return a|0}function Gg(a){a=a|0;var b=0;D[a+8>>2]=8652;D[a>>2]=9716;b=D[a+56>>2];if(b){D[a+60>>2]=b;ma(b)}D[a+8>>2]=8904;b=D[a+44>>2];if(b){ma(b)}b=D[a+32>>2];if(b){ma(b)}return a|0}function zh(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;D[a+4>>2]=b;d=D[D[D[b+4>>2]+8>>2]+(c<<2)>>2];D[a+12>>2]=c;D[a+8>>2]=d;return D[D[D[D[b+4>>2]+8>>2]+(c<<2)>>2]+28>>2]==9|0}function Ca(a,b){if(b){Ca(a,D[b>>2]);Ca(a,D[b+4>>2]);a=D[b+28>>2];D[b+28>>2]=0;if(a){Ca(a+12|0,D[a+16>>2]);Ba(a,D[a+4>>2]);ma(a)}if(B[b+27|0]<0){ma(D[b+16>>2])}ma(b)}}function Kg(a){a=a|0;var b=0;D[a+8>>2]=9588;D[a>>2]=9424;b=D[a+56>>2];if(b){D[a+60>>2]=b;ma(b)}D[a+8>>2]=9404;b=D[a+44>>2];if(b){ma(b)}b=D[a+32>>2];if(b){ma(b)}ma(a)}function Fg(a){a=a|0;var b=0;D[a+8>>2]=8652;D[a>>2]=9716;b=D[a+56>>2];if(b){D[a+60>>2]=b;ma(b)}D[a+8>>2]=8904;b=D[a+44>>2];if(b){ma(b)}b=D[a+32>>2];if(b){ma(b)}ma(a)}function Oe(a,b){a=a|0;b=b|0;var c=0;a:{if(!(ba[D[D[a>>2]+36>>2]](a,b)|0)){break a}if(!(ba[D[D[a>>2]+40>>2]](a,b)|0)){break a}c=ba[D[D[a>>2]+44>>2]](a)|0}return c|0}function Ud(a){a=a|0;var b=0;a:{if(!D[a- -64>>2]|!D[a+68>>2]|(!D[a+44>>2]|!D[a+48>>2])){break a}if(!D[a+52>>2]|!D[a+56>>2]){break a}b=D[a+92>>2]!=-1}return b|0}function uc(a){a=a|0;var b=0;D[a>>2]=2164;b=D[a+20>>2];D[a+20>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}D[a>>2]=1948;b=D[a+16>>2];D[a+16>>2]=0;if(b){ya(b)}return a|0}function _h(a,b){a=a|0;b=b|0;var c=0;b=D[b+88>>2];if(!(!b|D[b>>2]!=2)){c=a;a=D[b+8>>2];D[c+4>>2]=E[a|0]|E[a+1|0]<<8|(E[a+2|0]<<16|E[a+3|0]<<24);c=1}return c|0}function Od(a){a=a|0;var b=0;a:{if(!D[a+48>>2]|!D[a+52>>2]|(!D[a+28>>2]|!D[a+32>>2])){break a}if(!D[a+36>>2]|!D[a+40>>2]){break a}b=D[a+76>>2]!=-1}return b|0}function zd(a){a=a|0;var b=0;D[a>>2]=8652;b=D[a+48>>2];if(b){D[a+52>>2]=b;ma(b)}D[a>>2]=8904;b=D[a+36>>2];if(b){ma(b)}b=D[a+24>>2];if(b){ma(b)}return a|0}function tc(a){a=a|0;var b=0;D[a>>2]=2164;b=D[a+20>>2];D[a+20>>2]=0;if(b){ba[D[D[b>>2]+4>>2]](b)}D[a>>2]=1948;b=D[a+16>>2];D[a+16>>2]=0;if(b){ya(b)}ma(a)}function Ng(a){a=a|0;var b=0;D[a>>2]=9588;b=D[a+48>>2];if(b){D[a+52>>2]=b;ma(b)}D[a>>2]=9404;b=D[a+36>>2];if(b){ma(b)}b=D[a+24>>2];if(b){ma(b)}return a|0}function sg(){var a=0,b=0;b=na(40);D[b>>2]=-1;a=b+8|0;D[a+16>>2]=0;D[a+20>>2]=0;D[a+8>>2]=0;D[a>>2]=0;D[a+4>>2]=0;D[a+24>>2]=0;D[a+28>>2]=0;return b|0}function _g(a){a=a|0;var b=0;D[a>>2]=8652;b=D[a+48>>2];if(b){D[a+52>>2]=b;ma(b)}D[a>>2]=8904;b=D[a+36>>2];if(b){ma(b)}b=D[a+24>>2];if(b){ma(b)}ma(a)}function Hg(a){a=a|0;var b=0;D[a>>2]=9588;b=D[a+48>>2];if(b){D[a+52>>2]=b;ma(b)}D[a>>2]=9404;b=D[a+36>>2];if(b){ma(b)}b=D[a+24>>2];if(b){ma(b)}ma(a)}function Ce(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;if(Ja(a,D[b+8>>2],f)){Bc(b,c,d,e);return}a=D[a+8>>2];ba[D[D[a>>2]+20>>2]](a,b,c,d,e,f)}function yh(a,b,c){a=a|0;b=b|0;c=c|0;a:{if(E[D[a+4>>2]+36|0]>=2){b=0;if(!(ba[D[D[a>>2]+52>>2]](a)|0)){break a}}b=fd(a+24|0,D[a+16>>2])}return b|0}function bg(){var a=0;a=na(108);Mc(a);D[a+84>>2]=0;D[a+88>>2]=0;D[a>>2]=10272;D[a+92>>2]=0;D[a+96>>2]=0;D[a+100>>2]=0;D[a+104>>2]=0;return a|0}function Lc(a,b){var c=0;c=-1;a:{if((b|0)==-1|(b|0)>4){break a}b=J(b,12)+a|0;a=D[b+20>>2];if((D[b+24>>2]-a|0)<=0){break a}c=D[a>>2]}return c}function $b(a,b,c,d,e,f,g){D[a>>2]=0;D[a+56>>2]=b;D[a+48>>2]=0;D[a+52>>2]=0;D[a+40>>2]=f;D[a+44>>2]=g;B[a+32|0]=e;D[a+28>>2]=d;B[a+24|0]=c}function Ue(a){a=a|0;var b=0;D[a>>2]=10272;b=D[a+96>>2];if(b){D[a+100>>2]=b;ma(b)}b=D[a+84>>2];if(b){D[a+88>>2]=b;ma(b)}ub(a);return a|0}function Mh(a,b,c){a=a|0;b=b|0;c=c|0;a:{if(E[D[a+4>>2]+36|0]>=2){b=0;if(!rc(a+24|0,cb(a),c)){break a}}b=fd(a+24|0,D[a+16>>2])}return b|0}function Te(a){a=a|0;var b=0;D[a>>2]=10272;b=D[a+96>>2];if(b){D[a+100>>2]=b;ma(b)}b=D[a+84>>2];if(b){D[a+88>>2]=b;ma(b)}ub(a);ma(a)}function _d(a){a=a|0;var b=0;D[a>>2]=3292;b=D[a+76>>2];if(b){ma(b)}D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}return a|0}function Uh(a){a=a|0;var b=0;D[a>>2]=4844;b=D[a+76>>2];if(b){ma(b)}D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}return a|0}function Ba(a,b){if(b){Ba(a,D[b>>2]);Ba(a,D[b+4>>2]);a=D[b+28>>2];if(a){D[b+32>>2]=a;ma(a)}if(B[b+27|0]<0){ma(D[b+16>>2])}ma(b)}}function Qf(){var a=0;a=na(28);D[a>>2]=0;D[a+4>>2]=0;D[a+24>>2]=0;D[a+16>>2]=0;D[a+20>>2]=0;D[a+8>>2]=0;D[a+12>>2]=0;return a|0}function Rb(a){var b=0;if(E[a+11|0]>>>7|0){b=D[a+4>>2]}else{b=E[a+11|0]}if(!b){Sb();T()}if(E[a+11|0]>>>7|0){a=D[a>>2]}return a}function Qe(a){a=a|0;var b=0;D[a>>2]=1804;b=D[a+16>>2];if(b){D[a+20>>2]=b;ma(b)}b=D[a+4>>2];if(b){D[a+8>>2]=b;ma(b)}return a|0}function Ge(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(Ja(a,D[b+8>>2],0)){Cc(b,c,d);return}a=D[a+8>>2];ba[D[D[a>>2]+28>>2]](a,b,c,d)}function $f(){var a=0,b=0;a=na(24);D[a+4>>2]=0;D[a+8>>2]=0;b=a+16|0;D[b>>2]=0;D[b+4>>2]=0;D[a>>2]=a+4;D[a+12>>2]=b;return a|0}function Zd(a){a=a|0;var b=0;D[a>>2]=3292;b=D[a+76>>2];if(b){ma(b)}D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}ma(a)}function Th(a){a=a|0;var b=0;D[a>>2]=4844;b=D[a+76>>2];if(b){ma(b)}D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}ma(a)}function na(a){var b=0;a=a?a:1;a:{while(1){b=Ac(a);if(b){break a}b=D[2880];if(b){ba[b|0]();continue}break}W();T()}return b}function hb(a,b){if(b){hb(a,D[b>>2]);hb(a,D[b+4>>2]);if(B[b+39|0]<0){ma(D[b+28>>2])}if(B[b+27|0]<0){ma(D[b+16>>2])}ma(b)}}function Pb(a){a=a|0;var b=0,c=0;D[a>>2]=11068;b=D[a+4>>2]-12|0;c=D[b+8>>2]-1|0;D[b+8>>2]=c;if((c|0)<0){ma(b)}return a|0}function Kb(a,b){a=a|0;b=b|0;var c=0;a:{switch(b|0){case 1:b=1;case 0:D[a+28>>2]=b;c=1;break;default:break a}}return c|0}function gg(){var a=0;a=na(24);D[a+8>>2]=0;D[a+12>>2]=0;D[a+4>>2]=-1;D[a>>2]=1140;D[a+16>>2]=0;D[a+20>>2]=0;return a|0}function yc(a,b,c){a=a|0;b=b|0;c=c|0;D[a+4>>2]=b;b=D[D[D[b+4>>2]+8>>2]+(c<<2)>>2];D[a+12>>2]=c;D[a+8>>2]=b;return 1}function nc(a){a=a|0;var b=0;if(!(!D[a+60>>2]|!D[a+44>>2]|(!D[a+48>>2]|!D[a+52>>2]))){b=D[a+56>>2]!=0}return b|0}function Eb(a,b){var c=0,d=0;gb(a,b);b=D[a+28>>2];c=b;d=b+1|0;b=D[a+24>>2]+1|0;D[a+24>>2]=b;D[a+28>>2]=b?c:d}function Xd(a){a=a|0;var b=0;D[a>>2]=3528;D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}return a|0}function Rh(a){a=a|0;var b=0;D[a>>2]=5068;D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}return a|0}function ff(a){a=a|0;if(a){if(B[a+39|0]<0){ma(D[a+28>>2])}Xb(a+12|0,D[a+16>>2]);hb(a,D[a+4>>2]);ma(a)}}function Zg(a){a=a|0;var b=0;D[a>>2]=8904;b=D[a+36>>2];if(b){ma(b)}b=D[a+24>>2];if(b){ma(b)}return a|0}function Pg(a){a=a|0;var b=0;D[a>>2]=9404;b=D[a+36>>2];if(b){ma(b)}b=D[a+24>>2];if(b){ma(b)}return a|0}function qb(a){a=a|0;var b=0;if(!(!D[a+52>>2]|(!D[a+44>>2]|!D[a+48>>2]))){b=D[a+56>>2]!=0}return b|0}function mc(a,b){a=a|0;b=b|0;var c=0;if(!(D[b+56>>2]|!b|E[b+24|0]!=3)){D[a+60>>2]=b;c=1}return c|0}function Wd(a){a=a|0;var b=0;D[a>>2]=3528;D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}ma(a)}function Qh(a){a=a|0;var b=0;D[a>>2]=5068;D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}ma(a)}function tg(a,b,c){a=a|0;b=b|0;c=c|0;D[a+16>>2]=0;D[a+20>>2]=0;D[a>>2]=b;D[a+8>>2]=c;D[a+12>>2]=0}function Yg(a){a=a|0;var b=0;D[a>>2]=8904;b=D[a+36>>2];if(b){ma(b)}b=D[a+24>>2];if(b){ma(b)}ma(a)}function Td(a,b){a=a|0;b=b|0;var c=0;if(!(D[b+56>>2]|E[b+24|0]!=3)){D[a- -64>>2]=b;c=1}return c|0}function Og(a){a=a|0;var b=0;D[a>>2]=9404;b=D[a+36>>2];if(b){ma(b)}b=D[a+24>>2];if(b){ma(b)}ma(a)}function Nd(a,b){a=a|0;b=b|0;var c=0;if(!(D[b+56>>2]|E[b+24|0]!=3)){D[a+48>>2]=b;c=1}return c|0}function Be(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;if(Ja(a,D[b+8>>2],f)){Bc(b,c,d,e)}}function ve(a){a=a|0;var b=0;D[a>>2]=1948;b=D[a+16>>2];D[a+16>>2]=0;if(b){ya(b)}return a|0}function ee(a){a=a|0;var b=0;D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}return a|0}function vh(a){a=a|0;var b=0;b=cb(a);return od(a+24|0,b?b:D[a+8>>2],D[D[a+4>>2]+32>>2])|0}function Ug(a){a=a|0;var b=0;D[a>>2]=1140;b=D[a+8>>2];if(b){D[a+12>>2]=b;ma(b)}return a|0}function ue(a){a=a|0;var b=0;D[a>>2]=1948;b=D[a+16>>2];D[a+16>>2]=0;if(b){ya(b)}ma(a)}function Nb(a){a=a|0;var b=0;D[a>>2]=2988;b=D[a+32>>2];if(b){D[a+36>>2]=b;ma(b)}ma(a)}function Lg(a){a=a|0;var b=0;D[a>>2]=1140;b=D[a+8>>2];if(b){D[a+12>>2]=b;ma(b)}ma(a)}function je(a,b){a=a|0;b=b|0;return ba[D[D[a>>2]+48>>2]](a,D[b+4>>2]-D[b>>2]>>2)|0}function Xb(a,b){if(b){Xb(a,D[b>>2]);Xb(a,D[b+4>>2]);hb(b+20|0,D[b+24>>2]);ma(b)}}function rg(a){a=a|0;var b=0;if(a){b=D[a+8>>2];if(b){D[a+12>>2]=b;ma(b)}ma(a)}}function qh(a){a=a|0;if(!D[a+44>>2]){return 0}return ba[D[D[a>>2]+48>>2]](a)|0}function ci(a){var b=0;while(1){if(a){a=a-1&a;b=b+1|0;continue}break}return b}function He(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(Ja(a,D[b+8>>2],0)){Cc(b,c,d)}}function oh(a,b){a=a|0;b=b|0;a=D[a+48>>2];return ba[D[D[a>>2]+20>>2]](a,b)|0}function gh(a,b){a=a|0;b=b|0;a=D[a+48>>2];return ba[D[D[a>>2]+12>>2]](a,b)|0}function fh(a,b){a=a|0;b=b|0;a=D[a+48>>2];return ba[D[D[a>>2]+16>>2]](a,b)|0}function Za(){var a=0;a=na(12);D[a>>2]=0;D[a+4>>2]=0;D[a+8>>2]=0;return a|0}function Ya(a){a=a|0;var b=0;if(a){b=D[a>>2];if(b){D[a+4>>2]=b;ma(b)}ma(a)}}function di(a){var b=0;b=a&31;a=0-a&31;return(-1>>>b&-2)<<b|(-1<<a&-2)>>>a}function zg(a,b){a=a|0;b=b|0;B[b+84|0]=1;D[b+72>>2]=D[b+68>>2];return 1}function of(a,b,c){a=a|0;b=b|0;c=c|0;D[a+32>>2]=c;D[a+28>>2]=b;return 1}function _f(a){a=a|0;if(a){Ca(a+12|0,D[a+16>>2]);Ba(a,D[a+4>>2]);ma(a)}}function mh(a){a=a|0;a=D[a+48>>2];return ba[D[D[a>>2]+24>>2]](a)|0}function lh(a){a=a|0;a=D[a+48>>2];return ba[D[D[a>>2]+28>>2]](a)|0}function hh(a){a=a|0;a=D[a+48>>2];return ba[D[D[a>>2]+36>>2]](a)|0}function dg(){var a=0;a=na(8);D[a+4>>2]=-1;D[a>>2]=1032;return a|0}function Bf(a,b,c){a=a|0;b=b|0;c=c|0;return D[D[b+8>>2]+(c<<2)>>2]}function uh(a,b){a=a|0;b=b|0;return yd(a+24|0,cb(a),D[a+8>>2])|0}function Lh(a,b){a=a|0;b=b|0;return xc(a+24|0,cb(a),D[a+8>>2])|0}function wh(a,b){a=a|0;b=b|0;return ba[D[D[a>>2]+56>>2]](a,b)|0}function dd(a,b){a=a|0;b=b|0;return ba[D[D[a>>2]+12>>2]](a,b)|0}function Xf(a){a=a|0;if(a){if(B[a+15|0]<0){ma(D[a+4>>2])}ma(a)}}function Ae(a,b){a=a|0;b=b|0;D[a+12>>2]=-1;D[a+8>>2]=b;return 1}function fg(a,b){a=a|0;b=b|0;return K(H[D[a+8>>2]+(b<<2)>>2])}function Wf(a,b){a=a|0;b=b|0;return K(H[D[a>>2]+(b<<2)>>2])}function ag(a){a=a|0;return(D[a+100>>2]-D[a+96>>2]|0)/12|0}function Yf(a){a=a|0;return(B[a+15|0]<0?D[a+4>>2]:a+4|0)|0}function Ne(a,b){a=a|0;b=b|0;return D[D[a+4>>2]+(b<<2)>>2]}function Tf(a,b){a=a|0;b=b|0;return C[D[a>>2]+(b<<1)>>1]}function Sf(a,b){a=a|0;b=b|0;return F[D[a>>2]+(b<<1)>>1]}function $c(a,b){a=a|0;b=b|0;return D[D[a>>2]+(b<<2)>>2]}function Pd(a,b,c){a=a|0;b=b|0;c=c|0;return vc(a,b,c)|0}function Ef(a,b,c){a=a|0;b=b|0;c=c|0;return Lc(b,c)|0}function ei(a){if(a){return 31-M(a-1^a)|0}return 32}
function Mc(a){D[a>>2]=10332;pa(a+4|0,0,80);return a}function cd(a){a=a|0;return D[a+12>>2]-D[a+8>>2]>>2}function xb(a){a=a|0;if(a){ba[D[D[a>>2]+4>>2]](a)}}function Vf(a,b){a=a|0;b=b|0;return B[D[a>>2]+b|0]}function Uf(a,b){a=a|0;b=b|0;return E[D[a>>2]+b|0]}function Fe(a){a=a|0;return D[a+8>>2]-D[a+4>>2]>>2}function Ed(a,b){a=a|0;b=b|0;D[a+4>>2]=b;return 1}function ad(a){a=a|0;return D[a+4>>2]-D[a>>2]>>1}function _b(a){a=a|0;return D[a+4>>2]-D[a>>2]>>2}function bd(a){a=a|0;return D[a+4>>2]-D[a>>2]|0}function zf(a,b){a=a|0;b=b|0;return D[b+4>>2]}function ye(a,b,c){a=a|0;b=b|0;c=c|0;return 1}function xg(a,b){a=a|0;b=b|0;return B[b+24|0]}function fe(a){a=a|0;return B[D[a+8>>2]+24|0]}function Kf(a,b){a=a|0;b=b|0;return D[b+8>>2]}function Jh(a){a=a|0;D[a>>2]=5956;return a|0}function Dh(a){a=a|0;D[a>>2]=6960;return a|0}function eg(a){a=a|0;return K(H[a+20>>2])}function Ih(a){a=a|0;D[a>>2]=5956;ma(a)}function Ch(a){a=a|0;D[a>>2]=6960;ma(a)}function og(a){a=a|0;return D[a+88>>2]}function ng(a){a=a|0;return D[a+56>>2]}function jg(a){a=a|0;return D[a+40>>2]}function ig(a){a=a|0;return D[a+48>>2]}function hg(a){a=a|0;return D[a+60>>2]}function db(a){a=a|0;return D[a+28>>2]}function ac(a){a=a|0;return D[a+80>>2]}function zc(a,b){a=a|0;b=b|0;return 1}function sc(a,b){a=a|0;b=b|0;return-1}function pd(a){a=a|0;return D[a+8>>2]}function lg(a){a=a|0;return B[a+24|0]}function kg(a){a=a|0;return E[a+32|0]}function Zf(a){a=a|0;return!D[a>>2]|0}function Va(a){a=a|0;return D[a+4>>2]}function Qd(a,b){a=a|0;b=b|0;return 6}function Ha(a,b){a=a|0;b=b|0;return 0}function Gh(a,b){a=a|0;b=b|0;return 2}function ed(a){a=a|0;return D[a>>2]}function Xa(a,b,c){if(c){oa(a,b,c)}}function qg(){return lb(na(64))|0}function cg(){return Mc(na(84))|0}function bc(a){a=a|0;if(a){ma(a)}}function Le(a){a=a|0;return 1273}function Je(a){a=a|0;Pb(a);ma(a)}function Ma(a){a=a|0;return a|0}function ah(a){a=a|0;ma(nd(a))}function Pe(a){a=a|0;ma(ub(a))}function Ke(a){a=a|0;ma(Pb(a))}function $g(a){a=a|0;ma(md(a))}function wa(a){a=a|0;return 1}function qc(a){a=a|0;return 4}function oc(a){a=a|0;return 5}function Ua(a){a=a|0;return 0}function Rd(a){a=a|0;return 2}function Mb(a){a=a|0;return 6}function Hh(a){a=a|0;return 3}function qa(){ra(1266);T()}function La(a){a=a|0;ma(a)}function Aa(){ra(1313);T()}function Pa(a){a=a|0;T()}function _e(){return 11}function Ze(){return 12}function $e(){return 10}function wb(){return-1}function vb(){return 1}function ua(){Sb();T()}function kb(){return 0}function ef(){return 5}function df(){return 6}function cf(){return 7}function bf(){return 8}function af(){return 9}function Ye(){return-2}function Yc(){return 3}function Xe(){return-3}function Xc(){return 4}function We(){return-4}function Wb(){return 2}function Ve(){return-5}function Sb(){W();T()}function Ec(a){a=a|0}function Me(){T()}function Vd(){}
// EMSCRIPTEN_END_FUNCS
e=E;p(ka);var ba=c([null,Ma,La,Rd,_h,bh,Rf,xc,qe,rc,mg,Qd,Gh,Pb,Ug,Lg,wa,xh,rh,ih,yd,dh,od,Qd,xg,Qe,Pa,of,Zc,Oe,Ne,Fe,db,Ha,Me,zc,wa,ve,ue,yc,Ae,ze,ye,zc,xe,we,me,le,te,se,ke,re,pe,oe,ne,uc,tc,yc,je,ie,vc,he,fe,ge,ee,Nb,wa,Va,qb,Ua,sc,Ha,Ua,wa,de,ce,Pa,Pa,be,ae,qc,qb,pc,$d,_d,Zd,oc,nc,wa,Ha,mc,lc,Yd,Xd,Wd,Mb,Ud,wa,Ha,Td,Sd,Zh,Ma,La,Kb,db,Lb,Pa,Nb,wa,qb,Yh,Pa,Xh,Wh,qc,qb,pc,Vh,Uh,Th,oc,nc,wa,Ha,mc,lc,Sh,Rh,Qh,Mb,Ud,wa,Ha,Td,Sd,Ph,Ma,La,Kb,db,Jb,Pa,Nb,Ua,wa,Oh,uc,tc,Nh,Mh,Pd,Kh,Rd,Lh,Jh,Ih,Mb,Va,Od,wa,Ha,Nd,wa,Hh,Md,Fh,Ma,La,Kb,db,Lb,Dh,Ch,Mb,Od,wa,Ha,Nd,Md,Bh,Ma,La,Kb,db,Jb,Ma,La,Ua,wa,Ua,sc,Ha,Eh,Ah,th,sh,zh,yh,Pd,wh,vh,uh,ph,Pa,wa,wa,qh,wg,vg,wa,Ua,Ha,Ha,kh,jh,nh,oh,lh,hh,gh,fh,mh,nd,ah,Ed,Dd,Cd,Bd,eh,wa,Va,pd,md,$g,Ed,Dd,Cd,Bd,ch,wa,Va,pd,zd,_g,Ad,Zg,Yg,Vg,Tg,Sg,Rg,Wg,Qg,Xg,Pg,Og,Mg,Kg,Jg,Ig,Ng,Hg,Gg,Fg,Eg,Dg,Ag,Bg,Cg,Ma,La,zg,yg,Pa,Ua,wa,Ue,Te,Se,Re,ub,Pe,Jc,Ic,Ma,La,Le,Ke,Va,Je,Ma,La,Ec,Ec,Ie,Be,De,He,La,Ce,Ee,Ge]);function ca(){return A.byteLength/65536|0}function ha(ia){ia=ia|0;var da=ca()|0;var ea=da+ia|0;if(da<ea&&ea<65536){var fa=new ArrayBuffer(J(ea,65536));var ga=new Int8Array(fa);ga.set(B);B=new Int8Array(fa);C=new Int16Array(fa);D=new Int32Array(fa);E=new Uint8Array(fa);F=new Uint16Array(fa);G=new Uint32Array(fa);H=new Float32Array(fa);I=new Float64Array(fa);A=fa;z.buffer=A;e=E}return da}return{"g":Vd,"h":ba,"i":bc,"j":ug,"k":tg,"l":bc,"m":sg,"n":ed,"o":rg,"p":qg,"q":bc,"r":pg,"s":ac,"t":og,"u":ng,"v":db,"w":lg,"x":kg,"y":jg,"z":ig,"A":hg,"B":ya,"C":gg,"D":dd,"E":Va,"F":fg,"G":eg,"H":xb,"I":dg,"J":dd,"K":Va,"L":xb,"M":cg,"N":cd,"O":ac,"P":xb,"Q":bg,"R":ag,"S":cd,"T":ac,"U":xb,"V":$f,"W":_f,"X":ed,"Y":Zf,"Z":Yf,"_":Xf,"$":Za,"aa":Wf,"ba":_b,"ca":Ya,"da":Za,"ea":Vf,"fa":bd,"ga":Ya,"ha":Za,"ia":Uf,"ja":bd,"ka":Ya,"la":Za,"ma":Tf,"na":ad,"oa":Ya,"pa":Za,"qa":Sf,"ra":ad,"sa":Ya,"ta":Za,"ua":$c,"va":_b,"wa":Ya,"xa":Za,"ya":$c,"za":_b,"Aa":Ya,"Ba":Qf,"Ca":Pf,"Da":Of,"Ea":Nf,"Fa":Mf,"Ga":Lf,"Ha":Kf,"Ia":Jf,"Ja":If,"Ka":Hf,"La":Gf,"Ma":Ff,"Na":Ef,"Oa":Df,"Pa":Cf,"Qa":Bf,"Ra":Af,"Sa":zf,"Ta":yf,"Ua":xf,"Va":wf,"Wa":vf,"Xa":uf,"Ya":tf,"Za":sf,"_a":_c,"$a":rf,"ab":qf,"bb":pf,"cb":nf,"db":_c,"eb":mf,"fb":lf,"gb":kf,"hb":jf,"ib":hf,"jb":gf,"kb":ff,"lb":wb,"mb":kb,"nb":vb,"ob":Wb,"pb":wb,"qb":kb,"rb":vb,"sb":Wb,"tb":Yc,"ub":Xc,"vb":wb,"wb":kb,"xb":vb,"yb":kb,"zb":vb,"Ab":Wb,"Bb":Yc,"Cb":Xc,"Db":ef,"Eb":df,"Fb":cf,"Gb":bf,"Hb":af,"Ib":$e,"Jb":_e,"Kb":Ze,"Lb":kb,"Mb":wb,"Nb":Ye,"Ob":Xe,"Pb":We,"Qb":Ve,"Rb":Ac,"Sb":ma}}return ja(la)}
// EMSCRIPTEN_END_ASM




)(asmLibraryArg)},instantiate:function(binary,info){return{then:function(ok){var module=new WebAssembly.Module(binary);ok({"instance":new WebAssembly.Instance(module)})}}},RuntimeError:Error};wasmBinary=[];if(typeof WebAssembly!=="object"){abort("no native wasm support detected")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}var UTF8Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str="";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127)++len;else if(u<=2047)len+=2;else if(u<=65535)len+=3;else len+=4}return len}function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module["HEAP8"]=HEAP8=new Int8Array(buf);Module["HEAP16"]=HEAP16=new Int16Array(buf);Module["HEAP32"]=HEAP32=new Int32Array(buf);Module["HEAPU8"]=HEAPU8=new Uint8Array(buf);Module["HEAPU16"]=HEAPU16=new Uint16Array(buf);Module["HEAPU32"]=HEAPU32=new Uint32Array(buf);Module["HEAPF32"]=HEAPF32=new Float32Array(buf);Module["HEAPF64"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module["INITIAL_MEMORY"]||16777216;if(Module["wasmMemory"]){wasmMemory=Module["wasmMemory"]}else{wasmMemory=new WebAssembly.Memory({"initial":INITIAL_MEMORY/65536,"maximum":2147483648/65536})}if(wasmMemory){buffer=wasmMemory.buffer}INITIAL_MEMORY=buffer.byteLength;updateGlobalBufferAndViews(buffer);var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeKeepaliveCounter=0;function keepRuntimeAlive(){return noExitRuntime||runtimeKeepaliveCounter>0}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};function abort(what){{if(Module["onAbort"]){Module["onAbort"](what)}}what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -s ASSERTIONS=1 for more info.";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}function isFileURI(filename){return filename.startsWith("file://")}var wasmBinaryFile;wasmBinaryFile="draco_decoder_gltf.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}var binary=tryParseAsDataURI(file);if(binary){return binary}if(readBinary){return readBinary(file)}else{throw"both async and sync fetching of the wasm failed"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==="function"&&!isFileURI(wasmBinaryFile)){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary(wasmBinaryFile)})}else{if(readAsync){return new Promise(function(resolve,reject){readAsync(wasmBinaryFile,function(response){resolve(new Uint8Array(response))},reject)})}}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={"a":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;wasmTable=Module["asm"]["h"];addOnInit(Module["asm"]["g"]);removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&!isFileURI(wasmBinaryFile)&&typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module["instantiateWasm"]){try{var exports=Module["instantiateWasm"](info,receiveInstance);return exports}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}instantiateAsync().catch(readyPromiseReject);return{}}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback(Module);continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){getWasmTableEntry(func)()}else{getWasmTableEntry(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var wasmTableMirror=[];function getWasmTableEntry(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func}function ___cxa_allocate_exception(size){return _malloc(size+16)+16}function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-16;this.set_type=function(type){HEAP32[this.ptr+4>>2]=type};this.get_type=function(){return HEAP32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAP32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAP32[this.ptr+8>>2]};this.set_refcount=function(refcount){HEAP32[this.ptr>>2]=refcount};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_type(type);this.set_destructor(destructor);this.set_refcount(0);this.set_caught(false);this.set_rethrown(false)};this.add_ref=function(){var value=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=value+1};this.release_ref=function(){var prev=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=prev-1;return prev===1}}var exceptionLast=0;var uncaughtExceptionCount=0;function ___cxa_throw(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw ptr}function _abort(){abort("")}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}var ASSERTIONS=false;function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}function intArrayToString(array){var ret=[];for(var i=0;i<array.length;i++){var chr=array[i];if(chr>255){if(ASSERTIONS){assert(false,"Character code "+chr+" ("+String.fromCharCode(chr)+")  at offset "+i+" not in 0x00-0xFF.")}chr&=255}ret.push(String.fromCharCode(chr))}return ret.join("")}var decodeBase64=typeof atob==="function"?atob:function(input){var keyStr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var output="";var chr1,chr2,chr3;var enc1,enc2,enc3,enc4;var i=0;input=input.replace(/[^A-Za-z0-9\+\/\=]/g,"");do{enc1=keyStr.indexOf(input.charAt(i++));enc2=keyStr.indexOf(input.charAt(i++));enc3=keyStr.indexOf(input.charAt(i++));enc4=keyStr.indexOf(input.charAt(i++));chr1=enc1<<2|enc2>>4;chr2=(enc2&15)<<4|enc3>>2;chr3=(enc3&3)<<6|enc4;output=output+String.fromCharCode(chr1);if(enc3!==64){output=output+String.fromCharCode(chr2)}if(enc4!==64){output=output+String.fromCharCode(chr3)}}while(i<input.length);return output};function intArrayFromBase64(s){if(typeof ENVIRONMENT_IS_NODE==="boolean"&&ENVIRONMENT_IS_NODE){var buf=Buffer.from(s,"base64");return new Uint8Array(buf["buffer"],buf["byteOffset"],buf["byteLength"])}try{var decoded=decodeBase64(s);var bytes=new Uint8Array(decoded.length);for(var i=0;i<decoded.length;++i){bytes[i]=decoded.charCodeAt(i)}return bytes}catch(_){throw new Error("Converting base64 string to bytes failed.")}}function tryParseAsDataURI(filename){if(!isDataURI(filename)){return}return intArrayFromBase64(filename.slice(dataURIPrefix.length))}var asmLibraryArg={"f":___cxa_allocate_exception,"e":___cxa_throw,"b":_abort,"c":_emscripten_memcpy_big,"d":_emscripten_resize_heap,"a":wasmMemory};var asm=createWasm();var ___wasm_call_ctors=Module["___wasm_call_ctors"]=function(){return(___wasm_call_ctors=Module["___wasm_call_ctors"]=Module["asm"]["g"]).apply(null,arguments)};var _emscripten_bind_VoidPtr___destroy___0=Module["_emscripten_bind_VoidPtr___destroy___0"]=function(){return(_emscripten_bind_VoidPtr___destroy___0=Module["_emscripten_bind_VoidPtr___destroy___0"]=Module["asm"]["i"]).apply(null,arguments)};var _emscripten_bind_DecoderBuffer_DecoderBuffer_0=Module["_emscripten_bind_DecoderBuffer_DecoderBuffer_0"]=function(){return(_emscripten_bind_DecoderBuffer_DecoderBuffer_0=Module["_emscripten_bind_DecoderBuffer_DecoderBuffer_0"]=Module["asm"]["j"]).apply(null,arguments)};var _emscripten_bind_DecoderBuffer_Init_2=Module["_emscripten_bind_DecoderBuffer_Init_2"]=function(){return(_emscripten_bind_DecoderBuffer_Init_2=Module["_emscripten_bind_DecoderBuffer_Init_2"]=Module["asm"]["k"]).apply(null,arguments)};var _emscripten_bind_DecoderBuffer___destroy___0=Module["_emscripten_bind_DecoderBuffer___destroy___0"]=function(){return(_emscripten_bind_DecoderBuffer___destroy___0=Module["_emscripten_bind_DecoderBuffer___destroy___0"]=Module["asm"]["l"]).apply(null,arguments)};var _emscripten_bind_AttributeTransformData_AttributeTransformData_0=Module["_emscripten_bind_AttributeTransformData_AttributeTransformData_0"]=function(){return(_emscripten_bind_AttributeTransformData_AttributeTransformData_0=Module["_emscripten_bind_AttributeTransformData_AttributeTransformData_0"]=Module["asm"]["m"]).apply(null,arguments)};var _emscripten_bind_AttributeTransformData_transform_type_0=Module["_emscripten_bind_AttributeTransformData_transform_type_0"]=function(){return(_emscripten_bind_AttributeTransformData_transform_type_0=Module["_emscripten_bind_AttributeTransformData_transform_type_0"]=Module["asm"]["n"]).apply(null,arguments)};var _emscripten_bind_AttributeTransformData___destroy___0=Module["_emscripten_bind_AttributeTransformData___destroy___0"]=function(){return(_emscripten_bind_AttributeTransformData___destroy___0=Module["_emscripten_bind_AttributeTransformData___destroy___0"]=Module["asm"]["o"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_GeometryAttribute_0=Module["_emscripten_bind_GeometryAttribute_GeometryAttribute_0"]=function(){return(_emscripten_bind_GeometryAttribute_GeometryAttribute_0=Module["_emscripten_bind_GeometryAttribute_GeometryAttribute_0"]=Module["asm"]["p"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute___destroy___0=Module["_emscripten_bind_GeometryAttribute___destroy___0"]=function(){return(_emscripten_bind_GeometryAttribute___destroy___0=Module["_emscripten_bind_GeometryAttribute___destroy___0"]=Module["asm"]["q"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_PointAttribute_0=Module["_emscripten_bind_PointAttribute_PointAttribute_0"]=function(){return(_emscripten_bind_PointAttribute_PointAttribute_0=Module["_emscripten_bind_PointAttribute_PointAttribute_0"]=Module["asm"]["r"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_size_0=Module["_emscripten_bind_PointAttribute_size_0"]=function(){return(_emscripten_bind_PointAttribute_size_0=Module["_emscripten_bind_PointAttribute_size_0"]=Module["asm"]["s"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_GetAttributeTransformData_0=Module["_emscripten_bind_PointAttribute_GetAttributeTransformData_0"]=function(){return(_emscripten_bind_PointAttribute_GetAttributeTransformData_0=Module["_emscripten_bind_PointAttribute_GetAttributeTransformData_0"]=Module["asm"]["t"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_attribute_type_0=Module["_emscripten_bind_PointAttribute_attribute_type_0"]=function(){return(_emscripten_bind_PointAttribute_attribute_type_0=Module["_emscripten_bind_PointAttribute_attribute_type_0"]=Module["asm"]["u"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_data_type_0=Module["_emscripten_bind_PointAttribute_data_type_0"]=function(){return(_emscripten_bind_PointAttribute_data_type_0=Module["_emscripten_bind_PointAttribute_data_type_0"]=Module["asm"]["v"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_num_components_0=Module["_emscripten_bind_PointAttribute_num_components_0"]=function(){return(_emscripten_bind_PointAttribute_num_components_0=Module["_emscripten_bind_PointAttribute_num_components_0"]=Module["asm"]["w"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_normalized_0=Module["_emscripten_bind_PointAttribute_normalized_0"]=function(){return(_emscripten_bind_PointAttribute_normalized_0=Module["_emscripten_bind_PointAttribute_normalized_0"]=Module["asm"]["x"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_byte_stride_0=Module["_emscripten_bind_PointAttribute_byte_stride_0"]=function(){return(_emscripten_bind_PointAttribute_byte_stride_0=Module["_emscripten_bind_PointAttribute_byte_stride_0"]=Module["asm"]["y"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_byte_offset_0=Module["_emscripten_bind_PointAttribute_byte_offset_0"]=function(){return(_emscripten_bind_PointAttribute_byte_offset_0=Module["_emscripten_bind_PointAttribute_byte_offset_0"]=Module["asm"]["z"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_unique_id_0=Module["_emscripten_bind_PointAttribute_unique_id_0"]=function(){return(_emscripten_bind_PointAttribute_unique_id_0=Module["_emscripten_bind_PointAttribute_unique_id_0"]=Module["asm"]["A"]).apply(null,arguments)};var _emscripten_bind_PointAttribute___destroy___0=Module["_emscripten_bind_PointAttribute___destroy___0"]=function(){return(_emscripten_bind_PointAttribute___destroy___0=Module["_emscripten_bind_PointAttribute___destroy___0"]=Module["asm"]["B"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0=Module["_emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0"]=function(){return(_emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0=Module["_emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0"]=Module["asm"]["C"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1=Module["_emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1"]=function(){return(_emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1=Module["_emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1"]=Module["asm"]["D"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform_quantization_bits_0=Module["_emscripten_bind_AttributeQuantizationTransform_quantization_bits_0"]=function(){return(_emscripten_bind_AttributeQuantizationTransform_quantization_bits_0=Module["_emscripten_bind_AttributeQuantizationTransform_quantization_bits_0"]=Module["asm"]["E"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform_min_value_1=Module["_emscripten_bind_AttributeQuantizationTransform_min_value_1"]=function(){return(_emscripten_bind_AttributeQuantizationTransform_min_value_1=Module["_emscripten_bind_AttributeQuantizationTransform_min_value_1"]=Module["asm"]["F"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform_range_0=Module["_emscripten_bind_AttributeQuantizationTransform_range_0"]=function(){return(_emscripten_bind_AttributeQuantizationTransform_range_0=Module["_emscripten_bind_AttributeQuantizationTransform_range_0"]=Module["asm"]["G"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform___destroy___0=Module["_emscripten_bind_AttributeQuantizationTransform___destroy___0"]=function(){return(_emscripten_bind_AttributeQuantizationTransform___destroy___0=Module["_emscripten_bind_AttributeQuantizationTransform___destroy___0"]=Module["asm"]["H"]).apply(null,arguments)};var _emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0=Module["_emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0"]=function(){return(_emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0=Module["_emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0"]=Module["asm"]["I"]).apply(null,arguments)};var _emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1=Module["_emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1"]=function(){return(_emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1=Module["_emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1"]=Module["asm"]["J"]).apply(null,arguments)};var _emscripten_bind_AttributeOctahedronTransform_quantization_bits_0=Module["_emscripten_bind_AttributeOctahedronTransform_quantization_bits_0"]=function(){return(_emscripten_bind_AttributeOctahedronTransform_quantization_bits_0=Module["_emscripten_bind_AttributeOctahedronTransform_quantization_bits_0"]=Module["asm"]["K"]).apply(null,arguments)};var _emscripten_bind_AttributeOctahedronTransform___destroy___0=Module["_emscripten_bind_AttributeOctahedronTransform___destroy___0"]=function(){return(_emscripten_bind_AttributeOctahedronTransform___destroy___0=Module["_emscripten_bind_AttributeOctahedronTransform___destroy___0"]=Module["asm"]["L"]).apply(null,arguments)};var _emscripten_bind_PointCloud_PointCloud_0=Module["_emscripten_bind_PointCloud_PointCloud_0"]=function(){return(_emscripten_bind_PointCloud_PointCloud_0=Module["_emscripten_bind_PointCloud_PointCloud_0"]=Module["asm"]["M"]).apply(null,arguments)};var _emscripten_bind_PointCloud_num_attributes_0=Module["_emscripten_bind_PointCloud_num_attributes_0"]=function(){return(_emscripten_bind_PointCloud_num_attributes_0=Module["_emscripten_bind_PointCloud_num_attributes_0"]=Module["asm"]["N"]).apply(null,arguments)};var _emscripten_bind_PointCloud_num_points_0=Module["_emscripten_bind_PointCloud_num_points_0"]=function(){return(_emscripten_bind_PointCloud_num_points_0=Module["_emscripten_bind_PointCloud_num_points_0"]=Module["asm"]["O"]).apply(null,arguments)};var _emscripten_bind_PointCloud___destroy___0=Module["_emscripten_bind_PointCloud___destroy___0"]=function(){return(_emscripten_bind_PointCloud___destroy___0=Module["_emscripten_bind_PointCloud___destroy___0"]=Module["asm"]["P"]).apply(null,arguments)};var _emscripten_bind_Mesh_Mesh_0=Module["_emscripten_bind_Mesh_Mesh_0"]=function(){return(_emscripten_bind_Mesh_Mesh_0=Module["_emscripten_bind_Mesh_Mesh_0"]=Module["asm"]["Q"]).apply(null,arguments)};var _emscripten_bind_Mesh_num_faces_0=Module["_emscripten_bind_Mesh_num_faces_0"]=function(){return(_emscripten_bind_Mesh_num_faces_0=Module["_emscripten_bind_Mesh_num_faces_0"]=Module["asm"]["R"]).apply(null,arguments)};var _emscripten_bind_Mesh_num_attributes_0=Module["_emscripten_bind_Mesh_num_attributes_0"]=function(){return(_emscripten_bind_Mesh_num_attributes_0=Module["_emscripten_bind_Mesh_num_attributes_0"]=Module["asm"]["S"]).apply(null,arguments)};var _emscripten_bind_Mesh_num_points_0=Module["_emscripten_bind_Mesh_num_points_0"]=function(){return(_emscripten_bind_Mesh_num_points_0=Module["_emscripten_bind_Mesh_num_points_0"]=Module["asm"]["T"]).apply(null,arguments)};var _emscripten_bind_Mesh___destroy___0=Module["_emscripten_bind_Mesh___destroy___0"]=function(){return(_emscripten_bind_Mesh___destroy___0=Module["_emscripten_bind_Mesh___destroy___0"]=Module["asm"]["U"]).apply(null,arguments)};var _emscripten_bind_Metadata_Metadata_0=Module["_emscripten_bind_Metadata_Metadata_0"]=function(){return(_emscripten_bind_Metadata_Metadata_0=Module["_emscripten_bind_Metadata_Metadata_0"]=Module["asm"]["V"]).apply(null,arguments)};var _emscripten_bind_Metadata___destroy___0=Module["_emscripten_bind_Metadata___destroy___0"]=function(){return(_emscripten_bind_Metadata___destroy___0=Module["_emscripten_bind_Metadata___destroy___0"]=Module["asm"]["W"]).apply(null,arguments)};var _emscripten_bind_Status_code_0=Module["_emscripten_bind_Status_code_0"]=function(){return(_emscripten_bind_Status_code_0=Module["_emscripten_bind_Status_code_0"]=Module["asm"]["X"]).apply(null,arguments)};var _emscripten_bind_Status_ok_0=Module["_emscripten_bind_Status_ok_0"]=function(){return(_emscripten_bind_Status_ok_0=Module["_emscripten_bind_Status_ok_0"]=Module["asm"]["Y"]).apply(null,arguments)};var _emscripten_bind_Status_error_msg_0=Module["_emscripten_bind_Status_error_msg_0"]=function(){return(_emscripten_bind_Status_error_msg_0=Module["_emscripten_bind_Status_error_msg_0"]=Module["asm"]["Z"]).apply(null,arguments)};var _emscripten_bind_Status___destroy___0=Module["_emscripten_bind_Status___destroy___0"]=function(){return(_emscripten_bind_Status___destroy___0=Module["_emscripten_bind_Status___destroy___0"]=Module["asm"]["_"]).apply(null,arguments)};var _emscripten_bind_DracoFloat32Array_DracoFloat32Array_0=Module["_emscripten_bind_DracoFloat32Array_DracoFloat32Array_0"]=function(){return(_emscripten_bind_DracoFloat32Array_DracoFloat32Array_0=Module["_emscripten_bind_DracoFloat32Array_DracoFloat32Array_0"]=Module["asm"]["$"]).apply(null,arguments)};var _emscripten_bind_DracoFloat32Array_GetValue_1=Module["_emscripten_bind_DracoFloat32Array_GetValue_1"]=function(){return(_emscripten_bind_DracoFloat32Array_GetValue_1=Module["_emscripten_bind_DracoFloat32Array_GetValue_1"]=Module["asm"]["aa"]).apply(null,arguments)};var _emscripten_bind_DracoFloat32Array_size_0=Module["_emscripten_bind_DracoFloat32Array_size_0"]=function(){return(_emscripten_bind_DracoFloat32Array_size_0=Module["_emscripten_bind_DracoFloat32Array_size_0"]=Module["asm"]["ba"]).apply(null,arguments)};var _emscripten_bind_DracoFloat32Array___destroy___0=Module["_emscripten_bind_DracoFloat32Array___destroy___0"]=function(){return(_emscripten_bind_DracoFloat32Array___destroy___0=Module["_emscripten_bind_DracoFloat32Array___destroy___0"]=Module["asm"]["ca"]).apply(null,arguments)};var _emscripten_bind_DracoInt8Array_DracoInt8Array_0=Module["_emscripten_bind_DracoInt8Array_DracoInt8Array_0"]=function(){return(_emscripten_bind_DracoInt8Array_DracoInt8Array_0=Module["_emscripten_bind_DracoInt8Array_DracoInt8Array_0"]=Module["asm"]["da"]).apply(null,arguments)};var _emscripten_bind_DracoInt8Array_GetValue_1=Module["_emscripten_bind_DracoInt8Array_GetValue_1"]=function(){return(_emscripten_bind_DracoInt8Array_GetValue_1=Module["_emscripten_bind_DracoInt8Array_GetValue_1"]=Module["asm"]["ea"]).apply(null,arguments)};var _emscripten_bind_DracoInt8Array_size_0=Module["_emscripten_bind_DracoInt8Array_size_0"]=function(){return(_emscripten_bind_DracoInt8Array_size_0=Module["_emscripten_bind_DracoInt8Array_size_0"]=Module["asm"]["fa"]).apply(null,arguments)};var _emscripten_bind_DracoInt8Array___destroy___0=Module["_emscripten_bind_DracoInt8Array___destroy___0"]=function(){return(_emscripten_bind_DracoInt8Array___destroy___0=Module["_emscripten_bind_DracoInt8Array___destroy___0"]=Module["asm"]["ga"]).apply(null,arguments)};var _emscripten_bind_DracoUInt8Array_DracoUInt8Array_0=Module["_emscripten_bind_DracoUInt8Array_DracoUInt8Array_0"]=function(){return(_emscripten_bind_DracoUInt8Array_DracoUInt8Array_0=Module["_emscripten_bind_DracoUInt8Array_DracoUInt8Array_0"]=Module["asm"]["ha"]).apply(null,arguments)};var _emscripten_bind_DracoUInt8Array_GetValue_1=Module["_emscripten_bind_DracoUInt8Array_GetValue_1"]=function(){return(_emscripten_bind_DracoUInt8Array_GetValue_1=Module["_emscripten_bind_DracoUInt8Array_GetValue_1"]=Module["asm"]["ia"]).apply(null,arguments)};var _emscripten_bind_DracoUInt8Array_size_0=Module["_emscripten_bind_DracoUInt8Array_size_0"]=function(){return(_emscripten_bind_DracoUInt8Array_size_0=Module["_emscripten_bind_DracoUInt8Array_size_0"]=Module["asm"]["ja"]).apply(null,arguments)};var _emscripten_bind_DracoUInt8Array___destroy___0=Module["_emscripten_bind_DracoUInt8Array___destroy___0"]=function(){return(_emscripten_bind_DracoUInt8Array___destroy___0=Module["_emscripten_bind_DracoUInt8Array___destroy___0"]=Module["asm"]["ka"]).apply(null,arguments)};var _emscripten_bind_DracoInt16Array_DracoInt16Array_0=Module["_emscripten_bind_DracoInt16Array_DracoInt16Array_0"]=function(){return(_emscripten_bind_DracoInt16Array_DracoInt16Array_0=Module["_emscripten_bind_DracoInt16Array_DracoInt16Array_0"]=Module["asm"]["la"]).apply(null,arguments)};var _emscripten_bind_DracoInt16Array_GetValue_1=Module["_emscripten_bind_DracoInt16Array_GetValue_1"]=function(){return(_emscripten_bind_DracoInt16Array_GetValue_1=Module["_emscripten_bind_DracoInt16Array_GetValue_1"]=Module["asm"]["ma"]).apply(null,arguments)};var _emscripten_bind_DracoInt16Array_size_0=Module["_emscripten_bind_DracoInt16Array_size_0"]=function(){return(_emscripten_bind_DracoInt16Array_size_0=Module["_emscripten_bind_DracoInt16Array_size_0"]=Module["asm"]["na"]).apply(null,arguments)};var _emscripten_bind_DracoInt16Array___destroy___0=Module["_emscripten_bind_DracoInt16Array___destroy___0"]=function(){return(_emscripten_bind_DracoInt16Array___destroy___0=Module["_emscripten_bind_DracoInt16Array___destroy___0"]=Module["asm"]["oa"]).apply(null,arguments)};var _emscripten_bind_DracoUInt16Array_DracoUInt16Array_0=Module["_emscripten_bind_DracoUInt16Array_DracoUInt16Array_0"]=function(){return(_emscripten_bind_DracoUInt16Array_DracoUInt16Array_0=Module["_emscripten_bind_DracoUInt16Array_DracoUInt16Array_0"]=Module["asm"]["pa"]).apply(null,arguments)};var _emscripten_bind_DracoUInt16Array_GetValue_1=Module["_emscripten_bind_DracoUInt16Array_GetValue_1"]=function(){return(_emscripten_bind_DracoUInt16Array_GetValue_1=Module["_emscripten_bind_DracoUInt16Array_GetValue_1"]=Module["asm"]["qa"]).apply(null,arguments)};var _emscripten_bind_DracoUInt16Array_size_0=Module["_emscripten_bind_DracoUInt16Array_size_0"]=function(){return(_emscripten_bind_DracoUInt16Array_size_0=Module["_emscripten_bind_DracoUInt16Array_size_0"]=Module["asm"]["ra"]).apply(null,arguments)};var _emscripten_bind_DracoUInt16Array___destroy___0=Module["_emscripten_bind_DracoUInt16Array___destroy___0"]=function(){return(_emscripten_bind_DracoUInt16Array___destroy___0=Module["_emscripten_bind_DracoUInt16Array___destroy___0"]=Module["asm"]["sa"]).apply(null,arguments)};var _emscripten_bind_DracoInt32Array_DracoInt32Array_0=Module["_emscripten_bind_DracoInt32Array_DracoInt32Array_0"]=function(){return(_emscripten_bind_DracoInt32Array_DracoInt32Array_0=Module["_emscripten_bind_DracoInt32Array_DracoInt32Array_0"]=Module["asm"]["ta"]).apply(null,arguments)};var _emscripten_bind_DracoInt32Array_GetValue_1=Module["_emscripten_bind_DracoInt32Array_GetValue_1"]=function(){return(_emscripten_bind_DracoInt32Array_GetValue_1=Module["_emscripten_bind_DracoInt32Array_GetValue_1"]=Module["asm"]["ua"]).apply(null,arguments)};var _emscripten_bind_DracoInt32Array_size_0=Module["_emscripten_bind_DracoInt32Array_size_0"]=function(){return(_emscripten_bind_DracoInt32Array_size_0=Module["_emscripten_bind_DracoInt32Array_size_0"]=Module["asm"]["va"]).apply(null,arguments)};var _emscripten_bind_DracoInt32Array___destroy___0=Module["_emscripten_bind_DracoInt32Array___destroy___0"]=function(){return(_emscripten_bind_DracoInt32Array___destroy___0=Module["_emscripten_bind_DracoInt32Array___destroy___0"]=Module["asm"]["wa"]).apply(null,arguments)};var _emscripten_bind_DracoUInt32Array_DracoUInt32Array_0=Module["_emscripten_bind_DracoUInt32Array_DracoUInt32Array_0"]=function(){return(_emscripten_bind_DracoUInt32Array_DracoUInt32Array_0=Module["_emscripten_bind_DracoUInt32Array_DracoUInt32Array_0"]=Module["asm"]["xa"]).apply(null,arguments)};var _emscripten_bind_DracoUInt32Array_GetValue_1=Module["_emscripten_bind_DracoUInt32Array_GetValue_1"]=function(){return(_emscripten_bind_DracoUInt32Array_GetValue_1=Module["_emscripten_bind_DracoUInt32Array_GetValue_1"]=Module["asm"]["ya"]).apply(null,arguments)};var _emscripten_bind_DracoUInt32Array_size_0=Module["_emscripten_bind_DracoUInt32Array_size_0"]=function(){return(_emscripten_bind_DracoUInt32Array_size_0=Module["_emscripten_bind_DracoUInt32Array_size_0"]=Module["asm"]["za"]).apply(null,arguments)};var _emscripten_bind_DracoUInt32Array___destroy___0=Module["_emscripten_bind_DracoUInt32Array___destroy___0"]=function(){return(_emscripten_bind_DracoUInt32Array___destroy___0=Module["_emscripten_bind_DracoUInt32Array___destroy___0"]=Module["asm"]["Aa"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_MetadataQuerier_0=Module["_emscripten_bind_MetadataQuerier_MetadataQuerier_0"]=function(){return(_emscripten_bind_MetadataQuerier_MetadataQuerier_0=Module["_emscripten_bind_MetadataQuerier_MetadataQuerier_0"]=Module["asm"]["Ba"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_HasEntry_2=Module["_emscripten_bind_MetadataQuerier_HasEntry_2"]=function(){return(_emscripten_bind_MetadataQuerier_HasEntry_2=Module["_emscripten_bind_MetadataQuerier_HasEntry_2"]=Module["asm"]["Ca"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_GetIntEntry_2=Module["_emscripten_bind_MetadataQuerier_GetIntEntry_2"]=function(){return(_emscripten_bind_MetadataQuerier_GetIntEntry_2=Module["_emscripten_bind_MetadataQuerier_GetIntEntry_2"]=Module["asm"]["Da"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_GetIntEntryArray_3=Module["_emscripten_bind_MetadataQuerier_GetIntEntryArray_3"]=function(){return(_emscripten_bind_MetadataQuerier_GetIntEntryArray_3=Module["_emscripten_bind_MetadataQuerier_GetIntEntryArray_3"]=Module["asm"]["Ea"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_GetDoubleEntry_2=Module["_emscripten_bind_MetadataQuerier_GetDoubleEntry_2"]=function(){return(_emscripten_bind_MetadataQuerier_GetDoubleEntry_2=Module["_emscripten_bind_MetadataQuerier_GetDoubleEntry_2"]=Module["asm"]["Fa"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_GetStringEntry_2=Module["_emscripten_bind_MetadataQuerier_GetStringEntry_2"]=function(){return(_emscripten_bind_MetadataQuerier_GetStringEntry_2=Module["_emscripten_bind_MetadataQuerier_GetStringEntry_2"]=Module["asm"]["Ga"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_NumEntries_1=Module["_emscripten_bind_MetadataQuerier_NumEntries_1"]=function(){return(_emscripten_bind_MetadataQuerier_NumEntries_1=Module["_emscripten_bind_MetadataQuerier_NumEntries_1"]=Module["asm"]["Ha"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_GetEntryName_2=Module["_emscripten_bind_MetadataQuerier_GetEntryName_2"]=function(){return(_emscripten_bind_MetadataQuerier_GetEntryName_2=Module["_emscripten_bind_MetadataQuerier_GetEntryName_2"]=Module["asm"]["Ia"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier___destroy___0=Module["_emscripten_bind_MetadataQuerier___destroy___0"]=function(){return(_emscripten_bind_MetadataQuerier___destroy___0=Module["_emscripten_bind_MetadataQuerier___destroy___0"]=Module["asm"]["Ja"]).apply(null,arguments)};var _emscripten_bind_Decoder_Decoder_0=Module["_emscripten_bind_Decoder_Decoder_0"]=function(){return(_emscripten_bind_Decoder_Decoder_0=Module["_emscripten_bind_Decoder_Decoder_0"]=Module["asm"]["Ka"]).apply(null,arguments)};var _emscripten_bind_Decoder_DecodeArrayToPointCloud_3=Module["_emscripten_bind_Decoder_DecodeArrayToPointCloud_3"]=function(){return(_emscripten_bind_Decoder_DecodeArrayToPointCloud_3=Module["_emscripten_bind_Decoder_DecodeArrayToPointCloud_3"]=Module["asm"]["La"]).apply(null,arguments)};var _emscripten_bind_Decoder_DecodeArrayToMesh_3=Module["_emscripten_bind_Decoder_DecodeArrayToMesh_3"]=function(){return(_emscripten_bind_Decoder_DecodeArrayToMesh_3=Module["_emscripten_bind_Decoder_DecodeArrayToMesh_3"]=Module["asm"]["Ma"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeId_2=Module["_emscripten_bind_Decoder_GetAttributeId_2"]=function(){return(_emscripten_bind_Decoder_GetAttributeId_2=Module["_emscripten_bind_Decoder_GetAttributeId_2"]=Module["asm"]["Na"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeIdByName_2=Module["_emscripten_bind_Decoder_GetAttributeIdByName_2"]=function(){return(_emscripten_bind_Decoder_GetAttributeIdByName_2=Module["_emscripten_bind_Decoder_GetAttributeIdByName_2"]=Module["asm"]["Oa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3=Module["_emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3=Module["_emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3"]=Module["asm"]["Pa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttribute_2=Module["_emscripten_bind_Decoder_GetAttribute_2"]=function(){return(_emscripten_bind_Decoder_GetAttribute_2=Module["_emscripten_bind_Decoder_GetAttribute_2"]=Module["asm"]["Qa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeByUniqueId_2=Module["_emscripten_bind_Decoder_GetAttributeByUniqueId_2"]=function(){return(_emscripten_bind_Decoder_GetAttributeByUniqueId_2=Module["_emscripten_bind_Decoder_GetAttributeByUniqueId_2"]=Module["asm"]["Ra"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetMetadata_1=Module["_emscripten_bind_Decoder_GetMetadata_1"]=function(){return(_emscripten_bind_Decoder_GetMetadata_1=Module["_emscripten_bind_Decoder_GetMetadata_1"]=Module["asm"]["Sa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeMetadata_2=Module["_emscripten_bind_Decoder_GetAttributeMetadata_2"]=function(){return(_emscripten_bind_Decoder_GetAttributeMetadata_2=Module["_emscripten_bind_Decoder_GetAttributeMetadata_2"]=Module["asm"]["Ta"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetFaceFromMesh_3=Module["_emscripten_bind_Decoder_GetFaceFromMesh_3"]=function(){return(_emscripten_bind_Decoder_GetFaceFromMesh_3=Module["_emscripten_bind_Decoder_GetFaceFromMesh_3"]=Module["asm"]["Ua"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetTriangleStripsFromMesh_2=Module["_emscripten_bind_Decoder_GetTriangleStripsFromMesh_2"]=function(){return(_emscripten_bind_Decoder_GetTriangleStripsFromMesh_2=Module["_emscripten_bind_Decoder_GetTriangleStripsFromMesh_2"]=Module["asm"]["Va"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetTrianglesUInt16Array_3=Module["_emscripten_bind_Decoder_GetTrianglesUInt16Array_3"]=function(){return(_emscripten_bind_Decoder_GetTrianglesUInt16Array_3=Module["_emscripten_bind_Decoder_GetTrianglesUInt16Array_3"]=Module["asm"]["Wa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetTrianglesUInt32Array_3=Module["_emscripten_bind_Decoder_GetTrianglesUInt32Array_3"]=function(){return(_emscripten_bind_Decoder_GetTrianglesUInt32Array_3=Module["_emscripten_bind_Decoder_GetTrianglesUInt32Array_3"]=Module["asm"]["Xa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeFloat_3=Module["_emscripten_bind_Decoder_GetAttributeFloat_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeFloat_3=Module["_emscripten_bind_Decoder_GetAttributeFloat_3"]=Module["asm"]["Ya"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3"]=Module["asm"]["Za"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeIntForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeIntForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeIntForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeIntForAllPoints_3"]=Module["asm"]["_a"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3"]=Module["asm"]["$a"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3"]=Module["asm"]["ab"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3"]=Module["asm"]["bb"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3"]=Module["asm"]["cb"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3"]=Module["asm"]["db"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3"]=Module["asm"]["eb"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5=Module["_emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5"]=function(){return(_emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5=Module["_emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5"]=Module["asm"]["fb"]).apply(null,arguments)};var _emscripten_bind_Decoder_SkipAttributeTransform_1=Module["_emscripten_bind_Decoder_SkipAttributeTransform_1"]=function(){return(_emscripten_bind_Decoder_SkipAttributeTransform_1=Module["_emscripten_bind_Decoder_SkipAttributeTransform_1"]=Module["asm"]["gb"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1=Module["_emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1"]=function(){return(_emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1=Module["_emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1"]=Module["asm"]["hb"]).apply(null,arguments)};var _emscripten_bind_Decoder_DecodeBufferToPointCloud_2=Module["_emscripten_bind_Decoder_DecodeBufferToPointCloud_2"]=function(){return(_emscripten_bind_Decoder_DecodeBufferToPointCloud_2=Module["_emscripten_bind_Decoder_DecodeBufferToPointCloud_2"]=Module["asm"]["ib"]).apply(null,arguments)};var _emscripten_bind_Decoder_DecodeBufferToMesh_2=Module["_emscripten_bind_Decoder_DecodeBufferToMesh_2"]=function(){return(_emscripten_bind_Decoder_DecodeBufferToMesh_2=Module["_emscripten_bind_Decoder_DecodeBufferToMesh_2"]=Module["asm"]["jb"]).apply(null,arguments)};var _emscripten_bind_Decoder___destroy___0=Module["_emscripten_bind_Decoder___destroy___0"]=function(){return(_emscripten_bind_Decoder___destroy___0=Module["_emscripten_bind_Decoder___destroy___0"]=Module["asm"]["kb"]).apply(null,arguments)};var _emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM"]=function(){return(_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM"]=Module["asm"]["lb"]).apply(null,arguments)};var _emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM"]=function(){return(_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM"]=Module["asm"]["mb"]).apply(null,arguments)};var _emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM"]=function(){return(_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM"]=Module["asm"]["nb"]).apply(null,arguments)};var _emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM"]=function(){return(_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM"]=Module["asm"]["ob"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_INVALID=Module["_emscripten_enum_draco_GeometryAttribute_Type_INVALID"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_INVALID=Module["_emscripten_enum_draco_GeometryAttribute_Type_INVALID"]=Module["asm"]["pb"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_POSITION=Module["_emscripten_enum_draco_GeometryAttribute_Type_POSITION"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_POSITION=Module["_emscripten_enum_draco_GeometryAttribute_Type_POSITION"]=Module["asm"]["qb"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_NORMAL=Module["_emscripten_enum_draco_GeometryAttribute_Type_NORMAL"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_NORMAL=Module["_emscripten_enum_draco_GeometryAttribute_Type_NORMAL"]=Module["asm"]["rb"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_COLOR=Module["_emscripten_enum_draco_GeometryAttribute_Type_COLOR"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_COLOR=Module["_emscripten_enum_draco_GeometryAttribute_Type_COLOR"]=Module["asm"]["sb"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD=Module["_emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD=Module["_emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD"]=Module["asm"]["tb"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_GENERIC=Module["_emscripten_enum_draco_GeometryAttribute_Type_GENERIC"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_GENERIC=Module["_emscripten_enum_draco_GeometryAttribute_Type_GENERIC"]=Module["asm"]["ub"]).apply(null,arguments)};var _emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE=Module["_emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE"]=function(){return(_emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE=Module["_emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE"]=Module["asm"]["vb"]).apply(null,arguments)};var _emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD=Module["_emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD"]=function(){return(_emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD=Module["_emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD"]=Module["asm"]["wb"]).apply(null,arguments)};var _emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH=Module["_emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH"]=function(){return(_emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH=Module["_emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH"]=Module["asm"]["xb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_INVALID=Module["_emscripten_enum_draco_DataType_DT_INVALID"]=function(){return(_emscripten_enum_draco_DataType_DT_INVALID=Module["_emscripten_enum_draco_DataType_DT_INVALID"]=Module["asm"]["yb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_INT8=Module["_emscripten_enum_draco_DataType_DT_INT8"]=function(){return(_emscripten_enum_draco_DataType_DT_INT8=Module["_emscripten_enum_draco_DataType_DT_INT8"]=Module["asm"]["zb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_UINT8=Module["_emscripten_enum_draco_DataType_DT_UINT8"]=function(){return(_emscripten_enum_draco_DataType_DT_UINT8=Module["_emscripten_enum_draco_DataType_DT_UINT8"]=Module["asm"]["Ab"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_INT16=Module["_emscripten_enum_draco_DataType_DT_INT16"]=function(){return(_emscripten_enum_draco_DataType_DT_INT16=Module["_emscripten_enum_draco_DataType_DT_INT16"]=Module["asm"]["Bb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_UINT16=Module["_emscripten_enum_draco_DataType_DT_UINT16"]=function(){return(_emscripten_enum_draco_DataType_DT_UINT16=Module["_emscripten_enum_draco_DataType_DT_UINT16"]=Module["asm"]["Cb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_INT32=Module["_emscripten_enum_draco_DataType_DT_INT32"]=function(){return(_emscripten_enum_draco_DataType_DT_INT32=Module["_emscripten_enum_draco_DataType_DT_INT32"]=Module["asm"]["Db"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_UINT32=Module["_emscripten_enum_draco_DataType_DT_UINT32"]=function(){return(_emscripten_enum_draco_DataType_DT_UINT32=Module["_emscripten_enum_draco_DataType_DT_UINT32"]=Module["asm"]["Eb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_INT64=Module["_emscripten_enum_draco_DataType_DT_INT64"]=function(){return(_emscripten_enum_draco_DataType_DT_INT64=Module["_emscripten_enum_draco_DataType_DT_INT64"]=Module["asm"]["Fb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_UINT64=Module["_emscripten_enum_draco_DataType_DT_UINT64"]=function(){return(_emscripten_enum_draco_DataType_DT_UINT64=Module["_emscripten_enum_draco_DataType_DT_UINT64"]=Module["asm"]["Gb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_FLOAT32=Module["_emscripten_enum_draco_DataType_DT_FLOAT32"]=function(){return(_emscripten_enum_draco_DataType_DT_FLOAT32=Module["_emscripten_enum_draco_DataType_DT_FLOAT32"]=Module["asm"]["Hb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_FLOAT64=Module["_emscripten_enum_draco_DataType_DT_FLOAT64"]=function(){return(_emscripten_enum_draco_DataType_DT_FLOAT64=Module["_emscripten_enum_draco_DataType_DT_FLOAT64"]=Module["asm"]["Ib"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_BOOL=Module["_emscripten_enum_draco_DataType_DT_BOOL"]=function(){return(_emscripten_enum_draco_DataType_DT_BOOL=Module["_emscripten_enum_draco_DataType_DT_BOOL"]=Module["asm"]["Jb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_TYPES_COUNT=Module["_emscripten_enum_draco_DataType_DT_TYPES_COUNT"]=function(){return(_emscripten_enum_draco_DataType_DT_TYPES_COUNT=Module["_emscripten_enum_draco_DataType_DT_TYPES_COUNT"]=Module["asm"]["Kb"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_OK=Module["_emscripten_enum_draco_StatusCode_OK"]=function(){return(_emscripten_enum_draco_StatusCode_OK=Module["_emscripten_enum_draco_StatusCode_OK"]=Module["asm"]["Lb"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_DRACO_ERROR=Module["_emscripten_enum_draco_StatusCode_DRACO_ERROR"]=function(){return(_emscripten_enum_draco_StatusCode_DRACO_ERROR=Module["_emscripten_enum_draco_StatusCode_DRACO_ERROR"]=Module["asm"]["Mb"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_IO_ERROR=Module["_emscripten_enum_draco_StatusCode_IO_ERROR"]=function(){return(_emscripten_enum_draco_StatusCode_IO_ERROR=Module["_emscripten_enum_draco_StatusCode_IO_ERROR"]=Module["asm"]["Nb"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_INVALID_PARAMETER=Module["_emscripten_enum_draco_StatusCode_INVALID_PARAMETER"]=function(){return(_emscripten_enum_draco_StatusCode_INVALID_PARAMETER=Module["_emscripten_enum_draco_StatusCode_INVALID_PARAMETER"]=Module["asm"]["Ob"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION=Module["_emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION"]=function(){return(_emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION=Module["_emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION"]=Module["asm"]["Pb"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_UNKNOWN_VERSION=Module["_emscripten_enum_draco_StatusCode_UNKNOWN_VERSION"]=function(){return(_emscripten_enum_draco_StatusCode_UNKNOWN_VERSION=Module["_emscripten_enum_draco_StatusCode_UNKNOWN_VERSION"]=Module["asm"]["Qb"]).apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return(_malloc=Module["_malloc"]=Module["asm"]["Rb"]).apply(null,arguments)};var _free=Module["_free"]=function(){return(_free=Module["_free"]=Module["asm"]["Sb"]).apply(null,arguments)};var calledRun;function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}Module["run"]=run;if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}run();function WrapperObject(){}WrapperObject.prototype=Object.create(WrapperObject.prototype);WrapperObject.prototype.constructor=WrapperObject;WrapperObject.prototype.__class__=WrapperObject;WrapperObject.__cache__={};Module["WrapperObject"]=WrapperObject;function getCache(__class__){return(__class__||WrapperObject).__cache__}Module["getCache"]=getCache;function wrapPointer(ptr,__class__){var cache=getCache(__class__);var ret=cache[ptr];if(ret)return ret;ret=Object.create((__class__||WrapperObject).prototype);ret.ptr=ptr;return cache[ptr]=ret}Module["wrapPointer"]=wrapPointer;function castObject(obj,__class__){return wrapPointer(obj.ptr,__class__)}Module["castObject"]=castObject;Module["NULL"]=wrapPointer(0);function destroy(obj){if(!obj["__destroy__"])throw"Error: Cannot destroy object. (Did you create it yourself?)";obj["__destroy__"]();delete getCache(obj.__class__)[obj.ptr]}Module["destroy"]=destroy;function compare(obj1,obj2){return obj1.ptr===obj2.ptr}Module["compare"]=compare;function getPointer(obj){return obj.ptr}Module["getPointer"]=getPointer;function getClass(obj){return obj.__class__}Module["getClass"]=getClass;var ensureCache={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(ensureCache.needed){for(var i=0;i<ensureCache.temps.length;i++){Module["_free"](ensureCache.temps[i])}ensureCache.temps.length=0;Module["_free"](ensureCache.buffer);ensureCache.buffer=0;ensureCache.size+=ensureCache.needed;ensureCache.needed=0}if(!ensureCache.buffer){ensureCache.size+=128;ensureCache.buffer=Module["_malloc"](ensureCache.size);assert(ensureCache.buffer)}ensureCache.pos=0},alloc:function(array,view){assert(ensureCache.buffer);var bytes=view.BYTES_PER_ELEMENT;var len=array.length*bytes;len=len+7&-8;var ret;if(ensureCache.pos+len>=ensureCache.size){assert(len>0);ensureCache.needed+=len;ret=Module["_malloc"](len);ensureCache.temps.push(ret)}else{ret=ensureCache.buffer+ensureCache.pos;ensureCache.pos+=len}return ret},copy:function(array,view,offset){offset>>>=0;var bytes=view.BYTES_PER_ELEMENT;switch(bytes){case 2:offset>>>=1;break;case 4:offset>>>=2;break;case 8:offset>>>=3;break}for(var i=0;i<array.length;i++){view[offset+i]=array[i]}}};function ensureString(value){if(typeof value==="string"){var intArray=intArrayFromString(value);var offset=ensureCache.alloc(intArray,HEAP8);ensureCache.copy(intArray,HEAP8,offset);return offset}return value}function ensureInt8(value){if(typeof value==="object"){var offset=ensureCache.alloc(value,HEAP8);ensureCache.copy(value,HEAP8,offset);return offset}return value}function VoidPtr(){throw"cannot construct a VoidPtr, no constructor in IDL"}VoidPtr.prototype=Object.create(WrapperObject.prototype);VoidPtr.prototype.constructor=VoidPtr;VoidPtr.prototype.__class__=VoidPtr;VoidPtr.__cache__={};Module["VoidPtr"]=VoidPtr;VoidPtr.prototype["__destroy__"]=VoidPtr.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_VoidPtr___destroy___0(self)};function DecoderBuffer(){this.ptr=_emscripten_bind_DecoderBuffer_DecoderBuffer_0();getCache(DecoderBuffer)[this.ptr]=this}DecoderBuffer.prototype=Object.create(WrapperObject.prototype);DecoderBuffer.prototype.constructor=DecoderBuffer;DecoderBuffer.prototype.__class__=DecoderBuffer;DecoderBuffer.__cache__={};Module["DecoderBuffer"]=DecoderBuffer;DecoderBuffer.prototype["Init"]=DecoderBuffer.prototype.Init=function(data,data_size){var self=this.ptr;ensureCache.prepare();if(typeof data=="object"){data=ensureInt8(data)}if(data_size&&typeof data_size==="object")data_size=data_size.ptr;_emscripten_bind_DecoderBuffer_Init_2(self,data,data_size)};DecoderBuffer.prototype["__destroy__"]=DecoderBuffer.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DecoderBuffer___destroy___0(self)};function AttributeTransformData(){this.ptr=_emscripten_bind_AttributeTransformData_AttributeTransformData_0();getCache(AttributeTransformData)[this.ptr]=this}AttributeTransformData.prototype=Object.create(WrapperObject.prototype);AttributeTransformData.prototype.constructor=AttributeTransformData;AttributeTransformData.prototype.__class__=AttributeTransformData;AttributeTransformData.__cache__={};Module["AttributeTransformData"]=AttributeTransformData;AttributeTransformData.prototype["transform_type"]=AttributeTransformData.prototype.transform_type=function(){var self=this.ptr;return _emscripten_bind_AttributeTransformData_transform_type_0(self)};AttributeTransformData.prototype["__destroy__"]=AttributeTransformData.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_AttributeTransformData___destroy___0(self)};function GeometryAttribute(){this.ptr=_emscripten_bind_GeometryAttribute_GeometryAttribute_0();getCache(GeometryAttribute)[this.ptr]=this}GeometryAttribute.prototype=Object.create(WrapperObject.prototype);GeometryAttribute.prototype.constructor=GeometryAttribute;GeometryAttribute.prototype.__class__=GeometryAttribute;GeometryAttribute.__cache__={};Module["GeometryAttribute"]=GeometryAttribute;GeometryAttribute.prototype["__destroy__"]=GeometryAttribute.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_GeometryAttribute___destroy___0(self)};function PointAttribute(){this.ptr=_emscripten_bind_PointAttribute_PointAttribute_0();getCache(PointAttribute)[this.ptr]=this}PointAttribute.prototype=Object.create(WrapperObject.prototype);PointAttribute.prototype.constructor=PointAttribute;PointAttribute.prototype.__class__=PointAttribute;PointAttribute.__cache__={};Module["PointAttribute"]=PointAttribute;PointAttribute.prototype["size"]=PointAttribute.prototype.size=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_size_0(self)};PointAttribute.prototype["GetAttributeTransformData"]=PointAttribute.prototype.GetAttributeTransformData=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_PointAttribute_GetAttributeTransformData_0(self),AttributeTransformData)};PointAttribute.prototype["attribute_type"]=PointAttribute.prototype.attribute_type=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_attribute_type_0(self)};PointAttribute.prototype["data_type"]=PointAttribute.prototype.data_type=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_data_type_0(self)};PointAttribute.prototype["num_components"]=PointAttribute.prototype.num_components=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_num_components_0(self)};PointAttribute.prototype["normalized"]=PointAttribute.prototype.normalized=function(){var self=this.ptr;return!!_emscripten_bind_PointAttribute_normalized_0(self)};PointAttribute.prototype["byte_stride"]=PointAttribute.prototype.byte_stride=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_byte_stride_0(self)};PointAttribute.prototype["byte_offset"]=PointAttribute.prototype.byte_offset=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_byte_offset_0(self)};PointAttribute.prototype["unique_id"]=PointAttribute.prototype.unique_id=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_unique_id_0(self)};PointAttribute.prototype["__destroy__"]=PointAttribute.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_PointAttribute___destroy___0(self)};function AttributeQuantizationTransform(){this.ptr=_emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0();getCache(AttributeQuantizationTransform)[this.ptr]=this}AttributeQuantizationTransform.prototype=Object.create(WrapperObject.prototype);AttributeQuantizationTransform.prototype.constructor=AttributeQuantizationTransform;AttributeQuantizationTransform.prototype.__class__=AttributeQuantizationTransform;AttributeQuantizationTransform.__cache__={};Module["AttributeQuantizationTransform"]=AttributeQuantizationTransform;AttributeQuantizationTransform.prototype["InitFromAttribute"]=AttributeQuantizationTransform.prototype.InitFromAttribute=function(att){var self=this.ptr;if(att&&typeof att==="object")att=att.ptr;return!!_emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1(self,att)};AttributeQuantizationTransform.prototype["quantization_bits"]=AttributeQuantizationTransform.prototype.quantization_bits=function(){var self=this.ptr;return _emscripten_bind_AttributeQuantizationTransform_quantization_bits_0(self)};AttributeQuantizationTransform.prototype["min_value"]=AttributeQuantizationTransform.prototype.min_value=function(axis){var self=this.ptr;if(axis&&typeof axis==="object")axis=axis.ptr;return _emscripten_bind_AttributeQuantizationTransform_min_value_1(self,axis)};AttributeQuantizationTransform.prototype["range"]=AttributeQuantizationTransform.prototype.range=function(){var self=this.ptr;return _emscripten_bind_AttributeQuantizationTransform_range_0(self)};AttributeQuantizationTransform.prototype["__destroy__"]=AttributeQuantizationTransform.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_AttributeQuantizationTransform___destroy___0(self)};function AttributeOctahedronTransform(){this.ptr=_emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0();getCache(AttributeOctahedronTransform)[this.ptr]=this}AttributeOctahedronTransform.prototype=Object.create(WrapperObject.prototype);AttributeOctahedronTransform.prototype.constructor=AttributeOctahedronTransform;AttributeOctahedronTransform.prototype.__class__=AttributeOctahedronTransform;AttributeOctahedronTransform.__cache__={};Module["AttributeOctahedronTransform"]=AttributeOctahedronTransform;AttributeOctahedronTransform.prototype["InitFromAttribute"]=AttributeOctahedronTransform.prototype.InitFromAttribute=function(att){var self=this.ptr;if(att&&typeof att==="object")att=att.ptr;return!!_emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1(self,att)};AttributeOctahedronTransform.prototype["quantization_bits"]=AttributeOctahedronTransform.prototype.quantization_bits=function(){var self=this.ptr;return _emscripten_bind_AttributeOctahedronTransform_quantization_bits_0(self)};AttributeOctahedronTransform.prototype["__destroy__"]=AttributeOctahedronTransform.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_AttributeOctahedronTransform___destroy___0(self)};function PointCloud(){this.ptr=_emscripten_bind_PointCloud_PointCloud_0();getCache(PointCloud)[this.ptr]=this}PointCloud.prototype=Object.create(WrapperObject.prototype);PointCloud.prototype.constructor=PointCloud;PointCloud.prototype.__class__=PointCloud;PointCloud.__cache__={};Module["PointCloud"]=PointCloud;PointCloud.prototype["num_attributes"]=PointCloud.prototype.num_attributes=function(){var self=this.ptr;return _emscripten_bind_PointCloud_num_attributes_0(self)};PointCloud.prototype["num_points"]=PointCloud.prototype.num_points=function(){var self=this.ptr;return _emscripten_bind_PointCloud_num_points_0(self)};PointCloud.prototype["__destroy__"]=PointCloud.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_PointCloud___destroy___0(self)};function Mesh(){this.ptr=_emscripten_bind_Mesh_Mesh_0();getCache(Mesh)[this.ptr]=this}Mesh.prototype=Object.create(WrapperObject.prototype);Mesh.prototype.constructor=Mesh;Mesh.prototype.__class__=Mesh;Mesh.__cache__={};Module["Mesh"]=Mesh;Mesh.prototype["num_faces"]=Mesh.prototype.num_faces=function(){var self=this.ptr;return _emscripten_bind_Mesh_num_faces_0(self)};Mesh.prototype["num_attributes"]=Mesh.prototype.num_attributes=function(){var self=this.ptr;return _emscripten_bind_Mesh_num_attributes_0(self)};Mesh.prototype["num_points"]=Mesh.prototype.num_points=function(){var self=this.ptr;return _emscripten_bind_Mesh_num_points_0(self)};Mesh.prototype["__destroy__"]=Mesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Mesh___destroy___0(self)};function Metadata(){this.ptr=_emscripten_bind_Metadata_Metadata_0();getCache(Metadata)[this.ptr]=this}Metadata.prototype=Object.create(WrapperObject.prototype);Metadata.prototype.constructor=Metadata;Metadata.prototype.__class__=Metadata;Metadata.__cache__={};Module["Metadata"]=Metadata;Metadata.prototype["__destroy__"]=Metadata.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Metadata___destroy___0(self)};function Status(){throw"cannot construct a Status, no constructor in IDL"}Status.prototype=Object.create(WrapperObject.prototype);Status.prototype.constructor=Status;Status.prototype.__class__=Status;Status.__cache__={};Module["Status"]=Status;Status.prototype["code"]=Status.prototype.code=function(){var self=this.ptr;return _emscripten_bind_Status_code_0(self)};Status.prototype["ok"]=Status.prototype.ok=function(){var self=this.ptr;return!!_emscripten_bind_Status_ok_0(self)};Status.prototype["error_msg"]=Status.prototype.error_msg=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_Status_error_msg_0(self))};Status.prototype["__destroy__"]=Status.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Status___destroy___0(self)};function DracoFloat32Array(){this.ptr=_emscripten_bind_DracoFloat32Array_DracoFloat32Array_0();getCache(DracoFloat32Array)[this.ptr]=this}DracoFloat32Array.prototype=Object.create(WrapperObject.prototype);DracoFloat32Array.prototype.constructor=DracoFloat32Array;DracoFloat32Array.prototype.__class__=DracoFloat32Array;DracoFloat32Array.__cache__={};Module["DracoFloat32Array"]=DracoFloat32Array;DracoFloat32Array.prototype["GetValue"]=DracoFloat32Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoFloat32Array_GetValue_1(self,index)};DracoFloat32Array.prototype["size"]=DracoFloat32Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoFloat32Array_size_0(self)};DracoFloat32Array.prototype["__destroy__"]=DracoFloat32Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoFloat32Array___destroy___0(self)};function DracoInt8Array(){this.ptr=_emscripten_bind_DracoInt8Array_DracoInt8Array_0();getCache(DracoInt8Array)[this.ptr]=this}DracoInt8Array.prototype=Object.create(WrapperObject.prototype);DracoInt8Array.prototype.constructor=DracoInt8Array;DracoInt8Array.prototype.__class__=DracoInt8Array;DracoInt8Array.__cache__={};Module["DracoInt8Array"]=DracoInt8Array;DracoInt8Array.prototype["GetValue"]=DracoInt8Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoInt8Array_GetValue_1(self,index)};DracoInt8Array.prototype["size"]=DracoInt8Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoInt8Array_size_0(self)};DracoInt8Array.prototype["__destroy__"]=DracoInt8Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoInt8Array___destroy___0(self)};function DracoUInt8Array(){this.ptr=_emscripten_bind_DracoUInt8Array_DracoUInt8Array_0();getCache(DracoUInt8Array)[this.ptr]=this}DracoUInt8Array.prototype=Object.create(WrapperObject.prototype);DracoUInt8Array.prototype.constructor=DracoUInt8Array;DracoUInt8Array.prototype.__class__=DracoUInt8Array;DracoUInt8Array.__cache__={};Module["DracoUInt8Array"]=DracoUInt8Array;DracoUInt8Array.prototype["GetValue"]=DracoUInt8Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoUInt8Array_GetValue_1(self,index)};DracoUInt8Array.prototype["size"]=DracoUInt8Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoUInt8Array_size_0(self)};DracoUInt8Array.prototype["__destroy__"]=DracoUInt8Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoUInt8Array___destroy___0(self)};function DracoInt16Array(){this.ptr=_emscripten_bind_DracoInt16Array_DracoInt16Array_0();getCache(DracoInt16Array)[this.ptr]=this}DracoInt16Array.prototype=Object.create(WrapperObject.prototype);DracoInt16Array.prototype.constructor=DracoInt16Array;DracoInt16Array.prototype.__class__=DracoInt16Array;DracoInt16Array.__cache__={};Module["DracoInt16Array"]=DracoInt16Array;DracoInt16Array.prototype["GetValue"]=DracoInt16Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoInt16Array_GetValue_1(self,index)};DracoInt16Array.prototype["size"]=DracoInt16Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoInt16Array_size_0(self)};DracoInt16Array.prototype["__destroy__"]=DracoInt16Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoInt16Array___destroy___0(self)};function DracoUInt16Array(){this.ptr=_emscripten_bind_DracoUInt16Array_DracoUInt16Array_0();getCache(DracoUInt16Array)[this.ptr]=this}DracoUInt16Array.prototype=Object.create(WrapperObject.prototype);DracoUInt16Array.prototype.constructor=DracoUInt16Array;DracoUInt16Array.prototype.__class__=DracoUInt16Array;DracoUInt16Array.__cache__={};Module["DracoUInt16Array"]=DracoUInt16Array;DracoUInt16Array.prototype["GetValue"]=DracoUInt16Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoUInt16Array_GetValue_1(self,index)};DracoUInt16Array.prototype["size"]=DracoUInt16Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoUInt16Array_size_0(self)};DracoUInt16Array.prototype["__destroy__"]=DracoUInt16Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoUInt16Array___destroy___0(self)};function DracoInt32Array(){this.ptr=_emscripten_bind_DracoInt32Array_DracoInt32Array_0();getCache(DracoInt32Array)[this.ptr]=this}DracoInt32Array.prototype=Object.create(WrapperObject.prototype);DracoInt32Array.prototype.constructor=DracoInt32Array;DracoInt32Array.prototype.__class__=DracoInt32Array;DracoInt32Array.__cache__={};Module["DracoInt32Array"]=DracoInt32Array;DracoInt32Array.prototype["GetValue"]=DracoInt32Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoInt32Array_GetValue_1(self,index)};DracoInt32Array.prototype["size"]=DracoInt32Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoInt32Array_size_0(self)};DracoInt32Array.prototype["__destroy__"]=DracoInt32Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoInt32Array___destroy___0(self)};function DracoUInt32Array(){this.ptr=_emscripten_bind_DracoUInt32Array_DracoUInt32Array_0();getCache(DracoUInt32Array)[this.ptr]=this}DracoUInt32Array.prototype=Object.create(WrapperObject.prototype);DracoUInt32Array.prototype.constructor=DracoUInt32Array;DracoUInt32Array.prototype.__class__=DracoUInt32Array;DracoUInt32Array.__cache__={};Module["DracoUInt32Array"]=DracoUInt32Array;DracoUInt32Array.prototype["GetValue"]=DracoUInt32Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoUInt32Array_GetValue_1(self,index)};DracoUInt32Array.prototype["size"]=DracoUInt32Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoUInt32Array_size_0(self)};DracoUInt32Array.prototype["__destroy__"]=DracoUInt32Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoUInt32Array___destroy___0(self)};function MetadataQuerier(){this.ptr=_emscripten_bind_MetadataQuerier_MetadataQuerier_0();getCache(MetadataQuerier)[this.ptr]=this}MetadataQuerier.prototype=Object.create(WrapperObject.prototype);MetadataQuerier.prototype.constructor=MetadataQuerier;MetadataQuerier.prototype.__class__=MetadataQuerier;MetadataQuerier.__cache__={};Module["MetadataQuerier"]=MetadataQuerier;MetadataQuerier.prototype["HasEntry"]=MetadataQuerier.prototype.HasEntry=function(metadata,entry_name){var self=this.ptr;ensureCache.prepare();if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_name&&typeof entry_name==="object")entry_name=entry_name.ptr;else entry_name=ensureString(entry_name);return!!_emscripten_bind_MetadataQuerier_HasEntry_2(self,metadata,entry_name)};MetadataQuerier.prototype["GetIntEntry"]=MetadataQuerier.prototype.GetIntEntry=function(metadata,entry_name){var self=this.ptr;ensureCache.prepare();if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_name&&typeof entry_name==="object")entry_name=entry_name.ptr;else entry_name=ensureString(entry_name);return _emscripten_bind_MetadataQuerier_GetIntEntry_2(self,metadata,entry_name)};MetadataQuerier.prototype["GetIntEntryArray"]=MetadataQuerier.prototype.GetIntEntryArray=function(metadata,entry_name,out_values){var self=this.ptr;ensureCache.prepare();if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_name&&typeof entry_name==="object")entry_name=entry_name.ptr;else entry_name=ensureString(entry_name);if(out_values&&typeof out_values==="object")out_values=out_values.ptr;_emscripten_bind_MetadataQuerier_GetIntEntryArray_3(self,metadata,entry_name,out_values)};MetadataQuerier.prototype["GetDoubleEntry"]=MetadataQuerier.prototype.GetDoubleEntry=function(metadata,entry_name){var self=this.ptr;ensureCache.prepare();if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_name&&typeof entry_name==="object")entry_name=entry_name.ptr;else entry_name=ensureString(entry_name);return _emscripten_bind_MetadataQuerier_GetDoubleEntry_2(self,metadata,entry_name)};MetadataQuerier.prototype["GetStringEntry"]=MetadataQuerier.prototype.GetStringEntry=function(metadata,entry_name){var self=this.ptr;ensureCache.prepare();if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_name&&typeof entry_name==="object")entry_name=entry_name.ptr;else entry_name=ensureString(entry_name);return UTF8ToString(_emscripten_bind_MetadataQuerier_GetStringEntry_2(self,metadata,entry_name))};MetadataQuerier.prototype["NumEntries"]=MetadataQuerier.prototype.NumEntries=function(metadata){var self=this.ptr;if(metadata&&typeof metadata==="object")metadata=metadata.ptr;return _emscripten_bind_MetadataQuerier_NumEntries_1(self,metadata)};MetadataQuerier.prototype["GetEntryName"]=MetadataQuerier.prototype.GetEntryName=function(metadata,entry_id){var self=this.ptr;if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_id&&typeof entry_id==="object")entry_id=entry_id.ptr;return UTF8ToString(_emscripten_bind_MetadataQuerier_GetEntryName_2(self,metadata,entry_id))};MetadataQuerier.prototype["__destroy__"]=MetadataQuerier.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_MetadataQuerier___destroy___0(self)};function Decoder(){this.ptr=_emscripten_bind_Decoder_Decoder_0();getCache(Decoder)[this.ptr]=this}Decoder.prototype=Object.create(WrapperObject.prototype);Decoder.prototype.constructor=Decoder;Decoder.prototype.__class__=Decoder;Decoder.__cache__={};Module["Decoder"]=Decoder;Decoder.prototype["DecodeArrayToPointCloud"]=Decoder.prototype.DecodeArrayToPointCloud=function(data,data_size,out_point_cloud){var self=this.ptr;ensureCache.prepare();if(typeof data=="object"){data=ensureInt8(data)}if(data_size&&typeof data_size==="object")data_size=data_size.ptr;if(out_point_cloud&&typeof out_point_cloud==="object")out_point_cloud=out_point_cloud.ptr;return wrapPointer(_emscripten_bind_Decoder_DecodeArrayToPointCloud_3(self,data,data_size,out_point_cloud),Status)};Decoder.prototype["DecodeArrayToMesh"]=Decoder.prototype.DecodeArrayToMesh=function(data,data_size,out_mesh){var self=this.ptr;ensureCache.prepare();if(typeof data=="object"){data=ensureInt8(data)}if(data_size&&typeof data_size==="object")data_size=data_size.ptr;if(out_mesh&&typeof out_mesh==="object")out_mesh=out_mesh.ptr;return wrapPointer(_emscripten_bind_Decoder_DecodeArrayToMesh_3(self,data,data_size,out_mesh),Status)};Decoder.prototype["GetAttributeId"]=Decoder.prototype.GetAttributeId=function(pc,type){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(type&&typeof type==="object")type=type.ptr;return _emscripten_bind_Decoder_GetAttributeId_2(self,pc,type)};Decoder.prototype["GetAttributeIdByName"]=Decoder.prototype.GetAttributeIdByName=function(pc,name){var self=this.ptr;ensureCache.prepare();if(pc&&typeof pc==="object")pc=pc.ptr;if(name&&typeof name==="object")name=name.ptr;else name=ensureString(name);return _emscripten_bind_Decoder_GetAttributeIdByName_2(self,pc,name)};Decoder.prototype["GetAttributeIdByMetadataEntry"]=Decoder.prototype.GetAttributeIdByMetadataEntry=function(pc,name,value){var self=this.ptr;ensureCache.prepare();if(pc&&typeof pc==="object")pc=pc.ptr;if(name&&typeof name==="object")name=name.ptr;else name=ensureString(name);if(value&&typeof value==="object")value=value.ptr;else value=ensureString(value);return _emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3(self,pc,name,value)};Decoder.prototype["GetAttribute"]=Decoder.prototype.GetAttribute=function(pc,att_id){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(att_id&&typeof att_id==="object")att_id=att_id.ptr;return wrapPointer(_emscripten_bind_Decoder_GetAttribute_2(self,pc,att_id),PointAttribute)};Decoder.prototype["GetAttributeByUniqueId"]=Decoder.prototype.GetAttributeByUniqueId=function(pc,unique_id){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(unique_id&&typeof unique_id==="object")unique_id=unique_id.ptr;return wrapPointer(_emscripten_bind_Decoder_GetAttributeByUniqueId_2(self,pc,unique_id),PointAttribute)};Decoder.prototype["GetMetadata"]=Decoder.prototype.GetMetadata=function(pc){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;return wrapPointer(_emscripten_bind_Decoder_GetMetadata_1(self,pc),Metadata)};Decoder.prototype["GetAttributeMetadata"]=Decoder.prototype.GetAttributeMetadata=function(pc,att_id){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(att_id&&typeof att_id==="object")att_id=att_id.ptr;return wrapPointer(_emscripten_bind_Decoder_GetAttributeMetadata_2(self,pc,att_id),Metadata)};Decoder.prototype["GetFaceFromMesh"]=Decoder.prototype.GetFaceFromMesh=function(m,face_id,out_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(face_id&&typeof face_id==="object")face_id=face_id.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetFaceFromMesh_3(self,m,face_id,out_values)};Decoder.prototype["GetTriangleStripsFromMesh"]=Decoder.prototype.GetTriangleStripsFromMesh=function(m,strip_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(strip_values&&typeof strip_values==="object")strip_values=strip_values.ptr;return _emscripten_bind_Decoder_GetTriangleStripsFromMesh_2(self,m,strip_values)};Decoder.prototype["GetTrianglesUInt16Array"]=Decoder.prototype.GetTrianglesUInt16Array=function(m,out_size,out_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetTrianglesUInt16Array_3(self,m,out_size,out_values)};Decoder.prototype["GetTrianglesUInt32Array"]=Decoder.prototype.GetTrianglesUInt32Array=function(m,out_size,out_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetTrianglesUInt32Array_3(self,m,out_size,out_values)};Decoder.prototype["GetAttributeFloat"]=Decoder.prototype.GetAttributeFloat=function(pa,att_index,out_values){var self=this.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(att_index&&typeof att_index==="object")att_index=att_index.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeFloat_3(self,pa,att_index,out_values)};Decoder.prototype["GetAttributeFloatForAllPoints"]=Decoder.prototype.GetAttributeFloatForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeIntForAllPoints"]=Decoder.prototype.GetAttributeIntForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeIntForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeInt8ForAllPoints"]=Decoder.prototype.GetAttributeInt8ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeUInt8ForAllPoints"]=Decoder.prototype.GetAttributeUInt8ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeInt16ForAllPoints"]=Decoder.prototype.GetAttributeInt16ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeUInt16ForAllPoints"]=Decoder.prototype.GetAttributeUInt16ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeInt32ForAllPoints"]=Decoder.prototype.GetAttributeInt32ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeUInt32ForAllPoints"]=Decoder.prototype.GetAttributeUInt32ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeDataArrayForAllPoints"]=Decoder.prototype.GetAttributeDataArrayForAllPoints=function(pc,pa,data_type,out_size,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(data_type&&typeof data_type==="object")data_type=data_type.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5(self,pc,pa,data_type,out_size,out_values)};Decoder.prototype["SkipAttributeTransform"]=Decoder.prototype.SkipAttributeTransform=function(att_type){var self=this.ptr;if(att_type&&typeof att_type==="object")att_type=att_type.ptr;_emscripten_bind_Decoder_SkipAttributeTransform_1(self,att_type)};Decoder.prototype["GetEncodedGeometryType_Deprecated"]=Decoder.prototype.GetEncodedGeometryType_Deprecated=function(in_buffer){var self=this.ptr;if(in_buffer&&typeof in_buffer==="object")in_buffer=in_buffer.ptr;return _emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1(self,in_buffer)};Decoder.prototype["DecodeBufferToPointCloud"]=Decoder.prototype.DecodeBufferToPointCloud=function(in_buffer,out_point_cloud){var self=this.ptr;if(in_buffer&&typeof in_buffer==="object")in_buffer=in_buffer.ptr;if(out_point_cloud&&typeof out_point_cloud==="object")out_point_cloud=out_point_cloud.ptr;return wrapPointer(_emscripten_bind_Decoder_DecodeBufferToPointCloud_2(self,in_buffer,out_point_cloud),Status)};Decoder.prototype["DecodeBufferToMesh"]=Decoder.prototype.DecodeBufferToMesh=function(in_buffer,out_mesh){var self=this.ptr;if(in_buffer&&typeof in_buffer==="object")in_buffer=in_buffer.ptr;if(out_mesh&&typeof out_mesh==="object")out_mesh=out_mesh.ptr;return wrapPointer(_emscripten_bind_Decoder_DecodeBufferToMesh_2(self,in_buffer,out_mesh),Status)};Decoder.prototype["__destroy__"]=Decoder.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Decoder___destroy___0(self)};(function(){function setupEnums(){Module["ATTRIBUTE_INVALID_TRANSFORM"]=_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM();Module["ATTRIBUTE_NO_TRANSFORM"]=_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM();Module["ATTRIBUTE_QUANTIZATION_TRANSFORM"]=_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM();Module["ATTRIBUTE_OCTAHEDRON_TRANSFORM"]=_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM();Module["INVALID"]=_emscripten_enum_draco_GeometryAttribute_Type_INVALID();Module["POSITION"]=_emscripten_enum_draco_GeometryAttribute_Type_POSITION();Module["NORMAL"]=_emscripten_enum_draco_GeometryAttribute_Type_NORMAL();Module["COLOR"]=_emscripten_enum_draco_GeometryAttribute_Type_COLOR();Module["TEX_COORD"]=_emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD();Module["GENERIC"]=_emscripten_enum_draco_GeometryAttribute_Type_GENERIC();Module["INVALID_GEOMETRY_TYPE"]=_emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE();Module["POINT_CLOUD"]=_emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD();Module["TRIANGULAR_MESH"]=_emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH();Module["DT_INVALID"]=_emscripten_enum_draco_DataType_DT_INVALID();Module["DT_INT8"]=_emscripten_enum_draco_DataType_DT_INT8();Module["DT_UINT8"]=_emscripten_enum_draco_DataType_DT_UINT8();Module["DT_INT16"]=_emscripten_enum_draco_DataType_DT_INT16();Module["DT_UINT16"]=_emscripten_enum_draco_DataType_DT_UINT16();Module["DT_INT32"]=_emscripten_enum_draco_DataType_DT_INT32();Module["DT_UINT32"]=_emscripten_enum_draco_DataType_DT_UINT32();Module["DT_INT64"]=_emscripten_enum_draco_DataType_DT_INT64();Module["DT_UINT64"]=_emscripten_enum_draco_DataType_DT_UINT64();Module["DT_FLOAT32"]=_emscripten_enum_draco_DataType_DT_FLOAT32();Module["DT_FLOAT64"]=_emscripten_enum_draco_DataType_DT_FLOAT64();Module["DT_BOOL"]=_emscripten_enum_draco_DataType_DT_BOOL();Module["DT_TYPES_COUNT"]=_emscripten_enum_draco_DataType_DT_TYPES_COUNT();Module["OK"]=_emscripten_enum_draco_StatusCode_OK();Module["DRACO_ERROR"]=_emscripten_enum_draco_StatusCode_DRACO_ERROR();Module["IO_ERROR"]=_emscripten_enum_draco_StatusCode_IO_ERROR();Module["INVALID_PARAMETER"]=_emscripten_enum_draco_StatusCode_INVALID_PARAMETER();Module["UNSUPPORTED_VERSION"]=_emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION();Module["UNKNOWN_VERSION"]=_emscripten_enum_draco_StatusCode_UNKNOWN_VERSION()}if(runtimeInitialized)setupEnums();else addOnInit(setupEnums)})();if(typeof Module["onModuleParsed"]==="function"){Module["onModuleParsed"]()}Module["Decoder"].prototype.GetEncodedGeometryType=function(array){if(array.__class__&&array.__class__===Module.DecoderBuffer){return Module.Decoder.prototype.GetEncodedGeometryType_Deprecated(array)}if(array.byteLength<8)return Module.INVALID_GEOMETRY_TYPE;switch(array[7]){case 0:return Module.POINT_CLOUD;case 1:return Module.TRIANGULAR_MESH;default:return Module.INVALID_GEOMETRY_TYPE}};


  return DracoDecoderModule.ready
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = DracoDecoderModule;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return DracoDecoderModule; });
else if (typeof exports === 'object')
  exports["DracoDecoderModule"] = DracoDecoderModule;
