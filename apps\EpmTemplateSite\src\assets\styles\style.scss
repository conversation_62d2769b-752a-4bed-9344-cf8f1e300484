/**
 * <PERSON>'s Reset CSS v2.0 (http://meyerweb.com/eric/tools/css/reset/)
 * http://cssreset.com
 * 全局初始化样式
 */
html,
body,
div,
span,
applet,
object,
iframe,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video,
input {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font-weight: normal;
  vertical-align: baseline;
  outline: none;
}

body {
  /* px-to-viewport-ignore-next */
  min-width: 1024px; /* px-to-viewport-ignore */
}
html {
  /* px-to-viewport-ignore-next */
  min-width: 1024px; /* px-to-viewport-ignore */
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
section {
  display: block;
}

blockquote,
q {
  quotes: none;
}

blockquote::before,
blockquote::after,
q::before,
q::after {
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* custom */
li {
  list-style: none;
}

button {
  outline: none;
  cursor: pointer;
  background-color: transparent;
  border-style: none;
}

.el-textarea__inner::placeholder {
  color: #999 !important;
}

.el-input__inner::placeholder {
  color: #999 !important;
}

input::-webkit-input-placeholder {
  color: #999 !important;
}

.el-input__inner::-webkit-input-placeholder {
  color: #999 !important;
}

input::placeholder {
  color: #999 !important;
}

textarea::placeholder {
  color: #999 !important;
}

* {
  box-sizing: border-box;
}
html,
body,
#app {
  width: 100%;
  height: 100%;
  font-family: Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif,
    Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbo;
}

/* //滚动条样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 10px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgb(150, 175, 234);
}
::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(249, 222, 222, 0.6);
}
// 过渡
.lowcode-transition {
  transition: all 0.4s;
}
