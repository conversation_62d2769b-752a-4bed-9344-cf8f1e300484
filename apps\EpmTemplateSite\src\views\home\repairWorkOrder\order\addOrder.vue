<template>
  <el-dialog
    :title="$t('repairWorkOrder.equipmentRepairApplication.title')"
    width="60%"
    v-model="dialogShow"
    :append-to-body="true"
    @close="closeFun(false)"
    @open="openFun"
    top="10px"
    class="order-dialog"
    center
  >
    <div class="order-form">
      <div class="mask" v-if="orderOpt.type == 'look'">
        <!-- 查看时给表单一个遮罩 -->
      </div>
      <el-form
        label-width="140"
        class="form"
        :model="orderForm"
        :rules="rules"
        ref="orderFormRef"
        :validate-on-rule-change="false"
        status-icon
        inline-message
      >
        <div class="head">
          <div class="no">
            {{
              $t("repairWorkOrder.equipmentRepairApplication.formNumber")
            }}：{{ orderForm.orderNum }}
          </div>
        </div>
        <el-row>
          <el-col :span="8">
            <el-form-item
              :label="$t('repairWorkOrder.equipmentRepairApplication.date')"
              prop="repairDate"
              label-width="60"
            >
              <el-date-picker
                v-model="orderForm.repairDate"
                type="date"
                style="width: 100%"
                :placeholder="$t('common.chooseText')"
                value-format="YYYY/MM/DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-left: 20px">
            <el-form-item
              :label="
                $t('repairWorkOrder.equipmentRepairApplication.applicant')
              "
              prop="applicant"
              label-width="70"
            >
              <el-input
                v-model="orderForm.applicant"
                :placeholder="$t('common.inputText')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <div class="serialNumber">
              <span
                >{{
                  $t("repairWorkOrder.equipmentRepairApplication.number")
                }}:</span
              >
              <span>{{ orderForm.sn }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row class="out-block">
          <el-col :span="12">
            <el-form-item
              :label="
                $t('repairWorkOrder.equipmentRepairApplication.applicationUnit')
              "
              prop="unit"
            >
              <el-input
                v-model="orderForm.unit"
                :placeholder="$t('common.inputText')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="
                $t('repairWorkOrder.equipmentRepairApplication.productionLine')
              "
              prop="line"
            >
              <el-select
                v-model="orderForm.line"
                :placeholder="$t('common.chooseText')"
              >
                <el-option
                  v-for="item of lineArr"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('repairWorkOrder.equipmentName')"
              prop="devName"
            >
              <el-select
                v-model="orderForm.devName"
                :placeholder="$t('common.chooseText')"
              >
                <el-option
                  v-for="item of devnameArr"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="
                $t('repairWorkOrder.equipmentRepairApplication.equipmentNumber')
              "
              prop="devCode"
            >
              <el-select
                v-model="orderForm.devCode"
                :placeholder="$t('common.chooseText')"
              >
                <el-option
                  v-for="item of devcodeArr"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="$t('repairWorkOrder.faultType')"
              prop="faultType"
            >
              <el-radio-group v-model="orderForm.faultType">
                <el-radio
                  :label="item.value"
                  size="large"
                  v-for="item of faultType"
                  :key="item.value"
                  >{{ item.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="$t('repairWorkOrder.faultDescription')"
              prop="faultPhoenomenon"
            >
              <el-autocomplete
                v-model="orderForm.faultPhoenomenon"
                :fetch-suggestions="querySearch"
                :trigger-on-focus="false"
                clearable
                style="width: 100%"
                :placeholder="$t('common.inputText')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="
                $t('repairWorkOrder.equipmentRepairApplication.faultTime')
              "
              prop="alarmTime"
            >
              <el-date-picker
                v-model="orderForm.alarmTime"
                type="datetime"
                style="width: 100%"
                :placeholder="$t('common.chooseText')"
                value-format="YYYY/MM/DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('repairWorkOrder.reportTime')"
              prop="reportTime"
            >
              <el-date-picker
                v-model="orderForm.reportTime"
                type="datetime"
                style="width: 100%"
                :placeholder="$t('common.chooseText')"
                value-format="YYYY/MM/DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('common.startTime')"
              prop="repairBeginTime"
            >
              <el-date-picker
                v-model="orderForm.repairBeginTime"
                type="datetime"
                style="width: 100%"
                :placeholder="$t('common.chooseText')"
                value-format="YYYY/MM/DD HH:mm:ss"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('repairWorkOrder.timeSection.repairTime')"
              prop="repairEndTime"
            >
              <el-date-picker
                v-model="orderForm.repairEndTime"
                type="datetime"
                style="width: 100%"
                :placeholder="$t('common.chooseText')"
                value-format="YYYY/MM/DD HH:mm:ss"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="$t('repairWorkOrder.consumedParts')"
              prop="sparepart"
            >
              <div class="bj" v-if="props.orderOpt.type == 'look'">
                {{ orderForm.sparepart.join(",") }}
              </div>
              <el-select
                v-model="orderForm.sparepart"
                :placeholder="$t('common.chooseText')"
                multiple
                clearable
                :disabled="readonly"
                v-else
              >
                <el-option
                  v-for="item in materialOptions"
                  :key="item.serialNumber"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="$t('repairWorkOrder.causeAnalysis.label')"
              prop="faultReason"
            >
              <el-input
                v-model="orderForm.faultReason"
                :rows="2"
                type="textarea"
                :placeholder="$t('common.inputText')"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="$t('repairWorkOrder.handlingMethod')"
              prop="faultSolution"
            >
              <el-input
                v-model="orderForm.faultSolution"
                :rows="2"
                type="textarea"
                :placeholder="$t('common.inputText')"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="
                $t('repairWorkOrder.causeAnalysis.relatedToProductQuality')
              "
            >
              <el-radio-group v-model="orderForm.isQuality">
                <el-radio :label="true" size="large">{{
                  $t("repairWorkOrder.qualityInspection.related")
                }}</el-radio>
                <el-radio :label="false" size="large">{{
                  $t("repairWorkOrder.qualityInspection.unrelated")
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="
                $t('repairWorkOrder.causeAnalysis.relatedToSoftwareVersion')
              "
            >
              <el-radio-group v-model="orderForm.isVersion">
                <el-radio :label="true" size="large">{{
                  $t("repairWorkOrder.softwareManagement.related")
                }}</el-radio>
                <el-radio :label="false" size="large">{{
                  $t("repairWorkOrder.qualityInspection.unrelated")
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('repairWorkOrder.maintenancePerson')"
              prop="repairer"
            >
              <el-input
                v-model="orderForm.repairer"
                :placeholder="$t('common.inputText')"
                :readonly="readonly"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$t('repairWorkOrder.acceptanceConfirmation')"
              prop="confirmer"
            >
              <el-input
                v-model="orderForm.confirmer"
                :placeholder="$t('common.inputText')"
                :readonly="readonly"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="btns" v-if="orderOpt.type != 'look'">
        <el-button type="primary" @click="cancel" class="btn" plain>{{
          $t("common.cancelText")
        }}</el-button>
        <el-button
          type="primary"
          @click="save"
          class="btn"
          :disabled="isSubmitting || orderOpt.type === 'look'"
          >{{ $t("common.submitText") }}</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { reactive, ref, onMounted, computed, nextTick } from "vue"
import {
  postWorkOrder,
  putWorkOrder,
  getFuzzySearch,
  getInitNo,
} from "@/api/front/repairOrder.js"
import dayjs from "dayjs"
import useTable from "@/hooks/useTable"
import { config } from "@/config"
import { useStore } from "vuex"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()
let store = useStore()
let props = defineProps({
  addShow: {
    type: Boolean,
    default: false,
  },
  orderOpt: {
    type: Object,
    default: () => {},
  },
})
let emit = defineEmits(["closeDialog"])

const homeServerPrefix = config.base_url.homeServerPrefix

const { getList: getQuickWearPartList } = useTable(
  `${homeServerPrefix}/SparePartsAssembly`,
  false,
  false
)

let orderFormRef = ref()
let orderForm = reactive({
  id: "",
  orderNum: "",
  sn: "",
  repairDate: "",
  applicant: "",
  unit: "",
  line: "",
  devName: "",
  devCode: "",
  faultType: 0,
  faultPhoenomenon: "",
  alarmTime: "",
  reportTime: "",
  repairBeginTime: "",
  repairEndTime: "",
  sparepart: [],
  faultReason: "",
  faultSolution: "",
  isQuality: false,
  isVersion: false,
  isProcessed: false,
  repairer: "",
  confirmer: "",
  state: 0,
})
let faultType = [
  { label: $t("repairWorkOrder.options.shutdown"), value: 0 },
  { label: $t("repairWorkOrder.options.noShutdown"), value: 1 },
  { label: $t("repairWorkOrder.options.speedReduction"), value: 2 },
  { label: $t("repairWorkOrder.options.other"), value: 3 },
]
let materialOptions = ref([])
let lineArr = ref([])
let devnameArr = ref([])
let devcodeArr = ref([])
let sparepartProps = { multiple: true }

let currentUser = computed(() => store.getters.authUser.user)
let readonly = computed(() => props.orderOpt.type !== "handle")
let dialogShow = computed(() => props.addShow)
let rules = computed(() => {
  let inputRule = [
    { required: true, message: $t("common.inputText"), trigger: "blur" },
  ]
  let timeRule = [
    {
      required: true,
      message: $t("repairWorkOrder.common.chooseTime"),
      trigger: "change",
    },
  ]
  let selectRule = [
    { required: true, message: $t("common.chooseText"), trigger: "change" },
  ]
  return props.orderOpt.type == "handle"
    ? {
        repairDate: timeRule,
        applicant: inputRule,
        unit: inputRule,
        line: selectRule,
        devName: selectRule,
        devCode: selectRule,
        faultType: selectRule,
        faultPhoenomenon: inputRule,
        alarmTime: timeRule,
        reportTime: timeRule,
        repairBeginTime: timeRule,
        repairEndTime: timeRule,
        sparepart: selectRule,
        faultReason: inputRule,
        faultSolution: inputRule,
        repairer: inputRule,
        confirmer: inputRule,
      }
    : {
        repairDate: timeRule,
        applicant: inputRule,
        unit: inputRule,
        line: inputRule,
        devName: inputRule,
        devCode: inputRule,
        faultType: selectRule,
        faultPhoenomenon: inputRule,
        alarmTime: timeRule,
        reportTime: timeRule,
      }
})

onMounted(() => {
  getMaterial()
  getValue()
})

async function getOrderNo() {
  let res = await getInitNo()
  orderForm.orderNum = res.orderNum
  orderForm.sn = res.sn
}

function closeFun(val = false) {
  emit("closeDialog", val)
  orderForm.isProcessed = false
  orderFormRef.value.resetFields()
}

function openFun() {
  if (props.orderOpt.type == "add") {
    getOrderNo()
    orderForm.repairDate = dayjs().format("YYYY/MM/DD")
    orderForm.applicant = currentUser.value?.name
    orderForm.devCode = devcodeArr.value[0] || ""
    orderForm.devName = devnameArr.value[0] || ""
    orderForm.line = lineArr.value[0] || ""
  } else {
    for (let key in orderForm) {
      if (key == "sparepart") {
        orderForm[key] = props.orderOpt.data[key]
          ? props.orderOpt.data[key]
          : []
      } else {
        orderForm[key] = props.orderOpt.data[key] ?? ""
      }
    }
  }
  nextTick(() => orderFormRef.value.clearValidate())
}

const isSubmitting = ref(false)

async function save() {
  if (isSubmitting.value) return
  isSubmitting.value = true

  try {
    if (props.orderOpt.type === "edit") {
      await putWorkOrder(orderForm.id, orderForm)
      ElMessage.success($t("repairWorkOrder.messages.updateSuccess"))
      closeFun(true)
    } else if (props.orderOpt.type === "add") {
      const valid = await orderFormRef.value.validate()
      if (valid) {
        await postWorkOrder(orderForm)
        ElMessage.success($t("repairWorkOrder.messages.createSuccess"))
        closeFun(true)
      }
    } else if (props.orderOpt.type === "handle") {
      const valid = await orderFormRef.value.validate()
      if (!valid) return

      await ElMessageBox.confirm(
        `${$t("repairWorkOrder.confirmation.confirmHandle")}?`,
        $t("common.prompt"),
        {
          confirmButtonText: $t("common.okText"),
          cancelButtonText: $t("common.cancelText"),
          type: "warning",
        }
      )

      orderForm.state = 1
      orderForm.isProcessed = true
      await putWorkOrder(orderForm.id, orderForm)
      ElMessage.success($t("repairWorkOrder.messages.handleSuccess"))
      closeFun(true)
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error(error)
    }
  } finally {
    isSubmitting.value = false
  }
}

function cancel() {
  closeFun(false)
}

async function getMaterial() {
  let res = await getQuickWearPartList()
  materialOptions.value = res?.items ?? []
}

async function querySearch(str, cb) {
  let res = await getFuzzySearch({ knowledgeContent: 1, content: str })
  cb(
    res.map(item => ({
      value: item,
    }))
  )
}

function getValue() {
  devcodeArr.value = ["J-B1-SCGD-QD-0004-00"]
  devnameArr.value = [
    $t("repairWorkOrder.equipmentRepairApplication.devNameExample"),
  ]
  lineArr.value = ["L-001"]
  // getEquiNum().then(res => (devcodeArr.value = res))
  // getEquiName().then(res => (devnameArr.value = res))
  // getLineName().then(res => (lineArr.value = res))
}
</script>
<style scoped lang="scss">
.order-form {
  position: relative;
  overflow: auto;
  .mask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 10;
  }
  .head {
    text-align: right;
    color: rgba(43, 43, 43, 0.8);
    margin-bottom: 10px;
  }
  .form {
    .out-block {
      border: 1px solid #797979;
    }
    .no {
      color: rgba(43, 43, 43, 0.8);
    }
    .el-select {
      width: 100%;
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    :deep(.el-form-item__label),
    :deep(.el-textarea__inner) {
      color: var(--g-font-color);
    }
    .el-col-12 {
      border: 1px solid #797979;
      :deep(.el-form-item__label) {
        border-right: 1px solid #797979;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .el-col-24 {
      border: 1px solid #797979;
      :deep(.el-form-item__label) {
        border-right: 1px solid #797979;
        height: 70px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .el-col-8 {
      :deep(.el-form-item__content) {
        border: 1px solid #797979;
        border-radius: 8px;
      }
      :deep(.el-form-item__label) {
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
    }
  }
  .btns {
    margin-top: 20px;
    padding: 0 20px;
    display: flex;
    justify-content: flex-end;
    .btn {
      width: 120px;
    }
  }

  :deep(.el-select .el-select__tags-text) {
    color: var(--g-font-color);
  }
  :deep(.el-tag .el-tag__close) {
    --el-tag-text-color: var(--g-font-color);
  }
  :deep(.el-radio__label) {
    color: var(--g-font-color);
  }
}

:deep(.el-form-item) {
  margin-bottom: 0;
}
.el-col-8 {
  :deep(.el-form-item) {
    margin-bottom: 10px;
  }
}

:deep(.el-input__wrapper) {
  background: #e7e7e7;
  border: none !important;
  box-shadow: none !important;
}

:deep(.el-textarea__inner) {
  border: none !important;
  box-shadow: none !important;
  background: #e7e7e7;
}

.serialNumber {
  height: 100%;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: right;
  span {
    margin-left: 20px;
  }
}
.el-radio-group {
  margin-left: 10px;
}
.bj {
  padding-left: 20px;
}
</style>
<style lang="scss">
.order-dialog {
  background: var(--dialog-body-background);
  border: 1px solid var(--tab-border-color);
  .el-dialog__body {
    padding: 10px 20px;
  }
}
</style>
