import { ref } from "vue"
/**
 * 将后端返回的数据处理成可展示的二级表格
 * @param arr 后端返回的结果数组
 */
const listConvertTree = (arr: any[] = []) => {
  const tableData = ref(arr)
  tableData.value = tableData.value.map(item => ({
    ...item,
    children: [],
    hasChildren: false,
  }))
  // 将子级数据添加到对应父级的children属性中
  for (const item of tableData.value) {
    if (item.dataType.indexOf("Array") !== -1) {
      item.hasChildren = true
    }
  }
  return tableData.value
}

export default listConvertTree
