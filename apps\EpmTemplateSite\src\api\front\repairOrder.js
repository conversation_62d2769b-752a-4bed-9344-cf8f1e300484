import { request } from "@xfe/request"
import { config } from "@/config"
const base = config.base_url.homeServerPrefix

// --------维修工单---------

export function getWorkOrder(params) {
  return request.get({
    url: `${base}/WorkOrder`,
    params,
  })
}

export function postWorkOrder(data) {
  return request.post({
    url: `${base}/WorkOrder`,
    data,
  })
}

export function putWorkOrder(id, data) {
  return request.put({
    url: `${base}/WorkOrder/${id}`,
    data,
  })
}

export function exportWorkOrder(params) {
  return request.get({
    url: `${base}/WorkOrder/export`,
    params,
  })
}

export function getSpareParts(params) {
  return request.get({
    url: `${base}/SpareParts`,
    params,
  })
}

export function getFuzzySearch(params) {
  return request.post(
    {
      url: `${base}/KnowledgeBase/Fuzzy`,
      params,
    },
    { joinParamsToUrl: true }
  )
}

export function getInitNo() {
  return request.get({
    url: `${base}/WorkOrder/Number`,
  })
}

export function getSparePartsTree() {
  return request.get({
    url: `${base}/SpareParts/tree`,
  })
}

export function useMaterials(data) {
  return request.put({
    url: `${base}/VulnerablePartsLife/Inventory`,
    data,
  })
}
//设备编号
export function getEquiNum() {
  return request.get({
    url: `${base}/WorkOrder/GetEquiNum`,
  })
}
//设备名称
export function getEquiName() {
  return request.get({
    url: `${base}/WorkOrder/GetEquiName`,
  })
}
//产线名称
export function getLineName() {
  return request.get({
    url: `${base}/WorkOrder/GetLine`,
  })
}

//  ------------维保计划-----

export function getRepairPlan(params) {
  return request.get({
    url: `${base}/Maintenance`,
    params,
  })
}

export function addRepairPlan(data) {
  return request.post({
    url: `${base}/Maintenance`,
    data,
  })
}

export function delRepairPlan(data) {
  return request.delete({
    url: `${base}/Maintenance`,
    data,
  })
}

export function putRepairPlan(id, data) {
  return request.put({
    url: `${base}/Maintenance/${id}`,
    data,
  })
}
