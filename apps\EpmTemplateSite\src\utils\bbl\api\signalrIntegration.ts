import { ModelStatusService, type RealTimeAlarmData } from './modelStatusService';

/**
 * SignalR原始数据类型
 */
interface SignalRRawData {
  type?: number;
  target?: string;
  arguments?: [string, {
    category: string;
    tags: string[];
    message: Array<{
      alarmCode: string;
      alarmContent: string;
      startTime: string;
      durationSeconds: number;
      moduleCode: string;
      isSparePart: boolean;
      alarmGroup: string | null;
    }>;
  }];
}

/**
 * SignalR集成工具
 * 用于与initSignalr.js进行集成
 */
export class SignalRIntegration {
  private static instance: SignalRIntegration | null = null;
  private modelStatusService: ModelStatusService | null = null;

  private constructor() { }

  /**
   * 获取单例实例
   */
  public static getInstance(): SignalRIntegration {
    if (!SignalRIntegration.instance) {
      SignalRIntegration.instance = new SignalRIntegration();
    }
    return SignalRIntegration.instance;
  }

  /**
   * 设置模型状态服务实例
   * @param service 模型状态服务实例
   */
  public setModelStatusService(service: ModelStatusService): void {
    this.modelStatusService = service;
  }

  /**
   * 处理从initSignalr.js接收到的报警数据
   * 这个方法会被挂载到全局window对象上，供initSignalr.js调用
   * @param rawData 从SignalR接收到的原始数据
   */
  public handleAlarmData(rawData: SignalRRawData | RealTimeAlarmData[]): void {
    try {
      console.log('SignalR集成工具接收到报警数据:', rawData);

      // 解析数据格式
      let alarmInfo: RealTimeAlarmData[] = [];

      if (rawData && 'arguments' in rawData && rawData.arguments && rawData.arguments[1] && rawData.arguments[1].message) {
        // 处理您提供的数据格式
        const messageData = rawData.arguments[1];
        if (Array.isArray(messageData.message)) {
          alarmInfo = messageData.message.map((item) => ({
            alarmCode: item.alarmCode || '',
            alarmContent: item.alarmContent || '',
            startTime: item.startTime || new Date().toISOString(),
            durationSeconds: item.durationSeconds || 0,
            moduleCode: item.moduleCode || '', // moduleCode直接对应模型ID
            isSparePart: item.isSparePart || false,
            alarmGroup: item.alarmGroup || null
          }));
        }
      } else if (Array.isArray(rawData)) {
        // 直接是报警数据数组
        alarmInfo = rawData as RealTimeAlarmData[];
      }

      // 传递给模型状态服务处理
      if (this.modelStatusService && alarmInfo.length > 0) {
        this.modelStatusService.handleSignalRAlarmData(alarmInfo);
      }
    } catch (error) {
      console.error('处理SignalR报警数据时出错:', error);
    }
  }

  /**
   * 初始化全局接口
   * 将报警数据处理函数挂载到window对象上，供initSignalr.js调用
   */
  public initGlobalInterface(): void {
    // 确保window对象上有BBL命名空间
    if (typeof window !== 'undefined') {
      if (!window.BBL) {
        window.BBL = {};
      }

      // 挂载报警数据处理函数
      window.BBL.handleAlarmData = (data: SignalRRawData | RealTimeAlarmData[]) => {
        this.handleAlarmData(data);
      };

      console.log('SignalR集成全局接口已初始化，initSignalr.js可通过window.BBL.handleAlarmData()调用');
    }
  }
}

// 扩展Window接口以包含BBL命名空间
declare global {
  interface Window {
    BBL?: {
      handleAlarmData?: (data: SignalRRawData | RealTimeAlarmData[]) => void;
    };
  }
}

// 自动初始化
const signalrIntegration = SignalRIntegration.getInstance();
signalrIntegration.initGlobalInterface();
