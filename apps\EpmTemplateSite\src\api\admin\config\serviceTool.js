import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 *  导出交互配置表
 */
export function exportExcel() {
  return request.get({
    url: `${homeServerPrefix}/RepairTool/export`,
    header: {
      headers: { "Content-Type": "application/x-download" },
    },
    responseType: "blob",
  })
}

/**
 *  导入交互配置表
 */
export function importExcel(data) {
  return request.post({
    url: `${homeServerPrefix}/RepairTool/import`,
    headers: { "Content-Type": "multipart/form-data" },
    data,
  })
}

// 增
export function postRepairTool(data) {
  return request.post({
    url: `${homeServerPrefix}/RepairTool`,
    headers: { "Content-Type": "multipart/form-data" },
    data: data,
  })
}

// 获取存放位置
export function getRepairTool() {
  return request.get({
    url: `${homeServerPrefix}/RepairTool/localData`,
  })
}

// 改
export function putRepairTool(data) {
  return request.put({
    url: `${homeServerPrefix}/RepairTool`,
    headers: { "Content-Type": "multipart/form-data" },
    data: data,
  })
}

export function getRepairToolByIds(params) {
  return request.get({
    url: `${homeServerPrefix}/RepairTool/ids${params}`,
  })
}
