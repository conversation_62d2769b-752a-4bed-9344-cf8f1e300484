import { ElMessageBox, ElNotification, ElMessage } from "element-plus"

type MsgType = "success" | "warning" | "info" | "error"
type NoticeType = "success" | "warning" | "info" | "error" | ""
export enum NoticePosition {
  BottomRight = "bottom-right",
  BottomLeft = "bottom-left",
  TopLeft = "top-left",
  TopRight = "top-right",
}

export function useMsg(
  message: string,
  type: MsgType,
  duration: number = 2000,
  dangerouslyUseHTMLString: boolean = false,
  options: any = {}
) {
  return ElMessage({
    message,
    type,
    duration,
    showClose: true,
    dangerouslyUseHTMLString,
    ...options,
  })
}

export function useNotice(
  title: string,
  message: string,
  type: NoticeType,
  noticePosition: NoticePosition = NoticePosition.TopRight,
  duration: number = 5000,
  showClose: boolean = false,
  dangerouslyUseHTMLString: boolean = false,
  options: any = {}
) {
  ElNotification({
    title,
    message,
    type,
    position: noticePosition,
    duration,
    dangerouslyUseHTMLString,
    showClose,
    ...options,
  })
}

export enum BoxType {
  Confirm = "confirm",
  Prompt = "prompt",
  Alert = "alert",
}

export function useConfirmMessageBox(
  boxType: BoxType = BoxType.Confirm,
  message: string,
  title: string,
  option: any
) {
  return ElMessageBox[boxType](message, title, { ...option }) // 返回promise 关闭回调可以在catch中捕获
}

export function useMessage() {
  return {
    createMessage: useMsg,
    notification: useNotice,
    createConfirm: useConfirmMessageBox,
  }
}
