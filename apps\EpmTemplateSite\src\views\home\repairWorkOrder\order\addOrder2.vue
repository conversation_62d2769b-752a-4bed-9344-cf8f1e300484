<template>
  <dialog-card
    dialogWidth="55%"
    dialogTitle="设备维修申请单"
    :isVisable="addShow"
    :append-to-body="true"
    @closeDialog="closeFun(false)"
    @openDialog="openFun"
    top="20px"
  >
    <div class="order-form">
      <div class="head">
        <div class="no">表单编号：{{ orderForm.orderNum }}</div>
        <div class="no">编号：{{ orderForm.sn }}</div>
      </div>
      <el-form
        label-width="140"
        class="form"
        :disabled="orderOpt.type == 'look'"
        :model="orderForm"
        :rules="rules"
        ref="orderFormRef"
      >
        <el-row>
          <el-col :span="4">
            <el-form-item label="日期" prop="repairDate">
              <el-date-picker
                v-model="orderForm.repairDate"
                type="date"
                placeholder="日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="申请人" prop="applicant">
              <el-input v-model="orderForm.applicant" placeholder="申请人" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="申请人" prop="applicant">
              <div class="head">
                <div class="no">表单编号：{{ orderForm.orderNum }}</div>
                <div class="no">编号：{{ orderForm.sn }}</div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请单位" prop="unit">
              <el-input v-model="orderForm.unit" placeholder="申请单位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属产线" prop="line">
              <el-input v-model="orderForm.line" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="devName">
              <el-input v-model="orderForm.devName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备编号" prop="devCode">
              <el-input v-model="orderForm.devCode" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障类型" prop="faultType">
              <el-select v-model="orderForm.faultType">
                <el-option
                  v-for="item of faultType"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障描述" prop="faultPhoenomenon">
              <el-autocomplete
                v-model="orderForm.faultPhoenomenon"
                :fetch-suggestions="querySearch"
                :trigger-on-focus="false"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="故障时间" prop="alarmTime">
              <el-date-picker
                v-model="orderForm.alarmTime"
                type="datetime"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报修时间" prop="reportTime">
              <el-date-picker
                v-model="orderForm.reportTime"
                type="datetime"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="repairBeginTime">
              <el-date-picker
                v-model="orderForm.repairBeginTime"
                type="datetime"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="修复时间" prop="repairEndTime">
              <el-date-picker
                v-model="orderForm.repairEndTime"
                type="datetime"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="消耗备件" prop="sparepart">
              <el-cascader
                v-model="orderForm.sparepart"
                :options="materialOptions"
                :props="sparepartProps"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="原因分析" prop="faultReason">
              <el-input
                v-model="orderForm.faultReason"
                :rows="2"
                type="textarea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="处理方法" prop="faultSolution">
              <el-input
                v-model="orderForm.faultSolution"
                :rows="2"
                type="textarea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="与产品质量相关">
              <el-radio-group v-model="orderForm.isQuality">
                <el-radio :label="true" size="large"
                  >相关(质检参与验收确认)</el-radio
                >
                <el-radio :label="false" size="large">不相关</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="与软件版本变化相关">
              <el-radio-group v-model="orderForm.isVersion">
                <el-radio :label="true" size="large"
                  >相关(按《设备软件管理办法》确认软件)</el-radio
                >
                <el-radio :label="false" size="large">不相关</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维修人" prop="repairer">
              <el-input v-model="orderForm.repairer"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="验收确认" prop="confirmer">
              <el-input v-model="orderForm.confirmer"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="btns">
        <el-button
          type="primary"
          @click="save"
          class="btn"
          v-if="orderOpt.type != 'look'"
          >提 交</el-button
        >
        <el-button type="primary" @click="cancel" class="btn">取 消</el-button>
      </div>
    </div>
  </dialog-card>
</template>

<script setup>
import dialogCard from "@/components/hmx-dialog.vue"
import { reactive, ref, onMounted, computed } from "vue"
import {
  postWorkOrder,
  putWorkOrder,
  getFuzzySearch,
  getInitNo,
  getSparePartsTree,
  useMaterials,
} from "@/api/front/repairOrder.js"

let props = defineProps({
  addShow: {
    type: Boolean,
    default: false,
  },
  orderOpt: {
    type: Object,
    default: () => {},
  },
})
let emit = defineEmits(["closeDialog"])

let orderFormRef = ref()
let orderForm = reactive({
  id: "",
  orderNum: "",
  sn: "",
  repairDate: "",
  applicant: "",
  unit: "",
  line: "",
  devName: "",
  devCode: "",
  faultType: "",
  faultPhoenomenon: "",
  alarmTime: "",
  reportTime: "",
  repairBeginTime: "",
  repairEndTime: "",
  sparepart: [],
  faultReason: "",
  faultSolution: "",
  isQuality: 0,
  isVersion: 0,
  repairer: "",
  confirmer: "",
  state: 0,
})
let faultType = [
  { label: "停机", value: 0 },
  { label: "不停机", value: 1 },
  { label: "速度下降", value: 2 },
  { label: "其他", value: 3 },
]
let materialOptions = ref([])
let sparepartProps = { multiple: true }

let rules = computed(() => {
  let inputRule = [{ required: true, message: "请输入", trigger: "blur" }]
  let timeRule = [{ required: true, message: "请选择时间", trigger: "change" }]
  let selectRule = [{ required: true, message: "请选择", trigger: "change" }]
  return props.orderOpt.type == "handle"
    ? {
        repairDate: timeRule,
        applicant: inputRule,
        unit: inputRule,
        line: inputRule,
        devName: inputRule,
        devCode: inputRule,
        faultType: selectRule,
        faultPhoenomenon: inputRule,
        alarmTime: timeRule,
        reportTime: timeRule,
        repairBeginTime: timeRule,
        repairEndTime: timeRule,
        sparepart: selectRule,
        faultReason: inputRule,
        faultSolution: inputRule,
        repairer: inputRule,
        confirmer: inputRule,
      }
    : {
        repairDate: timeRule,
        applicant: inputRule,
        unit: inputRule,
        line: inputRule,
        devName: inputRule,
        devCode: inputRule,
        faultType: selectRule,
        faultPhoenomenon: inputRule,
        alarmTime: timeRule,
        reportTime: timeRule,
      }
})

onMounted(() => {
  getMaterial()
})

async function getOrderNo() {
  let res = await getInitNo()
  orderForm.orderNum = res.orderNum
  orderForm.sn = res.sn
}

function closeFun(val = false) {
  emit("closeDialog", val)
}

function openFun() {
  if (props.orderOpt.type == "add") {
    for (let key in orderForm) {
      if (key == "isQuality" || key == "isVersion") {
        orderForm[key] = 0
      } else if (key == "sparepart") {
        orderForm[key] = ["15716d92-f0cd-489c-a17e-f45d19fc2916"]
      } else {
        orderForm[key] = ""
      }
    }
    getOrderNo()
  } else {
    for (let key in orderForm) {
      if (key == "sparepart") {
        orderForm[key] = props.orderOpt.data[key]
          ? props.orderOpt.data[key]
          : []
      } else {
        orderForm[key] = props.orderOpt.data[key] ?? ""
      }
    }
  }
}

async function save() {
  if (orderForm.sparepart.length > 0) {
    let arr = orderForm.sparepart.map(item => item?.[1] ?? null)
    orderForm.sparepart = arr
  }
  if (props.orderOpt.type == "edit") {
    await putWorkOrder(orderForm.id, orderForm)
    ElMessage.success("工单修改成功")
    closeFun(true)
  } else if (props.orderOpt.type == "add") {
    orderFormRef.value.validate(async val => {
      if (val) {
        await postWorkOrder(orderForm)
        ElMessage.success("新增工单成功")
        closeFun(true)
      }
    })
  } else if (props.orderOpt.type == "handle") {
    orderFormRef.value.validate(async val => {
      if (val) {
        orderForm.state = 1
        await putWorkOrder(orderForm.id, orderForm)

        let res = await useSparepart()
        res
          ? ElMessage.success("工单处理成功")
          : ElMessage.error("易损件材料记录出错")
        closeFun(true)
      }
    })
  }
}
async function useSparepart() {
  try {
    await useMaterials(orderForm.sparepart)
    return true
  } catch {
    return false
  }
}
function cancel() {
  closeFun(false)
}

async function getMaterial() {
  let res = await getSparePartsTree()
  materialOptions.value = res ?? []
}

async function querySearch(str, cb) {
  let res = await getFuzzySearch({ phenomenon: str })
  cb(
    res.map(item => ({
      value: item.phenomenon,
    }))
  )
}
</script>
<style scoped lang="scss">
.order-form {
  .head {
    padding: 0 20px;
    text-align: right;
    color: #fff;
  }
  .form {
    height: 550px;
    overflow: auto;
    margin: 10px 0;
    .el-select {
      width: 100%;
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    :deep(.el-form-item__label) {
      color: #fff;
    }
  }
  .btns {
    padding: 0 80px;
    display: flex;
    justify-content: flex-end;
    .btn {
      width: 120px;
    }
  }
  :deep(.el-textarea.is-disabled .el-textarea__inner) {
    color: #666 !important;
  }
  :deep(.el-input.is-disabled .el-input__inner) {
    color: #666;
    -webkit-text-fill-color: #666;
  }
  :deep(.el-select .el-select__tags-text) {
    color: #666;
  }
}
</style>
