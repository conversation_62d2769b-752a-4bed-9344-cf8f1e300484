<template>
  <!-- 绑定易损件弹框 -->
  <hmx-dialog
    dialogWidth="50%"
    :dialogTitle="$t('jobDirection.relatedVulnerablePartsDetail')"
    customClass="hymson-dialog detail-dialog"
    :isVisable="isShow"
    :isFooter="true"
    @closeDialog="closeFun"
    @save="saveDialog(formRef)"
    top="50px"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="add-user-ruleForm"
      status-icon
      v-loading="isLoading"
    >
      <el-form-item :label="`${$t('jobDirection.fileName')}：`">
        <el-text>{{ currentItem.fileName }}</el-text>
      </el-form-item>
      <el-form-item
        :label="`${$t('jobDirection.relatedVulnerableParts')}：`"
        prop="sparePartsNumberList"
      >
        <el-select
          v-model="form.sparePartsNumberList"
          class="form-input"
          :placeholder="$t('jobDirection.selectFilePlaceholder')"
          multiple
          clearable
        >
          <el-option
            v-for="item in quickWearPartList"
            :key="item.serialNumber"
            :label="item.name"
            :value="item.serialNumber"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </hmx-dialog>
</template>

<script setup name="DetailDialog">
import {
  reactive,
  ref,
  toRefs,
  watch,
  computed,
  onMounted,
  defineEmits,
} from "vue"
import HmxDialog from "@/components/hmx-dialog.vue"
import useTable from "@/hooks/useTable"
import { config } from "@/config"
import { useI18n } from "@xfe/locale"
import { setSpareParts } from "@/api/front/jobDirection"
import { useMessage } from "@/hooks/web/useMessage"

const { createMessage } = useMessage()

const homeServerPrefix = config.base_url.homeServerPrefix

const { getList: getQuickWearPartList } = useTable(
  `${homeServerPrefix}/SparePartsAssembly`,
  false,
  false
)

const { t: $t } = useI18n()
const emit = defineEmits(["onSure", "onClose"])

const props = defineProps({
  // 是否展示弹窗
  isShow: {
    type: Boolean,
    default: false,
  },
  // 当前选中备件/易损件
  currentItem: {
    type: Object,
    default: () => ({}),
  },
})

const isLoading = ref(false)

const currentItem = computed(() => props.currentItem)

const data = reactive({
  form: {
    id: "", // 文件id
    sparePartsNumberList: [], // 关联易损件序号列表
  },
})

const formRef = ref()

const rules = {
  sparePartsNumberList: [
    {
      required: true,
      message: $t("jobDirection.selectVulnerablePartsTip"),
      trigger: "blur",
    },
  ],
}

const saveDialog = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      data.form.id = currentItem.value.id
      try {
        // 转换格式为后端需要的结构
        const params = data.form.sparePartsNumberList.map(serialNumber => ({
          serialNumber,
          sparePartsName:
            quickWearPartList.value.find(
              item => item.serialNumber === serialNumber
            )?.name || "",
        }))
        await setSpareParts(params, data.form.id)
        createMessage($t("jobDirection.linkSuccess"), "success")
        emit("onSure")
        closeFun()
      } catch (error) {
        createMessage($t("jobDirection.linkFailure"), "warning")
      }
    }
  })
}

// 关闭弹框
const closeFun = () => {
  data.form = {
    id: "",
    sparePartsNumberList: [],
  }
  emit("onClose")
}

const quickWearPartList = ref([])
watch(
  () => props.isShow,
  async val => {
    if (val) {
      isLoading.value = true
      const res = await getQuickWearPartList()
      quickWearPartList.value = res.items
      data.form.sparePartsNumberList =
        currentItem.value?.spareParts.map(item => item.serialNumber) || []
      isLoading.value = false
    }
  }
)

const { form } = toRefs(data)
</script>

<style lang="scss">
.detail-dialog.el-dialog {
  .add-user-ruleForm {
    padding: 0 20px 0 0;

    .el-form-item__label {
      flex: none;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
    }

    .el-form-item__content {
      .el-select,
      .el-input,
      .el-input-number {
        flex: none;
        width: 100%;
      }
    }
  }
  .hymson-table {
    height: 400px;
  }
}
</style>

<style scoped lang="scss">
.form {
  width: 100%;
  height: 100%;
  .form-left {
    width: 100%;

    .reference-life {
      display: flex;

      .reference-life-left {
        width: 75%;
      }

      .reference-life-right {
        width: 25%;
      }
    }
  }

  .form-right {
    width: 48%;
    .three-d {
      display: flex;
      height: 100px;
      margin-bottom: 10px;
      .three-d-left {
        width: 65%;
      }
      .three-d-right {
        background-color: #bfa;
        width: 30%;
        display: flex;
        justify-content: center;
        align-items: center;
        .t-img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

::v-deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.three-d {
  display: flex;
  height: 100px;
  margin-bottom: 10px;
  .three-d-left {
    width: 65%;
  }
  .three-d-right {
    background-color: #bfa;
    width: 30%;
    display: flex;
    justify-content: center;
    align-items: center;
    .t-img {
      width: 100%;
      height: 100%;
    }
  }
}

::v-deep(.el-input.is-disabled .el-input__inner) {
  -webkit-text-fill-color: #fff !important;
}
::v-deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: rgb(29, 31, 34) !important;
}
::v-deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: rgb(29, 31, 34) !important;
}
</style>
