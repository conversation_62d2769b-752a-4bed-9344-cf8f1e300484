<template>
  <div class="log-detail" v-loading="alarmLoading">
    <div v-for="(v, index) in alarmList" :key="index" class="log-content">
      <div class="title">
        <div class="image">
          <img src="@/assets/images/box/icon-mode.svg" alt="" />
        </div>
        <div class="text">{{ v.module }}</div>
      </div>
      <div class="classify">
        <div class="left"></div>
        <div class="right">{{ $t("monitor.remainingDowntimeDistance") }}</div>
      </div>
      <div class="content">
        <div v-for="v in v.item" :key="v.barcode" class="content-box">
          <div class="name">{{ v.baseDto.name }}</div>
          <RemainingLife
            :option="getRemainingOption(v)"
            :isShow="true"
            class="charts"
          />
          <div class="age">{{ v.baseDto.stopDistance }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="ProDetail">
import { ref, onMounted } from "vue"
import { getAllVulnerableList } from "@/api/front/home"
import RemainingLife from "./RemainingLife.vue"
import { RemainingLifeEcharts } from "../../echartsConfig.js"
import { cloneDeep } from "lodash-es"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()

const alarmList = ref([])

const alarmLoading = ref(false)
onMounted(async () => {
  alarmLoading.value = true
  const res = await getAllVulnerableList()
  alarmList.value = res.map(v => {
    return {
      ...v,
      item: v.list.map(moduleItem => {
        return {
          ...moduleItem,
        }
      }),
    }
  })
  alarmLoading.value = false
})

const getRemainingOption = v => {
  if (v) {
    const { usedPercentage, alarmPercentage } = v
    let options = ref({})
    options.value = cloneDeep(RemainingLifeEcharts([v.color]))
    if (usedPercentage < 0) {
      options.value.yAxis[0].show = false
      options.value.yAxis[3].show = true
      options.value.series[0].label.normal.position = "left"
      options.value.series[1].data = [-99.5]
      options.value.series[2].data = [-100]
    }
    options.value.series[0].data = [
      {
        value: Math.min(v.usedPercentage, 100), // 限制最大值为100
        rawValue: v.usedPercentage, // 保留原始值用于显示
        name: v.baseDto.name,
        barcode: v.baseDto.barcode,
        currentDistance: v.baseDto.usedDistance,
        alarmDistance: v.baseDto.alarmDistance,
        stopDistance: v.baseDto.stopDistance,
      },
    ]
    // 修改标签显示
    options.value.series[0].label.normal.formatter = param => {
      return `${param.data.rawValue}%` // 显示原始值
    }
    options.value.series[0].markLine = {
      data: [{ xAxis: alarmPercentage }],
    }
    return options.value
  }
}
</script>

<style lang="scss" scoped>
.log-detail {
  position: relative;
  background: #000000;
  color: #fff;
  height: 500px;
  overflow: auto;

  .log-content {
    position: relative;
    margin: 30px 20px 20px 20px;
    padding: 10px;
    background-color: #1e2023;
    .title {
      width: 100%;
      display: flex;
      align-items: center;
      .image {
        width: 18px;
        height: 18px;
        margin-right: 10px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .classify {
      display: flex;
      justify-content: center;
      width: 100%;
      height: 30px;
      .left {
        width: 70%;
        height: 100%;
      }
      .right {
        width: 20%;
        height: 100%;
        margin-left: 60px;
      }
    }
    .content {
      width: 100%;
      &-box {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        .name {
          display: flex;
          justify-content: flex-end;
          width: 20%;
          margin-right: 30px;
        }
        .charts {
          width: 50%;
          height: 40px;
        }
        .age {
          width: 20%;
          margin-left: 30px;
        }
      }
    }
  }
}
</style>
