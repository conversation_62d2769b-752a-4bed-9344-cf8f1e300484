<template>
  <div class="b-chart" ref="outputStatisticsRef"></div>
</template>

<script setup name="BarCharts">
import {
  ref,
  defineEmits,
  defineProps,
  defineExpose,
  watch,
  nextTick,
} from "vue"
import useEcharts from "@/hooks/useEcharts"
import { outputStatisticsEcharts } from "../echartsConfig.js"
const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  isShow: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  "chart-click", // 点击chart
])

let outputStatisticsRef = ref(null)
const { resize } = useEcharts(
  outputStatisticsRef,
  emits,
  props,
  outputStatisticsEcharts
)
defineExpose({
  resize,
})
watch(
  () => props.isShow,
  v => {
    v &&
      nextTick(() => {
        resize()
      })
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.b-chart {
  width: 100%;
  height: 100%;
}
</style>
