@font-face {
  font-family: 'icomoon';
  src:  url('./icomoon.eot?av241r');
  src:  url('./icomoon.eot?av241r#iefix') format('embedded-opentype'),
    url('./icomoon.ttf?av241r') format('truetype'),
    url('./icomoon.woff?av241r') format('woff'),
    url('./icomoon.svg?av241r#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="hmx-icon-"], [class*=" hmx-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.hmx-icon-help:before {
  content: "\e900";
  color: #747a7d;
}
.hmx-icon-alarm-search:before {
  content: "\e901";
}
.hmx-icon-bianji:before {
  content: "\e902";
}
.hmx-icon-computer:before {
  content: "\e903";
}
.hmx-icon-config:before {
  content: "\e904";
}
.hmx-icon-feed:before {
  content: "\e905";
}
.hmx-icon-fenlei:before {
  content: "\e906";
}
.hmx-icon-guanbi:before {
  content: "\e907";
}
.hmx-icon-mes:before {
  content: "\e909";
}
.hmx-icon-mode-change:before {
  content: "\e90a";
}
.hmx-icon-plc:before {
  content: "\e90b";
}
.hmx-icon-print:before {
  content: "\e90c";
}
.hmx-icon-production-search:before {
  content: "\e90d";
}
.hmx-icon-quanping:before {
  content: "\e90e";
}
.hmx-icon-user-login:before {
  content: "\e90f";
}
.hmx-icon-user-setting:before {
  content: "\e910";
}
