import useAuths from "./index"
import store from "@/store"
import router from "@/router"
import { useMessage } from "@/hooks/web/useMessage"
import { config } from "@/config"

let load = 0 //定义变量判断是否已经动态添加过，如果刷新后load永远为 0

// addWhiteList回调  白名单扩展，需传入一个扩展白名单数组
export default function bootStrapPermission() {
  let w_list = config.whiteList // 不进行跳转检查白名单
  const { createMessage } = useMessage()

  const { getToken, logoutFun } = useAuths()
  // 给有权限的菜单添加路由   目前只添加notFound页面
  const addRoutesFun = () => {
    router.addRoute({
      path: "/:catchAll(.*)",
      redirect: "/404",
      name: "NotFound",
    })
  }
  router.beforeEach(async (to, from, next) => {
    const hasToken = getToken()
    if (hasToken) {
      if (to.path === "/login") {
        // 已经登录跳转首页
        next({ path: "/" })
      } else if (w_list.includes(to.path)) {
        // 登录后只进入反 / 会匹配进404页面，但是/有重定向到首页，这里特殊处理一下
        if (to.redirectedFrom?.path == "/") {
          next({ path: "/home/<USER>" })
        } else {
          next()
        }
      } else {
        const {
          hasInitAsyncRoutes,
          // isListenDataLoop,
          accessMenus,
          isAdministrator,
        } = store.getters
        // 用于第一次进系统时调用signalr 实时通讯   预留
        // if (!isListenDataLoop) {
        //   store.dispatch("setListenDataLoopFlag")
        //   bootstrap();
        // }
        if (hasInitAsyncRoutes) {
          // 刷新时动态路由丢失
          if (load === 0 && to.name !== "Login") {
            load++
            if (accessMenus.length === 0 && !isAdministrator) {
              logoutFun()
              next({ path: "/login?iserror=1&unTourist=true" })
            } else {
              // 给有权限的菜单添加路由
              addRoutesFun()
              next({ ...to, replace: true })
            }
          } else {
            // let menuList = accessMenus.map(m => m.router)
            // let isLowcode = to.path.includes("/lowcode")
            // if (
            //   menuList.indexOf(to.path) === -1 &&
            //   !isAdministrator &&
            //   !isLowcode
            // ) {
            //   createMessage("暂无权限，禁止访问！", "warning", 3000)
            //   next(from.path)
            // } else {
            //   next()
            // }
            next()
          }
        } else {
          // 登录成功后第一次跳转 初始化路由
          store.commit("SET_INIT_ROUTES", true)
          try {
            if (accessMenus.length === 0 && !isAdministrator) {
              logoutFun()
              next({ path: "/login?iserror=1&unTourist=true" })
            } else {
              // 给有权限的菜单添加路由
              addRoutesFun()
              next({ ...to, replace: true })
            }
          } catch (error) {
            logoutFun()
            next({ path: "/login?iserror=1&unTourist=true" })
          }
        }
      }
    } else {
      /* 未存token */
      // if (w_list.indexOf(to.path) !== -1) {
      //   next()
      // } else {
      //   next(`/login?redirect=${to.path}&unTourist=true`)
      // }
      next()
    }
  })
}
