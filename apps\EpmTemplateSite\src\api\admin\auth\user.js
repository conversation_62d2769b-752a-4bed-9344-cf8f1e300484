import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 *  导出用户权限表
 */
export function exportUserExcel() {
  return request.get({
    url: `${homeServerPrefix}/IdentityData/ExportExcel`,
    header: {
      headers: { "Content-Type": "application/x-download" },
    },
    responseType: "blob",
  })
}

/**
 *  导入交互配置表
 */
export function importUserExcel(data) {
  return request.post({
    url: `${homeServerPrefix}/IdentityData/ImportExcel`,
    headers: { "Content-Type": "multipart/form-data" },
    data,
  })
}
