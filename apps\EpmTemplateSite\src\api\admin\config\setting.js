import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix
export function getLoadChild(url, params) {
  return request.get({
    url: url,
    params,
  })
}

/**
 *  首页切换国际化更新
 */
export function updateHomeNation(name, data) {
  return request.put({
    url: `${homeServerPrefix}/GlobalSetting/localization/${name}`,
    data,
  })
}

/**
 *  首页国际化配置同步
 */
export function SyncHomeNation() {
  return request.delete({
    url: `${homeServerPrefix}/GlobalSetting/localization/reset`,
  })
}

/**
 *  子站获取PLC地址
 */
export function getPlcUrl(data) {
  return request.get({
    url: `${homeServerPrefix}/GlobalSetting/ip`,
    data,
  })
}
