import { computed, reactive } from "vue"
import { useTable } from "@/hooks/useTable"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix
/**
 * @param logType 日志类型
 */

export default function useLogDetail(logType) {
  const { list, total, loading, getList } = useTable(
    `${homeServerPrefix}/Log/${logType}`,
    true,
    false
  )

  const data = reactive({
    // 绑定数据
    form: {},
    page: {
      pageSize: 10,
      pageIndex: 1,
    },
  })
  const tableOptions = computed(() => {
    return {
      loading: loading.value,
      showPagination: true,
      border: true,
      height: "100%",
      paginationConfig: {
        total: total.value,
        currentPage: data.page.pageIndex,
        pageSize: data.page.pageSize,
      },
    }
  })
  const params = computed(() => {
    let form = JSON.parse(JSON.stringify(data.form))
    Object.keys(form).forEach(key => {
      if (form[key] === null) {
        form[key] = ""
      }
    })
    return {
      ...form,
      ...data.page,
    }
  })
  const checkList = value => {
    data.form = value
    data.page.pageIndex = 1
    getList(params.value)
  }
  // 表请求条数改变
  const handlerPageSize = pageSize => {
    data.page.pageSize = pageSize
    data.page.pageIndex = 1
    getList(params.value)
  }
  // 表格页数改变
  const handlerPageIndex = pageIndex => {
    data.page.pageIndex = pageIndex
    getList(params.value)
  }

  function handleChange() {
    getList(params.value)
  }

  return {
    list,
    tableOptions,
    handlerPageSize,
    handlerPageIndex,
    getList,
    checkList,
    handleChange,
  }
}
