import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

//mes登录
export function loginMes(data) {
  return request.post({
    url: `${homeServerPrefix}/MesData/UserRightVerify`,
    data,
  })
}

// 获取登录信息
export function loginMesInfo(data) {
  return request.get({
    url: `${homeServerPrefix}/MesData/GetLoginMessage`,
    data,
  })
}

// 获取投料信息
export function getFeed() {
  return request.get({
    url: `${homeServerPrefix}/MesData/GetMaterialMessage`,
  })
}

// 提交投料信息
export function postFeed(data) {
  return request.post({
    url: `${homeServerPrefix}/MesData/MaterialInput`,
    data,
  })
}

// 获取mes状态
export function getMesState() {
  return request.get(
    {
      url: `${homeServerPrefix}/MesData/GetMesStatus`,
    },
    {
      isReturnNativeResponse: true,
    }
  )
}

// 设置mes状态
export function putMesState(params) {
  return request.post({
    url: `${homeServerPrefix}/MesData/SetMesStatus?status=${params}`,
  })
}

// 更新配方
export function updateRecipe(recipeCode, version) {
  return request.post({
    url: `${homeServerPrefix}/MesData/UpdateRecipe?RecipeCode=${recipeCode}&version=${version}`,
  })
}

// 获取配方
export function getRecipeList(params) {
  return request.get({
    url: `${homeServerPrefix}/MesData/GetRecipeList`,
    params,
  })
}

// 配方校验
export function verifyRecipe() {
  return request.post({
    url: `${homeServerPrefix}/MesData/VerifyRecipe`,
  })
}

// 获取配方详情
export function getRecipeDetailList(params) {
  return request.get({
    url: `${homeServerPrefix}/MesData/GetRecipeDetailList`,
    params,
  })
}

// 获取工单
export function getWorkOrderList() {
  return request.get({
    url: `${homeServerPrefix}/MesData/GetWorkOrderList`,
  })
}

// 激活工单
export function activateWorkOrder(workOrderCode, productCode) {
  return request.post({
    url: `${homeServerPrefix}/MesData/ActivateWorkOrder?isActivate=true&workOrderCode=${workOrderCode}&productCode=${productCode}`,
  })
}
