import {
  getCTLists,
  getServerVersion,
  getDatumLineConfig,
} from "@/api/front/home"
import { getSysNameAndLogo, getLogo } from "@/api/admin/config/baseConfig"
import { getReasonList } from "@/api/admin/config/stopReason"

const common = {
  state: {
    fullScreen: false, // 全屏
    hasInitAsyncRoutes: false, // 是否初始化同步路由
    isListenDataLoopFlag: false, // 是否打开数据监听 用于是否建立数据连接
    logIsFlod: false, // 底部log是否折叠
    CTList: [], // 设备列表
    homeConfig: false, //首页编辑
    workstationConfig: false, //子站编辑
    currentTab: 1, //当前高亮页
    currentPath: "", //当前路径
    isLanguage: false, //是否语言切换
    isShowDataPanel: true, // 是否显示数据面板
    hasWebsocket: false, // 是否已经存在websocket的连接
    sysInfo: {
      sysName: "", // 系统设备名字
      sysLogo: "", // 系统logo
    },
    versionInfo: {
      // 版本信息
    },
    isTimeOver: false, // 是否长时间未操作
    outputStatistics: 0, // 产量统计基准值
    baseNum: 0, // 质量统计基准值
    stopReasonList: [], // 停机原因列表
    equipShot: {
      //点检记录状态
      auto: 0,
      hand: 0,
    },
    isAlarmDialog: false, // 是否开启弹窗
    isFirstLoadHome: true, // 是否首次加载首页
    isCoolDown: false, // 是否冷却
  },
  mutations: {
    SET_FULLSCREEN: (state, val) => {
      state.fullScreen = val
    },
    // 是否获取初始化路由
    SET_INIT_ROUTES: (state, val) => {
      state.hasInitAsyncRoutes = val
    },
    SET_LISTENFLAG: (state, data) => {
      state.isListenDataLoopFlag = data
    },
    SET_LOG_ISFLOD: (state, val) => {
      state.logIsFlod = val
    },
    SET_CT_LIST: (state, val) => {
      state.CTList = val
    },
    SET_HOME_CONFIG: (state, val) => {
      state.homeConfig = val
    },
    SET_WORK_STATION_CONFIG: (state, val) => {
      state.workstationConfig = val
    },
    SET_CURRENT_Options: (state, payload) => {
      const { tab, path } = payload
      state.currentTab = tab
      state.currentPath = path
    },
    SET_LANGUAGE: (state, val) => {
      state.isLanguage = val
    },
    SET_SHOW_DATA_PANEL: state => {
      state.isShowDataPanel = !state.isShowDataPanel
    },
    SET_HAS_WEBSOCKET: (state, val) => {
      state.hasWebsocket = val
    },
    SET_SYS_INFO: (state, val) => {
      state.sysInfo = val
    },
    SET_VERSION_INFO: (state, val) => {
      state.versionInfo = val
    },
    SET_OVER_TIME: (state, val) => {
      state.isTimeOver = val
    },
    SET_HOME_BASE_LINE: (state, val) => {
      state.outputStatistics = val.outputStatistics
      state.baseNum = val.baseNum
    },
    SET_STOP_REASON_LIST: (state, val) => {
      state.stopReasonList = val
    },
    SET_EQUIP_SHOT: (state, val) => {
      state.equipShot[val.key] = val.value
    },
    SET_IS_ALARM_DIALOG: (state, val) => {
      state.isAlarmDialog = val
    },
    SET_IS_FIRST_LOAD_HOME: (state, val) => {
      state.isFirstLoadHome = val
    },
    SET_IS_COOL_DOWN: (state, val) => {
      state.isCoolDown = val
    },
  },
  actions: {
    updateFullScreen({ state, commit }) {
      let element = document.documentElement
      if (state.fullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      } else {
        if (element.requestFullscreen) {
          element.requestFullscreen()
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen()
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen()
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen()
        }
      }
      commit("SET_FULLSCREEN", !state.fullScreen)
    },
    updateLogIsFlod({ commit }, val) {
      commit("SET_LOG_ISFLOD", val)
    },
    getCTData({ commit }) {
      getCTLists().then(res => {
        commit("SET_CT_LIST", res.items)
      })
    },
    async getSysInfo({ commit }) {
      let obj = {
        sysName: "",
        sysLogo: "",
      }
      await getSysNameAndLogo().then(res => {
        obj.sysName = res.sysName
      })
      let fileUrl = await getLogo()
      var reader = new FileReader()
      reader.readAsDataURL(fileUrl)
      reader.onload = function (e) {
        obj.sysLogo = e.target.result
        commit("SET_SYS_INFO", obj)
      }
    },
    getvVersionInfo({ commit }) {
      getServerVersion().then(res => {
        commit("SET_VERSION_INFO", res)
      })
    },
    async getHomeBaseLine({ commit }) {
      let obj = {
        outputStatistics: 0,
        baseNum: 0,
      }
      await getDatumLineConfig({
        kCode: "production",
        SubKCode: "#outputStatistics",
      }).then(res => {
        if (res.items.length > 0) {
          obj.outputStatistics = res.items[0]?.prmValue
        }
      })
      await getDatumLineConfig({
        kCode: "productQualitys",
        SubKCode: "#baseNum",
      }).then(res => {
        if (res.items.length > 0) {
          obj.baseNum = res.items[0]?.prmValue
        }
      })
      commit("SET_HOME_BASE_LINE", obj)
    },
    getStopReason({ commit }) {
      getReasonList().then(res => {
        commit("SET_STOP_REASON_LIST", res)
      })
    },
  },
  getters: {
    fullScreen: state => state.fullScreen,
    hasInitAsyncRoutes: state => state.hasInitAsyncRoutes,
    logIsFlod: state => state.logIsFlod,
    CTList: state => state.CTList,
    currentTab: state => state.currentTab,
    currentPath: state => state.currentPath,
    isLanguage: state => state.isLanguage,
    isShowDataPanel: state => state.isShowDataPanel,
    hasWebsocket: state => state.hasWebsocket,
    versionInfo: state => state.versionInfo,
    isTimeOver: state => state.isTimeOver,
    outputStatistics: state => state.outputStatistics,
    baseNum: state => state.baseNum,
    stopReasonList: state => state.stopReasonList,
    isAlarmDialog: state => state.isAlarmDialog,
    isFirstLoadHome: state => state.isFirstLoadHome,
    isCoolDown: state => state.isCoolDown,
  },
}

export default common
