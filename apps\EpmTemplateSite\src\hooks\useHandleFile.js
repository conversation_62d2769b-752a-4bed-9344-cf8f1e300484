import { ref, unref } from "vue"
import { useMessage } from "@/hooks/web/useMessage"
import { downloadImage } from "@/api/admin/config/twoDModule"
import { useI18n } from "@xfe/locale"
/**
 * 点击选择文件
 * @param { 文件ref } fileRef
 * @param { 是否限制文件大小 } isLimit
 * @param { 限制的文件最大size，需换算 1kb = 1024  1mb= 1024*1024 } maxSize  Kb计算
 */
export default function useHandleFile(fileRef, isLimit = false, maxSize) {
  const { t: $t } = useI18n()
  const { createMessage } = useMessage()
  const file = ref(null)
  const fileBolb = ref("") // 文件预览（图片bolb流）

  const handleImport = () => {
    fileRef.value.click() // 打开选择文件的弹框
  }
  const getFile = async e => {
    file.value = e.target.files[0]
    const size = unref(file).size
    if (isLimit && size > maxSize * 1024) {
      createMessage(
        `${$t("table.visual.config_prompt_4")} ${maxSize}kb`,
        "error"
      )
      return
    }
    let reader = new FileReader()
    reader.readAsDataURL(file.value)
    reader.onload = function (f) {
      fileBolb.value = f.target.result
    }
  }
  return {
    file,
    fileBolb,
    handleImport,
    getFile,
  }
}

/**
 * @param {远程图片code} code
 */
export async function perviewRemoteImg(code) {
  let imgUrl = ""
  let result = ""
  await downloadImage(code)
    .then(res => {
      result = res
    })
    .catch(() => {})
  await readFile(result).then(f => {
    imgUrl = f
  })
  return imgUrl
}

export function readFile(file) {
  return new Promise(function (resovle, reject) {
    let reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = function (e) {
      resovle(e.target.result)
    }
  })
}
