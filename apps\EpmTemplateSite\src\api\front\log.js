import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix
/**
 * 查询日志
 * @param warn 报警
 * @param operate 操作
 */
export function getLogStatisticsDays(params, type) {
  return request.get({
    url: `${homeServerPrefix}/Statistics/bar/${type}/day`,
    params,
  })
}

/**
 * 查询日志饼图和分布
 * @param warn 报警
 * @param operate 操作
 */
export function getLogStatisticsPies(params, type) {
  return request.get({
    url: `${homeServerPrefix}/Statistics/pie/${type}/server-name`,
    params,
  })
}

// 报警日志导出
export function exportWarnLog(params) {
  return request.get({
    url: `${homeServerPrefix}/Log/ExportWarnLog`,
    headers: { "Content-Type": "application/x-download" },
    responseType: "blob",
    params,
  })
}

// 操作日志导出
export function exportOperateLog(params) {
  return request.get({
    url: `${homeServerPrefix}/Log/ExportOperateLog`,
    headers: { "Content-Type": "application/x-download" },
    responseType: "blob",
    params,
  })
}
