<template>
    <div class="object-tree-panel">
        <h3>对象结构树</h3>

        <!-- 搜索框 -->
        <div class="search-box">
            <input type="text" v-model="searchQuery" placeholder="搜索对象..." class="search-input" />
            <span v-if="searchQuery" class="clear-search" @click="searchQuery = ''">✕</span>
        </div>

        <!-- 对象树 -->
        <div class="mesh-tree">
            <div v-if="filteredMeshTree.length === 0" class="info-empty">
                {{ meshTree.length === 0 ? '无可用对象' : '未找到匹配的对象' }}
            </div>

            <div v-else>
                <div v-for="node in filteredMeshTree" :key="node.id" class="tree-node">
                    <TreeNode :node="node" :selected-id="selectedObjectId" :search-query="searchQuery"
                        :alarm-target-ids="alarmTargetIds" @node-click="handleNodeClick"
                        @toggle-alarm-target="handleToggleAlarmTarget" />
                </div>
            </div>
        </div>

        <!-- 对象详细信息 -->
        <div class="info-content" v-if="selectedObjectInfo">
            <div class="info-item">
                <span class="info-label">ID:</span>
                <span class="info-value">{{ selectedObjectInfo.id }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">名称:</span>
                <span class="info-value">{{ selectedObjectInfo.name }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">顶点数量:</span>
                <span class="info-value">{{ selectedObjectInfo.vertices.toLocaleString() }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">面数量:</span>
                <span class="info-value">{{ selectedObjectInfo.faces.toLocaleString() }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">位置:</span>
                <span class="info-value">
                    X: {{ selectedObjectInfo.position.x }},
                    Y: {{ selectedObjectInfo.position.y }},
                    Z: {{ selectedObjectInfo.position.z }}
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">旋转:</span>
                <span class="info-value">
                    X: {{ selectedObjectInfo.rotation.x }},
                    Y: {{ selectedObjectInfo.rotation.y }},
                    Z: {{ selectedObjectInfo.rotation.z }}
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">缩放:</span>
                <span class="info-value">
                    X: {{ selectedObjectInfo.scaling.x }},
                    Y: {{ selectedObjectInfo.scaling.y }},
                    Z: {{ selectedObjectInfo.scaling.z }}
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">可见:</span>
                <span class="info-value">{{ selectedObjectInfo.isVisible ? '是' : '否' }}</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';
import TreeNode from './TreeNode.vue';

// 对象树节点类型定义
type MeshTreeNode = {
    id: string;
    name: string;
    children: MeshTreeNode[];
    level: number;
    vertices: number;
    isVisible: boolean;
    parentId?: string;
};

// 对象信息类型
type MeshInfo = {
    id: string;
    name: string;
    position: { x: number; y: number; z: number };
    rotation: { x: number; y: number; z: number };
    scaling: { x: number; y: number; z: number };
    vertices: number;
    faces: number;
    isVisible: boolean;
    parentId?: string;
    children: string[];
    materialName?: string;
};

// 组件属性
const props = defineProps<{
    meshTree: MeshTreeNode[];
    selectedObjectId: string;
    selectedObjectInfo: MeshInfo | null;
    alarmTargetIds: string[];
}>();

// 组件事件
const emit = defineEmits<{
    'update:selectedObjectId': [id: string];
    'toggle-alarm-target': [id: string];
}>();

// 搜索查询
const searchQuery = ref('');

// 过滤后的对象树
const filteredMeshTree = computed(() => {
    if (!searchQuery.value) {
        return props.meshTree;
    }

    const searchText = searchQuery.value.toLowerCase();

    const filterNode = (node: MeshTreeNode): MeshTreeNode | null => {
        // 检查当前节点是否匹配
        const isMatch = node.name.toLowerCase().includes(searchText) ||
            node.id.toLowerCase().includes(searchText);

        // 递归过滤子节点
        const filteredChildren = node.children
            .map(child => filterNode(child))
            .filter((child): child is MeshTreeNode => child !== null);

        // 如果当前节点匹配或者有匹配的子节点，则保留该节点
        if (isMatch || filteredChildren.length > 0) {
            return {
                ...node,
                children: filteredChildren
            };
        }

        return null;
    };

    return props.meshTree
        .map(node => filterNode(node))
        .filter((node): node is MeshTreeNode => node !== null);
});

// 处理节点点击
const handleNodeClick = (nodeId: string) => {
    emit('update:selectedObjectId', nodeId);
};

// 处理节点告警选择
const handleToggleAlarmTarget = (nodeId: string) => {
    emit('toggle-alarm-target', nodeId);
};
</script>

<style scoped>
.object-tree-panel {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
}

h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #555;
}

/* 搜索框样式 */
.search-box {
    position: relative;
    margin-bottom: 10px;
}

.search-input {
    width: 100%;
    padding: 8px 30px 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    transition: all 0.2s;
}

.search-input:focus {
    outline: none;
    border-color: #4c84ff;
    box-shadow: 0 0 0 2px rgba(76, 132, 255, 0.2);
}

.clear-search {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #999;
    font-size: 14px;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.2s;
}

.clear-search:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #666;
}

/* 对象树样式 */
.mesh-tree {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 5px;
}

.tree-node {
    margin: 2px 0;
}

.info-empty {
    text-align: center;
    padding: 20px 0;
    color: #6c757d;
    font-style: italic;
}

/* 信息展示样式 */
.info-content {
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
}

.info-item {
    margin-bottom: 8px;
    font-size: 0.9rem;
    display: flex;
    flex-direction: column;
}

.info-label {
    font-weight: 500;
    color: #555;
}

.info-value {
    color: #333;
    word-break: break-word;
}

/* 高亮搜索结果 */
:deep(.highlight) {
    background-color: rgba(76, 132, 255, 0.2);
    border-radius: 2px;
    padding: 0 2px;
}
</style>
