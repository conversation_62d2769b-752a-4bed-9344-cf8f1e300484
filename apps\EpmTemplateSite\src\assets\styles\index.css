[data-theme="dark"] {
  /* 历史变量 */
  --f-card-background: rgba(24, 87, 106, 0.8);
  --el-table-cell: rgb(12, 52, 88);
  --log-table-background: rgb(24, 52, 87);
  --params-background: rgb(14, 17, 41);

  /* 全新变量 */
  --g-font-color: #fff;
  --g-font-color-1: #fff;
  --g-wihte-font-color: #fff;
  --g-background-color: linear-gradient(
    90deg,
    #202230 0%,
    #012636 15%,
    #011d37 35%,
    #011d2f 100%
  );
  --g-input-background: rgba(0, 0, 0, 0.25);
  --body-background: rgba(0, 75, 144, 0.3);
  --g-tree-node-hover-bg-color: rgba(5, 69, 130);
  --g-checkbox-border-color: #0084fe;

  --layout-background: #0e1129;

  /* 前台板块通用背景色 */
  --front-box-border: 1px solid #0084fe;
  --front-layout-background: #02213e;

  --head-background: linear-gradient(
    90deg,
    #202230 0%,
    #012636 15%,
    #011d37 35%,
    #011d2f 100%
  );
  --head-color: #fff;
  --head-nav-color: #e4e7ed;
  --head-active-nav-color: #0084fe;
  --head-active-nav-background: transparent;
  --head-start-task-background: rgba(22, 120, 44, 0.65);
  --head-start-task-shadow: inset 4px 4px 4px 0px rgba(101, 231, 56, 0.25),
    inset -4px -4px 4px 0px rgba(101, 231, 56, 0.25);
  --head-start-task-border: 2px solid #29e093;
  --head-close-task-background: rgba(105, 36, 14, 0.65);
  --head-close-task-shadow: inset 4px 4px 4px 0px rgba(226, 37, 25, 0.25),
    inset -4px -4px 4px 0px rgba(226, 37, 25, 0.25);
  --head-close-task-border: 2px solid #e05529;
  --head-theme-hover-color: #b6dcff;

  --home-side-background: rgba(0, 19, 36, 0.8);
  --home-alarm-background: transparent;
  --home-status-color: #fff;
  /* --home-side-box-background: url("@/assets/images/common/side-box-bk-light.png")
    no-repeat; */
  --mudule-background: rgba(15, 107, 113, 0.8);
  /* table */
  --g-table-background-color: #101129;
  --g-table-header-bg-color: #063866;
  --g-table-empty-background: #011124;

  --tab-color: #a8abb2;
  --tab-active-color: #0084fe;
  --tab-background: rgba(0, 19, 36, 0.8);
  --tab-item-background: #0d2234;
  --tab-border-color: #73767a;
  --tab-bottom-line-color: #0050b3;
  --tab-active-bottom-color: #101129;
  /* dailog */
  --dialog-body-background: rgba(5, 69, 130);
  --dialog-close-color: rgba(0, 19, 36, 0.8);
  --dialog-btn-cancel-backbackground: #fff;
  --dialog-btn-cancel-color: #606266;
  /*tootlip*/
  --g-tooltip-background: url("~@/assets/images/box/tab_panel.png") no-repeat
    center / 100% 100%;

  --vulnerability-background: #20212f;
  --vulnerability-dashed-line: #fff;
  --vulnerability-form-background: #1b304e;
  --home-detail-dialog-background: rgba(39, 40, 56, 0.8);
  --front-detail-dialog-background: #1c1e23;
  --front-mes-content-background: rgba(0, 0, 0, 0.45);
  --front-info-swtich-background: #0084fe;
  --front-info-echarts-background: #011f3a;
  --log-layout-background: rgba(0, 43, 83, 0.9);
  --only-ready-show-area: #101129;
  --custom-switch-button-background: #fff;
  --custom-switch-button-active-background: #0084fe;
  --custom-switch-button-active-color: #fff;
  --custom-switch-button-nromal-color: #606266;
  --mes-material-from-background: #032a50;
  --mes-module-background: #131f3b;
  --spc-layout-background: transparent;
  --dataAnalysis-background: #011222;
  --dataAnalysis-border: 1px solid #0050b3;
  /* 后台背景色 */
  --admin-layout-background: #00162c;
  --admin-layout-padding: 10px 0;
  --admin-normal-background: #02213e;
  --admin-normal-border: 1px solid #004b90;
  --admin-menu-item-background: #02213e;
  --admin-menu-arrow: #0084fe;
  --admin-menu-active-color: #0084fe;
  --admin-menu-normal-color: #fff;
  --role-tip-color: #b6dcff;
  --role-permission-background: rgba(0, 0, 0, 0.25);
  --permission-box-border: #4c4d4f;
  --admin-module-layout-background: #02213e;

  /* 班次设置 */
  --opt-btn-background: none;
  --check-background: #004b90;
  /* 刻码规则 */
  --code-box-left-border: 1px solid transparent;
  --code-box-right-border: 1px solid #29e0e0;
  --code-box-background: transparent;
  /* 交互表配置 */
  --interaction-list-color: #0084fe;
  --interaction-list-active-background: rgba(0, 75, 144, 0.3);
  --interaction-list-active-color: #fff;
  --interaction-segmentation-line-color: #0050b3;
  /* 可视化配置 */
  --visual-config-background: transparent;
  --visual-config-border: 1px solid transparent;
  --visual-config-content-background: #101129;
  --visual-box-background: #004b90;
  --visual-type-color: #00feef;
  --visual-add-btn-background: #004b90;
  /* 2d */
  --tow-d-opt-background: rgba(0, 75, 144, 0.7);
  --two-d-opt-border-color: #0084fe;
  --two-d-opt-img-background: rgba(0, 0, 0, 0.25);
  --two-d-opt-img-color: #b6dcff;
  /* 刻码调试 */
  --laser-debug-background: #02213e;
  --laser-debug-layout-background: #00162c;
  /* 列设置 */
  --set-column-background: #00274a;
  --set-column-box-background: #00315f;
  /* 提示弹窗 */
  --messagebox-background: rgba(5, 69, 130);
  /* border */
  --hmx--border-common-color-1: 1px solid #337ecc;
  --hmx--border-common-color-2: 1px solid transparent;
  --hmx--border-common-color-3: 1px solid #0084fe;
  --hmx--border-common-color-4: 1px solid #0050b3;
  /* 低代码画布背景 */
  --lowcode-canvas-background: #232324;
  --lowcode-box-border: 1px solid #0084fe;
}
[data-theme="light"] {
  /* 历史变量 */
  --f-card-background: rgba(239, 240, 245, 0.4);
  --el-table-cell: rgb(37, 88, 254);
  --log-table-background: #fff;
  --params-background: #fff;
  /* 新变量 */
  --g-font-color: #606266;
  --g-font-color-1: #303133;
  --g-wihte-font-color: #fff;
  --g-background-color: #fff;
  --g-input-background: #fff;
  --g-checkbox-border-color: #dcdfe6;

  --body-background: #f2f3f5;
  --g-tree-node-hover-bg-color: #f5f7fa;

  --layout-background: #fff;

  /* 前台板块通用背景色 */
  --front-box-border: 1px solid #e4e7ed;
  --front-layout-background: #fff;

  /* 头部 */
  --head-background: #fff;
  --head-color: #606266;
  --head-nav-color: #606266;
  --head-active-nav-color: #0084fe;
  --head-active-nav-background: rgba(0, 132, 254, 0.1);
  --head-start-task-background: rgba(22, 120, 44, 0.65);
  --head-start-task-shadow: inset 4px 4px 4px 0px rgba(101, 231, 56, 0.25),
    inset -4px -4px 4px 0px rgba(101, 231, 56, 0.25);
  --head-start-task-border: 2px solid #29e093;
  --head-close-task-background: rgba(105, 36, 14, 0.65);
  --head-close-task-shadow: inset 4px 4px 4px 0px rgba(226, 37, 25, 0.25),
    inset -4px -4px 4px 0px rgba(226, 37, 25, 0.25);
  --head-close-task-border: 2px solid #e05529;
  --head-theme-hover-color: #b6dcff;

  --home-side-background: #fff;
  --home-alarm-background: #fff;
  --home-status-color: #303133;
  /* --home-side-box-background: url("@/assets/images/common/side-box-bk.png")
    no-repeat; */
  --mudule-background: rgba(15, 107, 113, 0.8);
  /* table */
  --g-table-background-color: transparent;
  --g-table-header-bg-color: #f0f2f5;
  --g-table-empty-background: #fff;

  --tab-color: #606266;
  --tab-active-color: #fff;
  --tab-background: #fff;
  --tab-item-background: #0084fe;
  --tab-border-color: #ebeef5;
  --tab-bottom-line-color: #e4e7ed;
  --tab-active-bottom-color: #fff;
  /* dialog */
  --dialog-body-background: #fff;
  --dialog-close-color: #909399;
  --dialog-btn-cancel-backbackground: #fff;
  --dialog-btn-cancel-color: #606266;
  /*tootlip*/
  --g-tooltip-background: #fff;

  --vulnerability-background: #f0f2f5;
  --vulnerability-dashed-line: #d4d7de;
  --vulnerability-form-background: #fff;
  --home-detail-dialog-background: #fff;
  --front-detail-dialog-background: #f0f2f5;
  --front-mes-content-background: #f0f2f5;
  --front-info-swtich-background: #0084fe;
  --front-info-echarts-background: #fff;
  --log-layout-background: #fff;
  --only-ready-show-area: #f2f3f5;
  --custom-switch-button-background: #fff;
  --custom-switch-button-active-background: #0084fe;
  --custom-switch-button-active-color: #fff;
  --custom-switch-button-nromal-color: #606266;
  --mes-material-from-background: #f0f2f5;
  --mes-module-background: #fff;
  --spc-layout-background: #fff;
  --dataAnalysis-background: #fff;
  --dataAnalysis-border: 1px solid #e4e7ed;
  /* 后台背景色 */
  --admin-layout-background: #f2f3f5;
  --admin-layout-padding: 0;
  --admin-normal-background: #fff;
  --admin-normal-border: 1px solid #f5f7fa;
  --admin-menu-item-background: #fff;
  --admin-menu-arrow: #0084fe;
  --admin-menu-active-color: #0084fe;
  --admin-menu-normal-color: #606266;
  --role-tip-color: #606266;
  --role-permission-background: #fff;
  --permission-box-border: #dcdfe6;
  --admin-module-layout-background: #fff;

  /* 班次设置 */
  --opt-btn-background: transparent;
  --check-background: #fff;
  /* 刻码规则 */
  --code-box-left-border: 1px solid #e4e7ed;
  --code-box-right-border: 1px solid #29e0e0;
  --code-box-background: #fafafa;
  /* 交互表配置 */
  --interaction-list-color: #303133;
  --interaction-list-active-background: rgba(182, 220, 255, 0.15);
  --interaction-list-active-color: #0084fe;
  --interaction-segmentation-line-color: #f0f2f5;
  /* 可视化配置 */
  --visual-config-background: #fff;
  --visual-config-border: 1px solid #0084fe;
  --visual-config-content-background: #fafafa;
  --visual-box-background: #e9e9eb;
  --visual-type-color: #0084fe;
  --visual-add-btn-background: #0084fe;
  /* 2d */
  --tow-d-opt-background: #ffffff;
  --two-d-opt-border-color: #ebeef5;
  --two-d-opt-img-background: #f0f2f5;
  --two-d-opt-img-color: #606266;
  /* 刻码调试 */
  --laser-debug-background: #fff;
  --laser-debug-layout-background: #f2f3f5;
  /* 列设置 */
  --set-column-background: #f0f2f5;
  --set-column-box-background: #fff;
  /* 提示弹窗 */
  --messagebox-background: #fff;
  /* border */
  --hmx--border-common-color-1: 1px solid #ebeef5;
  --hmx--border-common-color-2: 1px solid #dcdfe6;
  --hmx--border-common-color-3: 1px solid #dcdfe6;
  --hmx--border-common-color-4: 1px solid #dcdfe6;
  /* 低代码画布背景 */
  --lowcode-canvas-background: #e3e3e4;
  --lowcode-box-border: 2px solid #e4e7ed;
}
[data-theme="black"] {
  /* 历史变量 */
  --f-card-background: rgba(24, 87, 106, 0.8);
  --el-table-cell: rgb(12, 52, 88);
  --log-table-background: rgb(24, 52, 87);
  --params-background: rgb(14, 17, 41);

  /* 全新变量 */
  --g-font-color: #fff;
  --g-font-color-1: #fff;
  --g-wihte-font-color: #fff;
  --g-background-color: linear-gradient(
    90deg,
    #202230 0%,
    #012636 15%,
    #011d37 35%,
    #011d2f 100%
  );
  --g-input-background: rgba(0, 0, 0, 0.25);
  --body-background: rgba(0, 0, 0, 0.8);
  --g-tree-node-hover-bg-color: rgba(5, 69, 130);
  --g-checkbox-border-color: #2c3237;

  --layout-background:#000;

  /* 前台板块通用背景色 */
  --front-box-border: 1px solid #0084fe;
  --front-layout-background: #141414;

  --head-background: #141414;
  --head-color: #fff;
  --head-nav-color: #e4e7ed;
  --head-active-nav-color: #0084fe;
  --head-active-nav-background: transparent;
  --head-start-task-background: rgba(22, 120, 44, 0.65);
  --head-start-task-shadow: inset 4px 4px 4px 0px rgba(101, 231, 56, 0.25),
    inset -4px -4px 4px 0px rgba(101, 231, 56, 0.25);
  --head-start-task-border: 2px solid #29e093;
  --head-close-task-background: rgba(105, 36, 14, 0.65);
  --head-close-task-shadow: inset 4px 4px 4px 0px rgba(226, 37, 25, 0.25),
    inset -4px -4px 4px 0px rgba(226, 37, 25, 0.25);
  --head-close-task-border: 2px solid #e05529;
  --head-theme-hover-color: #b6dcff;

  --home-side-background: rgba(20, 20, 20, 0.75);
  --home-alarm-background: transparent;
  --home-status-color: #fff;
  /* --home-side-box-background: url("@/assets/images/common/side-box-bk-light.png")
    no-repeat; */
  --mudule-background: rgba(15, 107, 113, 0.8);
  /* table */
  --g-table-background-color: #101129;
  --g-table-header-bg-color: #272828;
  --g-table-empty-background: #000000;

  --tab-color: #a8abb2;
  --tab-active-color: #0084fe;
  --tab-background: #000000;
  --tab-item-background: #1a1a1a;
  --tab-border-color: #73767a;
  --tab-bottom-line-color: #4c4d4f;
  --tab-active-bottom-color: #101129;
  /* dailog */
  --dialog-body-background: #1f1f1f;
  --dialog-close-color: #fff;
  --dialog-btn-cancel-backbackground: rgba(0, 19, 36, 0.8);
  --dialog-btn-cancel-color: #fff;
  /*tootlip*/
  --g-tooltip-background: url("~@/assets/images/box/tab_panel.png") no-repeat
    center / 100% 100%;

  --vulnerability-background: #010101;
  --vulnerability-dashed-line: #fff;
  --vulnerability-form-background: #0e0e0e;
  --home-detail-dialog-background: #0e0e0e;
  --front-detail-dialog-background: #1c1c1c;
  --front-mes-content-background: #1c1c1c;
  --front-info-swtich-background: #0084fe;
  --front-info-echarts-background: #141414;
  --log-layout-background: rgba(0, 43, 83, 0.9);
  --only-ready-show-area: #262727;
  --custom-switch-button-background: #0d1a26;
  --custom-switch-button-active-background: #0084fe;
  --custom-switch-button-active-color: #fff;
  --custom-switch-button-nromal-color: #fff;
  --mes-material-from-background: #141414;
  --mes-module-background: #141414;
  --spc-layout-background: transparent;
  --dataAnalysis-background: #1c1c1c;
  --dataAnalysis-border: 1px solid #4c4d4f;
  /* 后台背景色 */
  --admin-layout-background: #000000;
  --admin-layout-padding: 10px 0;
  --admin-normal-background: #141414;
  --admin-normal-border: 1px solid #212629;
  --admin-menu-item-background: #141414;
  --admin-menu-arrow: #0084fe;
  --admin-menu-active-color: #0084fe;
  --admin-menu-normal-color: #fff;
  --role-tip-color: #b6dcff;
  --role-permission-background: rgba(0, 0, 0, 0.25);
  --permission-box-border: #4c4d4f;
  --admin-module-layout-background: #141414;

  /* 班次设置 */
  --opt-btn-background: none;
  --check-background: #004b90;
  /* 刻码规则 */
  --code-box-left-border: 1px solid transparent;
  --code-box-right-border: 1px solid #29e0e0;
  --code-box-background: transparent;
  /* 交互表配置 */
  --interaction-list-color: #0084fe;
  --interaction-list-active-background: rgba(0, 75, 144, 0.3);
  --interaction-list-active-color: #fff;
  --interaction-segmentation-line-color: #212629;
  /* 可视化配置 */
  --visual-config-background: transparent;
  --visual-config-border: 1px solid transparent;
  --visual-config-content-background: #000205;
  --visual-box-background: #303133;
  --visual-type-color: #00feef;
  --visual-add-btn-background: #004b90;
  /* 2d */
  --tow-d-opt-background: #141414;
  --two-d-opt-border-color: #212629;
  --two-d-opt-img-background: rgba(0, 0, 0, 0.25);
  --two-d-opt-img-color: #b6dcff;
  /* 刻码调试 */
  --laser-debug-background: #141414;
  --laser-debug-layout-background: rgba(0, 0, 0, 0.8);
  /* 列设置 */
  --set-column-background: #0e0e0e;
  --set-column-box-background: #303133;
  /* 提示弹窗 */
  --messagebox-background: #1f1f1f;
  /* border */
  --hmx--border-common-color-1: 1px solid #2a2f34;
  --hmx--border-common-color-2: 1px solid #555555;
  --hmx--border-common-color-3: 1px solid #212629;
  --hmx--border-common-color-4: 1px solid #212629;
  /* 低代码画布背景 */
  --lowcode-canvas-background: #02213e;
  --lowcode-box-border: 1px solid #0084fe;
}
