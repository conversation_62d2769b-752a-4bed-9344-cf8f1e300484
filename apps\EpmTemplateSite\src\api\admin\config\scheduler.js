//统一请求封装
import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix
/**
 *  查询接口
 * @param {*} data
 */
export function getSheduleList(params) {
  return request.get({
    url: `${homeServerPrefix}}/JobInfo/GetPagedList`,
    params: params,
  })
}

/**
 *  创建接口
 * @param {*} data
 */
export function createShedule(options) {
  return request.post()({
    url: `${homeServerPrefix}}/JobInfo/Create`,
    data: options,
  })
}

/**
 *  修改任务
 * @param {*} data
 */
export function updateShedule(params, options) {
  return request.put({
    url: `${homeServerPrefix}}/JobInfo/Update/${params}`,
    data: options,
  })
}

/**
 *  开启任务
 * @param {*} data
 */
export function startShedule(params) {
  return request.get({
    url: `${homeServerPrefix}}/JobInfo/Start`,
    params: params,
  })
}

/**
 *  停止任务
 * @param {*} data
 */
export function stopShedule(params) {
  return request.get({
    url: `${homeServerPrefix}}/JobInfo/Stop`,
    params: params,
  })
}

/**
 *  重启任务
 * @param {*} data
 */
export function restartShedule(params) {
  return request.get({
    url: `${homeServerPrefix}}/JobInfo/Resume`,
    params: params,
  })
}

/**
 *  删除任务
 * @param {*} data
 */
export function deleteShedule(params) {
  return request.get({
    url: `${homeServerPrefix}}/JobInfo/Delete`,
    params: params,
  })
}

/**
 *  删除任务
 * @param {*} data
 */
export function getTriggerList(params) {
  return request.get({
    url: `${homeServerPrefix}}/JobInfo/GetTriggerList`,
    params: params,
  })
}
