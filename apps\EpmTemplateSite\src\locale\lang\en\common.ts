export default {
  common: {
    okText: "Confirm",
    close: "Close",
    cancelText: "Cancel",
    submitText: "Submit",
    loadingText: "Loading...",
    saveText: "Save",
    add: "Add",
    edit: "Edit",
    preview: "Preview",
    delText: "Delete",
    startText: "start",
    restartText: "restart",
    stopText: "stop",
    resetText: "Reset",
    resetpdText: "Reset password",
    searchText: "Search",
    queryText: "Query",
    time: "Time",
    times: "Times",
    date: "Date",
    unit: "Unit",
    a: "PCS",
    seconds: "Second",
    status: "Status",
    minutes: "Minute",
    noData: "No data",
    to: "To",
    view: "View",
    publish: "Publish",

    day: "Today",
    week: "Week",
    month: "Month",

    userName: "User name",
    baseLine: "Baseline",
    detail: "Details",
    baseValue: "Base value",
    ctGood: "CT good",
    ctBad: "CT bad",

    inputText: "Please enter",
    chooseText: "Please select",

    redo: "Refresh",
    back: "Back",

    light: "Light",
    dark: "Dark",

    startTime: "Start time",
    endTime: "End time",
    alarm: "Alarm",
    alarmTime: "Alarm time",
    alarmAnalysis: "Alarm analysis",
    alarmQuery: "Alarm query",
    alarmDetail: "Alarm details",
    logTaskScheduling: "Log task scheduling",
    logKeyWord: "Keyword",
    warningCode: "Alarm code",
    operate: "Operation",
    operateTime: "Op. time",
    operateAnalysis: "Op. analysis",
    operateQuery: "Op. query",
    equipStatus: "Equipment status",
    logType: "Log type",
    scale: "Scale",

    click: "Click ",
    enter: "Enter ",
    barCode: "BarCode",

    export: "Export",
    fullTableExport: "Full table export",
    import: "Import",
    oneKeyImport: "One-click import",
    oneKeyExport: "One-click derivation",
    sync: "Synchronization",
    enable: "Enable",
    disable: "Disable",
    yes: "YES",
    no: "NO",
    show: "Show",
    hide: "Hide",
    prompt: "Prompt",
    Maximize: "Maximize",
    Minimize: "Minimize",
    read: "read",

    all: "All",
    normal: "Normal",
    selectAll: "Select all",
    isDelete: "Sure to delete?",

    success: "success",
    failure: "failure",

    download: "Download",

    reasonTitle: "Submit shutdown reasons",
    submitReason: "Submit",

    fileOversize: "File size exceeds the maximum allowed limit of",
    noFileSelected: "No file selected",

    today: "Today",
    last3Days: "Last 3 Days",
    last7Days: "Last 7 Days",

    timePeriod: "Time range",
    hour: "hour",
    minute: "minute",
    second: "second",
  },
  prompt: {
    errMsg504: "Network timeout",
    errMsg500: "Network connection error",
    prompt_1: "Interface path not found",
    prompt_2: "You have been logged out, please log in again",
    prompt_3: "Logout successful",
    prompt_4: "Failed to log out, do you want to force logout?",
    prompt_5: "Login failed, this user has no menu permissions",
    prompt_6: "Card number not detected, please retry",
    prompt_7: "Add successful!",
    prompt_8: "Edit successful!",
    prompt_9: "Delete successful!",
    prompt_10: "Import successful!",
    prompt_11: "Product historical data",
    prompt_12: "Pending start",
    prompt_13: "Sync successful",
    prompt_14: "International resource configuration table",
    prompt_15: "Do you want to reset this user's password?",
    prompt_16: "Password reset successful",
    prompt_17: "Loading",
    prompt_18:
      "Please go to the background to publish the visual configuration",
    prompt_19: "Publishing",
    prompt_20: "Please upload the background image",
    prompt_21: "Image upload failed",
    prompt_22: "Save successful",
    prompt_23: "Background image upload failed",
    prompt_24: "CT Good",
    prompt_25: "CT Abnormal",
    prompt_26: "Configuration successful",
    prompt_27: "Unchecked",
    prompt_28: "Running time",
    prompt_29: "Downtime",
    prompt_30: "Fault time",
    prompt_31: "Maintenance time",
    prompt_32: "Please enter parameter type",
    prompt_33: "Please enter MES type",
    prompt_34: "Setting successful!",
    prompt_35: "Interaction table",
    prompt_36:
      "Please enter the corresponding key and value pair, and then click confirm parameters to display on the top",
    prompt_37: "log historical data",
    prompt_38: "No product data available, please try again later!",
    prompt_39: "Release success!",
    prompt_40: "No permission ~",
    prompt_41: "Error in importing the file",
    prompt_42: "Exporting data, please wait...",
  },
  echarts: {
    oee: {
      synthesis: "Composite index",
      available: "Availability",
      performance: "Performance",
      quality: "Quality index",
    },
    station: {
      none: "None",
      offLine: "Off-Line",
      run: "Run",
      failure: "Failure",
      standby: "Standby",
      stop: "Stop",
      idle: "Idle",
      maintenance: "maintain",
      notConnect: "Not connect",
      deviceName: "Device name",
      scanStatus: "Scanning equipment",
      mesStatus: "MES status",
      deviceStatus: "Device status",
      plcStatus: "PLC status",
      alarmClearing: "Alarm clearing",
      beStarting: "Be starting",
      alarm: "Alarm",
      resetting: "Resetting",
      waitForInitialization: "Wait for initialization",
    },
    title: {
      productQuality: "Quality statistics",
      outputCount: "Output statistics",
      alarmInfo: "Alarm info",
      productTask: "Product task",
      CT: "CT process chart",
      realTimeCt: "Real time Ct(unit: seconds)",
      warnPrompt: "Warn prompt",
      oee: "Real-time OEE",
      output: "Output",
      outputYield: "Yield Trends",
      baseCapacity: "Base capacity",
      capacity: "Capacity",
      count: "Count",
      logInfo: "Log information",
      productInfo: "Product information",
      yield: "Yield",
      noYield: "Defect Rate",
      total: "total",
      narmal: "narmal",
      important: "important",
      emergent: "emergent",
      pending: "pending",
      processed: "processed",
      runningState: "Running State",
      productionAlarm: "Production Alarm",
      statusStatistics: "Status Statistics",
      connectionMonitoring: "Connection Monitoring",
      currentShiftProduction: "Current Shift Production",
      equipEnergyAndEnviron: "Equipment Energy Consumption & Environment",
      equipmentMaintenance: "Equipment Maintenance",
    },
    dialogTitle: {
      productQuality: "Product quality trend analysis",
      outputCount: "Output statistics trend analysis",
      CT: "CT process chart trend analysis",
      CtDetail: "CT table detail",
      warnPrompt: "Early warning information trend analysis",
      oee: "Real-time OEE trend analysis",
      qualityConfig: "Product quality config",
      outputConfig: "Output count config",
      ctConfig: "Ct config",
      realTimeCtConfig: "Real-time Ct config",
      warnPromptConfig: "Warn prompt config",
      oeeConfig: "Real oee config",
      outputCapacityAnalysis: "(output/Capacity)Analysis",
      outputCapacityConfig: "(output/Capacity)config",
      outputCapacityDetail: "detail-(output/Capacity)Count",
      qualityStatistics: "Quality statistics",
      qualityStatisticsAnalysis: "Quality statistics analysis",
      qualityStatisticsConfig: "Quality statistics config",
      statusStatistics: "Status statistics",
      statusStatisticsAnalysis: "Status statistics trend analysis",
      productionTask: "Product Task",
      earlyWarningInfo: "Warn prompt",
      infoDetails: " Info details",
      hstoricalLog: "Log info",
    },
    table: {
      num: "Num",
      time: "Time",
      alarmInfo: "Alarm info",
      taskNum: "Task num",
      taskName: "Task name",
      content: "content",
      warnPrompt: "Warn prompt",
    },
    tab: {
      oknum: "Qualified count",
      ngnum: "Unqualified count",
      outputCount: "Output count",
      capacityCount: "Capacity count",
      available: "Available",
      performance: "Performance",
      quality: "Quality",
    },
    log: {
      alarmStatistics: "Alarm statistics",
      alarmDistribution: "Alarm distribution",
      alarmArrangement: "Alarm layout",
      operationStatistics: "Operation statistics",
      operationLayout: "Operation layout",
      operationDistribution: "Operation distribution",
      systemStatistics: "System statistics",
      systemDistribution: "System distribution",
      systemArrangement: "System layout",
      mesStatistics: "MES statistics",
      mesDistribution: "MES distribution",
      mesArrangement: "MES layout",
      statistics: "Statistics",
      distribution: "Distribution",
      total: "Total count",
      displayMode: "Display mode",
      fiveDaysData: "Statistics for the last five days",
      hstoricalAlarmLog: "hstorical alarm log",
      hstoricalOperationLog: "hstorical operation log",
    },
    content: {
      taskName: "Task name",
      interactiveContent: "Content",
      time: "Time",
      warnInfo: "Warn prompt",
    },
    spc: {
      xbar: "XBar chart",
      r: "R chart",
      average: "Average",
      upperLimit: "Upper control limit",
      lowerLimit: "Upper control limit",
      centerLimit: "Center control limit",
      rangeValue: "Range",
      parameter: "SPC parameter",
      subgroupCapacity: "Subgroup capacity",
      a2: "A2 value",
      d3: "D3 value",
      d4: "D4 value",
      generateGraph: "Generate SPC chart",
      noData: "No SPC parameters",
    },
    product: {
      classes: "Shift",
      productStatistics: "Product statistics",
      productQuality: "Product yield",
      productData: "Product data",
      productNg: "Product NG cause distribution",
    },
  },
  card: {
    title: {
      columnSetting: "Column setting",
      clearData: "Clear data",
      hstoricalData: "Historical data",
      alarmLog: "Alarm log",
      systemLog: "System log",
      operationLog: "Operation log",
      mesLog: "MES log",
      fold: "Fold",
      spread: "Spread",
      baseInfor: "Base infor",
      processFormula: "Process formula",
      mesConfig: "MES config",
    },
    dialogTitle: {
      logDetails: "Log details",
      details: "Details",
      historicalData: "Historical data",
      editWorkInfo: "Edit workstation information",
      aluminumShell1: "Aluminum shell loading station 1",
      aluminumShell2: "Aluminum shell loading station 2",
    },
    dialogContent: {
      cellCoding: "Cell barcode",
    },
    table: {
      alarmLog: {
        alarmTime: "Alarm time",
        moduleName: "Mdl. name",
        alarmInformation: "Alarm info",
        operationSuggestion: "Op. sgst.",
        operation: "Operation",
        details: "Details",
      },
      systemLog: {
        time: "Time",
        information: "Information",
      },
      operationLog: {
        operateTime: "Op. time",
        operateTor: "Operator",
        operateType: "Op. type",
        operateDetails: "Op. details",
        operation: "Operation",
        details: "Details",
      },
      mesLog: {
        time: "Time",
        information: "Information",
      },
      productList: {
        productionResult: "Production result",
        productionType: "Production type",
        operation: "Operation",
        details: "Details",
        ngReason: "NG reason",
        regularParts: "Regular parts",
        firstPiece: "First piece",
        challengePiece: " Challenge piece",
      },
      historicalData: {
        productBarCode: "Product barcode",
        productModel: "Product model",
        productionTime: "Production time",
        startTime: "Start btime",
        endTime: "End time",
        query: "Query",
        export: "Export",
        time: "Time",
        operation: "Operation",
        details: "Details",
        result: "result",
        ngReason: "NG Reason",
        offlineUpload: "Offline upload",
        record: "Data Record",
        queryTip: "The query time cannot exceed 7 days",
      },
    },
  },
  table: {
    alarmQuery: {
      alarmCode: "Alarm code",
      content: "Content",
      startTime: "Start time",
      alarmDuration: "Alarm duration (s)",
      alarmTimes: "Alarm counts",
      alarmInformation: "Alarm information",
      serviceName: "Service name",
      moduleName: "Mdl. name",
      operationSuggestion: "Op. suggestion",
      remarks: "Remarks",
      operation: "Operation",
      details: "Details",
    },
    logTask: {
      taskName: "Task name",
      taskGroupName: "Task group name",
      startTime: "Start time",
      lastExecutionTime: "Last execution time",
      endTime: "End time",
      executionsNumber: "Execution times",
      failuresNumber: "Failures times",
      failureCause: "Failure reason",
      jobStatus: "Job status",
      triggerType: "Trigger type",
      description: "Description",
      operation: "Operation",
    },
    operateQuery: {
      userName: "User name",
      serviceName: "Service name",
      interfacePath: "Interface path",
      logLevel: "Op. type",
      content: "Content",
      request: "Request",
      creationTime: "Creation time",
      remark: "Remarks",
    },
    equipManage: {
      deviceName: "Device name",
      serviceName: "Service name",
      remark: "Remarks",
      serialNumber: "SerialNumber",
    },
    referenceLineConfig: {
      coding: "Encoding",
      secondCode: "Sec. ID code",
      value: "Value",
      remark: "Remarks",
      creationTime: "Creation time",
      updateTime: "Update time",
      productInfoParams: "Product info param",
      relevantFailNG: "Associated failure NG",
      upperLimitConfig: "Set a ceiling",
      floorLimitConfig: "Set a lower limit",
      selectType: "selectType",
    },
    paramsConfig: {
      paramTypeName: "Param. type name",
      paramType: "Param. type",
      paramKey: "Param. key",
      paramValue: "Param. value",
      remark: "Remarks",
      newParamType: "Add param. type",
      currentParamType: "Current param. type",
      newParam: "Add param.",
      syncConfig: "Sync config",
    },
    nationConfig: {
      key: "Key",
      value: "Value",
      language: "Language",
    },
    plcConfig: {
      connectionName: "Conn. name",
      connectionCode: "Conn. code",
      protocolType: "Protocol type",
      readPolicy: "Read strategy",
      writePolicy: "Write strategy",
      numFailedRetries: "Retry attempts",
      description: "Description",
      readWriteSeparation: "Read-write separation",
      failedRetryTime: "Retry time",
      connectionParameters: "Conn. param.",
    },
    interactConfig: {
      deviceName: "Device name",
      deviceNumber: "Device code",
      stationName: "Station name",
      dataNumber: "Data code",
      stationNumber: "Station code",
      dataName: "Data name",
      arrayNumber: "Array code",
      arrayIndex: "Array index",
      dataType: "Data type",
      dataLength: "Data length",
      PLCAddress: "PLC address",
      collectionFrequency: "Coll. freq.",
      SpcOrNot: "SPC or not",
      classification: "Classification",
      orderBy: "OrderBy",
    },
    userManagement: {
      name: "Full name",
      accountName: "Acct. name",
      nickname: "Nickname",
      idCard: "ID card no.",
      role: "Role",
      gender: "Gender",
      age: "Age",
      phoneNumber: "Phone no.",
      email: "Email",
      introduction: "Introduction",
      status: "Status",
      createTime: "Creation time",
      remark: "Remarks",
      male: "Male",
      female: "Female",
      unknown: "Unknown",
    },
    roleManagement: {
      roleName: "Role name",
      roleNumber: "Role code",
      displayOrder: "Display order",
      automaticLogout: "Automatic logout",
      logoutTime: "logout time(min) ",
      status: "Status",
      createTime: "Creation time",
      remark: "Remarks",
    },
    menuManagement: {
      menuName: "Menu name",
      menuIcon: "Menu icon",
      menuOrder: "Menu order",
      permissionCode: "Perm. code",
      routePath: "Route address",
      componentPath: "Component path",
      menuType: "Menu type",
      displayStatus: "Display status",
      isExternalLink: "Is External Link",
      menuStatus: "Menu status",
      createTime: "Creation time",
      remark: "Remarks",
      content: "Content",
      menu: "Menu",
      button: "Button",
      lowcode: "LowCode page",
      pageSelect: "Page selection",
    },
    mesTable: {
      interfaceName: "Ntf. name",
      interfaceType: "Ntf. type",
      equipType: "Device type",
      mesType: "Mes type",
      version: "Version",
      remark: "Remarks",
      add: "Add MES ntf. type",
      addParams: "Add param. config",
      currentMesType: "Current mes type",
      paramType: "Param type",
      paramName: "Param name",
      paramCode: "Param code",
      paramValue: "Param value",
      dataType: "Data type",
      setCurrentMesType: "Set current mes type",
      debug: "Debug",
    },
    shiftTable: {
      config: "Settings",
    },
    mudoleConfig: {
      moduleName: "Mdl. name",
      showModule: "Mod. display",
      showData: "Data display",
      setImg: "Background image setting",
      backgroundImg: "Background image",
      selectImg: "Click to select",
      moduleListSetting: "Module list setting",
    },
    visual: {
      haveReleased: "Have released",
      Instructions: "Instructions for use",
      Instructions_1:
        "1. When the interactive table data is updated, the data of the original configuration item cannot be matched, and the configuration item will be invalid.",
      Instructions_2: "2. Limit the number of configuration items to four.",
      Instructions_3:
        "3. Double-click the configuration item to enter the canvas and edit the configuration item",
      configList: "Visual list",
      addConfig: "Add config item",
      configName: "Config name",
      configType: "Config type",
      cover: "Cover",
      config_prompt_1: "Please keep the picture size within 20k",
      config_prompt_2: "There can be no more than four 2D modules",
      config_prompt_3: "There can be no more than four 3D modules",
      config_prompt_4: "Cover size cannot exceed",
      config_prompt_5: "Double click to enter",
    },
  },
  dialog: {
    remarkInfo: "Please enter remark",
    task: {
      newTask: "Add task",
      editTask: "Edit task",
      taskName: "Task name",
      taskGroupName: "Task group name",
      triggerType: "Trigger type",
      executionInterval: "Execution interval",
      executionsNumber: "Execution times",
      description: "Description",
      cycle: "(Default infinite loop)",
    },
    equipManage: {
      newEquip: "Add device",
      editEquip: "Edit device",
      deviceInfo: "Please enter device name (not number)",
      serviceInfo: "Please enter a service name",
      serialInfo: "Please enter serial number",
    },
    referenceLineConfig: {
      newReference: "Add reference",
      editReference: "Edit reference",
      codingInfo: "Please enter the code",
      secondCodeInfo: "Please enter the secondary identification code",
      valueInfo: "Please enter the value",
      productInfo: "Please select product information parameters",
      relevantFailInfo: "Please select relevant failure NG",
      upperLimitInfo: "Please set upper limit",
      floorLimitInfo: "Please set lower limit",
      selectTypeInfo: "Please select a type",
    },
    paramsConfig: {
      newParamColumns: "Add param. type",
      editParamColumns: "Edit param. type",
      parameColumnName: "Param. type name",
      parameNameInfo: "Please enter a param. type name",
      parameColumnCode: "Param. type encoding",
      parameCodeInfo: "Please enter the param. type code",
      parameColumnRemark: "Param. type remarks",
      parameRemarkInfo: "Please enter comments for the param. type",
      newParam: "Add param.",
      editParam: "Edit param.",
      paramValueRemark: "Param. value remarks",
      paramKeyInfo: "Please enter the param. key",
      paramValueInfo: "Please enter param. values",
      paramRemarkInfo: "Please enter param. values remarks",
    },
    nationConfig: {
      newNationConfig: "Added international config",
      editNationConfig: "Edit internationalization config",
      keyInfo: "Please enter key",
      valueInfo: "Please enter the value",
      langInfo: "Please enter language",
    },
    plcConfig: {
      newEquip: "Add PLC config",
      editEquip: "Edit PLC config",
      confirmParam: "Confirm parameter",
      nameInfo: "Please enter connection name",
      codeInfo: "Please enter the connection code",
      codeError: "The connection code already exists. Please enter a new one",
      protocolInfo: "Please enter the protocol type",
      readInfo: "Please enter the read strategy",
      writeInfo: "Please enter a write strategy",
      selectInfo: "Please select",
      descriptionInfo: "Please enter a description",
      connectParamInfo: "Please enter connection parameters",
      connectParamError: "The connection parameter must be a JSON object",
      connectParamRule:
        "The connection parameters should be in a valid JSON format",
      keyInfo: "Please enter key",
      keyError: "The key value cannot be empty",
      keyRule: "The key value cannot contain semicolons(;)",
      valueInfo: "Please enter value",
      valueError: "The value cannot be empty",
      valueRule: "The value cannot contain semicolons(;)",
    },
    interactConfig: {
      newInteractConfig: "Added interactive table config",
      editInteractConfig: "Edit the interaction table config",
      deviceNumberInfo: "Please enter the device number",
      deviceNameInfo: "Please enter the device name",
      stationNumberInfo: "Please enter the station number",
      stationNameInfo: "Please enter the station name",
      dataNumberInfo: "Please enter the data number",
      dataNameInfo: "Please enter the data name",
      dataLengthInfo: "Please enter the data length",
      dataTypeInfo: "Please enter the data type",
      classificationInfo: "Please enter the classification",
      collectionFrequencyInfo: "Please enter the coll. freq.",
      PLCAddressInfo: "Please enter the PLC address",
      arrayNumberInfo: "Please enter the array number",
      arrayIndexInfo: "Please enter the array index",
      spcInfo: "Please select SPC",
      orderInfo: "Please enter serial number",
    },
    userManagement: {
      userPassword: "Password",
      accountNumber: "Acct. no.",
      resetPassword: "Reset password",
      newUser: "Add user",
      editUser: "Edit user",
      nameInfo: "Please enter your name",
      nickInfo: "Please enter user nickname",
      phoneInfo: "Please enter Phone no.",
      emailInfo: "Please enter email address",
      accountInfo: "Please enter the Acct. no.",
      passwordInfo: "Please enter user password",
      cardInfo: "Please enter ID card number",
      selectInfo: "Please select",
      introInfo: "Please enter user introduction",
      newPdInfo: "new password",
      enterInfo: "Please enter",
      resetPdInfo: "Please enter reset password",
    },
    roleManagement: {
      newRole: "Add role",
      editRole: "Edit role",
      nameInfo: "Please enter role name",
      codeInfo: "Please enter role number",
      menuPermission: "Menu perm.",
    },
    menuManagement: {
      newMenu: "Add menu",
      editMenu: "Edit menu",
      rootMenu: "Root directory",
      displaySort: "Display sort",
      permissionChar: "Perm. character",
      routeParameter: "Route parameter",
      superiorMenu: "Superior menu",
      superiorInfo: "Select superior menu",
      iconInfo: "Click to select icon",
      nameInfo: "Please enter menu name",
      routeURLInfo: "Please enter route path",
      nameError: "Menu name cannot be empty",
      orderError: "Menu order cannot be empty",
      routeError: "Route path cannot be empty",
      compoInfo: "Please enter the component path",
      authInfo: "Please enter the permission identifier",
      routeParamInfo: "Please enter route parameters",
    },
    configForm: {
      dataNumber: "DataNumber",
      standardLineCode: "StandardLineCode",
      PLCAddress: "PLCAddress",
      Baseline: "Baseline",
      PLCInfo: "Please enter the PLC address",
      referInfo: "Please enter the reference line",
    },
    shiftTable: {
      shiftName: "Shift name",
      shiftInfo: "Please enter the shift name",
      startTime: "Start time",
      startInfo: "Please enter the start time",
      endTime: "End time",
      endInfo: "Please enter the end time",
      timesInfo: "Please enter flight times",
      successInfo: "Shift set successfully!",
    },
    mesDialog: {
      add: "Add MES ntf. type",
      edit: "Edit MES ntf. type",
      addParams: "Add param. config",
      editParams: "Edit param. config",
      interfaceName: "Ntf. name",
      interfaceType: "Ntf. type",
      equipType: "Device type",
      mesType: "Mes type",
      version: "Version",
      remark: "Remarks",
      paramType: "Param type",
      paramName: "Param name",
      paramCode: "Param code",
      paramValue: "Param value",
      dataType: "Data type",
    },
  },
  title: {
    equipManage: "Equipment management",
    referenceLineConfig: "Reference line configuration",
    paramConfig: "Parameter configuration",
    nationConfig: "Internationalization configuration",
    plcConfig: "PLC configuration",
    interactConfig: "Interactive table configuration",
    shiftConfig: "Shift allocation",
    mesConfig: "MES configuration",
    workOrderConfig: "Work order config",
  },
  tooltip: {
    common: {
      compoTooltip:
        "Click this button to switch the home page componentized configuration",
    },
    menuManagement: {
      linkTooltip:
        "If external links are selected, the routing address must begin with 'http(s)://'",
      routeURLTooltip:
        "The route address to visit, such as: user, if the external network address needs to be linked internally, it starts with http(s)://",
      compoTooltip:
        "The access component path, such as' system/user/index ', defaults to the 'views' directory",
      permissionTooltip:
        "Permission characters defined in the controller, (such as: system:user:add, add user permission, used for permission control)",
      routeParamTooltip:
        'Default pass parameters for access routes, such as: `{"id": 1, "name": "ry"}`',
      displayTooltip:
        "If you choose to hide, the route will not appear in the sidebar, but you can still visit",
      menuTooltip:
        "If you choose to disable, the route will not appear in the sidebar, and you cannot visit",
    },
    interactConfig: {
      categoryTooltip:
        "If multiple categories exist, separate them with commas (,)",
    },
  },
  vision: {
    versionInformation: "version information",
    projectName: "Project name",
    backEndVersion: "Backend version",
    releaseDate: "Release date",
    frontEndVersion: "Frontend version",
    upperComputer: "upper computer",
  },
}
