<template>
  <div class="base">
    <el-form :inline="true" class="search">
      <el-form-item label="时间">
        <el-date-picker
          v-model="form.time"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :disabled-date="disabledDate"
          :range-separator="$t('common.to')"
          :clearable="false"
          :shortcuts="setShortcuts('YYYY-MM-DD HH:mm:ss')"
        />
      </el-form-item>
      <el-form-item label="关键字">
        <el-input v-model="form.message" placeholder="关键字" clearable>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search" icon="search">查询</el-button>
        <el-button type="primary" @click="reset" icon="refresh">重置</el-button>
        <el-button
          type="primary"
          @click="downLoadLog"
          icon="download"
          :loading="downLoading"
          v-if="hasDown.includes(name)"
        >
          导出
        </el-button>
      </el-form-item>
    </el-form>
    <div class="table">
      <HmxTable
        :table-data="tableData"
        :options="tableOptions"
        :columns="tableColumn"
        @size-change="handlerPageSize"
        @current-change="handlerPageIndex"
        @command="handleAction"
      ></HmxTable>
    </div>
    <HmxProgress v-model="downLoading" v-if="downLoading" />
  </div>
</template>
<script setup>
import dayjs from "dayjs"
import { reactive, computed, ref, onMounted } from "vue"
import HmxTable from "@/components/hmx-table/index.vue"
import { formatTime, setShortcuts } from "@/utils"
import { getTableLogs, exportLogs } from "@/api/front/diaitalState.js"
import exportByBlob from "@/utils/exportByBlob"
import HmxProgress from "@/components/hmx-progress"

let props = defineProps({
  tableColumn: {
    type: Array,
    default: () => [],
  },
  url: {
    type: String,
    default: () => "",
  },
  exportUrl: String,
  slot: {
    type: Array,
    default: () => [],
  },
  baseOption: {
    type: Object,
    default: () => {},
  },
  name: String,
})
let emit = defineEmits(["row-opera"])

let hasDown = ["报警日志", "操作日志"]
let initTime = [
  dayjs().subtract(7, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss"),
  dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
]
let form = reactive({
  time: initTime,
  message: "",
})

let tableData = ref([])
let total = ref(0)
let loading = ref(false)
let downLoading = ref(false)
let page = reactive({
  pageIndex: 1,
  pageSize: 10,
})

const tableOptions = computed(() => {
  return {
    loading: loading.value,
    showPagination: true,
    paginationPosition: "right",
    border: true,
    paginationConfig: {
      total: total.value,
      currentPage: page.pageIndex,
      pageSize: page.pageSize,
    },
  }
})
const params = computed(() => {
  let startTime = formatTime(form.time[0])

  let endTime = formatTime(form.time[1])
  return {
    startTime,
    endTime,
    message: form.message,
    ...page,
  }
})

onMounted(() => {
  search()
})

function handlerPageSize(pageSize) {
  page.pageSize = pageSize
  page.pageIndex = 1
  getTable()
}
// 表格页数改变
function handlerPageIndex(pageIndex) {
  page.pageIndex = pageIndex
  getTable()
}

function handleAction(command, row) {
  emit("row-opera", {
    type: command,
    row: row,
    form: form,
  })
}

function disabledDate(time) {
  return time.getTime() > new Date(new Date().getTime())
}

function search() {
  page.pageIndex = 1
  getTable()
}

function reset() {
  form.time = initTime
  form.keyword = ""
}

async function getTable() {
  tableData.value = []
  loading.value = true
  try {
    let res = await getTableLogs(props.url, params.value)
    tableData.value = res?.items ?? []
    total.value = res?.totalCount ?? 0
    loading.value = false
  } catch (e) {
    console.error("日志查询出错" + e)
    loading.value = false
  }
}

async function downLoadLog() {
  downLoading.value = true
  let res = await exportLogs(props.exportUrl, params.value)
  await exportByBlob(
    res,
    props.name + params.value.startTime + "-" + params.value.endTime
  )
  downLoading.value = false
}
</script>
<style scoped lang="scss">
.base {
  width: 100%;
  height: 100%;
  padding: 0 10px;
  .search {
    height: 50px;
    a {
      color: #fff;
      text-decoration: none;
    }
  }
  .table {
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;
  }
}
</style>
