import { homeRoutes } from "@/router"
// 用户登录权限相关

const user = {
  state: {
    accessMenus: [], // 路由菜单
    navAccessMenus: [], // 前台渲染导航菜单
    frontAccessMenus: [], // 前台菜单权限列表
    adminAccessMenus: [], // 后台菜单权限列表
    authUser: {}, // 用户信息
    permissionList: [], // 用户权限列表
    isAdministrator: false,
    isFront: true, // 是否有前台看板权限，用户后台管理界面头部显示内容
    adminDefaultRoute: "/admin", // 设置后台管理默认进入的路由
    autoLogoutInfo: {
      enablePermissionDowngrade: false, // 是否开启长时间未操作处理
      permissionDowngradeTime: "", // 多久未操作的时间
    },
    mesInfo: {
      user_Name: "",
      mesLogined: false,
      online: false,
    },
  },
  mutations: {
    SET_ACCESS_MENUS: (state, val) => {
      state.accessMenus = val
      state.frontAccessMenus = val
        .filter(m => m.router.indexOf("/home/") !== -1)
        .map(n => n.router)
      state.navAccessMenus = val
        .filter(
          m =>
            m.router.indexOf("/home/") !== -1 && m.router.indexOf("help") === -1
        )
        .sort((a, b) => {
          return a.orderNum - b.orderNum
        })
        .map(item => {
          const currentItem = homeRoutes[0].children.find(
            v => v.path === item.router.substring(6)
          )
          // 单独处理preview
          const currentPreviewItem = homeRoutes[0].children.find(v =>
            v.path.includes(item.router.substring(6).split("/")[0])
          )
          if (currentItem) {
            return {
              text: item.menuName,
              class: currentItem.path,
              name: currentItem.name,
              path: item.router,
              isShow: item.isShow,
              icon: item.menuIcon,
              selectIcon: item.menuIcon + "-selected",
            }
          } else if (currentPreviewItem) {
            return {
              text: item.menuName,
              class: item.router.substring(6),
              name: currentPreviewItem.name,
              path: item.router,
              isShow: item.isShow,
              icon: item.menuIcon,
              selectIcon: item.menuIcon + "-selected",
            }
          }
        })
        .filter(item => item !== undefined)
      state.adminAccessMenus = val
        .filter(m => m.router.indexOf("admin") !== -1)
        .map(n => n.router)
    },
    SET_USER_INFO(state, val) {
      state.authUser = val
      state.permissionList = val.permissionCodes
    },
    SET_IS_ADMINISTRATOR: (state, val) => {
      state.isAdministrator = val
    },
    SET_FRONT_AUTH: (state, val) => {
      state.isFront = val
    },
    SET_ADMIN_DEFAULT_ROUTE: (state, val) => {
      state.adminDefaultRoute = val
    },
    SET_AUTO_LOGOUT_INFO: (state, val) => {
      state.autoLogoutInfo = val
    },
    SET_MES_INFO: (state, val) => {
      state.mesInfo.user_Name = val.user_Name
      state.mesInfo.mesLogined = val.mesLogined
    },
    SET_MES_ONLINE: (state, val) => {
      state.mesInfo.online = val
    },
  },
  actions: {
    updateAccessMenus({ commit }, val) {
      commit("SET_ACCESS_MENUS", val)
    },
    updateUserInfo({ commit }, val) {
      commit("SET_USER_INFO", val)
    },
  },
  getters: {
    accessMenus: state => state.accessMenus,
    frontAccessMenus: state => state.frontAccessMenus,
    adminAccessMenus: state => state.adminAccessMenus,
    authUser: state => state.authUser,
    permissionList: state => state.permissionList,
    isAdministrator: state => state.isAdministrator,
    adminDefaultRoute: state => state.adminDefaultRoute,
    isFront: state => state.isFront,
    autoLogoutInfo: state => state.autoLogoutInfo,
  },
}

export default user
