/**
 * 重置element样式
 */

$form-border-color: #0084fe;
$form-bk-color: #0084fe;
$form-input-bk-color: rgba(0, 0, 0, 0.25);
$check-bk-color: #00feef;
$check-size: 14px;
$check-width: 8px;
$check-height: 8px;
$form-color: #ffffff;
$disabled-border-color: rgba(0, 75, 144, 0.25);
$disabled-bk-color: #00274a;
$disabled-check-bk-color: #004b90;
$form-error: #fe0005;

.el-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-tabs__content {
    flex: 1;
    .el-tab-pane {
      height: 100%;
    }
  }
}

/* 表格 */
.el-table {
  --el-table-header-bg-color: var(--g-table-header-bg-color) !important;
  --el-table-tr-bg-color: #f5f7fa;
  --el-table-text-color: #303133;
  background-color: var(--g-table-background-color) !important;
}

.el-table__body-wrapper {
  background: var(--g-table-empty-background);
}

.el-table__body tr.current-row > td.el-table__cell {
  border-top: 1px solid #29e0e0;
  border-bottom: 1px solid #29e0e0;
}

/* 输入框 */
.el-input__wrapper {
  background-color: var(--g-input-background) !important;
}
.el-textarea__inner {
  background-color: var(--g-input-background) !important;
}
.el-input.is-disabled .el-input__wrapper {
  background-color: var(--el-disabled-bg-color) !important;
}

/* 弹窗 */
.el-dialog {
  --el-dialog-bg-color: var(--dialog-body-background) !important;
}
.el-dialog__headerbtn .el-dialog__close {
  font-size: 24px !important;
}
.el-dialog__body {
  padding-top: 0;
}

/* tab */
.el-tabs.el-tabs--border-card {
  .el-tabs__header {
    box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.2);
  }
}

.el-tabs__item {
  color: var(--table-cell-color) !important;
}
.el-tabs__item.is-active {
  color: var(--admin-menu-active-color) !important;
  font-weight: bold;
}

.el-collapse {
  --el-collapse-header-bg-color: transparent !important;
  --el-collapse-header-text-color: var(--g-font-color);
  --el-collapse-border-color: none !important;
  --el-collapse-content-bg-color: transparent !important;
}
.el-collapse-item__header {
  background-color: transparent !important;
}
.el-collapse-item__content {
  padding-bottom: 0 !important;
}

.search-form-input {
  width: 200px !important;
}
