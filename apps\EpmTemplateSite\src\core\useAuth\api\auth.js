import { request } from "@xfe/request"
// import { request as requests } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix
/**
 *  用户登录
 * @param {*} data 账号密码
 */
export function loginApi(data) {
  return request.post({
    url: `${homeServerPrefix}/Account/login`,
    data,
  })
}

// export function loginApi(data) {
//   return requests.post({
//     url: "${homeServerPrefix}/Account/login",
//     data,
//   })
// }

/**
 *  刷卡登录
 * @param {*} data
 */
export function loginByCardApi(data) {
  return request.post({
    url: `${homeServerPrefix}/Account/login/card`,
    data,
  })
}

/**
 *  刷新token
 */
export function refreshTokenApi() {
  return request.post({
    url: `${homeServerPrefix}/Account/login/refresh_token`,
  })
}

/**
 *  管理员重置密码
 * @param {*} id 用户id
 */
export function resetPwdApi(id, data) {
  return request.put({
    url: `${homeServerPrefix}/User/password/reset/${id}`,
    data,
  })
}

/**
 *  用户修改密码
 * @param {*} data 账号密码
 */
export function updatePwdApi(data) {
  return request.put({
    url: `${homeServerPrefix}/User/password`,
    data,
  })
}

/**
 *  获取登录用户信息、角色、菜单权限列表
 */
export function getUserInfoApi() {
  return request.get({
    url: `${homeServerPrefix}/Account`,
  })
}

/**
 *  获取登录用户的动态菜单路由
 */
export function getAccessMenusApi() {
  return request.get({
    url: `${homeServerPrefix}/Account/router`,
  })
}

/**
 *  用户退出
 */
export function logoutApi() {
  return request.post({
    url: `${homeServerPrefix}/Account/logout`,
  })
}
