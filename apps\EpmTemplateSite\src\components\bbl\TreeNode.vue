<template>
  <div class="node-content" :class="{ 'active': isSelected, 'has-children': node.children.length > 0 }"
    @click.stop="handleClick">
    <span class="node-checkbox">
      <input type="checkbox" :checked="isAlarmTarget" @click.stop="toggleAlarmTarget"
        :title="isAlarmTarget ? '取消选择此对象进行告警' : '选择此对象进行告警'" />
    </span>
    <span class="node-toggle" v-if="node.children.length > 0" @click.stop="toggleExpanded">
      {{ isExpanded ? '▼' : '►' }}
    </span>
    <span class="node-indent" v-else :style="{ width: `${16}px` }"></span>
    <span class="node-name" v-html="highlightedName" @click.stop="handleClick"></span>
    <span class="node-vertices" v-if="node.vertices > 0">
      ({{ node.vertices.toLocaleString() }})
    </span>
  </div>
  <div class="node-children" v-if="isExpanded && node.children.length > 0">
    <div v-for="child in node.children" :key="child.id" class="tree-node child-node"
      :style="{ paddingLeft: `${16}px` }">
      <TreeNode :node="child" :selected-id="selectedId" :search-query="searchQuery" :alarm-target-ids="alarmTargetIds"
        @node-click="$emit('node-click', $event)" @toggle-alarm-target="$emit('toggle-alarm-target', $event)" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// 对象树节点类型定义
type MeshTreeNode = {
  id: string;
  name: string;
  children: MeshTreeNode[];
  level: number;
  vertices: number;
  isVisible: boolean;
  parentId?: string;
};

const props = defineProps<{
  node: MeshTreeNode;
  selectedId: string;
  searchQuery?: string;
  alarmTargetIds?: string[];
}>();

const emit = defineEmits<{
  'node-click': [nodeId: string];
  'toggle-alarm-target': [nodeId: string];
}>();

const isExpanded = ref(props.node.level === 0); // 默认根节点展开

const isSelected = computed(() => {
  return props.selectedId === props.node.id;
});

const isAlarmTarget = computed(() => {
  return props.alarmTargetIds?.includes(props.node.id) || false;
});

const handleClick = () => {
  emit('node-click', props.node.id);
};

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

const toggleAlarmTarget = () => {
  emit('toggle-alarm-target', props.node.id);
};

// 高亮显示搜索结果
const highlightedName = computed(() => {
  if (!props.searchQuery) {
    return props.node.name;
  }

  const searchText = props.searchQuery.toLowerCase();
  const nodeName = props.node.name;
  const index = nodeName.toLowerCase().indexOf(searchText);

  if (index === -1) {
    return nodeName;
  }

  const before = nodeName.slice(0, index);
  const match = nodeName.slice(index, index + searchText.length);
  const after = nodeName.slice(index + searchText.length);

  return `${before}<span class="highlight">${match}</span>${after}`;
});
</script>

<style scoped>
.node-content {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.5);
  transition: all 0.2s;
  color: #333;
}

.node-content:hover {
  background-color: rgba(233, 236, 239, 0.8);
}

.node-content.active {
  background-color: #4c84ff;
  color: white;
}

.node-checkbox {
  margin-right: 4px;
  display: flex;
  align-items: center;
}

.node-checkbox input {
  cursor: pointer;
  width: 14px;
  height: 14px;
}

.node-toggle {
  width: 16px;
  height: 16px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 10px;
  margin-right: 4px;
}

.node-indent {
  display: inline-block;
  margin-right: 4px;
}

.node-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-vertices {
  font-size: 0.8rem;
  opacity: 0.7;
  margin-left: 4px;
}

.node-children {
  margin-left: 8px;
}

.child-node {
  border-left: 1px dotted rgba(0, 0, 0, 0.2);
}
</style>
