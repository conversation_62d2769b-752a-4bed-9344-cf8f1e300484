import { request } from "@xfe/request"

// 表格 增删改查 API
// url: 表格的接口地址 如：/Menu
export default url => {
  return {
    // 获取全量 / 分页 列表 opt: { data, params }等其他发送给后端的参数
    getListApi: opt =>
      request.get({
        url: `${url}`,
        ...opt,
      }),
    // 添加 opt: { data, params }等其他发送给后端的参数
    addApi: opt =>
      request.post({
        url: `${url}`,
        ...opt,
      }),
    // 修改 id: 当前修改数据的id opt: { data, params }等其他发送给后端的参数
    updateApi: (id, opt, params = "") =>
      request.put({
        url: `${url}/${id}?${params}`,
        ...opt,
      }),

    // 删除 opt: { data, params }等其他发送给后端的参数
    deleteApi: opt =>
      request.delete({
        url: `${url}`,
        ...opt,
      }),

    // 修改表格当前行的状态（用户、角色有该功能） opt: { data, params }等其他发送给后端的参数
    editStateApi: (options, opt) =>
      request.put({
        url: `${url}/${options.id}/${options.state}`,
        ...opt,
      }),
  }
}
