import { request } from "@xfe/request"
import { config } from "@/config"
const base = config.base_url.homeServerPrefix

export function getAutoItem(params) {
  return request.get({
    url: `${base}/Inspection/auto`,
    params,
  })
}

export function getTeamTimeRecord(params) {
  return request.get({
    url: `${base}/Inspection/teamTimeRecord`,
    params,
  })
}

export function postSingleItem(data) {
  return request.post({
    url: `${base}/Inspection/submitSingleItem`,
    data,
  })
}

export function postInitReport(data) {
  return request.post({
    url: `${base}/Inspection/initReport`,
    data,
  })
}

export function getRecordsChart(params) {
  return request.get({
    url: `${base}/Inspection/GetRecordsChart`,
    params,
  })
}
