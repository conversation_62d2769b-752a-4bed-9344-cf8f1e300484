import { AxiosResponse } from "axios"
import { request } from "@xfe/request"
import { config } from "@/config"
const base = config.base_url.homeServerPrefix

/**
 * 组件配置化更新参数
 */
export interface IUpdateConfig {
  dataCode: string
  datumLineCode: string
  plcAddress: string
  datumLine: string
}

/**
 * 获取组件配置化参数
 */
export interface IGetConfig {
  dataCode: string
  datumLineCode: string
}

/**
 * 组件配置化更新
 */
export function updateComponentConfig(
  params: IUpdateConfig
): Promise<AxiosResponse> {
  return request.put({
    url: `${base}/ScadaV1/Components`,
    params,
  })
}

/**
 * 获取组件化配置
 */
export function getComponentConfig(
  params: IGetConfig[]
): Promise<AxiosResponse> {
  return request.post({
    url: `${base}/ScadaV1/components`,
    params,
  })
}

/**
 * 子站组件配置化更新
 */
export function updateChildComponentConfig(
  params: IUpdateConfig
): Promise<AxiosResponse> {
  return request.put({
    url: `${base}/ScadaV1/Components`,
    params,
  })
}

/**
 * 子站获取组件化配置
 */
export function getChildComponentConfig(
  params: IGetConfig[]
): Promise<AxiosResponse> {
  return request.post({
    url: `${base}/ScadaV1/components`,
    params,
  })
}
