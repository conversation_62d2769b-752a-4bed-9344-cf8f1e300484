<template>
  <Base
    :tableColumn="tableColumn"
    :baseOption="baseOption"
    url="DigitalReport/EquipmentStatus"
    exportUrl="EquipmentStatus/export"
    :name="$t('digitalStatement.equipmentStatus')"
  ></Base>
</template>
<script setup>
import Base from "../base/index.vue"
import { ref, computed, watch } from "vue"
import { useI18n } from "@xfe/locale"
import { useStore } from "vuex"

const store = useStore()
let themeColor = computed(() => store.state.theme.themeColor)

const { t: $t } = useI18n()

let tableColumn = ref([
  {
    type: "index",
    label: "No.",
    align: "center",
    fixed: "left",
    width: "50",
    show: true,
  },
  {
    prop: "date",
    label: $t("digitalStatement.date"),
    align: "center",
    slot: "date",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "equipmentName",
    label: $t("digitalStatement.equipmentName"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "teamTime",
    label: $t("digitalStatement.shift"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "equipmentStatus",
    label: $t("digitalStatement.equipmentStatus"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "statusDuration",
    label: `${$t("digitalStatement.statusDuration")}(${$t(
      "digitalStatement.minute"
    )})`,
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "statusCount",
    label: $t("digitalStatement.statusCount"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "totalFaultTime",
    label: `${$t("digitalStatement.totalAlarmTime")}(${$t(
      "digitalStatement.minute"
    )})`,
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
])
let baseOption = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
      textStyle: {
        color: "#fff",
      },
    },
  },
  grid: {
    borderWidth: 0,
    top: "12%",
    left: "5%",
    right: "5%",
    bottom: "15%",
    textStyle: {
      color: "#fff",
    },
  },
  legend: {
    rigtt: "10%",
    top: "2%",
    textStyle: {
      color: "#fff",
    },
    data: [
      $t("digitalStatement.resetStatus"),
      $t("digitalStatement.alarmStatus"),
      $t("digitalStatement.initializationStatus"),
      $t("digitalStatement.runningStatus"),
      $t("digitalStatement.downtimeStatus"),
      $t("digitalStatement.totalAlarmTime"),
    ],
  },

  xAxis: [
    {
      type: "category",
      axisLine: {
        lineStyle: {
          color: "#fff",
        },
      },
      axisLabel: {
        color: "#fff",
        formatter: function (value) {
          return value.length > 3 ? value.slice(0, 3) + "..." : value
        },
      },
      axisTick: {
        alignWithLabel: false,
        length: 10,
      },
      data: [],
    },
    {
      type: "category",
      axisLine: {
        lineStyle: {
          color: "#fff",
        },
      },
      axisLabel: {
        color: "#fff",
        formatter: function (value) {
          return value.length > 3 ? value.slice(0, 3) + "..." : value
        },
      },
      axisTick: {
        alignWithLabel: false,
        length: 30,
      },
      axisLabel: {
        interval: 0,
      },
      position: "bottom",
      offset: 13,
      data: [],
    },
  ],
  yAxis: [
    {
      type: "value",
      name: $t("digitalStatement.minute"),
      splitLine: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: "#fff",
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        interval: 0,
      },
      splitArea: {
        show: false,
      },
    },
    {
      type: "value",
      name: "",
      splitLine: {
        show: true,
      },
      axisLine: {
        lineStyle: {
          color: "#fff",
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        interval: 0,
      },
      splitArea: {
        show: false,
      },
    },
  ],

  series: [
    {
      name: $t("digitalStatement.resetStatus"),
      type: "bar",
      yAxisIndex: 0,
      // barMaxWidth: 35,
      barGap: "10%",
      label: {
        show: false,
        position: "inside",
      },
      color: "#0084FE",
      data: [],
    },
    {
      name: $t("digitalStatement.alarmStatus"),
      type: "bar",
      yAxisIndex: 0,
      label: {
        show: false,
        position: "inside",
      },
      color: "#FF0060",
      data: [],
    },
    {
      name: $t("digitalStatement.initializationStatus"),
      type: "bar",
      yAxisIndex: 0,
      symbolSize: 10,
      label: {
        show: false,
        position: "top",
      },
      color: "#E44EF4",
      data: [],
    },
    {
      name: $t("digitalStatement.runningStatus"),
      type: "bar",
      yAxisIndex: 0,
      symbolSize: 10,
      label: {
        show: false,
        position: "top",
      },
      color: "#00DFA2",
      data: [],
    },
    {
      name: $t("digitalStatement.downtimeStatus"),
      type: "bar",
      yAxisIndex: 0,
      symbolSize: 10,
      label: {
        show: false,
        position: "top",
      },
      color: "#F6FA70",
      data: [],
    },
    {
      name: $t("digitalStatement.totalAlarmTime"),
      type: "line",
      yAxisIndex: 1,
      symbolSize: 10,
      symbol: "circle",
      label: {
        show: false,
        position: "top",
      },
      color: "#4EEEDB",
      data: [],
    },
  ],
}

const DARK_COLOR = "#fff"
const LIGHT_COLOR = "#606266"

watch(
  () => themeColor.value,
  v => {
    if (v === "light") {
      // 质量统计
      baseOption.legend.textStyle.color = LIGHT_COLOR
      baseOption.xAxis[0].axisLine.lineStyle.color = LIGHT_COLOR
      baseOption.yAxis[0].axisLabel.color = LIGHT_COLOR
      baseOption.yAxis[0].axisLine.lineStyle.color = LIGHT_COLOR
      baseOption.yAxis[1].axisLabel.color = LIGHT_COLOR
      baseOption.yAxis[1].axisLine.lineStyle.color = LIGHT_COLOR
      baseOption.series[2].label.color = LIGHT_COLOR
    } else {
      // 质量统计
      baseOption.legend.textStyle.color = DARK_COLOR
      baseOption.xAxis[0].axisLine.lineStyle.color = DARK_COLOR
      baseOption.yAxis[0].axisLabel.color = DARK_COLOR
      baseOption.yAxis[0].axisLine.lineStyle.color = DARK_COLOR
      baseOption.yAxis[1].axisLabel.color = DARK_COLOR
      baseOption.yAxis[1].axisLine.lineStyle.color = DARK_COLOR
      baseOption.series[2].label.color = DARK_COLOR
    }
  },
  { immediate: true }
)
</script>
