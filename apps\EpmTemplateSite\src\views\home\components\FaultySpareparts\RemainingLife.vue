<template>
  <div class="b-chart" ref="RemainingLifeRef"></div>
</template>

<script setup name="RemainingLife">
import {
  ref,
  defineEmits,
  defineProps,
  defineExpose,
  watch,
  nextTick,
} from "vue"
import useEcharts from "@/hooks/useEcharts"
import { RemainingLifeEcharts } from "../echartsConfig"
const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  isShow: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  "chart-click", // 点击chart
])

let RemainingLifeRef = ref(null)
const { resize } = useEcharts(
  RemainingLifeRef,
  emits,
  props,
  RemainingLifeEcharts()
)
defineExpose({
  resize,
})
watch(
  () => props.isShow,
  v => {
    v &&
      nextTick(() => {
        resize()
      })
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.b-chart {
  width: 100%;
  height: 100%;
}
</style>
