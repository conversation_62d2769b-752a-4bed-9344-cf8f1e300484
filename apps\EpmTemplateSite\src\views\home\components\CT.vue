<template>
  <div class="b-chart" ref="ct"></div>
</template>

<script setup name="BarCharts">
import {
  ref,
  defineEmits,
  defineProps,
  defineExpose,
  watch,
  nextTick,
} from "vue"
import useEcharts from "@/hooks/useEcharts"
import { CTEcharts } from "./echartsConfig"
const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  isShow: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  "chart-click", // 点击chart
])

let ct = ref(null)
const { resize } = useEcharts(ct, emits, props, CTEcharts)
defineExpose({
  resize,
})

watch(
  () => props.isShow,
  v => {
    if (v) {
      nextTick(() => {
        resize()
      })
    }
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.b-chart {
  width: 100%;
  height: 100%;
}
</style>
