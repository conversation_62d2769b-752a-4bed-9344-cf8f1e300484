<template>
  <hmx-dialog
    dialogWidth="520px"
    dialogTitle="易损件设置"
    customClass="hymson-dialog interact-dialog"
    :isVisable="options.show"
    :isFooter="true"
    @closeDialog="closeFun"
    @save="saveDialog"
  >
    <p class="title">字段列表</p>
    <div class="content">
      <el-checkbox-group v-model="data">
        <template v-for="item of options.colList" :key="item.label">
          <div class="item-box" v-if="item.label != '序号'">
            <el-checkbox :label="item.label">{{ item.label }}</el-checkbox>
          </div>
        </template>
      </el-checkbox-group>
    </div>
  </hmx-dialog>
</template>

<script setup name="HmxSetColumn">
import { ref, watch } from "vue"
import HmxDialog from "@/components/hmx-dialog.vue"

const emit = defineEmits(["onSure", "onCancel"])

const props = defineProps({
  options: {
    type: Object,
    default: () => {
      return {
        show: false,
        colList: [], // 列设置列表
      }
    },
  },
  selectedCol: {
    type: Array,
    default: () => [],
  },
})

const data = ref(props.selectedCol)

// 添加、修改设备
const saveDialog = () => {
  emit("onSure", data.value)
}

// 关闭弹框
const closeFun = () => {
  emit("onCancel")
}

watch(
  () => props.selectedCol,
  val => {}
)
</script>
<style lang="scss" scoped>
.title {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  padding-bottom: 11px;
}
.content {
  height: 336px;
  width: 100%;
  background: #00274a;
  border-radius: 1px 1px 1px 1px;
  padding: 10px;
  overflow: auto;
  .item-box {
    height: 37px;
    background: #00315f;
    border-radius: 1px 1px 1px 1px;
    margin-bottom: 10px;
    padding-left: 10px;
    :deep(.el-checkbox) {
      width: 100%;
    }
  }
}
</style>

