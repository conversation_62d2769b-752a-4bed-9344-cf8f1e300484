export default {
  repairWorkOrder: {
    repairWorkOrder: "维修工单",
    maintenancePlan: "维修计划",
    reportTime: "报修时间",
    equipmentName: "设备名称",
    faultType: "故障类型",
    status: "状态",
    faultDescription: "故障描述",
    faultReason: "故障原因",
    handlingMethod: "处理方法",
    addOrder: "新增工单",
    handleOrder: "处理工单",
    repairTime: "维修时间",
    newPlan: "新建维保计划",
    detail: "维保计划详情",
    edit: "编辑维保计划",
    deleteConfirm: "删除确认",

    causeAnalysis: {
      label: "原因分析",
      relatedToProductQuality: "与产品质量有关",
      relatedToSoftwareVersion: "与软件版本变化有关",
    },

    equipmentRepairApplication: {
      title: "设备维修申请单",
      formNumber: "表单编号",
      date: "日期",
      applicant: "申请人",
      number: "编号",
      applicationUnit: "申请单位",
      productionLine: "所属产线",
      equipmentNumber: "设备编号",
      faultTime: "故障时间",
      devNameExample: "制片应用-EPM智能运维系统",
    },

    messages: {
      updateSuccess: "工单修改成功",
      createSuccess: "新增工单成功",
      handleSuccess: "工单处理成功",
      handleError: "工单处理失败",
      updatePlanSuccess: "修改维保计划成功",
      createPlanSuccess: "新增维保计划成功",
    },

    validation: {
      chooseDate: "请选择日期",
      enterDescription: "请输入描述",
    },

    confirmation: {
      confirmHandle: "确定维修工单内容无误",
      deletePlan: "该操作不可撤回，请问确定要删除该维保计划吗",
    },

    common: {
      chooseTime: "请选择时间",
    },

    options: {
      shutdown: "停机",
      noShutdown: "不停机",
      speedReduction: "速度下降",
      other: "其他",
    },

    timeSection: {
      startTime: "开始时间",
      repairTime: "修复时间",
    },

    consumedParts: "消耗备件",
    maintenancePerson: "维修人",
    acceptanceConfirmation: "验收确认",

    maintenancePlanManagement: {
      newPlan: "新增维保计划",
      date: "日期",
      description: "描述",
      maintenancePlan: "维保计划",
    },

    qualityInspection: {
      related: "相关(质检参与验收确认)",
      unrelated: "不相关",
    },

    softwareManagement: {
      related: "相关(按《设备软件管理办法》确认软件)",
    },
  },
}
