// @ts-nocheck
import { useI18n } from "@xfe/locale"

export function lineChartsOpton(type: number, isSmooth: boolean) {
  const { t: $t } = useI18n()
  let baseLine = $t("common.baseLine")
  let options = {
    grid: {
      show: true,
      left: "8%",
      right: "9%",
      top: "18.5%",
      bottom: "5%",
      containLabel: true,
    },
    legend: {
      orient: "horizontal",
      show: true,
      x: "right",
      y: -1,
      textStyle: {
        color: "#fff", // 图例文字颜色
        fontSize: "14",
      },
      data: [baseLine],
    },
    xAxis: {
      name: dealWithXUnit(type),
      axisLine: {
        lineStyle: {
          color: "#fff", // 坐标轴线线的颜色
          type: "solid", // 坐标轴线线的类型（solid实线类型；dashed虚线类型；dotted点状类型）
          fontSize: "16",
        },
      },
      axisLabel: {
        color: "#fff",
        formatter: function (val) {
          var str = ""
          for (var i = 0; i < val.length; i++) {
            str += val[i]
            if ((i + 1) % 5 === 0) {
              str += "\n"
            }
          }
          return str
        },
      },
      data: [],
    },
    tooltip: {
      trigger: "item",
      formatter: dealWithFormatter(type),
    },
    yAxis: {
      type: "value",
      name: dealWithYUnit(type),
      axisLine: {
        lineStyle: {
          color: "#fff", // 坐标轴线线的颜色
          type: "solid", // 坐标轴线线的类型（solid实线类型；dashed虚线类型；dotted点状类型）
          fontSize: "16",
        },
      },
      axisLabel: {
        color: "#fff",
        textStyle: {},
        fontSize: 12,
      },
    },
    series: [
      {
        smooth: false,
        type: "line",
        data: [],
        symbol: "circle",
        step: "",
        lineStyle: {
          color: "#54DD13",
        },
        itemStyle: {
          normal: {
            color: "#54DD13 ", //折线点的颜色
            borderColor: "#54DD13 ", //拐点边框颜色
          },
        },
      },
      {
        name: baseLine,
        smooth: false,
        type: "line",
        symbol: "circle",
        data: [],
        lineStyle: {
          color: "#0084FE",
        },
        itemStyle: {
          normal: {
            color: "#0084FE", //折线点的颜色
            borderColor: "#0084FE", //拐点边框颜色
          },
        },
      },
    ],
  }
  if (type === 4) {
    options.series[0].smooth = false
    options.series[0].step = "start"
    options.legend.show = false
    options.yAxis.axisLabel.fontSize = 14
    options.yAxis.axisLabel.fontWeight = "bold"
    options.yAxis.axisLabel.formatter = val => {
      let text = ""
      switch (val) {
        case 0:
          text = $t("echarts.station.none")
          break
        case 1:
          text = $t("echarts.station.idle")
          break
        case 2:
          text = $t("echarts.station.run")
          break
        case 3:
          text = $t("echarts.station.stop")
          break
        case 4:
          text = $t("echarts.station.failure")
          break
        case 5:
          text = $t("echarts.station.failure")
          break
      }
      return text
    }

    options.tooltip.formatter = val => {
      let text = ""
      switch (val.data) {
        case "0":
          text = $t("echarts.station.none")
          break
        case "1":
          text = $t("echarts.station.idle")
          break
        case 2:
          text = $t("echarts.station.run")
          break
        case 3:
          text = $t("echarts.station.stop")
          break
        case 4:
          text = $t("echarts.station.failure")
          break
        case 5:
          text = $t("echarts.station.failure")
          break
      }
      return text
    }
    delete options.series[1]
  }
  return options
}

export function dealWithYUnit(type: number) {
  const { t: $t } = useI18n()
  let unit = ""
  switch (type) {
    case 1:
      unit = $t("common.unit") + "(" + $t("common.a") + ")"
      break
    case 2:
      unit = $t("common.unit") + "(" + "%" + ")"
      break
    case 3:
      unit = $t("common.unit") + "(" + $t("common.seconds") + ")"
      break
    case 4:
      unit = $t("common.status")
      break
    default:
      unit = $t("common.a")
      break
  }
  return unit
}

export function dealWithXUnit(type: number) {
  const { t: $t } = useI18n()
  let unit = ""
  switch (type) {
    case 1:
      unit = $t("common.date")
      break
    case 2:
      unit = $t("common.date")
      break
    case 3:
      unit = $t("common.date")
      break
    case 4:
      unit = $t("common.time")
      break
    default:
      unit = $t("common.date")
      break
  }
  return unit
}

export function dealWithFormatter(type: number) {
  const { t: $t } = useI18n()
  let unit = ""
  switch (type) {
    case 1:
      unit = `{c} (${$t("common.a")})`
      break
    case 2:
      unit = "{c} (%)"
      break
    case 3:
      unit = `{c} (${$t("common.seconds")})`
      break
    case 4:
      unit = ""
      break
    default:
      unit = `{c} (${$t("common.a")})`
      break
  }
  return unit
}

interface tab {
  DataCode: string
  key: string
}

export type tabs = tab[]

export const productQuality: tabs = [
  { DataCode: "oknum", key: "echarts.tab.oknum" },
  { DataCode: "ngnum", key: "echarts.tab.ngnum" },
]

export const productQualityStation: tabs = [
  { DataCode: "#okNum", key: "echarts.tab.oknum" },
  { DataCode: "#ngNum", key: "echarts.tab.ngnum" },
]

export const outputStatistic: tabs = [
  { DataCode: "outputstatistics", key: "echarts.tab.outputCount" },
]

export const productStatistics: tabs = [
  { DataCode: "#outputStatistics", key: "echarts.tab.outputCount" },
  { DataCode: "#capacityStatistics", key: "echarts.tab.capacityCount" },
]

export const realTimeOEE: tabs = [
  { DataCode: "oee", key: "OEE" },
  { DataCode: "available", key: "echarts.tab.available" },
  { DataCode: "performance", key: "echarts.tab.performance" },
  { DataCode: "quality", key: "echarts.tab.quality" },
]

export const realTimeOEEStation: tabs = [
  { DataCode: "#oee", key: "OEE" },
  { DataCode: "#available", key: "echarts.tab.available" },
  { DataCode: "#performance", key: "echarts.tab.performance" },
  { DataCode: "#quality", key: "echarts.tab.performance" },
]

export const statusEnum = [
  "echarts.station.alarmClearing",
  "echarts.station.stop",
  "echarts.station.beStarting",
  "echarts.station.idle",
  "echarts.station.none",
  "echarts.station.run",
  "echarts.station.stop",
  "echarts.station.alarm",
  "echarts.station.alarm",
  "echarts.station.none",
  "echarts.station.none",
  "echarts.station.none",
  "echarts.station.none",
  "echarts.station.none",
  "echarts.station.resetting",
  "echarts.station.none",
  "echarts.station.none",
  "echarts.station.none",
  "echarts.station.waitForInitialization",
]

export enum stopDeviceUnit {
  min = "分钟",
  hour = "小时",
  day = "天",
}
