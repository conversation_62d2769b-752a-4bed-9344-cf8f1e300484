<template>
  <div class="hymson-vertical-menu">
    <menu-tree :menu="menus" :collapse="isCollapse" />
  </div>
</template>

<script setup name="Menu">
import { reactive, toRefs, onMounted, onUnmounted } from "vue"
// import { useStore } from "vuex";
import { clientWidth } from "@/utils/index"
import MenuTree from "../menu-tree/menu.vue"
import { adminRoutes } from "@/router"

const menus = adminRoutes[0].children

const data = reactive({
  isCollapse: false,
})

// const treeMenus = computed(() => {
//   return store.getters.accessMenus;
// });

onMounted(() => {
  window.addEventListener("resize", clientChange, true)
})
onUnmounted(() => {
  window.removeEventListener("resize", clientChange)
})

let clientChange = () => {
  if (clientWidth()) {
    data.isCollapse = true
  } else {
    data.isCollapse = false
  }
}

const { isCollapse } = toRefs(data)
</script>

<style lang="scss" scoped>
.hymson-vertical-menu {
  position: relative;
  // box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.2);
  height: 100%;
  width: 200px;
  padding: 10px;
  background-color: rgba(0, 75, 144, 0.3);
  border: 1px solid #0084fe;
  .collapse-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    // box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.2);
    position: absolute;
    right: 0;
    top: 70px;
    background-color: #fff;
    z-index: 99;
    cursor: pointer;
    &:hover {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }
  }
  .logo-box {
    padding-top: 4px;
    width: 200px;
    height: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.5s;
    .logo {
      width: 120px;
    }
    > span {
      flex: none;
      font-size: 12px;
      color: var(--el-color-primary);
    }
  }
  .logo-collapse {
    width: calc(
      var(--el-menu-icon-width) + var(--el-menu-base-level-padding) * 2
    );
    .logo {
      width: 48px;
    }
    > span {
      display: none;
    }
  }
}
</style>
