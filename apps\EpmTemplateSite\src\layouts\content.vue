<template>
  <div class="xfe-layout">
    <containerLayout>
      <router-view></router-view>
    </containerLayout>
  </div>
</template>

<script setup name="content">
import containerLayout from "./components/containerLayout.vue"
</script>

<style lang="scss" scoped>
.xfe-layout {
  width: 100vw;
  height: 100vh;
  display: flex;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  // background-color: #14142b;
}

.home-module {
  position: absolute;
  z-index: 1;
  top: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  .module-box {
    position: relative;
    // margin-top: 55px;
    width: 100%;
    height: 100%;
    .td-iframe {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: calc(100% - 70px);
    }
  }
}
</style>
