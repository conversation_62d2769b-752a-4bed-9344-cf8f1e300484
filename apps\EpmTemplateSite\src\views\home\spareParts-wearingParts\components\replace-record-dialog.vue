<template>
  <!-- 增改用户弹框 -->
  <hmx-dialog
    dialogWidth="80%"
    :dialogTitle="$t('sparePartsWearingParts.replacementRecords')"
    customClass="hymson-dialog user-dialog"
    :isVisable="isShow"
    @closeDialog="closeFun"
    top="50px"
  >
    <el-form :model="form" :inline="true" class="pro-select-box">
      <el-form-item :label="$t('sparePartsWearingParts.replacementTime')">
        <el-date-picker
          v-model="form.time"
          type="datetimerange"
          :start-placeholder="$t('digitalStatement.startTime')"
          :end-placeholder="$t('digitalStatement.endTime')"
          :disabled-date="disabledDate"
          :range-separator="$t('common.to')"
          :clearable="false"
          :shortcuts="setShortcuts('YYYY-MM-DD HH:mm:ss')"
          unlink-panels
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="checkList">{{
          $t("common.queryText")
        }}</el-button>
        <el-button type="primary" @click="handleReset">{{
          $t("common.resetText")
        }}</el-button>
      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <hmx-table
      :table-data="tableData"
      :options="tableOptions"
      :columns="tableColumn"
    >
    </hmx-table>
  </hmx-dialog>
</template>

<script setup name="UserDialog">
import dayjs from "dayjs"
import { reactive, ref, computed, toRefs, watch, onMounted } from "vue"
import HmxDialog from "@/components/hmx-dialog.vue"
import { useI18n } from "@xfe/locale"
import { formatTime, setShortcuts } from "@/utils"
import { getReplacement } from "@/api/front/spareParts"

const { t: $t } = useI18n()
const emit = defineEmits(["onClose"])

const props = defineProps({
  // 是否展示弹窗
  isShow: {
    type: Boolean,
    default: false,
  },
  // 当前选中备件/易损件
  currentItem: {
    type: Object,
    default: () => ({}),
  },
})

// 对应的备件名称
const sparePartName = computed(() => props.currentItem.name)

function disabledDate(time) {
  return time.getTime() > new Date(new Date().getTime())
}

let initTime = [
  dayjs().subtract(6, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss"),
  dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
]

let tableColumn = [
  {
    prop: "name",
    label: $t("sparePartsWearingParts.name"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "usedDistance",
    label: $t("sparePartsWearingParts.usageDistance"),
    width: "80",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "alarmDistance",
    label: $t("sparePartsWearingParts.alarmThreshold"),
    width: "110",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "stopDistance",
    label: $t("sparePartsWearingParts.shutdownThreshold"),
    width: "110",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "replaceTime",
    label: $t("sparePartsWearingParts.replacementTime"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "lastBarcode",
    label: $t("sparePartsWearingParts.newBarcode"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "beforeBarcode",
    label: $t("sparePartsWearingParts.oldBarcode"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "operatorId",
    label: $t("sparePartsWearingParts.operatorId"),
    width: "100",
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "remarks",
    label: $t("sparePartsWearingParts.remarks"),
    width: "120",
    align: "center",
    showOverflowTooltip: true,
  },
]

const data = reactive({
  form: {
    time: initTime,
  },
})

const tableData = ref([])

const tableOptions = computed(() => {
  return {
    showPagination: false,
    border: false,
  }
})

const params = computed(() => {
  let startTime = formatTime(data.form.time[0])
  let endTime = formatTime(data.form.time[1])
  return {
    startTime,
    endTime,
    name: sparePartName.value,
  }
})

// 点击查询
const checkList = async () => {
  tableData.value = await getReplacement(params.value)
}

const handleReset = () => {
  data.form = {
    time: initTime,
  }
  checkList()
}

// 关闭弹框
const closeFun = () => {
  data.form = {
    time: initTime,
  }
  emit("onClose")
}

const { form } = toRefs(data)

watch(
  () => props.isShow,
  async val => {
    if (val) {
      tableData.value = await getReplacement(params.value)
    }
  }
)
</script>

<style lang="scss">
.user-dialog.el-dialog {
  .add-user-ruleForm {
    padding: 0 20px 0 0;

    .el-form-item__label {
      flex: none;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
    }

    .el-form-item__content {
      .el-select,
      .el-input,
      .el-input-number {
        flex: none;
        width: 100%;
      }
    }
  }
  .hymson-table {
    height: 400px;
  }
}
</style>

<style scoped lang="scss">
.form {
  width: 100%;
  height: 100%;
  .form-left {
    width: 100%;

    .reference-life {
      display: flex;

      .reference-life-left {
        width: 75%;
      }

      .reference-life-right {
        width: 25%;
      }
    }
  }

  .form-right {
    width: 48%;
    .three-d {
      display: flex;
      height: 100px;
      margin-bottom: 10px;
      .three-d-left {
        width: 65%;
      }
      .three-d-right {
        background-color: #bfa;
        width: 30%;
        display: flex;
        justify-content: center;
        align-items: center;
        .t-img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

::v-deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.three-d {
  display: flex;
  height: 100px;
  margin-bottom: 10px;
  .three-d-left {
    width: 65%;
  }
  .three-d-right {
    background-color: #bfa;
    width: 30%;
    display: flex;
    justify-content: center;
    align-items: center;
    .t-img {
      width: 100%;
      height: 100%;
    }
  }
}

::v-deep(.el-input.is-disabled .el-input__inner) {
  -webkit-text-fill-color: #fff !important;
}
::v-deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: rgb(29, 31, 34) !important;
}
::v-deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: rgb(29, 31, 34) !important;
}
</style>
