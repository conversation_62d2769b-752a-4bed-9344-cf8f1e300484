<template>
  <div class="alarm-control-panel">
    <h3>告警控制</h3>

    <!-- 已选对象显示 -->
    <div class="selected-targets" v-if="alarmTargetIds.length > 0">
      <h4>已选告警目标:</h4>
      <div class="target-list">
        <div v-for="meshId in alarmTargetIds" :key="meshId" class="target-item">
          <span>{{ getMeshName(meshId) }}</span>
          <button class="remove-btn" @click="removeAlarmTarget(meshId)">✕</button>
        </div>
      </div>
      <button class="clear-btn" @click="clearAlarmTargets">清空所有</button>
    </div>

    <!-- 提示信息 -->
    <div v-else class="info-empty">
      请在对象树中选择告警目标
    </div>

    <div class="alarm-controls">
      <!-- 告警颜色选择 -->
      <input type="color" :value="colorHexString" @input="handleColorChange" :disabled="!modelLoaded" />

      <!-- 中心按钮开关 -->
      <div class="switch-container">
        <label>
          <input type="checkbox" v-model="showCenterButton" />
          显示中心操作按钮
        </label>
      </div>

      <!-- 告警触发按钮 -->
      <button @click="triggerAlarm" :disabled="!modelLoaded || alarmTargetIds.length === 0"
        :class="{ 'alarm-active': isAlarming }">
        {{ isAlarming ? '停止告警' : '开始告警' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';

// 组件属性
const props = defineProps<{
  alarmTargetIds: string[];
  meshNames: Map<string, string>;
  modelLoaded: boolean;
  isAlarming: boolean;
}>();

// 组件事件
const emit = defineEmits<{
  'remove-alarm-target': [id: string];
  'clear-alarm-targets': [];
  'update:isAlarming': [value: boolean];
  'update:color': [value: { r: number; g: number; b: number }];
  'update:showCenterButton': [value: boolean];
}>();

// 颜色设置
const color = ref({ r: 1, g: 0, b: 0 });

// 中心按钮显示状态
const showCenterButton = ref(false);

// 将RGB颜色转换为十六进制字符串
const colorHexString = computed(() => {
  const r = Math.round(color.value.r * 255).toString(16).padStart(2, '0');
  const g = Math.round(color.value.g * 255).toString(16).padStart(2, '0');
  const b = Math.round(color.value.b * 255).toString(16).padStart(2, '0');
  return `#${r}${g}${b}`;
});

// 根据网格ID获取网格名称
const getMeshName = (meshId: string): string => {
  return props.meshNames.get(meshId) || meshId;
};

// 移除告警目标
const removeAlarmTarget = (meshId: string) => {
  emit('remove-alarm-target', meshId);
};

// 清空所有告警目标
const clearAlarmTargets = () => {
  emit('clear-alarm-targets');
};

// 处理颜色变更
const handleColorChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (!target.value) return;

  // 将HTML颜色字符串转换为RGB对象
  const hex = target.value.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  color.value = { r, g, b };
  emit('update:color', { r, g, b });
};

// 告警触发
const triggerAlarm = () => {
  emit('update:isAlarming', !props.isAlarming);
  emit('update:showCenterButton', showCenterButton.value);
};
</script>

<style scoped>
.alarm-control-panel {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

h3 {
  font-size: 1rem;
  margin-bottom: 10px;
  color: #555;
}

h4 {
  font-size: 0.9rem;
  margin: 10px 0;
  color: #666;
}

.selected-targets {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.target-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-height: 120px;
  overflow-y: auto;
}

.target-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 8px;
  background-color: rgba(245, 245, 245, 0.8);
  border-radius: 4px;
  font-size: 0.9rem;
}

.remove-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 0 5px;
  font-size: 0.9rem;
  border-radius: 3px;
  transition: all 0.2s;
}

.remove-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #666;
}

.clear-btn {
  margin-top: 10px;
  padding: 6px 10px;
  background-color: #f8f9fa;
  color: #666;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s;
  align-self: flex-end;
}

.clear-btn:hover {
  background-color: #e9ecef;
}

.info-empty {
  text-align: center;
  padding: 20px 0;
  color: #6c757d;
  font-style: italic;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  margin-bottom: 10px;
}

.alarm-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

input[type="color"] {
  width: 100%;
  height: 40px;
  border: 1px solid rgba(221, 221, 221, 0.7);
  border-radius: 4px;
  cursor: pointer;
}

.switch-container {
  display: flex;
  align-items: center;
  margin: 5px 0;
}

.switch-container label {
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.switch-container input[type="checkbox"] {
  margin-right: 8px;
  cursor: pointer;
}

button {
  padding: 8px 12px;
  background-color: #4c84ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

button:hover:not(:disabled) {
  background-color: #3a6fd9;
}

button:disabled {
  background-color: rgba(204, 204, 204, 0.7);
  cursor: not-allowed;
}

.alarm-active {
  background-color: #dc3545;
}

.alarm-active:hover:not(:disabled) {
  background-color: #c82333;
}
</style>
