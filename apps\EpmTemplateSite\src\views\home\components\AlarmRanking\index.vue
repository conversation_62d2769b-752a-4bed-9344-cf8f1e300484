<template>
  <div class="b-chart" ref="alarmRankingRef"></div>
</template>

<script setup name="BarCharts">
import {
  ref,
  defineEmits,
  defineProps,
  defineExpose,
  watch,
  nextTick,
  onMounted,
} from "vue"
import useEcharts from "@/hooks/useEcharts"
import { alarmRankingEcharts } from "../echartsConfig.js"
const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  isShow: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  "chart-click", // 点击chart
])

let alarmRankingRef = ref(null)
const { resize } = useEcharts(
  alarmRankingRef,
  emits,
  props,
  alarmRankingEcharts
)
onMounted(() => {
  // const charts = getChart()
  // // 事件监听（建议放在图表初始化后）
  // charts.on("mousemove", { componentType: "yAxis" }, params => {
  //   charts.dispatchAction({
  //     type: "showTip",
  //     seriesIndex: 0,
  //     dataIndex: params.dataIndex,
  //     position: [params.event.offsetX, params.event.offsetY],
  //   })
  // })
})
defineExpose({
  resize,
})
watch(
  () => props.isShow,
  v => {
    v &&
      nextTick(() => {
        resize()
      })
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.b-chart {
  width: 100%;
  height: 100%;
}
</style>
