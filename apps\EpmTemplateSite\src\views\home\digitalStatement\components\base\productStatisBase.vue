<template>
  <div class="base">
    <el-form :inline="true" class="search">
      <el-form-item :label="$t('common.time')">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          :start-placeholder="$t('digitalStatement.startTime')"
          :end-placeholder="$t('digitalStatement.endTime')"
          :disabled-date="disabledDate"
          :range-separator="$t('common.to')"
          :clearable="false"
          :shortcuts="setShortcuts()"
        />
      </el-form-item>
      <el-form-item :label="$t('digitalStatement.shift')">
        <el-select
          v-model="form.class"
          class="form-input"
          :placeholder="$t('digitalStatement.selectShift')"
          clearable
        >
          <el-option
            v-for="team of teamType"
            :label="team.label"
            :value="team.value"
            :key="team.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search" icon="search">{{
          $t("digitalStatement.query")
        }}</el-button>
        <el-button type="primary" @click="reset" icon="refresh">{{
          $t("digitalStatement.reset")
        }}</el-button>
      </el-form-item>
    </el-form>
    <div class="chart">
      <BarLine
        :baseOption="baseOption"
        :option="barOption"
        class="base-chart"
        @chart-legend="chartLegend"
      />
      <Pie
        :option="pieOption"
        :pieState="pieState"
        class="pie"
        v-if="name == $t('digitalStatement.equipmentStatus')"
      />
      <div
        class="fault-rank"
        v-if="name == $t('digitalStatement.faultAnalysis')"
      >
        <BarRank
          class="rank"
          :title="$t('digitalStatement.faultMinutes')"
          :option="rankOption.time"
        />
        <BarRank
          class="rank"
          :title="$t('digitalStatement.faultCounts')"
          :option="rankOption.count"
        />
      </div>
    </div>
    <div class="table">
      <HmxTable
        :table-data="tableData"
        :options="tableOptions"
        :columns="columns"
        @size-change="handlerPageSize"
        @current-change="handlerPageIndex"
        @command="handleAction"
      >
        <template #date="{ row }">
          {{ formatTime(row.date, "YYYY-MM-DD") }}
        </template>
      </HmxTable>
    </div>
  </div>
</template>
<script setup name="productStatisBase">
import dayjs from "dayjs"
import { reactive, computed, ref, onMounted } from "vue"
import BarLine from "./barLine.vue"
import Pie from "./pie.vue"
import BarRank from "./barRank.vue"
import HmxTable from "@/components/hmx-table/index.vue"
import {
  getTableChart,
  getTeamTime,
  getFaultCount,
  getFaultTime,
  getProductionBar,
} from "@/api/front/diaitalState.js"
import { useI18n } from "@xfe/locale"
import { formatTime, setShortcuts } from "@/utils"
import { config } from "@/config"

let props = defineProps({
  tableColumn: {
    type: Array,
    default: () => [],
  },
  url: {
    type: String,
    default: () => "",
  },
  exportUrl: String,
  slot: {
    type: Array,
    default: () => [],
  },
  baseOption: {
    type: Object,
    default: () => {},
  },
  name: String,
})
let emit = defineEmits(["row-opera"])

const { t: $t } = useI18n()

let initTime = [
  dayjs().subtract(6, "day").startOf("day").format("YYYY-MM-DD HH:mm:ss"),
  dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
]
let form = reactive({
  time: initTime,
  class: "",
})
let legend = ref({})
let ngInfo = ref([])
let teamType = ref([])
let pieOption = ref({
  series: { data: [] },
})
let pieState = ref([
  { name: $t("digitalStatement.resetStatus"), value: 0, time: 0 },
  { name: $t("digitalStatement.alarmStatus"), value: 0, time: 0 },
  { name: $t("digitalStatement.initializationStatus"), value: 0, time: 0 },
  { name: $t("digitalStatement.runningStatus"), value: 0, time: 0 },
  { name: $t("digitalStatement.downtimeStatus"), value: 0, time: 0 },
])
let productOption = reactive({
  xAxis: { data: [] },
  series: [{ data: [] }, { data: [] }],
})
let barOption = reactive({
  xAxis: [{ data: [] }, { data: [] }],
  series: [
    { data: [] },
    { data: [] },
    { data: [] },
    { data: [] },
    { data: [] },
    { data: [] },
    { data: [] },
  ],
})
let rankOption = reactive({
  time: {
    yAxis: [{}, { data: [] }],
    series: { data: [] },
  },
  count: {
    yAxis: [{}, { data: [] }],
    series: { data: [] },
  },
})

let downLoading = ref(false)
let tableData = ref([])
let total = ref(0)
let loading = ref(false)
let page = reactive({
  pageIndex: 1,
  pageSize: 10,
})

let columns = computed(() => {
  return props.tableColumn.filter(item => item.show)
})

const tableOptions = computed(() => {
  return {
    loading: loading.value,
    showPagination: true,
    paginationPosition: "right",
    border: true,
    paginationConfig: {
      total: total.value,
      currentPage: page.pageIndex,
      pageSize: page.pageSize,
    },
  }
})
const params = computed(() => {
  let startDate = formatTime(form.time[0], "YYYY-MM-DD 00:00:00")
  let endDate = formatTime(form.time[1], "YYYY-MM-DD 23:59:59")
  return {
    startDate,
    endDate,
    teamTime: form.class,
    ...page,
  }
})

onMounted(() => {
  getTeam()
  search()
})

function handlerPageSize(pageSize) {
  page.pageSize = pageSize
  page.pageIndex = 1
  getTable(params.value)
}
// 表格页数改变
function handlerPageIndex(pageIndex) {
  page.pageIndex = pageIndex
  getTable(params.value)
}

function handleAction(command, row) {
  emit("row-opera", {
    type: command,
    row: row,
    form: form,
  })
}

function disabledDate(time) {
  return time.getTime() > new Date(new Date().getTime())
}

function search() {
  let diffTime = dayjs(form.time[1]).diff(dayjs(form.time[0]))
  if (diffTime > 7 * 24 * 60 * 60 * 1000) {
    return ElMessage.warning($t("digitalStatement.timeWarningInfo"))
  }

  page.pageIndex = 1
  getTable()
  props.name == $t("digitalStatement.faultAnalysis") && getRankvalue()
}

async function getTeam() {
  teamType.value = [
    {
      value: 1,
      label: $t("digitalStatement.dayShift"),
    },
    {
      value: 2,
      label: $t("digitalStatement.nightShift"),
    },
  ]
}

function reset() {
  form.time = initTime
  form.class = ""
  search()
}

async function getTable() {
  tableData.value = []
  loading.value = true
  let StartDate = formatTime(form.time[0], "YYYY-MM-DD 00:00:00")
  let EndDate = formatTime(form.time[1], "YYYY-MM-DD 23:59:59")
  let TeamTime = form.class
  try {
    let res = await getTableChart(props.url, params.value)
    tableData.value = res.items ?? []
    total.value = res?.totalCount > 0 ? res?.totalCount : 0
    let chartData = {
      xAxis0: [], //白晚班x轴
      xAxis1: [], //日期x轴
      yAxis0: [],
      yAxis1: [],
      yAxis2: [],
      yAxis3: [],
      yAxis4: [],
      yAxis5: [],
      yAxis6: [],
    }
    switch (props.name) {
      case $t("digitalStatement.productionStatistics"):
        const productionRes = await getProductionBar({
          StartDate,
          EndDate,
          TeamTime,
        })
        chartData = setProductData(productionRes, chartData)
        break
      case $t("digitalStatement.faultAnalysis"):
        chartData = setFaultData(res, chartData)
        break
      case $t("digitalStatement.equipmentStatus"):
        chartData = setDeviceData(res, chartData)
        break
      default:
        break
    }
    // 生产统计charts数据不同
    if (props.name == $t("digitalStatement.productionStatistics")) {
      productOption.xAxis.data = chartData.xAxis0
      productOption.series[0].data = chartData.yAxis0
      productOption.series[1].data = chartData.yAxis1
      // 由于生产统计数据格式不同，替换option
      barOption = productOption
    } else {
      barOption.xAxis[0].data = chartData.xAxis0
      barOption.xAxis[1].data = chartData.xAxis1
      barOption.series[0].data = chartData.yAxis0
      barOption.series[1].data = chartData.yAxis1
      barOption.series[2].data = chartData.yAxis2
      barOption.series[3].data = chartData.yAxis3
      barOption.series[4].data = chartData.yAxis4
      if (props.name == $t("digitalStatement.faultAnalysis")) {
        barOption.series[5].data = chartData.yAxis5
        barOption.series[6].data = chartData.yAxis6
      }
      if (props.name == $t("digitalStatement.equipmentStatus")) {
        barOption.xAxis[1].data = chartData.xAxis1.map(item =>
          formatTime(item, "YYYY-MM-DD")
        )
        barOption.series[5].data = chartData.yAxis5
        console.log(barOption, "barOption")
      }
    }
    loading.value = false
  } catch (e) {
    loading.value = false
    console.error($t("digitalStatement.queryTableWarningInfo") + e)
  }
}
function setPieData() {
  pieOption.value = {
    series: {
      data: pieState.value,
    },
  }
}
function getRankvalue() {
  let StartDate = formatTime(form.time[0], "YYYY-MM-DD")
  let EndDate = formatTime(form.time[1], "YYYY-MM-DD")
  let TeamTime = form.class
  setTimes({ StartDate, EndDate, TeamTime })
  setCounts({ StartDate, EndDate, TeamTime })
}
async function setTimes(params) {
  let res = await getFaultTime(params)
  let data = res ?? []
  data.sort((a, b) => a.totalTime - b.totalTime).slice(0, 10)
  rankOption.time.yAxis[1].data = data?.map(item => item.faultName)
  rankOption.time.series.data = data?.map(item => ({
    name: item.faultName,
    value: item.totalTime,
  }))
}
async function setCounts(params) {
  let res = await getFaultCount(params)
  let data = res ?? []
  data.sort((a, b) => a.totalCount - b.totalCount).slice(0, 10)
  rankOption.count.yAxis[1].data = data?.map(item => item.faultName)
  rankOption.count.series.data = data?.map(item => ({
    name: item.faultName,
    value: item.totalCount,
  }))
}

function setProductData(res, chartData) {
  const productList = res ?? []
  // 1. 按日期聚合班次产量
  const dateMap = {}

  productList.forEach(item => {
    // 创建日期键（保留原始日期格式）
    const dateKey = item.date
    // 初始化日期条目
    if (!dateMap[dateKey]) {
      dateMap[dateKey] = {
        [$t("digitalStatement.dayShift")]: 0,
        [$t("digitalStatement.nightShift")]: 0,
      }
    }

    // 遍历productionBarBases数组
    item.productionBarBases?.forEach(bar => {
      if (
        bar.teamTime === $t("digitalStatement.dayShift") ||
        bar.teamTime === $t("digitalStatement.nightShift")
      ) {
        dateMap[dateKey][bar.teamTime] += bar.yield
      }
    })
  })

  // 2. 获取并排序所有日期（带时间）
  const sortedDates = Object.keys(dateMap).sort(
    (a, b) => new Date(a) - new Date(b)
  )

  chartData.xAxis0 = sortedDates
  chartData.yAxis0 = sortedDates.map(
    date => dateMap[date][$t("digitalStatement.dayShift")]
  )
  chartData.yAxis1 = sortedDates.map(
    date => dateMap[date][$t("digitalStatement.nightShift")]
  )

  return chartData
}
function setFaultData(res, chartData) {
  let items = res.items ?? []
  items.forEach(item => {
    chartData.xAxis0.push(item.teamTime)
    !chartData.xAxis1.includes(item.faultDate) &&
      chartData.xAxis1.push(item.faultDate)
    chartData.yAxis0.push(item.totalTime)
    chartData.yAxis1.push(item.repairTime)
    chartData.yAxis2.push(item.mttr)
    chartData.yAxis3.push(item.mtbf)
    chartData.yAxis4.push(item.totalCount)
    chartData.yAxis5.push(item.faultRate)
    chartData.yAxis6.push(item.oee)
  })
  return chartData
}
function setDeviceData(res, chartData) {
  let items = res.items ?? []
  pieState.value = [
    { name: $t("digitalStatement.resetStatus"), value: 0, time: 0 },
    { name: $t("digitalStatement.alarmStatus"), value: 0, time: 0 },
    { name: $t("digitalStatement.initializationStatus"), value: 0, time: 0 },
    { name: $t("digitalStatement.runningStatus"), value: 0, time: 0 },
    { name: $t("digitalStatement.downtimeStatus"), value: 0, time: 0 },
  ]
  let preDay = {}
  items.forEach(item => {
    if (!preDay[item.date]) {
      preDay[item.date] = []
    }
    !preDay[item.date].includes(item.teamTime) &&
      preDay[item.date].push(item.teamTime)
    !chartData.xAxis1.includes(item.date) && chartData.xAxis1.push(item.date)
    if (item.equipmentStatus == $t("digitalStatement.resetStatus")) {
      pieState.value[0].time += item.statusDuration
      pieState.value[0].value += item.statusCount
      chartData.yAxis0.push(item.statusDuration)
    } else if (item.equipmentStatus == $t("digitalStatement.alarmStatus")) {
      pieState.value[1].time += item.statusDuration
      pieState.value[1].value += item.statusCount
      chartData.yAxis1.push(item.statusDuration)
    } else if (
      item.equipmentStatus == $t("digitalStatement.initializationStatus")
    ) {
      pieState.value[2].time += item.statusDuration
      pieState.value[2].value += item.statusCount
      chartData.yAxis2.push(item.statusDuration)
      chartData.yAxis4.push(item.totalFaultTime)
    } else if (item.equipmentStatus == $t("digitalStatement.runningStatus")) {
      pieState.value[3].time += item.statusDuration
      pieState.value[3].value += item.statusCount
      chartData.yAxis3.push(item.statusDuration)
    } else if (item.equipmentStatus == $t("digitalStatement.downtimeStatus")) {
      pieState.value[4].time += item.statusDuration
      pieState.value[4].value += item.statusCount
      chartData.yAxis4.push(item.statusDuration)
      chartData.yAxis5.push(item.totalFaultTime)
    }
  })
  for (let key in preDay) {
    chartData.xAxis0.push(...preDay[key])
  }
  setPieData() //处理饼图数据
  return chartData
}
function setEfficencyData(res, chartData) {
  let items = res.items ?? []
  items.forEach(item => {
    chartData.xAxis0.push(item.teamTime)
    !chartData.xAxis1.includes(item.creationTime) &&
      chartData.xAxis1.push(item.creationTime)
    chartData.yAxis0.push(item.averagePPM)
    chartData.yAxis1.push(item.instantaneousPPM)
    chartData.yAxis2.push(item.standardPPM)
    chartData.yAxis3.push(item.instantaneousPPM)
    chartData.yAxis4.push(item.averagePPM)
  })
  return chartData
}

function chartLegend(data) {
  let col = props.tableColumn

  for (let item of col) {
    for (let key in data.selected) {
      if (item.label.includes(key)) {
        item.show = data.selected[key]
      }
    }
  }
  if (props.name == $t("digitalStatement.equipmentStatus")) {
    legend.value = data.selected
  }
}
</script>
<style scoped lang="scss">
.base {
  width: 100%;
  height: 100%;
  padding: 0 10px;
  .search {
    height: 50px;
    a {
      color: #fff;
      text-decoration: none;
    }
  }
  .chart {
    height: 300px;
    display: flex;
    .base-chart {
      flex: 1;
      margin: 0 10px;
    }
    .pie {
      width: 500px;
    }
    .fault-rank {
      width: 700px;
      display: flex;
      .rank {
        flex: 1;
      }
    }
  }
  .table {
    height: calc(100% - 350px);
    display: flex;
    flex-direction: column;
  }
}
</style>
