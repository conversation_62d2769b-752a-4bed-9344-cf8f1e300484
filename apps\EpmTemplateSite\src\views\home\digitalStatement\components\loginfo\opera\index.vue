<template>
  <baseCard
    :tableColumn="tableColumn"
    url="log/operate"
    name="操作日志"
    exportUrl="Log/ExportOperateLog"
  />
</template>
<script setup>
import baseCard from "../base/index.vue"
import { useI18n } from "@xfe/locale"
const { t: $t } = useI18n()

let tableColumn = [
  { type: "index", width: "50", label: "No.", align: "center" },
  {
    prop: "userName",
    label: $t("table.operateQuery.userName"),
    //align: "center",
  },
  // {
  //   prop: "serverName",
  //   label: $t("table.operateQuery.serviceName"),
  //   //align: "center",
  // },
  {
    prop: "apiName",
    label: $t("table.operateQuery.interfacePath"),
    //align: "center",
  },
  {
    prop: "operationType",

    label: $t("table.operateQuery.logLevel"),
    //align: "center",
  },
  {
    prop: "message",
    label: $t("table.operateQuery.content"),
    //align: "center",
  },
  {
    prop: "creationTime",
    label: $t("table.operateQuery.creationTime"),
    //align: "center",
    slot: "creationTime",
  },
  {
    prop: "remark",
    label: $t("table.operateQuery.remark"),
    //align: "center",
  },
]
</script>
