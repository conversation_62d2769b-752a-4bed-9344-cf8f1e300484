import * as echarts from "echarts"
import "echarts-liquidfill"
import { clientWidth, wrapText } from "@/utils"
import { useI18n } from "@xfe/locale"
import dayjs from "dayjs"

const { t: $t } = useI18n()

let paramsValue = 0.5
const gradientColor = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
  {
    offset: 0,
    color: "#5c53de",
  },
  {
    offset: 1,
    color: "#18c8ff",
  },
])
export const RealOEECharts = {
  series: [
    {
      name: $t("common.scale"),
      type: "gauge",
      radius: "100%",
      center: ["50%", "60%"],
      min: 0, //最小刻度
      max: 16, //最大刻度
      splitNumber: 8, //刻度数量
      startAngle: 225,
      endAngle: -45,
      axisLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: [[1, "#004B90"]],
        },
      },
      axisLabel: {
        show: false,
      },
      //刻度标签。
      axisTick: {
        show: true,
        splitNumber: 7,
        lineStyle: {
          color: "#004B90",
          width: 1,
        },
        length: -10,
      },
      //刻度样式
      splitLine: {
        show: false,
      },
      //分隔线样式
      detail: {
        show: false,
      },
      pointer: {
        show: false,
      },
    },
    {
      type: "gauge",
      radius: "80%",
      center: ["50%", "60%"],
      splitNumber: 0, //刻度数量
      startAngle: 225,
      endAngle: -45,
      axisLine: {
        show: true,
        lineStyle: {
          width: 10,
          color: [
            [paramsValue, gradientColor],
            [1, "#004B90"],
          ],
        },
      },
      //分隔线样式。
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      pointer: {
        show: false,
      },
      title: {
        show: true,
        offsetCenter: [0, "80%"], // x, y，单位px
        textStyle: {
          color: "#fff",
          fontSize: 13,
        },
      },
      //仪表盘详情，用于显示数据。
      detail: {
        show: true,
        offsetCenter: [0, "10%"],
        color: "#fff",
        formatter: function (params) {
          return params + "%"
        },
        textStyle: {
          fontSize: 16,
        },
      },
      data: [],
    },
  ],
}

export const CTEcharts = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
  },
  dataZoom: [
    {
      type: "slider",
      show: true,
      yAxisIndex: [0],
      left: "90%",
      width: 10,
      maxValueSpan: 6,
      minValueSpan: 1,
      backgroundColor: "#616573",
      borderColor: "none",
      fillerColor: "#2B98FF",
      textStyle: {
        color: "#fff",
      },
    },
  ],
  legend: {
    show: true,
    x: "4%",
    itemWidth: 12,
    textStyle: {
      color: "#fff",
    },
    icon: "square",
    data: ["基准值", "CT良好", "CT异常"],
  },
  grid: {
    top: "16%",
    left: "3%",
    right: "15%",
    bottom: clientWidth() ? "10%" : "3%",
    containLabel: true,
  },
  xAxis: {
    type: "value",
    data: [],
    axisLabel: {
      fontSize: clientWidth() ? "8" : "10", // 文字字体大小
      color: "#fff",
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: ["#315070"],
        width: 1,
        type: "solid",
      },
    },
  },
  yAxis: {
    type: "category",
    data: [],
    axisLabel: {
      color: "#fff", // 刻度标签文字的颜色
      fontSize: clientWidth() ? "8" : "12", // 文字字体大小
      interval: 0,
      formatter: function (value) {
        // y轴自定义数据
        return wrapText(value)
      },
    },
  },
  series: [
    {
      type: "bar",
      name: "基准值",
      barWidth: "60%",
      itemStyle: {
        color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
          {
            //只要修改前四个参数就ok
            offset: 0,
            color: "#B6DCFF", // #0055FF  #005BEA
          }, //柱图渐变色
          {
            offset: 1,
            color: "#D9D9D9", //#0783FA   #00C6FB
          },
        ]),
      },
      data: [],
    },
    {
      type: "bar",
      name: "CT良好",
      itemStyle: {
        color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
          {
            //只要修改前四个参数就ok
            offset: 0,
            color: "#54DD13", // #0055FF  #005BEA
          }, //柱图渐变色
          {
            offset: 1,
            color: "#FEF900", //#0783FA   #00C6FB
          },
        ]),
      },
      barWidth: "60%",
      data: [],
    },
    {
      type: "bar",
      name: "CT异常",
      itemStyle: {
        color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
          {
            //只要修改前四个参数就ok
            offset: 0,
            color: "#FE0005", // #0055FF  #005BEA
          }, //柱图渐变色
          {
            offset: 1,
            color: "#FF9900", //#0783FA   #00C6FB
          },
        ]),
      },
      barWidth: "60%",
      data: [],
    },
  ],
}

var colorList = ["#84FE00", "#FE0005"]
export const productQualityEcharts = {
  title: {
    x: "center",
    y: "center",
    textStyle: {
      color: "#fff",
    },
  },
  tooltip: {
    trigger: "item",
    formatter: `{b} : {c} ${$t("common.times")}  ({d}%)`,
    position: "right",
  },
  legend: {
    orient: "vertical",
    right: "right",
    itemWidth: 12,
    icon: "square",
    textStyle: {
      color: "#fff",
    },
    data: [$t("echarts.tab.oknum"), $t("echarts.tab.ngnum")],
  },
  series: [
    {
      type: "pie",
      z: 3,
      center: ["50%", "50%"],
      radius: ["30%", "45%"],
      clockwise: true,
      avoidLabelOverlap: true,
      hoverOffset: 15,
      itemStyle: {
        normal: {
          color: function (params) {
            return colorList[params.dataIndex]
          },
          label: {
            formatter: function (params) {
              if (params.name !== "") {
                return params.value + "\n" + "\n" + params.percent + "%"
              } else {
                return ""
              }
            },
          },
        },
      },
      labelLine: {
        normal: {
          length: 10,
          length2: 20,
          lineStyle: {
            width: 1,
          },
        },
      },
      data: [],
    },
  ],
}

export const oeeEcharts = {
  series: [
    {
      name: "刻度",
      type: "gauge",
      radius: "100%",
      min: 0, //最小刻度
      max: 16, //最大刻度
      splitNumber: 8, //刻度数量
      startAngle: 225,
      endAngle: -45,
      axisLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: [[1, "#00FEEF"]],
        },
      },
      axisLabel: {
        show: false,
      },
      //刻度标签。
      axisTick: {
        show: true,
        splitNumber: 7,
        lineStyle: {
          color: "#00FEEF",
          width: 1,
        },
        length: -10,
      },
      splitLine: {
        show: false,
      },
      //分隔线样式
      detail: {
        show: false,
      },
      pointer: {
        show: false,
      },
    },
    {
      type: "gauge",
      radius: "80%",
      center: ["50%", "50%"],

      splitNumber: 0, //刻度数量
      startAngle: 225,
      endAngle: -45,
      axisLine: {
        show: true,
        lineStyle: {
          width: 5,
          color: [
            [
              0.9,
              new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: "#5c53de",
                },
                {
                  offset: 1,
                  color: "#00FEEF",
                },
              ]),
            ],
            [1, "rgba(0, 104, 118)"],
          ],
        },
      },
      //分隔线样式。
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      pointer: {
        show: false,
      },
      title: {
        show: true,
        offsetCenter: [0, "26%"], // x, y，单位px
        textStyle: {
          color: "#fff",
          fontSize: 10,
        },
      },
      //仪表盘详情，用于显示数据。
      detail: {
        show: true,
        offsetCenter: [0, "-16%"],
        color: "#fff",
        formatter: function (params) {
          return params
        },
        textStyle: {
          fontSize: 10,
        },
      },
      data: [],
    },
  ],
}

//产量
export const outputStatisticEcharts = {
  grid: {
    show: false,
    top: "20%",
    left: "5%",
    right: "10%",
    bottom: "5%",
    containLabel: true,
  },
  legend: {
    show: true,
    left: 80,
    top: -3,
    itemHeight: 10,
    itemWidth: 10,
    textStyle: {
      color: "#fff",
      fontSize: 10,
    },
  },
  tooltip: {
    trigger: "axis",
    show: false,
    backgroundColor: "rgba(0, 75, 144, 0.80)",
    borderWidth: 0,
    textStyle: {
      color: "#fff",
    },
  },
  xAxis: [
    {
      type: "category",
      name: "(时)",
      axisLine: {
        show: false,
        lineStyle: {
          color: "#fff",
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(182, 220, 255, 0.15)",
        },
      },
      axisTick: {
        show: false,
      },

      data: [],
    },
  ],
  yAxis: [
    {
      type: "value",
      // name: "米",
      axisLine: {
        lineStyle: {
          color: "#fff",
        },
      },
      nameTextStyle: {
        color: "#fff",
        fontSize: "8",
      },
      splitLine: {
        lineStyle: {
          color: "rgba(182, 220, 255, 0.15)",
        },
      },

      // data: [],
    },
  ],
  series: [
    {
      name: $t("echarts.title.output"),
      type: "bar",
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#0084FE" },
          // { offset: 0.5, color: "#11569E" },
          { offset: 1, color: "rgba(0, 132, 254, 0.3)" },
        ]),
      },
      barWidth: "10px",
      label: {
        show: true,
        position: "top",
        textStyle: {
          fontSize: 9,
          color: "#FEF900",
        },
      },
      data: [],
    },
    {
      name: $t("echarts.title.outputYield"),
      type: "line",
      data: [],
    },
    {
      name: $t("echarts.title.baseCapacity"),
      type: "line",
      data: [],
    },
  ],
}
//质量
export const qualityStaticEcharts = {
  grid: {
    top: "20%",
    left: "6%",
    right: "8%",
    bottom: "5%",
    containLabel: true,
  },
  tooltip: {
    trigger: "axis",
    backgroundColor: "transparent",
    padding: 0,
    backgroundColor: "rgba(0, 75, 144, 0.80)",
    borderWidth: 0,
    position: "right",
  },
  legend: {
    left: 20,
    top: -3,
    itemHeight: 10,
    itemWidth: 10,
    textStyle: {
      color: "#fff",
      fontSize: 10,
    },
  },
  xAxis: [
    {
      type: "category",
      name: "(时)",
      nameLocation: "end",
      nameTextStyle: {
        padding: [0, 0, 0, 25],
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: "#fff",
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(182, 220, 255, 0.15)",
        },
      },

      axisTick: {
        show: false,
      },
      data: [],
    },
  ],
  yAxis: [
    {
      type: "value",
      axisLine: {
        lineStyle: {
          color: "#fff",
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(182, 220, 255, 0.15)",
        },
      },
      data: [],
    },
    {
      type: "value",
      name: "良品率",
      nameTextStyle: {
        color: "#fff",
        fontSize: "10",
      },
      min: 0,
      max: 100,
      axisTick: {
        show: true,
      },
      splitLine: {
        show: false,
      },
      axisLine: {
        show: true,
      },
      position: "right",
      axisLabel: {
        show: true,
        textStyle: {
          color: "#ffffff",
        },
        // verticalAlign: "middle",
        // margin: 60,
        //右侧Y轴文字显示
        formatter: "{value}%",
      },
      data: [20, 40, 60, 80, 100],
    },
  ],
  series: [
    {
      name: "良品数",
      type: "bar",
      barWidth: "10px",
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "rgba(84, 221, 19, 1)" },
          { offset: 1, color: "rgba(84, 221, 19, 0.10)" },
        ]),
      },
      data: [],
      stack: "num",
    },
    {
      name: "不良品数",
      type: "bar",
      barWidth: "10px",
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "rgba(254, 0, 5, 1)" },
          { offset: 1, color: "rgba(254, 0, 5, 0.10)" },
        ]),
      },
      data: [],
      stack: "num",
    },
    {
      name: "实际良率",
      yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
      type: "line",
      data: [],
      lineStyle: {
        color: "#C1E975",
      },
      itemStyle: {
        color: "#C1E975",
      },
    },
    {
      name: "标准良率",
      type: "line",
      yAxisIndex: 1,
      data: [],
      lineStyle: {
        color: "#FF9900",
      },
      itemStyle: {
        color: "#FF9900",
      },
    },
  ],
}
//报警分布（按等级）
export const policePieEcharts = {
  grid: {
    top: "0",
    left: "0",
    right: "8%",
    bottom: "5%",
    containLabel: false,
    borderColor: "#fff",
  },
  color: [
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#FE0005" },
      { offset: 0.5, color: "#FE4C03" },
      { offset: 1, color: "#FF7F01" },
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#FF9800" },
      { offset: 0.5, color: "#FFA601" },
      { offset: 1, color: "#FEF400" },
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#0084FE" },
      { offset: 0.5, color: "#0072FE" },
      { offset: 1, color: "#0029FE" },
    ]),
  ],
  tooltip: {
    trigger: "item",
    formatter: "{b} : {c} ({d}%)",
  },
  title: {
    text: "{a|" + $t("echarts.title.total") + "}\n{c|" + "0" + "}",
    top: "40%",
    left: "40%",
    textStyle: {
      rich: {
        a: {
          fontSize: 10,
          color: "#fff",
        },
        c: {
          fontSize: 8,
          color: "#00FEEF",
        },
      },
    },
  },
  series: [
    {
      type: "pie",
      radius: ["0", "30%"],
      center: ["50%", "50%"],
      silent: true,
      itemStyle: {
        normal: {
          color: "rgba(98, 98, 98, 1)",
        },
      },
      data: [1],
    },
    {
      type: "pie",
      radius: ["40%", "70%"],
      roseType: "area",
      zlevel: 10,
      silent: true,
      label: {
        show: true,
        position: "outside",
        formatter: function (params) {
          return "{value|" + params.value + "}\n{name|" + params.name + "}"
        },
        rich: {
          value: {
            fontSize: 10,
            padding: [0, -15],
          },
          name: {
            color: "#eeeeee",
            fontSize: 10,
            padding: [0, -15],
          },
        },
      },
      labelLine: {
        normal: {
          show: true,
          length: 5,
          length2: 10,
          lineStyle: {
            width: 1,
          },
        },
        emphasis: {
          show: false,
        },
      },
      data: [],
    },
  ],
}

export const alarmStatistics = {
  grid: {
    show: false,
    left: "8%",
    right: "12%",
    top: "10%",
    bottom: "5%",
    containLabel: true,
  },
  legend: {
    show: false,
  },
  tooltip: {
    trigger: "item",
    axisPointer: {
      type: "shadow",
    },
    backgroundColor: "transparent",
    trigger: "item",
    borderColor: "transparent",
    padding: 0,
    borderWidth: 0,
  },
  xAxis: {
    name: "(时)",
    axisLine: {
      show: false,
      lineStyle: {
        color: "#fff", // 坐标轴线线的颜色
        type: "solid", // 坐标轴线线的类型（solid实线类型；dashed虚线类型；dotted点状类型）
        fontSize: "12",
      },
    },

    axisLabel: {
      color: "#fff",
    },
    data: [],
  },
  yAxis: {
    type: "value",
    name: "",
    axisLine: {
      show: false,
      lineStyle: {
        color: "#fff", // 坐标轴线线的颜色
        type: "solid", // 坐标轴线线的类型（solid实线类型；dashed虚线类型；dotted点状类型）
        fontSize: "16",
      },
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: "rgba(182, 220, 255, 0.15)",
      },
    },
    axisLabel: {
      color: "#fff",
      textStyle: {},
      fontSize: 12,
    },
  },
  series: [
    {
      smooth: true,
      type: "line",
      data: [],
      lineStyle: {
        color: "#54DD13",
      },
      itemStyle: {
        normal: {
          color: "#54DD13 ", //折线点的颜色
          borderColor: "#54DD13 ", //拐点边框颜色
        },
      },
    },
  ],
}

export const runningState = {
  grid: {
    show: true,
    top: "30%",
    left: "8%",
    right: "10%",
    bottom: "0%",
    containLabel: true,
    borderColor: "#fff", // 设置边框颜色
    borderWidth: 1, // 设置边框宽度
  },
  tooltip: {
    show: true,
    position: "right", // 将 tooltip 靠右显示
    formatter: function (params) {
      // 使用回调函数自定义 tooltip 显示内容
      const data = params.data
      const tooltipContent = `
        当前状态：${params?.seriesName}<br>
        开始时间：${dayjs(data?.startTime).format("HH:mm:ss")}<br>
        结束时间：${dayjs(data?.creationTime).format("HH:mm:ss")}<br>
        持续时间：${data.durationTime}
      `
      return tooltipContent
    },
  },
  legend: {
    show: true,
    itemWidth: 10,
    itemHeight: 10,
    itemGap: 20,
    selectedMode: false,
    textStyle: {
      color: "#fff",
    },
    data: ["运行", "待机", "故障", "停机"],
  },
  xAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#fff", // 设置轴线颜色
      },
    },
    axisTick: {
      show: false,
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: ["#fff"],
        type: "dotted",
      },
    },
    axisLabel: {
      show: true,
      formatter: function (value) {
        if (value > 24) {
          return value - 24
        } else {
          return value
        }
      },
    },
  },
  yAxis: {
    data: [""],
    axisLabel: {
      show: false,
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "#fff", // 设置轴线颜色
      },
    },
    axisTick: {
      show: false,
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: ["#fff"],
      },
    },
  },
  series: [],
}

// 当前班次生产
export const currentProductEcharts = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
      textStyle: {
        color: "#fff",
      },
    },
    formatter: function (params) {
      let res = ""
      let count = 0
      for (var i = 0, l = params.length; i < l; i++) {
        if (params[i].data && params[i].data.startTime) {
          count += 1
        }
        if (count === 1) {
          res += `开始时间:${params[i].data.startTime}<br/>结束时间:${params[i].data.endTime}`
        }
        if (params[i].componentSubType === "line") {
          res += `<br/>${params[i].seriesName}:${params[i].value}%`
        } else {
          res += `<br/>${params[i].seriesName}:${params[i].value}`
        }
      }
      return res
    },
  },
  grid: {
    borderWidth: 0,
    top: 30,
    bottom: 35,
    left: 45,
    right: 45,
    textStyle: {
      color: "#fff",
    },
  },
  legend: {
    show: true,
    top: "0%",
    textStyle: {
      color: "#fff",
      fontSize: 10,
    },
    orient: "horizontal", // 设置图例排列方式为水平排列
    itemWidth: 10, // 设置图例项的宽度
    itemHeight: 10, // 设置图例项的高度
    data: ["良品数", "不良品数", "实际良率", "基准良率"],
  },
  calculable: true,
  xAxis: {
    type: "category",
    axisLine: {
      lineStyle: {
        color: "rgba(255,255,255,0.25)",
      },
      show: true,
    },
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    splitArea: {
      show: false,
    },
    axisLabel: {
      interval: 0,
      color: "#fff",
      formatter: function (val) {
        var str = ""
        for (var i = 0; i < val.length; i++) {
          if (val[i] !== "-") {
            str += val[i]
          }
          if ((i + 1) % 6 === 0) {
            str += "\n"
          }
        }
        return str
      },
      fontSize: 10,
    },
    data: [],
  },
  yAxis: [
    {
      type: "value",
      axisLine: {
        lineStyle: {
          color: "rgba(255,255,255,0.25)",
        },
        show: true,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        interval: 0,
        fontSize: 10,
        color: "#fff",
      },
      splitArea: {
        show: false,
      },
    },
    {
      type: "value",
      splitLine: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: "rgba(255,255,255,0.25)",
        },
        show: true,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        interval: 0,
        formatter: function (value) {
          return value + "%"
        },
        color: "#fff",
        fontSize: 10,
      },
      splitArea: {
        show: false,
      },
    },
  ],
  dataZoom: [
    {
      show: true,
      orient: "horizontal",
      bottom: 10,
      xAxisIndex: 0,
      filterMode: "empty",
      realtime: false,
      handleSize: 20,
      showDataShadow: false,
      height: 5, // 修改为高度
      startValue: 0, // 添加起始位置
      endValue: 100, // 添加结束位置
    },
    {
      type: "slider",
      show: false, // 关键设置
    },
  ],
  series: [
    {
      name: "良品数",
      type: "bar",
      stack: "产量",
      barMaxWidth: 8,
      barGap: "10%",
      yAxisIndex: 0,
      itemStyle: {
        normal: {
          color: "#4EEEA1",
        },
      },
      data: [],
    },
    {
      name: "不良品数",
      type: "bar",
      stack: "产量",
      yAxisIndex: 0,
      itemStyle: {
        normal: {
          color: "#EE4E4E",
          barBorderRadius: 0,
        },
      },
      data: [],
    },
    {
      name: "实际良率",
      type: "line",
      symbol: "emptyCircle",
      symbolSize: 4,
      yAxisIndex: 1,
      itemStyle: {
        normal: {
          color: "#EECB4E",
          barBorderRadius: 0,
        },
      },
      data: [],
    },
    // {
    //   name: "基准良率",
    //   type: "line",
    //   symbol: "emptyCircle",
    //   symbolSize: 4,
    //   yAxisIndex: 1,
    //   itemStyle: {
    //     normal: {
    //       color: "#EE9D4E",
    //       barBorderRadius: 0,
    //     },
    //   },
    //   data: [],
    // },
  ],
}

// 剩余寿命值
const baseColor = ["#EE4E4E"]
export const RemainingLifeEcharts = (remainingLifeColor = baseColor) => {
  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "none",
      },
      show: true,
      formatter: function (params) {
        const data = params[0]?.data
        return `
        <div style="margin: -5px;">
          <div style="display: flex; justify-content: space-between">
            <div>${$t("sparePartsWearingParts.barcode")}：${
          data?.barcode || ""
        }</div>
            <div>${$t("sparePartsWearingParts.name")}：${data?.name || ""}</div>
          </div>
          <div style="display: flex; justify-content: space-between; margin-top: 6px">
            <div>${$t("sparePartsWearingParts.usageDistance")}：${
          data?.currentDistance || 0
        }m</div>
            <div style="margin: 0 10px">${$t(
              "sparePartsWearingParts.alarmThreshold"
            )}：${data?.alarmDistance || 0}m</div>
            <div>${$t("sparePartsWearingParts.shutdownThreshold")}：${
          data?.stopDistance || 0
        }m</div>
          </div>
        </div>`
      },
      position: ["0", "-60"],
    },
    grid: {
      left: "5%",
      top: "0%",
      right: "15%",
      bottom: "0%",
    },
    xAxis: [
      {
        show: false,
        type: "value", // 新增类型定义
        min: 0, // 新增最小值
        max: 100, // 新增最大值
      },
    ],
    yAxis: [
      {
        show: true,
        axisTick: "none",
        axisLine: "none",
        offset: "-11",
        axisLabel: {
          textStyle: {
            color: "#000",
            fontSize: "12",
          },
        },
        data: ["0"],
        z: 3,
      },
      {
        axisTick: "none",
        axisLine: "none",
        axisLabel: {
          textStyle: {
            color: "#ffffff",
            fontSize: "16",
          },
        },
        data: [],
      },
      {
        axisLine: {
          lineStyle: {
            color: "rgba(0,0,0,0)",
          },
        },
        data: [],
      },
      {
        show: false,
        axisTick: "none",
        axisLine: "none",
        offset: "0",
        position: "right", // 设置位置为右侧
        axisLabel: {
          textStyle: {
            color: "#000",
            fontSize: "1",
          },
        },
        data: ["0"],
        z: 3,
      },
    ],
    series: [
      {
        name: "条",
        type: "bar",
        stack: "圆",
        yAxisIndex: 0,
        data: [],
        label: {
          normal: {
            show: true,
            position: "right",
            distance: 10,
            formatter: function (param) {
              return param.value + "%"
            },
            textStyle: {
              color: "#fff",
              fontSize: "12",
            },
          },
        },
        barWidth: 10,
        itemStyle: {
          normal: {
            color: function (params) {
              var num = remainingLifeColor.length
              return remainingLifeColor[params.dataIndex % num]
            },
          },
        },
        z: 2,
        markLine: {
          silent: "false",
          symbol: "none",
          lineStyle: {
            type: "dashed",
            color: "#FFF",
            width: 1,
          },
          data: [],
        },
      },
      {
        name: "白框",
        type: "bar",
        yAxisIndex: 1,
        barGap: "-100%",
        data: [99.5],
        barWidth: 17,
        itemStyle: {
          normal: {
            color: "#0e2147",
            barBorderRadius: 5,
          },
        },
        z: 1,
      },
      {
        name: "外框",
        type: "bar",
        yAxisIndex: 2,
        barGap: "-100%",
        data: [100],
        barWidth: 20,
        itemStyle: {
          normal: {
            color: function (params) {
              var num = remainingLifeColor.length
              return remainingLifeColor[params.dataIndex % num]
            },
            barBorderRadius: 5,
          },
        },
        z: 0,
      },
      {
        name: "外圆",
        type: "scatter",
        hoverAnimation: false,
        data: [0],
        yAxisIndex: 2,
        symbolSize: 25,
        itemStyle: {
          normal: {
            color: function (params) {
              var num = remainingLifeColor.length
              return remainingLifeColor[params.dataIndex % num]
            },
            opacity: 1,
          },
        },
        z: 2,
      },
    ],
  }
}

export const tensionMonitorEcharts = {
  series: [
    {
      type: "liquidFill",
      radius: "78.1%",
      center: ["50%", "50%"],
      data: [], // data个数代表波浪数
      amplitude: 15,
      // 图形样式
      itemStyle: {
        opacity: 1, // 波浪的透明度
        shadowBlur: 0, // 波浪的阴影范围
      },
      backgroundStyle: {
        borderColor: "#156ACF",
        borderWidth: 1,
        color: "#141414",
      },
      label: {
        show: true,
        textStyle: {
          color: "#5594fa",
          insideColor: "#12786f",
          fontSize: 12,
        },
        formatter: params => {
          return `${(params.value * 100).toFixed(2)}%`
        },
      },
      outline: {
        show: false,
      },
    },
  ],
}

export const outputStatisticsEcharts = {
  tooltip: {
    trigger: "axis",
  },
  grid: {
    left: "5%",
    right: "6%",
    bottom: "5%",
    top: "18%",
    containLabel: true,
  },
  legend: {
    icon: "circle",
    right: "center",
    itemWidth: 12,
    itemHeight: 12,
    textStyle: {
      color: "#fff",
    },
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      interval: 0, // 强制显示所有标签
      overflow: "none", // 禁止文字截断
      textStyle: {
        color: "#fff",
      },
      // 保留原有的截断逻辑（可选）
      formatter: function (value) {
        const maxLength = 15
        return value.length > maxLength
          ? value.substring(0, maxLength) + "..."
          : value
      },
    },
    axisLine: {
      lineStyle: {
        color: "#e5e5e5",
      },
    },
    axisTick: {
      show: false,
    },
    splitLine: {
      show: false,
    },
  },
  yAxis: {
    type: "value",
    boundaryGap: ["0%", "20%"],
    axisLabel: {
      //坐标轴字体颜色
      textStyle: {
        color: "#fff",
      },
    },
    axisLine: {
      show: false,
    },
    axisTick: {
      //y轴刻度线
      show: false,
    },
    splitLine: {
      //网格
      show: true,
      lineStyle: {
        color: "#dadde4",
        type: "dashed",
      },
    },
  },
  series: [
    {
      name: $t("monitor.laser1"),
      type: "bar",
      barMaxWidth: "40%", //柱子宽度
      itemStyle: {
        //柱子颜色
        color: "#5B8FF9",
      },
      data: [],
    },
    {
      name: $t("monitor.laser2"),
      type: "bar",
      barMaxWidth: "40%", //柱子宽度
      itemStyle: {
        //柱子颜色
        color: "#61DDAA",
      },
      data: [],
    },
  ],
}

const rankColor = ["#597EFF", "#59AFFF", "#FFA959", "#FFDB59", "#C1E975"]
const truncateText = (text, limit) =>
  text.length > limit ? text.substring(0, limit) + "..." : text

export const alarmRankingEcharts = {
  grid: {
    top: "2%",
    bottom: -15,
    right: "10%",
    left: "0%", // 增加左侧留白空间
    containLabel: true,
  },
  tooltip: {
    trigger: "axis",
    show: true,
    formatter: `{b}: {c}${$t("common.times")}`,
  },
  dataZoom: [
    {
      type: "slider",
      show: true,
      yAxisIndex: 0,
      orient: "vertical",
      right: "3%",
      width: 12,
      filterMode: "filter",
      start: 0,
      end: 100,
      maxValueSpan: 5, // 关键配置：最大显示5个条目
      backgroundColor: "#2B3546",
      fillerColor: "rgba(43,53,70,0.8)",
      borderColor: "transparent",
      showDetail: false,
      handleSize: "100%",
      handleStyle: {
        color: "#434D5E",
      },
    },
  ],
  xAxis: { show: false },
  yAxis: [
    {
      triggerEvent: true,
      show: true,
      inverse: true,
      data: [],
      axisLine: { show: false },
      splitLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        interval: 0,
        fontSize: 13,
        formatter: function (value, index) {
          const rank = index + 1
          // 使用固定2位排名数字保证对齐
          return `{rank|${rank.toString().padEnd(2)}}{text|${truncateText(
            value,
            5
          )}}`
        },
        rich: {
          rank: {
            width: 24, // 排名列固定宽度
            align: "right",
            color: "#fff", // 可自定义排名颜色
          },
          text: {
            width: 70, // 文本列固定宽度
            align: "left",
            color: "#fff",
          },
        },
      },
    },
  ],
  series: [
    {
      name: "条",
      type: "bar",
      yAxisIndex: 0,
      data: [],
      barWidth: 10,
      itemStyle: {
        color: val => rankColor[val.dataIndex] || "#1990FF",
      },
      label: {
        show: true,
        position: "insideBottomRight",
        color: "#fff",
        fontSize: 13,
        formatter: "{c}",
        offset: [0, -5],
      },
    },
  ],
}

export const equipmentMaintenanceEcharts = {
  grid: {
    top: "5%",
    bottom: "15%",
    left: "22%",
    right: "18%",
  },
  tooltip: {
    trigger: "item",
    formatter: function (params) {
      return `${params.name}: ${params.data.distance}m`
    },
  },
  dataZoom: [
    {
      type: "slider",
      show: true,
      yAxisIndex: 0,
      orient: "vertical",
      right: "3%",
      width: 12,
      filterMode: "filter",
      start: 0,
      end: 100, // 始终初始化为100%
      maxValueSpan: 5, // 新增最大显示条目控制
      backgroundColor: "#2B3546",
      fillerColor: "rgba(43,53,70,0.8)",
      borderColor: "transparent",
      showDetail: false,
      handleSize: "100%",
      handleStyle: {
        color: "#434D5E",
      },
    },
  ],
  xAxis: {
    type: "value",
    axisLabel: {
      formatter: function (value) {
        // 修改格式化函数
        return `${value}%` // 值乘以100后添加百分比符号
      },
      color: "#fff",
    },
  },
  yAxis: {
    type: "category",
    inverse: true,
    data: [],
    axisLabel: {
      fontSize: 10,
      color: "#fff",
      formatter: function (value) {
        return `${truncateText(value, 5)}`
      },
    },
  },
  series: [
    {
      data: [],
      type: "bar",
      itemStyle: {
        color: "",
      },
      label: {
        show: true,
        position: "insideBottomRight",
        color: "#fff",
        offset: [0, -5],
        formatter: function (c) {
          // 数值转换为百分比（保留1位小数）
          return `${c.value}%`
        },
      },
      barWidth: 12,
    },
  ],
}

export const createSeries = (name, color, startTime, endTime) => ({
  name: name,
  type: "custom",
  renderItem: function (params, api) {
    const categoryIndex = api.value(0)
    const start = api.coord([api.value(1), categoryIndex])
    const end = api.coord([api.value(2), categoryIndex])
    const height = 36

    return {
      type: "rect",
      shape: echarts.graphic.clipRectByRect(
        {
          x: start[0],
          y: start[1] - height / 2,
          width: end[0] - start[0],
          height: height,
        },
        params.coordSys
      ),
      style: api.style(),
    }
  },
  data: [
    {
      value: [0, startTime, endTime],
      itemStyle: { color: color },
    },
  ],
})

export const equipmentStatus = {
  tooltip: {
    trigger: "item",
    position: "right",
    formatter: function (params) {
      const start = dayjs(params.value[1])
      const end = dayjs(params.value[2])
      const formatTime = date => date.format("HH:mm")
      return `${params.marker}${params.seriesName}<br/>${$t(
        "common.timePeriod"
      )}：${formatTime(start)} - ${formatTime(end)}`
    },
  },
  grid: {
    show: true,
    top: "0%",
    left: "2%",
    right: "2%",
    bottom: "15%",
    containLabel: true,
    borderColor: "#fff",
    borderWidth: 1,
  },
  xAxis: {
    type: "time",
    min: dayjs("2025-04-09 00:00:00").valueOf(),
    max: dayjs("2025-04-09 24:00:00").valueOf(),
    boundaryGap: false,
    axisLabel: {
      color: "#fff",
      formatter: function (value) {
        return dayjs(value).format("HH:mm")
      },
      align: "center",
      interval: 0,
      // 新增边界处理
      showMaxLabel: true, // 强制显示最大标签
      showMinLabel: true, // 强制显示最小标签
    },
    // 新增轴范围扩展
    maxInterval: 3600 * 1000 * 24,
    minInterval: 3600 * 1000,
    axisTick: { show: false },
    axisLine: { show: false },
    splitLine: { show: false },
    axisPointer: { type: "shadow" },
  },
  yAxis: {
    data: ["设备"],
    axisTick: { show: false },
    axisLine: { show: false },
    axisLabel: { show: false },
  },
  series: [],
}
