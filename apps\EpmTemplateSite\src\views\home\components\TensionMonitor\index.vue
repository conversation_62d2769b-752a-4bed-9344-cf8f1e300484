<template>
  <div class="tension-monitor">
    <ul class="tension-monitor-box">
      <li>
        <div class="chart"><waterPolo :option="options1" /></div>
        <div class="name">{{ $t("monitor.equipmentOee") }}</div>
      </li>
      <li>
        <div class="chart"><waterPolo :option="options2" /></div>
        <div class="name">{{ $t("monitor.timeUtilizationRate") }}</div>
      </li>
      <li>
        <div class="chart"><waterPolo :option="options3" /></div>
        <div class="name">{{ $t("monitor.performanceUtilizationRate") }}</div>
      </li>
    </ul>
  </div>
</template>

<script setup name="TensionMonitor">
import { ref, defineProps, watch, computed } from "vue"
import waterPolo from "./waterPolo.vue"
import { tensionMonitorEcharts } from "../echartsConfig"
import { useI18n } from "@xfe/locale"
import { useStore } from "vuex"
import { cloneDeep } from "lodash-es"

const store = useStore()
const { t: $t } = useI18n()

const themeColor = computed(() => store.state.theme.themeColor)
const DARK_COLOR = "#fff"
const LIGHT_COLOR = "#303133"
let base_color = computed(() =>
  themeColor.value === "light" ? LIGHT_COLOR : DARK_COLOR
)
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        oee: 0, // oee
        timeOperateRate: 0, // 时间稼动率
        performanceOperateRate: 0, // 性能稼动率
      }
    },
  },
})

let options1 = ref(cloneDeep(tensionMonitorEcharts))
let options2 = ref(cloneDeep(tensionMonitorEcharts))
let options3 = ref(cloneDeep(tensionMonitorEcharts))

const initOption = params => {
  options1.value.series[0].data = [params.oee, params.oee]
  options2.value.series[0].data = [
    params.timeOperateRate,
    params.timeOperateRate,
  ]
  options3.value.series[0].data = [
    params.performanceOperateRate,
    params.performanceOperateRate,
  ]
}

initOption(props.data)

watch(
  () => props.data,
  v => {
    initOption(v)
  },
  { deep: true }
)

watch(
  () => themeColor.value,
  v => {
    initOption(v)
  }
)
</script>

<style scoped lang="scss">
.tension-monitor {
  display: flex;
  width: 100%;
  height: calc(100% - 38px);
  align-items: flex-end;
  padding: 5px;
  padding-bottom: 0px;
  .tension-monitor-box {
    flex: 1;
    height: 100%;
    display: flex;
    li {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: calc(100% / 3);
      height: 100%;
      .chart {
        width: 100%;
        height: 100%;
      }
      .name {
        font-size: 16px;
      }
    }
  }
}
</style>
