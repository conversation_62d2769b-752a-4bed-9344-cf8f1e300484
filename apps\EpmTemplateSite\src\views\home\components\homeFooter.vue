<template>
  <div class="footer-nav">
    <template v-for="(n, i) of nav" :key="n.text">
      <div
        v-if="n.isShow"
        :class="{ activeTab: i === activeTabs, [n.class]: true }"
        @click="changeTabs(n, i)"
        style="z-index: 999"
        v-on:mouseenter="handleMouseEnter(i)"
        v-on:mouseleave="handleMouseLeave"
      >
        <div class="icon">
          <div class="background"></div>
          <svg-icon
            :name="i === activeTabs || i === hoverIndex ? n.selectIcon : n.icon"
            :size="20"
          />
        </div>
        <el-tooltip
          effect="dark"
          :content="n.text"
          placement="top"
          :disabled="n.text.length <= 4"
        >
          <div class="text">{{ n.text }}</div>
        </el-tooltip>
      </div>
    </template>
    <div class="bottom"></div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue"
import { useRouter, useRoute } from "vue-router"
import { useStore } from "vuex"
import { useI18n, useLocale } from "@xfe/locale"

const { changeLocale } = useLocale()
const { t: $t } = useI18n()
const router = useRouter()
const route = useRoute()
const store = useStore()

// 添加 hoverIndex 响应式变量
const hoverIndex = ref(null)

// 添加鼠标事件处理函数
const handleMouseEnter = index => {
  hoverIndex.value = index
}

const handleMouseLeave = () => {
  hoverIndex.value = null
}

const currentTab = computed(() => store.getters["currentTab"])

let activeTabs = ref(currentTab.value)

let nav = computed(() => [
  {
    text: $t("homeRoute.monitor"),
    class: "index",
    name: "HomeIndex",
    path: "/home/<USER>",
    isShow: true,
    icon: "monitorInfo",
    selectIcon: "monitorInfo-selected",
  },
  {
    text: $t("homeRoute.digitalStatement"),
    class: "digital-statement",
    name: "DigitalStatement",
    path: "/home/<USER>",
    isShow: true,
    icon: "digitalStatement",
    selectIcon: "digitalStatement-selected",
  },
  {
    text: $t("homeRoute.knowledgeBase"),
    class: "knowledge-base",
    name: "knowledgeBase",
    path: "/home/<USER>",
    isShow: true,
    icon: "knowledgeBase",
    selectIcon: "knowledgeBase-selected",
  },
  {
    text: $t("homeRoute.jobDirection"),
    class: "job-direction",
    name: "jobDirection",
    path: "/home/<USER>",
    isShow: true,
    icon: "jobDirection",
    selectIcon: "jobDirection-selected",
  },
  {
    text: $t("homeRoute.sparePartsWearingParts"),
    class: "spareParts-wearingParts",
    name: "SpareParts-WearingParts",
    path: "/home/<USER>",
    isShow: true,
    icon: "spareParts",
    selectIcon: "spareParts-selected",
  },
  {
    text: $t("homeRoute.repairWorkOrder"),
    class: "repair-workOrder",
    name: "repairWorkOrder",
    path: "/home/<USER>",
    isShow: true,
    icon: "repairWorkOrder",
    selectIcon: "repairWorkOrder-selected",
  },
])

// 切换tabs
const changeTabs = async (n, i) => {
  if (activeTabs.value === i) return
  // activeTabs.value = i
  store.commit("SET_CURRENT_Options", { tab: i, path: n.class })
  // activeTabs.value = currentTab.value
  router.push("/home/" + n.class)
}

const language = computed(() => store.getters["notice/language"])
const changeLanguage = async language => {
  await changeLocale(language)
}

// 监听路由跳转时的tab
watch(
  () => route.path,
  v => {
    nav.value.forEach((item, index) => {
      if (item.path === v) {
        store.commit("SET_CURRENT_Options", { tab: index, path: item.class })
        activeTabs.value = index
      }
    })
  },
  { immediate: true }
)

watch(
  () => language.value,
  v => {
    if (v) {
      changeLanguage(v)
    }
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.footer-nav {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  .language_btn {
    width: 100px;
    height: 100px;
    .icon {
      width: 30px;
      height: 30px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    .Language {
      background-image: url("~@/assets/images/nav/Language-CN.png");
      &:hover {
        background-image: url("~@/assets/images/nav/Language-CN(1).png");
      }
    }
    .Language_EN {
      background-image: url("~@/assets/images/nav/Language-EN.png");
      &:hover {
        background-image: url("~@/assets/images/nav/Language-EN(1).png");
      }
    }
  }
  > div {
    width: 130px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #0084fe;
    margin: 10px;
    cursor: pointer;
    .icon {
      margin-bottom: 5px;
    }

    &:hover,
    &.activeTab {
      color: #e5983e;
      .icon {
        transition: all 1s ease; // 添加过渡效果
        animation: rise 0.7s ease forwards; //添加动画效果
        position: relative;
        background-size: 100% auto no-repeat;
        top: -10px;
        transform: scale(1.2);
        .background {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 45px;
          height: 45px;
          background: url("~@/assets/images/nav/nav-select.png") no-repeat;
          background-size: 100% auto;
        }
      }
    }
    @keyframes rise {
      from {
        top: 0px;
      }
      to {
        top: -10px;
      }
    }
  }
  .bottom {
    position: absolute;
    bottom: -2px;
    width: 100%;
    height: 30px;
    background: url("~@/assets/images/nav/nav-bottom.svg") no-repeat;
    background-size: 100% auto;
  }
  .text {
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 16px;
  }
}
</style>
<style scoped lang="scss">
@media screen and (max-width: 1180px) {
  .footer-nav {
    > div {
      font-size: 12px;
    }
  }
}
</style>
