/**
 * 导出Blob流文件
 * @param data 后端返回的结果
 * @param fileName 导出文件名
 */
const exportByBlob = (data: any, fileName: string) => {
  let blob: Blob = null
  const fileType = data.type
  fileName = fileName.replace(/[\/\\:*?"<>|]/g, "-")
  switch (fileType) {
    case "application/csv":
      fileName = fileName + ".csv"
      blob = new Blob([data])
      break
    default:
      blob = new Blob([data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8",
      }) // 此处为生成doc
      break
  }

  let link = document.createElement("a")
  let href = window.URL.createObjectURL(blob)
  link.href = href
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(href) // 释放掉blob对象
}

export default exportByBlob
