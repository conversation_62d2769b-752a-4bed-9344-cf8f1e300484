/**
 *  产品详情接口
 * @param {*} data
 */
import { request } from "@xfe/request"
import { config } from "@/config"
const base = config.base_url.homeServerPrefix

// 获取产品详情弹框列表数据
export function GetListByRelId(params) {
  return request.get({
    url: `${base}/ProductData/detail`,
    params,
  })
}

// 获取动态列
export function GetColunmns() {
  return request.get({
    url: `${base}/ProductData/header`,
  })
}

/**
 *  导出产品历史表
 */
export function exportProductExcel(params) {
  return request.get({
    url: `${base}/ProductData/export`,
    header: {
      headers: { "Content-Type": "application/x-download" },
    },
    responseType: "blob",
    params,
  })
}

// 获取产品数据统计
export function getProductBarNum(params) {
  return request.get({
    url: `${base}/ProductData/bar/number`,
    params,
  })
}

// 获取数据字段统计
export function getProductLineField(params) {
  return request.get({
    url: `${base}/ProductData/bar/field`,
    params,
  })
}

// 获取产品数据良率
export function getProductYield(params) {
  return request.get({
    url: `${base}/ProductData/bar/yield`,
    params,
  })
}

// 获取产品数据NG分布
export function getProductNg(params) {
  return request.get({
    url: `${base}/ProductData/bar/ng`,
    params,
  })
}

// 离线上传
export function offlineData(data) {
  return request.post({
    url: `${base}/MesData/PostOfflineData`,
    data,
  })
}

export function getNgReason() {
  return request.get({
    url: `${base}/ProductData/ngcode`,
  })
}
