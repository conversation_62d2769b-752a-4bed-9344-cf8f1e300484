export interface Language {
  en: Record<string, string>
  cn: Record<string, string>
}

export const languageConfig: Language = {
  en: {
    user: "User name",
    password: "Password",
    login: "Sign in",
    otherLogin: "Other login methods",
    loginByAccount: "Account login",
    loginByCard: "Card login",
    loginByVistor: "Guest login",
    enterAccount: "Please enter account number",
    enterPasswrod: "Please enter account password",
  },
  cn: {
    user: "用 户 名",
    password: "密 码",
    login: "登 录",
    otherLogin: "其他登录方法",
    loginByAccount: "密码登录",
    loginByCard: "刷卡登录",
    loginByVistor: "游客登录",
    enterAccount: "请输入账号",
    enterPasswrod: "请输入密码",
  },
}
