const DB_NAME = 'BJSModelCache';
const DB_VERSION = 1;
const STORE_NAME = 'models';

interface CachedModel {
  url: string;
  data: ArrayBuffer;
  timestamp: number;
}

export class IndexedDBManager {
  private db: IDBDatabase | null = null;

  constructor() {
    this.initDB();
  }

  private async initDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = (event) => {
        console.error('IndexedDB error:', (event.target as IDBOpenDBRequest).error);
        reject((event.target as IDBOpenDBRequest).error);
      };

      request.onsuccess = (event) => {
        this.db = (event.target as IDBOpenDBRequest).result;
        console.log('IndexedDB initialized successfully.');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(STORE_NAME)) {
          const store = db.createObjectStore(STORE_NAME, { keyPath: 'url' });
          store.createIndex('timestamp', 'timestamp', { unique: false });
          console.log('IndexedDB object store created.');
        }
      };
    });
  }

  public async saveModel(url: string, data: ArrayBuffer): Promise<void> {
    if (!this.db) {
      console.warn('IndexedDB not initialized. Attempting to re-init.');
      await this.initDB();
      if (!this.db) {
        console.error('Failed to initialize IndexedDB after retry. Cannot save model.');
        return Promise.reject('IndexedDB not available');
      }
    }

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db!.transaction([STORE_NAME], 'readwrite');
        const store = transaction.objectStore(STORE_NAME);
        const modelToCache: CachedModel = {
          url,
          data,
          timestamp: Date.now()
        };

        const request = store.put(modelToCache);

        request.onsuccess = () => {
          console.log(`Model ${url} saved to IndexedDB.`);
          resolve();
        };

        request.onerror = (event) => {
          console.error(`Error saving model ${url} to IndexedDB:`, (event.target as IDBRequest).error);
          reject((event.target as IDBRequest).error);
        };
      } catch (error) {
        console.error('Error initiating IndexedDB transaction:', error);
        reject(error);
      }
    });
  }

  public async getModel(url: string): Promise<ArrayBuffer | null> {
    if (!this.db) {
      console.warn('IndexedDB not initialized. Attempting to re-init.');
      await this.initDB();
      if (!this.db) {
        console.error('Failed to initialize IndexedDB after retry. Cannot get model.');
        return Promise.reject('IndexedDB not available');
      }
    }

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db!.transaction([STORE_NAME], 'readonly');
        const store = transaction.objectStore(STORE_NAME);
        const request = store.get(url);

        request.onsuccess = (event) => {
          const result = (event.target as IDBRequest<CachedModel>).result;
          if (result) {
            console.log(`Model ${url} loaded from IndexedDB.`);
            resolve(result.data);
          } else {
            console.log(`Model ${url} not found in IndexedDB.`);
            resolve(null);
          }
        };

        request.onerror = (event) => {
          console.error(`Error getting model ${url} from IndexedDB:`, (event.target as IDBRequest).error);
          reject((event.target as IDBRequest).error);
        };
      } catch (error) {
        console.error('Error initiating IndexedDB transaction:', error);
        reject(error);
      }
    });
  }

  public async getAllCachedModelInfo(): Promise<{ url: string, timestamp: number }[]> {
    if (!this.db) {
      console.warn('IndexedDB not initialized. Attempting to re-init.');
      await this.initDB();
      if (!this.db) {
        console.error('Failed to initialize IndexedDB after retry. Cannot get all model info.');
        return Promise.reject('IndexedDB not available');
      }
    }

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db!.transaction([STORE_NAME], 'readonly');
        const store = transaction.objectStore(STORE_NAME);
        const request = store.getAll();

        request.onsuccess = (event) => {
          const results = (event.target as IDBRequest<CachedModel[]>).result;
          if (results) {
            resolve(results.map(item => ({ url: item.url, timestamp: item.timestamp })));
          } else {
            resolve([]);
          }
        };

        request.onerror = (event) => {
          console.error('Error getting all cached model info from IndexedDB:', (event.target as IDBRequest).error);
          reject((event.target as IDBRequest).error);
        };
      } catch (error) {
        console.error('Error initiating IndexedDB transaction for getAllCachedModelInfo:', error);
        reject(error);
      }
    });
  }

  public async clearCache(): Promise<void> {
    if (!this.db) {
      console.warn('IndexedDB not initialized. Attempting to re-init.');
      await this.initDB();
      if (!this.db) {
        console.error('Failed to initialize IndexedDB after retry. Cannot clear cache.');
        return Promise.reject('IndexedDB not available');
      }
    }
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db!.transaction([STORE_NAME], 'readwrite');
        const store = transaction.objectStore(STORE_NAME);
        const request = store.clear();

        request.onsuccess = () => {
          console.log('IndexedDB cache cleared.');
          resolve();
        };

        request.onerror = (event) => {
          console.error('Error clearing IndexedDB cache:', (event.target as IDBRequest).error);
          reject((event.target as IDBRequest).error);
        };
      } catch (error) {
        console.error('Error initiating IndexedDB transaction:', error);
        reject(error);
      }
    });
  }

  // 可以添加更多方法，如删除单个模型、获取缓存大小等
}
