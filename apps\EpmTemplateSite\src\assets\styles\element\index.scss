// styles/element/index.scss
/* 只需要重写你需要的即可 */
@forward "element-plus/theme-chalk/src/common/var.scss" with ($colors: ("primary": ("base": #0084fe,
    ),
    "success": ("base": #6cb71b,
    ),
    "warning": ("base": #faa818,
    ),
    "danger": ("base": #ff5335,
    ),
    "error": ("base": #ff5335,
    ),
    "info": ("base": #7587ff,
    ),
  ),
  $text-color: ("regular": #000,
    "secondary": #606266,
  ),
  // $bg-color: ()
);

// 如果只是按需导入，则可以忽略以下内容。
// 如果你想导入所有样式:
@use "element-plus/theme-chalk/src/index.scss" as *;

//导入黑色主题
@use "element-plus/theme-chalk/src/dark/css-vars.scss" as *;

// 自定义暗黑主题变量
@use "./dark/index.scss";

/* 弹窗 */
.el-dialog__headerbtn .el-dialog__close {
  // color: var(--g-font-color-1) !important;
}

.el-textarea__inner {
  // color: #fff !important;
  // background: #1e1d1d;
}

.el-button--text {
  // color: #fff;
}

// .el-button--text:not(.is-disabled):hover,
// .el-button--text:not(.is-disabled):focus {
//   color: #01c2ff;
// }

.el-link.el-link--primary {
  // color: #fff;
}

.el-loading-spinner {
  .path {
    // stroke: #fff;
  }

  .el-loading-text {
    // color: #fff;
  }
}

.el-loading-mask {

  // z-index: 0 !important;
  // background-color: rgba(15, 20, 43, 0.6) !important;
  .el-loading-spinner {
    .path {
      // stroke: #fff;
    }
  }
}

.el-radio {
  // color: #fff !important;
  margin-right: 10px;

  &__label {
    // color: #fff !important;
  }
}

.el-dropdown__popper {
  border: none !important;
  opacity: 0.9 !important;

  .el-dropdown-menu {
    padding: 0;
    // border: 3px solid rgba(0, 75, 144, 0.5);
    // background: #004b90;
  }

  .el-dropdown__list {
    max-height: 300px;

    .el-dropdown-menu__item {
      color: var(--g-font-color);
      margin: 5px 0;

      &:hover {
        color: var(--g-wihte-font-color);
        background: var(--el-color-primary) center !important;
      }
    }

    .active-tab {
      color: var(--g-wihte-font-color);
      background: var(--el-color-primary) center;
      background-size: 100% auto;
    }
  }
}

.el-select__popper {
  border: none !important;
  opacity: 0.9 !important;

  .el-select-dropdown__list {
    .el-select-dropdown__item {
      color: var(--g-font-color);
      margin: 5px 0;
      background-color: transparent;

      &:hover,
      .hover {
        color: var(--g-wihte-font-color) !important;
        background: var(--el-color-primary) center !important;
      }
    }
  }
}

.el-message-box {
  --el-messagebox-title-color: var(--g-font-color);
  --el-messagebox-content-color: var(--g-font-color);
  border: var(--visual-config-border);
  background-color: var(--messagebox-background);

  &__btns {
    .el-button {
      color: #fff;
      background-color: var(--el-color-primary);
    }
  }
}

// .el-message {
//   background-color: transparent;

//   &--success {
//     border: 1px solid #6cb71b !important;
//     background: rgba(45, 89, 91) !important;
//   }

//   &--error {
//     border: 1px solid #fe0005 !important;
//     background: rgba(61, 48, 78) !important;
//   }

//   &--warning {
//     border: 1px solid #ff9900 !important;
//     background: #653906 !important;
//   }

//   &--info {
//     border: 1px solid #b6dcff !important;
//     background: #566674 !important;
//   }

//   &__content {
//     font-size: 20px;
//     color: #fff !important;
//   }

//   &__icon {
//     svg {
//       display: none;
//     }
//   }

//   &-icon--success {
//     background: url("~@/assets/images/message/complete.png");
//     background-size: 100% auto;
//   }

//   &-icon--error {
//     background: url("~@/assets/images/message/close.png");
//     background-size: 100% auto;
//   }

//   &-icon--warning {
//     background: url("~@/assets/images/message/warn.png");
//     background-size: 100% auto;
//   }

//   &-icon--info {
//     background: url("~@/assets/images/message/info.png");
//     background-size: 100% auto;
//   }
// }

.el-time-spinner__item {
  // color: #fff !important;
}

.el-time-spinner__item:hover:not(.is-disabled):not(.is-active) {
  // color: #4e7cc2 !important;
}

.el-time-panel__btn.confirm {
  // color: #fff !important;
}

.el-time-panel__btn {
  // color: #dce5f8 !important;
}

.el-picker-panel {
  .el-date-range-picker__time-header {
    .el-input.is-disabled .el-input__wrapper {
      background-color: transparent !important;
    }
  }

  .el-time-panel {
    // background-color: #004b90 !important;
  }

  .el-date-table td.today .el-date-table-cell__text {
    // color: #fff;
  }

  &__body {
    // color: #fff;
    // background-color: #004b90;
    // --el-datepicker-active-color: #0084fe;
    // --el-datepicker-off-text-color: #cccccc95;
    // --el-datepicker-header-text-color: #fff;
    // --el-datepicker-icon-color: #fff;
    // --el-datepicker-hover-text-color: #ccc;
    // --el-datepicker-inrange-bg-color: rgb(19, 39, 66);
    // --el-datepicker-inrange-hover-bg-color: rgb(35, 74, 124);
    // --el-datepicker-inner-border-color: transparent;
  }

  &__footer {

    // background-color: #004b90 !important;
    .el-button.is-text {
      // color: #fff;
    }

    .el-button.is-text:not(.is-disabled):hover,
    .el-button.is-text:not(.is-disabled):focus {
      background-color: var(--el-color-primary);
      // border: 1px solid #fff;
    }

    .el-button.is-disabled,
    .el-button.is-disabled:hover,
    .el-button.is-disabled:focus {
      // color: #fff;
      // background-color: #80a5c8;
    }
  }
}

.el-message {
  z-index: 3000 !important;
}

.is-message-box {
  z-index: 3000 !important;
}

// 提示弹窗的按钮样式更改
.el-overlay-message-box .el-message-box__btns {
  justify-content: flex-end;
}

.message-box-cancel-btn.el-button {
  background-color: var(--dialog-btn-cancel-backbackground);
  color: var(--dialog-btn-cancel-color);
  border-color: var(--el-button-border-color) !important;
}

.el-overlay-message-box .el-message-box__header .el-message-box__headerbtn .el-message-box__close {
  color: var(--g-font-color-1);
}

.el-popover.el-popper {
  min-width: 100px !important;
  width: 100px !important;
  padding: 10px 0 !important;
}

.el-popper.is-dark {
  color: var(--g-font-color-1);
  background: var(--messagebox-background);
  border: 1px solid var(--messagebox-background);
}

.el-popper.is-dark .el-popper__arrow::before {
  background: var(--messagebox-background);
  border: 1px solid var(--messagebox-background);
}
