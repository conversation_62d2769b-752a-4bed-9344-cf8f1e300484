export default {
  repairWorkOrder: {
    repairWorkOrder: "Maintenance work order",
    maintenancePlan: "Maintenance plan",
    reportTime: "Repair request time",
    equipmentName: "Equipment name",
    faultType: "Fault type",
    status: "Status",
    faultDescription: "Fault description",
    faultReason: "Fault cause",
    handlingMethod: "Solution",
    addOrder: "New Work Order",
    handleOrder: "Process Work Order",
    repairTime: "Repair Time",
    newPlan: "New Maintenance Plan",
    detail: "Maintenance Plan Details",
    edit: "Edit Maintenance Plan",
    deleteConfirm: "Delete Confirmation",

    causeAnalysis: {
      label: "Cause Analysis",
      relatedToProductQuality: "Related to product quality",
      relatedToSoftwareVersion: "Related to software version changes",
    },

    equipmentRepairApplication: {
      title: "Equipment maintenance application form",
      formNumber: "Form No.",
      date: "Date",
      applicant: "Applicant",
      number: "No.",
      applicationUnit: "Requesting Dept.",
      productionLine: "Production line",
      equipmentNumber: "Equipment No.",
      faultTime: "Fault time",
      devNameExample: "EPM intelligent maintenance system",
    },

    messages: {
      updateSuccess: "Work order modified successfully",
      createSuccess: "New work order created successfully",
      handleSuccess: "Work order processed successfully",
      handleError: "Work order processing failed",
      updatePlanSuccess: "Maintenance plan updated successfully",
      createPlanSuccess: "New maintenance plan created successfully",
    },

    validation: {
      chooseDate: "Please select date",
      enterDescription: "Please enter description",
    },

    confirmation: {
      confirmHandle: "Confirm the work order content is correct?",
      deletePlan:
        "This action cannot be undone. Are you sure you want to delete this maintenance plan?",
    },

    common: {
      chooseTime: "Please select time",
    },

    options: {
      shutdown: "Shutdown",
      noShutdown: "No Shutdown",
      speedReduction: "Speed reduction",
      other: "Other",
    },

    timeSection: {
      startTime: "Start time",
      repairTime: "Repair time",
    },

    consumedParts: "Spares consumed",
    maintenancePerson: "Maintenance person",
    acceptanceConfirmation: "Acceptance confirmation",

    maintenancePlanManagement: {
      newPlan: "New maintenance plan",
      date: "Date",
      description: "Description",
      maintenancePlan: "Maintenance plan",
    },

    qualityInspection: {
      related: "Related (QC involved in acceptance confirmation)",
      unrelated: "Not related",
    },

    softwareManagement: {
      related:
        "Related (Software confirmed according to Equipment Software Management Measures)",
    },
  },
}
