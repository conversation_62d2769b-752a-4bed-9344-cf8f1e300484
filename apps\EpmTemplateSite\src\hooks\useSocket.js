import { ref, onUnmounted } from "vue"
import * as signalr from "@microsoft/signalr"
import { Session } from "@/utils/storage" // Local
import { useLocale } from "@xfe/locale"

export default function () {
  // 建立连接容器
  let connection = null
  // let isManualStop = false;
  const { getLocale } = useLocale()

  // 设置定时任务 给后端传对应的心跳 后端用于检测长连接是否断开
  let timer = ref(null)

  const registerNoticeSignalr = (socketUrl, url) => {
    connection = new signalr.HubConnectionBuilder()
      .configureLogging(signalr.LogLevel.Error)
      .withAutomaticReconnect() //断线自动重连
      .withUrl(socketUrl + url, {
        headers: {
          Authorization: `Bearer ${Session.get("AccessToken")}`,
          "Accept-Language": getLocale.value,
        },
      })
      .build()
    // 调用后端方法 接收定时数据
    // connection.on(methodName, method);
    setTimeout(() => {
      // 启动
      connection.start()
      // connection.start().then(function () {
      //   let userId = Local.get("AUTH_USER")?.user?.id || emptyId;
      //   let tenantId =
      //     Session.get("tenantId") || "00000000-0000-0000-0000-000000000000";
      //   // 给后端发送消息
      //   console.log(connection);
      //   connection.invoke("On", userId, tenantId);
      //   console.log(222);
      //   // 两分钟调用一次
      //   timer.value = setInterval(() => {
      //     connection.invoke("SignalRHeartBeat", tenantId);
      //   }, 1000 * 60 * 2);
      // });
    }, 500)

    //(默认4次重连)，任何一次只要回调成功，调用
    connection.onreconnected(() => {
      // let r = confirm("重连成功！请点击确定重新加载数据");
      // if (r) {
      //   location.reload();
      // }
    })
    //(默认4次重连) 全部都失败后，调用
    connection.onclose(() => {
      // if (!isManualStop) {
      //   let r = confirm(
      //     "重连失败！请检查网络和服务器是否正常连接，如正常连接请点击确定重连"
      //   );
      //   if (r) {
      //     location.reload();
      //   }
      //   isManualStop = false;
      // }
    })
  }
  // 调用后端方法 接收定时数据
  const connectionFun = (methodName, method) => {
    connection.on(methodName, method)
  }

  // 手动断开连接
  const connectionStop = () => {
    // isManualStop = true;
    connection?.stop()
  }

  // 页面销毁
  onUnmounted(() => {
    // connectionStop()
    // 清除定时器
    clearInterval(timer.value)
  })

  return {
    registerNoticeSignalr,
    connectionFun,
    connectionStop,
  }
}
