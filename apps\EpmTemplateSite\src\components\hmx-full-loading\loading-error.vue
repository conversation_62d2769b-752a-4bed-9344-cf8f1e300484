<template>
  <div class="layout flex justify-center">
    <div class="text-center">
      <img width="350" src="@/assets/images/common/403.svg" alt="" />
      <div class="text-14px text-[var(--el-color-info)]">页面加载失败。</div>
    </div>
  </div>
</template>

<script setup name="HMXLoadingError"></script>

<style lang="scss" scoped>
.layout {
  width: 100%;
  height: 100vh;
}
.flex {
  display: flex;
}
.justify-center {
  justify-content: center;
}
.text-center {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}
.text-14px {
  font-size: 14px;
  color: #909399;
}
</style>
