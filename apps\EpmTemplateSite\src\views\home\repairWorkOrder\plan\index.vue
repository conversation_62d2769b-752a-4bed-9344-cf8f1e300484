<template>
  <div class="plan">
    <div class="head">
      <el-button type="primary" @click="add">{{
        $t("repairWorkOrder.newPlan")
      }}</el-button>
    </div>
    <div class="calendar">
      <v-calendar
        :masks="masks"
        :attributes="attributes"
        disable-page-swipe
        is-expanded
        @did-move="didmove"
        :locale="locale"
      >
        <template v-slot:day-content="{ day, attributes }">
          <div class="date-item">
            <div class="date-text">{{ day.day }}</div>
            <div
              v-for="attr in attributes"
              :key="attr.key"
              class="tip-block"
              :title="attr.customData.title"
            >
              <div class="text">
                <div class="dot"></div>
                {{ attr.customData.title }}
              </div>
              <div class="mask">
                <div class="icons">
                  <el-icon class="ic" @click="handlePlan(attr, 'look')"
                    ><View
                  /></el-icon>
                  <el-icon class="ic" @click="handlePlan(attr, 'edit')"
                    ><EditPen
                  /></el-icon>
                  <el-icon class="ic" @click="handlePlan(attr, 'delete')"
                    ><Delete
                  /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </template>
      </v-calendar>
    </div>
    <addPlan
      :add-show="addShow"
      :plan-opt="planOpt"
      @close-dialog="closeDialog"
    />
  </div>
</template>
<script setup>
import addPlan from "./addPlan.vue"
import { ref, reactive, computed, onMounted } from "vue"
import { getRepairPlan, delRepairPlan } from "@/api/front/repairOrder.js"
import { View, EditPen, Delete } from "@element-plus/icons-vue"
import dayjs from "dayjs"
import { useI18n, useLocale } from "@xfe/locale"

const { t: $t } = useI18n()
const { getLocale } = useLocale()
const elLocaleMap = {
  "zh-CN": "zh-CN",
  "en-US": "en-US",
}
const locale = computed(() => {
  return elLocaleMap[getLocale.value]
})
let addShow = ref(false)
let planOpt = reactive({
  type: "add",
  data: {},
})
let masks = {
  weekdays: "WWW",
}

let attributes = ref([])

onMounted(() => {
  didmove(null, new Date())
})

let queryPage = "" //记录当前页面日期
async function didmove(page, date) {
  attributes.value = []
  queryPage = page ? page[0].id : date
  let StartTime = dayjs(queryPage).startOf("month").format("YYYY-MM-DD")
  let EndTime = dayjs(queryPage).endOf("month").format("YYYY-MM-DD")

  let res = await getRepairPlan({ StartTime, EndTime })
  if (res.items?.length > 0) {
    for (let item of res.items) {
      attributes.value.push({
        key: item.id,
        customData: {
          title: $t(
            "repairWorkOrder.maintenancePlanManagement.maintenancePlan"
          ),
        },
        dates: item.startTime,
        editData: item,
      })
    }
  }
}

function closeDialog(val) {
  addShow.value = false
  val && didmove(null, queryPage)
}

function add() {
  planOpt.type = "add"
  addShow.value = true
  planOpt.title = $t("repairWorkOrder.maintenancePlanManagement.newPlan")
}

function handlePlan(attr, type) {
  planOpt.type = type
  planOpt.data = attr.editData
  if (type == "look") {
    planOpt.title = $t("repairWorkOrder.detail")
    addShow.value = true
  } else if (type == "edit") {
    planOpt.title = $t("repairWorkOrder.edit")
    addShow.value = true
  } else if (type == "delete") {
    ElMessageBox.confirm(
      `${$t("repairWorkOrder.confirmation.deletePlan")}？`,
      $t("repairWorkOrder.deleteConfirm"),
      {
        confirmButtonText: $t("common.okText"),
        cancelButtonText: $t("common.cancelText"),
        type: "warning",
      }
    )
      .then(async () => {
        await delRepairPlan([attr.editData.id])
        ElMessage.success($t("prompt.prompt_9"))
        didmove(null, queryPage)
      })
      .catch(() => {})
  }
}
</script>
<style scoped lang="scss">
.plan {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 10px;
  .head {
    position: absolute;
    left: 0px;
    top: 0px;
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
  }
  .calendar {
    display: flex;
    justify-content: center;
    :deep(.vc-container) {
      background-color: rgba(0, 132, 254, 0.25);
      border: none;
      color: var(--g-font-color);
    }
    :deep(.vc-header button) {
      color: var(--g-font-color);
    }
    :deep(.vc-weekdays) {
      background-color: rgba(0, 132, 254, 0.25);
    }
    :deep(.vc-weekday) {
      color: var(--g-font-color);
    }
    .date-item {
      position: relative;
      width: 200px;
      height: 85px;
      border: 1px solid rgba(44, 50, 75, 1);
      overflow: hidden;
    }
    .date-text {
      position: absolute;
      width: 100%;
      height: 100%;
      font-size: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .tip-block {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      &:hover {
        .mask {
          display: block;
        }
      }
      .text {
        margin: 5px auto;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #42cd8a;
        font-size: 16px;
        .dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background-color: #42cd8a;
          margin-right: 10px;
        }
      }
      .mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.4);
        display: none;
        z-index: 2;
        .icons {
          position: absolute;
          width: 100%;
          bottom: 5px;
          display: flex;
          justify-content: space-around;
          .ic:hover {
            background-color: #999;
          }
        }
      }
    }
  }
}
</style>
