import { ElMessage } from "element-plus" //
import store from "@/store"
import router from "@/router"
import { Session, Local } from "@/utils/storage"
import {
  loginApi,
  loginByCardApi,
  refreshTokenApi,
  getAccessMenusApi,
  getUserInfo<PERSON>pi,
  logoutApi,
  updatePwdApi,
} from "./api/auth"
import { homeRoutes } from "@/router"
import { useI18n } from "@xfe/locale"
import { compact } from "lodash-es"

const TokenKey = "AccessToken"
const RefreshTokenKey = "RefreshToken"
export const AUTH_MENUS = "AUTH_MENUS"
export const AUTH_USER = "AUTH_USER"

export default function useAuths(opt) {
  const { t: $t } = useI18n()
  const defaultOpt = {
    loginUrl: "/login", // 登录页跳转url 默认: /login
    loginReUrl: "", // 登录页登陆成功后带重定向redirect=的跳转url 默认为空
    homeUrl: "/home", // 主页跳转url 默认: /home
    otherQuery: {}, // 成功登录后携带的（除redirect外）其他参数
  }

  let option = {
    ...defaultOpt,
    ...opt,
  }

  // 获取token
  const getToken = () => {
    return Session.get(TokenKey)
  }

  // 获取刷新token
  const getRefreshToken = () => {
    return Session.get(RefreshTokenKey)
  }

  // 存储token到cookies
  const setToken = (token, refreshToken) => {
    if (token == null) {
      return false
    }
    Session.set(TokenKey, token)
    if (RefreshTokenKey) Session.set(RefreshTokenKey, refreshToken)
    return true
  }

  // 删除token
  const removeToken = () => {
    Session.remove(TokenKey)
    Session.remove(RefreshTokenKey)
    return true
  }

  // 退出登录
  const logoutFun = async () => {
    let flag = true
    try {
      await logoutApi().then(res => {
        ElMessage({
          message: $t("prompt.prompt_3"),
          type: "info",
          duration: 2000,
        })
      })
    } catch (error) {
      return true
    }
    if (flag) {
      clearStorage()
    }
  }

  // 清空本地存储的信息
  const clearStorage = () => {
    Session.clear()
    Local.clear()
    removeToken()
    window.location.reload()
    Session.set("vuex", null)
    store.commit("SET_HAS_WEBSOCKET", false)
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const token = getRefreshToken()
      setToken(token)
      let res = await refreshTokenApi()
      const { accessToken, refreshToken } = res
      setToken(accessToken, refreshToken)
      return true
    } catch (error) {
      logoutFun()
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject($t("header.loginOut"))
    }
  }

  // 用户名密码登录
  const loginFun = async params => {
    const res = await loginApi(params)
    loginSuccess(res)
    return res
  }

  // 刷卡登录
  const loginByCard = async params => {
    const res = await loginByCardApi(params)
    loginSuccess(res)
    return res
  }

  // 用户更新密码
  const updatePwd = async params => {
    const res = await updatePwdApi(params)
    // 用户更新完自己的密码之后退出登录
    logoutFun()
    return res
  }

  // 获取用户基本信息、角色、菜单权限
  const getUserInfo = async () => {
    try {
      let result = await getUserInfoApi()
      store.dispatch("updateUserInfo", result)
      return result
    } catch (error) {
      return {}
    }
  }
  const updateNavList = async () => {
    const { menus } = await getUserInfo() // 用户信息
    const menuList = menus.filter(
      item => item.menuType === 1 || item.menuType === 3
    ) // 获取菜单信息
    store.dispatch("updateAccessMenus", menuList)
  }

  // 登录成功之后的操作
  const loginSuccess = async res => {
    const { accessToken, refreshToken } = res

    setToken(accessToken, refreshToken)
    try {
      // 存储用户信息
      const { menus, roleCodes, roles } = await getUserInfo() // 用户信息
      store.commit("SET_IS_ADMINISTRATOR", roleCodes.includes("administrator"))
      const menuList = menus.filter(
        item => item.menuType === 1 || item.menuType === 3
      ) // 获取菜单信息
      const menusArry = compact(menuList.map(n => n.router))
      if (!menuList.length && !roleCodes.includes("administrator")) {
        ElMessage({
          message: $t("prompt.prompt_5"),
          type: "error",
          duration: 2000,
        })
        // router.replace({ path: option.loginUrl }) // 默认 /login
        return
      }

      const isFront = menusArry.join(",").indexOf("home/")
      // const routeMenus = adminRoutes[0].children
      if (isFront === -1) {
        store.commit("SET_FRONT_AUTH", false)
      } else {
        store.commit("SET_FRONT_AUTH", true)
      }
      // 筛选出第一个匹配到的路由
      // let isAdminRouter = false
      // tiledRoute(routeMenus)
      //   .reverse()
      //   .forEach(r1 => {
      //     menus.forEach(r2 => {
      //       if (r1.path === r2.router) {
      //         if (isFront === -1) option.homeUrl = r1.path
      //         isAdminRouter = true
      //         store.commit("SET_ADMIN_DEFAULT_ROUTE", r1.path)
      //       }
      //     })
      //   })
      // if (isFront === -1 && !isAdminRouter) {
      //   ElMessage({
      //     message: $t("prompt.prompt_40"),
      //     type: "error",
      //     duration: 2000,
      //   })
      //   return
      // }

      // 筛选出前台第一个
      const frontMenus = homeRoutes[0].children
      if (isFront != -1) {
        const frontRouter = frontMenus.filter(
          item => item.path.includes("/home/") && item.meta.type === "nav"
        )
        let num = 0
        // 可以改为find
        frontRouter.forEach(e => {
          menusArry.forEach(e1 => {
            if (e.path === e1 && num === 0) {
              num++
              option.homeUrl = e.path
            }
          })
        })
      }

      // 从角色中处理该用户是否开启了角色降权
      const openAutoLogoutRoles = roles.filter(
        role => role.enablePermissionDowngrade
      )
      if (openAutoLogoutRoles.length > 0) {
        let overTimes = openAutoLogoutRoles.map(t => t.permissionDowngradeTime)
        let maxTime = Math.max(...overTimes)
        let info = {
          enablePermissionDowngrade: true,
          permissionDowngradeTime: maxTime,
        }
        store.commit("SET_AUTO_LOGOUT_INFO", info)
      } else {
        let info = {
          enablePermissionDowngrade: false,
          permissionDowngradeTime: 0,
        }
        store.commit("SET_AUTO_LOGOUT_INFO", info)
      }

      store.commit("SET_OVER_TIME", false)

      store.dispatch("updateAccessMenus", menuList)
      store.commit("SET_INIT_ROUTES", true)
      // store.dispatch("getvVersionInfo")
      // store.dispatch("getHomeBaseLine")
      // store.dispatch("getStopReason")
      // 登录成功后 路由跳转
      router.replace({
        path: option.loginReUrl ? option.loginReUrl : option.homeUrl,
        query: option.otherQuery,
      })
    } catch (error) {
      removeToken()
      return false
    }
  }

  // 平铺后台管理路由表 (写死的两极，出现三级四级用递归)
  function tiledRoute(list) {
    let adminList = []
    list.forEach(l1 => {
      if (!l1.hidden && !l1.children) {
        adminList.push(l1)
      } else {
        l1.children?.forEach(l2 => {
          adminList.push(l2)
        })
      }
    })
    return adminList
  }

  // 租户ID根据项目来，是定死的
  const getTenantId = () => {
    return "00000000-0000-0000-0000-000000000000"
  }

  return {
    getToken,
    setToken,
    removeToken,
    loginFun,
    loginByCard,
    refreshToken,
    getUserInfo,
    logoutFun,
    updatePwd,
    clearStorage,
    getTenantId,
    updateNavList,
  }
}
