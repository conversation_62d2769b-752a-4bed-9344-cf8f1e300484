<template>
  <div class="hmx-head">
    <div class="h-left">
      <img src="@/assets/images/common/logo.png" @click="toHome" class="img" />
    </div>
    <div class="h-right">
      <div class="item" @click="backHome">
        <el-icon><Back /></el-icon>
        <div class="text">{{ $t("header.goHome") }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue"
import { useRouter } from "vue-router"
import { useStore } from "vuex"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()
const router = useRouter()
const store = useStore()
const currentPath = computed(() => store.getters["currentPath"])
const backHome = async () => {
  router.push("/home/" + currentPath.value)
}
const toHome = () => {
  router.push("/home/")
}
</script>

<style lang="scss" scoped>
.hmx-head {
  height: 85px !important;
  line-height: 85px !important;
  padding: 10px 20px;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .h-left {
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
  }
  .h-right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    .item {
      width: 100%;
      height: 80%;
      padding: 0 10px;
      cursor: pointer;
      display: flex;
      color: #fff;
      justify-content: space-around;
      align-items: center;
      border: 1px solid;
      background: #004b90;
      border-radius: 5%;
      border-image: linear-gradient(
          180deg,
          rgba(72, 167, 255, 1),
          rgba(0, 132, 254, 1)
        )
        1 1;
      .text {
        font-size: 12px;
        font-weight: bold;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
@media screen and (max-width: 1024px) {
  :deep(.el-dropdown-menu__item) {
    line-height: 24px;
  }
}
</style>
<style lang="scss">
.el-dropdown-menu__item.language-bg {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}
</style>
