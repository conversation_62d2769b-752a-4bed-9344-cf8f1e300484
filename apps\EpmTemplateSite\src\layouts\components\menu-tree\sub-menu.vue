<template>
  <!-- 如果有子菜单，就渲染成sub-menu（折叠item）-->
  <template v-if="item.children?.length">
    <!--  -->
    <el-sub-menu
      :index="item.path"
      expand-close-icon="CaretBottom"
      expand-open-icon="CaretTop"
      v-if="!item.hidden && permissionList.includes(item.meta.code)"
    >
      <template #title>
        <div class="item">
          <div class="icon">
            <img :src="getIconPath(item.meta.icon ?? 'config')" alt="" />
          </div>
          <div class="content">{{ $t(item.meta.title) }}</div>
        </div>
      </template>
      <!-- 递归组件 调用自己 -->
      <my-sub-menu
        v-for="i in item.children"
        :key="i.path"
        :item="i"
      ></my-sub-menu>
    </el-sub-menu>
  </template>
  <!-- 否则就渲染成menu-item-->
  <template v-else>
    <!--  -->
    <el-menu-item
      :index="item.path"
      v-if="!item.hidden && permissionList.includes(item.meta.code)"
    >
      <!-- <svg-icon :name="item.meta?.icon || 'fenlei'" /> -->
      <template #title>
        <span>{{ $t(item.meta.title) }}</span>
      </template>
    </el-menu-item>
  </template>
</template>

<script setup>
import { computed } from "vue"
import MySubMenu from "./sub-menu.vue" // 导入自己
import { useI18n } from "@xfe/locale"
import { useStore } from "vuex"

const { t: $t } = useI18n()
const store = useStore()
defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const getIconPath = icon => {
  return require(`@/assets/images/menu/${icon}-icon.svg`)
}

const permissionList = computed(() => {
  return store.getters.permissionList ?? []
})
</script>
<style lang="scss" scoped>
:deep(.el-menu) {
  margin-top: 10px;
}
.item {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .icon {
    display: flex;
    width: 20px;
    height: 20px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .content {
    flex: 1;
    font-weight: bold;
    color: #fff;
    text-align: center;
  }
}
.el-menu-item {
  height: 48px;
  font-size: 16px;
  border-left: 5px solid transparent;
  > span {
    font-size: 16px;
  }
}

:deep(.el-sub-menu__title) {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  background: url("@/assets/images/menu/sub-title-icon.svg");
  .el-sub-menu__icon-arrow {
    width: 15px;
    height: 15px;
    color: #fef900;
    svg {
      width: 100%;
      height: 100%;
    }
  }
}

.el-menu-item.is-active {
  // background-color: var(--el-color-primary-light-9);
  background: rgba(182, 220, 255, 0.15);
  // border-left: 5px solid var(--el-color-primary);
  border-right: 5px solid #1890ff;
}

.is-active {
  :deep(.el-sub-menu__title.el-tooltip__trigger) {
    background-color: var(--el-color-primary-light-9);
    border-left: 5px solid var(--el-color-primary);
  }
}
</style>
