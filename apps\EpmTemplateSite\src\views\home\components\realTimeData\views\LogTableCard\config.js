import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()

export const logOpType = ["登录", "注销", "新增", "修改", "删除", "查询"]

// 日志类型tab
export const logTypeTab = [
  {
    value: 0,
    label: $t("card.title.alarmLog"),
    path: "/home/<USER>",
  },
  {
    value: 1,
    label: $t("card.title.operationLog"),
    path: "/home/<USER>",
  },
  {
    value: 2,
    label: $t("card.title.systemLog"),
    path: "",
  },
  {
    value: 3,
    label: $t("card.title.mesLog"),
    path: "",
  },
]

// 子站报警日志表头
export const tableColumnWarn = [
  {
    prop: "createTime",
    label: $t("card.table.alarmLog.alarmTime"),
    width: "170",
  },
  {
    prop: "siteName",
    label: $t("card.table.alarmLog.moduleName"),
    width: "130",
  },
  {
    prop: "message",
    label: $t("card.table.alarmLog.alarmInformation"),
    showOverflowTooltip: true,
  },
  {
    prop: "advice",
    showOverflowTooltip: true,
    label: $t("card.table.alarmLog.operationSuggestion"),
  },
  {
    width: "80",
    label: $t("card.table.alarmLog.details"),
    fixed: "right",
    buttons: [
      {
        name: $t("common.view"),
        type: "default",
        command: "detail",
      },
    ],
  },
]
// 子站系统日志表头
export const tableColumnSys = [
  { prop: "createTime", label: $t("card.table.systemLog.time") },
  {
    prop: "message",
    label: $t("card.table.systemLog.information"),
  },
]
// 子站操作日志表头
export const tableColumnOpt = [
  {
    width: "180",
    prop: "createTime",
    label: $t("card.table.operationLog.operateTime"),
  },
  {
    prop: "user",
    width: "70",
    label: $t("card.table.operationLog.operateTor"),
  },
  {
    prop: "opType",
    width: "80",
    label: $t("card.table.operationLog.operateType"),
  },
  {
    prop: "message",
    label: $t("card.table.operationLog.operateDetails"),
  },
  {
    width: "80",
    label: $t("card.table.alarmLog.details"),
    fixed: "right",
    buttons: [
      {
        name: $t("common.view"),
        type: "default",
        command: "detail",
      },
    ],
  },
]
