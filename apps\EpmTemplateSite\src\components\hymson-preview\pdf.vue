<template>
  <iframe
    v-if="fileType === 'pdf'"
    :src="pdfSrc"
    frameborder="0"
    style="width: 100%; height: 100%"
    class="iframe"
  >
    <p>您的浏览器不支持 iframe 标签,请从列表中下载预览</p>
  </iframe>

  <!-- 新增txt文件预览 -->
  <pre
    v-else-if="fileType === 'txt'"
    style="width: 100%; height: 100%; padding: 20px; white-space: pre-wrap"
    >{{ txtContent }}</pre
  >
</template>

<script setup name="PreviewPdf">
import { reactive, toRefs, watch, onUnmounted } from "vue"

const props = defineProps({
  file: {
    type: [Object, String],
    default: () => {},
    required: true,
  },
  fileType: {
    type: [Object, String],
    default: () => {},
  },
})

const data = reactive({
  pdfSrc: "",
  txtContent: "", // 新增txt内容存储
})

watch(
  () => props.file,
  async val => {
    if (val && props.fileType === "pdf") {
      data.pdfSrc = props.file
    } else if (props.fileType === "txt") {
      try {
        // 通过fetch获取txt文件内容并指定编码
        const response = await fetch(val)
        const buffer = await response.arrayBuffer()
        const decoder = new TextDecoder("utf-8")
        data.txtContent = decoder.decode(buffer)
      } catch (e) {
        data.txtContent = "文件加载失败"
      }
    }
  },
  { immediate: true }
)

onUnmounted(() => {
  data.pdfSrc = ""
  data.txtContent = ""
})

const { pdfSrc, txtContent } = toRefs(data)
</script>

<style scoped>
.iframe {
  background-color: #fff;
}
</style>
