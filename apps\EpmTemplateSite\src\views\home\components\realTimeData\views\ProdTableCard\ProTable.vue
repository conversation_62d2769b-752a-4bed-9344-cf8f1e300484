<template>
  <div class="prod-table">
    <el-form
      :inline="true"
      :model="form"
      class="demo-form-inline"
      label-width="80"
    >
      <el-form-item :label="$t('card.table.historicalData.productionTime')">
        <el-date-picker
          v-model="times"
          type="datetimerange"
          :start-placeholder="$t('card.table.historicalData.startTime')"
          :end-placeholder="$t('card.table.historicalData.endTime')"
          :default-time="defaultTime"
          :disabled-date="disabledDate"
          :shortcuts="setShortcuts('YYYY-MM-DD HH:mm:ss')"
        />
        <!-- value-format="YYYY-MM-DD HH:mm:ss" -->
      </el-form-item>
      <el-form-item :label="$t('card.table.historicalData.productBarCode')">
        <el-input
          v-model="form.barCode"
          :placeholder="$t('card.table.historicalData.productBarCode')"
          class="search-form-input"
        />
      </el-form-item>
      <el-form-item :label="$t('card.table.historicalData.result')">
        <el-select v-model="form.result">
          <el-option label="OK" value="OK"></el-option>
          <el-option label="NG" value="NG"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('card.table.historicalData.ngReason')">
        <el-select v-model="form.reason">
          <el-option label="OK" value="OK"></el-option>
          <el-option label="NG" value="NG"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="checkList" class="btn">
          {{ $t("card.table.historicalData.query") }}
        </el-button>
        <el-button type="primary" @click="reset" class="btn"> 重置 </el-button>
        <el-button
          type="primary"
          @click="handleExport"
          :loading="exportLoading"
          class="btn"
        >
          {{ $t("card.table.historicalData.export") }}
        </el-button>
        <el-button type="primary" class="btn" @click="offLineUpload">
          {{ $t("card.table.historicalData.offlineUpload") }}
        </el-button>
        <el-button type="primary" class="btn">
          {{ $t("card.table.historicalData.record") }}
        </el-button>
        <!-- <column-set
          name="aluminum-history-column-set"
          v-bind="$attrs"
          :columns="tableColumn"
          @columnSetting="v => (tableColumn = v)"
        /> -->
      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <hmx-table
      :table-data="productList"
      :options="tableOptions"
      :columns="tableColumn"
      @command="handleAction"
      @size-change="handlerPageSize"
      @current-change="handlerPageIndex"
      @selection-change="handleSelectionChange"
    >
      <template #result="{ row }">
        <span :style="{ color: handlerColor(row.result) }">
          {{ row.result }}
        </span>
      </template>
      <template #isPostMes="{ row }">
        <span>
          {{ row.isPostMes == true ? "是" : "否" }}
        </span>
      </template>
      <template #productType="{ row }">
        <span>
          {{
            row.productType == 0
              ? $t("card.table.productList.regularParts")
              : row.productType == 1
              ? $t("card.table.productList.firstPiece")
              : $t("card.table.productList.challengePiece")
          }}
        </span>
      </template>
    </hmx-table>
  </div>
</template>

<script setup name="ProdTableCard">
import {
  reactive,
  toRefs,
  defineEmits,
  computed,
  onMounted,
  unref,
  ref,
  watch,
} from "vue"
import dayjs from "dayjs"
import ColumnSet from "./column-set.vue"
import useTable from "@/hooks/useTable"
import HmxTable from "@/components/hmx-table/index.vue"
import { useColumn } from "./useColumn"
import { exportProductExcel, offlineData } from "@/api/front/production"
import exportByBlob from "@/utils/exportByBlob"
import { useI18n } from "@xfe/locale"
import { config } from "@/config"
import { formatTime, setShortcuts } from "@/utils"
import { useMessage } from "@/hooks/web/useMessage"

const homeServerPrefix = config.base_url.homeServerPrefix
const { createMessage, createConfirm } = useMessage()

const { t: $t } = useI18n()

const { list, total, loading, getList, addFun, updateFun, deleteFun } =
  useTable(`${homeServerPrefix}/ProductData`, true, false)

const colors = {
  success: "#20E6A4",
  error: "#FF1E26",
}

const props = defineProps({
  allColumns: {
    type: Array,
    require: true,
    default: () => [],
  },
  proHisColumn: {
    type: Array,
    default: () => [],
  },
  checkParams: {
    type: String,
    default: "",
  },
})

// data--------
const times = ref([
  dayjs(dayjs().add(-1, "hours").valueOf()).format("YYYY-MM-DD HH:mm:ss"),
  dayjs(dayjs().valueOf()).format("YYYY-MM-DD HH:mm:ss"),
])

// let tableColumn = useColumn(true, props.proHisColumn)
let baseColumn = useColumn(true)
let tableColumn = computed(() => {
  return [...baseColumn, ...props.proHisColumn]
})

const data = reactive({
  // 绑定数据
  form: {
    barCode: "",
    start: unref(times)[0],
    end: unref(times)[1],
    result: props.checkParams ?? "",
    reason: "",
  },
  page: {
    pageSize: 20,
    pageIndex: 1,
  },
  options: {
    show: false,
    type: "", // 用于判断是编辑还是删除 add edit
    curRole: null,
  },
})
watch(
  () => times.value,
  v => {
    data.form.start = formatTime(v[0])
    data.form.end = formatTime(v[1])
  }
)

const tableOptions = computed(() => {
  return {
    loading: loading.value,
    showPagination: true,
    paginationPosition: "right",
    border: true,
    paginationConfig: {
      total: total.value,
      currentPage: data.page.pageIndex,
      pageSize: data.page.pageSize,
    },
  }
})

const params = computed(() => {
  let form = JSON.parse(JSON.stringify(data.form))
  Object.keys(form).forEach(key => {
    if (form[key] === null) {
      form[key] = ""
    }
  })
  return {
    ...form,
    ...data.page,
  }
})

let productList = computed(() => {
  // 动态添加对应列数据
  list.value.forEach(item => {
    if (item.details?.length > 0) {
      item.details.forEach(el => {
        el.value.length > 0 &&
          el.value.forEach(e => {
            item[e.dataCode] = e.dataValue
          })
      })
    }
  })
  return list.value
})

const emit = defineEmits([
  "command", // 查看详情
])

// 操作事件
const handleAction = (command, row) => {
  emit("command", command, row, 2)
}
// 点击查询
const checkList = async () => {
  let ctime = times.value[1] - times.value[0]
  if (ctime > 7 * 24 * 60 * 60 * 1000) {
    ElMessage.warning($t("card.table.historicalData.queryTip"))
    return
  }
  data.page.pageIndex = 1
  loading.value = true
  await getList(params.value)
  loading.value = false
}

// 重置
const reset = () => {
  data.form = {
    barCode: "",
    start: unref(times)[0],
    end: unref(times)[1],
    result: "",
    reason: "",
  }
}

const defaultTime = new Date()

const disabledDate = time => {
  return time.getTime() > new Date(new Date().getTime())
}

// 导出
let exportLoading = ref(false)
const handleExport = async () => {
  let pramas = {
    start: unref(params).start,
    end: unref(params).end,
    barCode: unref(params).barCode,
  }
  exportLoading.value = true
  try {
    const excelBolb = await exportProductExcel(pramas)
    const date = dayjs(new Date().getTime())
      .format("YYYY-MM-DD HH:mm:ss")
      .toString()
    exportByBlob(excelBolb, date + $t("prompt.prompt_11"))
  } catch (error) {
    exportLoading.value = false
  }
  exportLoading.value = false
}

const handlerColor = status => {
  let color = ""
  switch (status) {
    case "OK":
      color = colors.success
      break
    case "NG":
      color = colors.error
      break
    default:
      break
  }
  return color
}

// 表请求条数改变
const handlerPageSize = pageSize => {
  data.page.pageSize = pageSize
  data.page.pageIndex = 1
  getList(params.value)
}
// 表格页数改变
const handlerPageIndex = pageIndex => {
  data.page.pageIndex = pageIndex
  getList(params.value)
}

let selectArr = ref([]) // 离线上传数据
function handleSelectionChange(val) {
  selectArr.value = val
}

// 离线上传
const offLineUpload = () => {
  if (selectArr.value.length === 0) {
    createMessage("请勾选需要上传的数据！", "warning")
    return
  }
  createConfirm("confirm", "确认是否离线上传", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  }).then(() => {
    let ids = selectArr.value.map(e => e.id)
    offlineData(ids).then(() => {
      createMessage("上传成功！", "success")
    })
  })
}

onMounted(() => {
  getList(params.value)
})
const { options, form } = toRefs(data)
</script>

<style lang="scss" scoped>
.prod-table {
  .demo-form-inline {
    width: 80%;
    height: 120px;
  }
  height: calc(100vh - 400px);
  :deep(.el-form) {
    .el-form-item__label {
      color: var(--card-title);
    }
    .btn {
      min-width: 100px;
      background-color: var(--btn-background);
    }
  }
  :deep(.hymson-table) {
    height: calc(100% - 160px);
  }
}
</style>
<style lang="scss" scoped>
@media screen and (max-width: 1024px) {
  .prod-table {
    .search-form-select {
      width: 90px;
    }
    .search-form-input {
      width: 140px;
    }
  }
}
</style>
