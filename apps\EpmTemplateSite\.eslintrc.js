module.exports = {
  // 表示ESLint规则，将被限制在根目录下
  root: true,
  env: {
    // 表示在node环境下，启用ESLint
    node: true,
    browser: true,
    es2021: true,
    "vue/setup-compiler-macros": true, // 处理error ‘defineProps’ is not defined no-undef
  },
  parser: "vue-eslint-parser",
  parserOptions: {
    // parser: "@babel/eslint-parser",
    requireConfigFile: false,
    parser: "@typescript-eslint/parser",
  },
  extends: [
    "plugin:vue/essential",
    "eslint:recommended",
    "@vue/typescript/recommended",
    "plugin:prettier/recommended",
  ],
  plugins: ["vue", "@typescript-eslint"],
  // 我们主要使用以下规则配置
  // 错误急别分为三类
  // 1、off 或 0 表示关闭验证规则
  // 2、warn 或 1 表示开启警告验证规则
  // 3、error 或 2 表示开启错误验证规则，程序会报错退出
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "error" : "warn",
    "no-debugger": process.env.NODE_ENV === "production" ? "error" : "warn",
    // 新增配置项，表示方法名与后面括号的空格不校验
    "space-before-function-paren": "off",
    "prettier/prettier": [
      "error",
      {
        singleQuote: false,
        // parser: "flow",
        printWidth: 80,
      },
    ],
    "vue/multi-word-component-names": "off",
    "no-useless-escape": "off",
    "vue/no-multiple-template-root": "off",
    // 允许非空断言
    "@typescript-eslint/no-non-null-assertion": "off",
    // 允许自定义模块和命名空间
    "@typescript-eslint/no-namespace": "off",
    // 允许对this设置alias
    "@typescript-eslint/no-this-alias": "off",
    // 允许使用any类型
    "@typescript-eslint/no-explicit-any": ["off"],
    // 关闭空函数警告
    "@typescript-eslint/no-empty-function": ["off"],
    "no-var": "off",
    "prefer-const": "off",
    "prefer-rest-params": "off",
    "vue/no-v-for-template-key": "off",
  },
  globals: {
    ElementPlusIconsVue: true,
    ElementPlusLocaleZhCn: true,
  },
};
