export default {
  knowledgeBase: {
    baseCategory: "Knowledge Base",
    equipmentElectrical: "Equipment & Electrical knowledge base",
    process: "Process knowledge base",
    quality: "Quality knowledge base",
    monthlyTop10: "Top 10 issues this month",
    faultPhenomenon: "Fault symptom",
    faultCause: "Fault cause",
    handlingMeasures: "Solution",
    addRecord: "Add history",
    editRecord: "Edit history",
    addSuccess: "Knowledge base added successfully",
    addFailure: "Failed to add knowledge base",
    problemPhenomenon: "Problem symptom",
    problemPhenomenonPlaceholder: "Enter problem symptom",
    relatedFaultCause: "Related Fault Causes",
    relatedFaultCausePlaceholder: "Enter related fault cause",
    handlingMeasuresPlaceholder: "Enter handling measures",
    referenceCount: "Reference Count",
    referenced: "Referenced",
    templateTable: "Knowledge Base data Sheet",
  },
}
