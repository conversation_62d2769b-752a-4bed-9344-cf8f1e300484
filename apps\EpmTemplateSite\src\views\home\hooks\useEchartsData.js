import { computed, onUnmounted, onMounted, ref, watch } from "vue"
import { useStore } from "vuex"
import useTable from "@/hooks/useTable"
import useSocket from "@/hooks/useSocket"
import { config } from "@/config"
import { useI18n } from "@xfe/locale"
import { getFilesDetails } from "@/api/front/spareParts"
import { useMessage } from "@/hooks/web/useMessage"
import { createSeries } from "@/views/home/<USER>/echartsConfig.js"
import emitter from "@/utils/mitt"
import dayjs from "dayjs"

/**
 * echarts数据组装
 */
export default function useEchartsData() {
  const { t: $t } = useI18n()
  const store = useStore()
  const { base_url } = config
  const homeServerPrefix = base_url.homeServerPrefix
  const { createMessage } = useMessage()

  const { getList: getSparePartsList } = useTable(
    `${homeServerPrefix}/SparePartsAssembly`
  )

  // 设备性能
  const tensionMonitorInfo = computed(() => store.getters["notice/oeeInfo"])

  // 生产统计
  const outputStatisticsInfo = computed(() => {
    const outputStatisticsData = store.getters["notice/outputStatisticsInfo"]
    return {
      xAxis: {
        data: outputStatisticsData?.map(item => {
          if (item.teamName === "上一班次") {
            return $t("monitor.theLastShift")
          } else if (item.teamName === "当前班次") {
            return $t("monitor.currentShift")
          }
        }),
      },
      series: [
        {
          data: outputStatisticsData?.map(item => item.laserOneYield),
        },
        {
          data: outputStatisticsData?.map(item => item.laserTwoYield),
        },
      ],
    }
  })

  // 报警排名
  const alarmRankingInfo = computed(() => {
    const alarmRankingData = store.getters["notice/alarmRankInfo"]
    return {
      yAxis: {
        data: alarmRankingData?.map(item => item.alarmContent),
      },
      series: [
        {
          data: alarmRankingData?.map(item => item.number),
        },
      ],
    }
  })

  // 设备维护
  const equipmentMaintenanceInfo = computed(() => {
    const equipmentMaintenanceData = store.getters["notice/sparePartsInfo"]
    return {
      yAxis: {
        data: equipmentMaintenanceData?.map(item => item.sparePartsName),
      },
      series: [
        {
          data: equipmentMaintenanceData?.map(item => ({
            value: item.usedPercentage,
            distance: item.usedDistance,
          })),
          itemStyle: {
            color: params =>
              equipmentMaintenanceData[params.dataIndex]?.color || "#1990FF",
          },
        },
      ],
    }
  })

  // 设备状态
  const equipmentStatusInfo = computed(() => {
    const equipmentStatusData = store.getters["notice/equipmentStatusInfo"]
    return {
      data: {
        currentStatus: equipmentStatusData.currentStatus,
        runningTime: equipmentStatusData.runningTime,
        stopTime: equipmentStatusData.stopTime,
        alarmTime: equipmentStatusData.alarmTime,
        resetTime: equipmentStatusData.resetTime,
        initializeTime: equipmentStatusData.initializeTime,
      },
      option: {
        xAxis: {
          min: equipmentStatusData.statusList?.length
            ? dayjs(equipmentStatusData.statusList[0]?.startTime).valueOf()
            : dayjs().startOf("day").valueOf(), // 空数组时设置默认值
          max: equipmentStatusData.statusList?.length
            ? dayjs(
                equipmentStatusData.statusList[
                  equipmentStatusData.statusList.length - 1
                ]?.endTime
              ).valueOf()
            : dayjs().endOf("day").valueOf(),
        },
        series: equipmentStatusData.statusList?.length
          ? equipmentStatusData.statusList.map(item =>
              createSeries(
                item.status,
                item.color,
                dayjs(item.startTime).valueOf(),
                dayjs(item.endTime).valueOf()
              )
            )
          : [],
      },
    }
  })

  // 当前报警
  const alarmLogs = computed(() => store.state.notice.alarmInfo)

  // 生产报警
  const productionTableData = computed(() =>
    store.state.notice.productionAlarmData.map(item => {
      return {
        ...item,
        alarmTime: dayjs(item.alarmTime).format("YYYY-MM-DD HH:mm:ss"),
        alarmName: item.alarmContent?.chinaName || "-",
      }
    })
  )
  const alarmDetailShow = ref(false)
  const alarmDetailData = ref({})
  const showAlarmDetail = info => {
    alarmDetailShow.value = true
    alarmDetailData.value = info
  }
  // 3d
  let tdIframeRef = ref()
  // 报警总数
  let alarmCountShow = ref(false)
  const handleAlarmCountClick = () => {
    alarmCountShow.value = true
  }
  const handlePosition = val => {
    tdIframeRef.value.flyToModel(val)
  }
  const isCoolDown = computed(() => store.getters["isCoolDown"])
  // watch(
  //   () => alarmLogs.value,
  //   val => {
  //     if (val && !isCoolDown.value) {
  //       alarmCountShow.value = true
  //     }
  //   },
  //   { deep: true }
  // )
  const sparePartShow = ref(false)
  const spareLoading = ref(false)
  const handleViewSparepart = async item => {
    if (item) {
      const { alarmCode, serialNumber } = item
      spareLoading.value = true
      sparePartShow.value = true
      // 弹窗点击先置空上次数据
      emitter.emit("getSparePartData", {})
      emitter.emit("getRelevanceList", [])
      try {
        const sparePartRes = await getSparePartsList({ name: alarmCode })
        emitter.emit("getSparePartData", sparePartRes?.items[0] ?? {})
        const relevanceRes = await getFilesDetails({
          serialNumber,
        })
        emitter.emit("getRelevanceList", relevanceRes ?? [])
        spareLoading.value = false
      } catch (error) {
        spareLoading.value = false
      }
    } else {
      createMessage("该备件暂无报警信息~", "warning")
    }
  }

  let timer = null

  onMounted(() => {
    // 刷新页面后重新建立signalr连接
    window.addEventListener("beforeunload", function (event) {
      // 在页面刷新或关闭时执行你的清理操作
      // 可以是清除数据、关闭连接等
      store.commit("SET_HAS_WEBSOCKET", false)
    })
  })

  onUnmounted(() => {
    clearInterval(timer)
  })

  return {
    productionTableData,
    alarmDetailShow,
    showAlarmDetail,
    alarmCountShow,
    handlePosition,
    handleAlarmCountClick,
    alarmLogs,
    moduleUrl: base_url.moduleUrl,
    tdIframeRef,
    sparePartShow,
    spareLoading,
    handleViewSparepart,
    tensionMonitorInfo,
    outputStatisticsInfo,
    alarmRankingInfo,
    equipmentMaintenanceInfo,
    equipmentStatusInfo,
  }
}
