<template>
  <div class="spareParts-box">
    <div class="table-form spareParts-form">
      <el-form
        ref="formRef"
        :model="form"
        :inline="true"
        class="pro-select-box"
      >
        <el-form-item :label="$t('sparePartsWearingParts.sparePartsName')">
          <el-input
            v-model="form.name"
            class="search-form-input"
            clearable
            :placeholder="$t('sparePartsWearingParts.sparePartsName')"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="checkList">{{
            $t("common.queryText")
          }}</el-button>
          <el-button type="primary" @click="handleReset">{{
            $t("common.resetText")
          }}</el-button>
          <el-button
            type="primary"
            @click="handleExport"
            :loading="exportLoading"
            >{{ $t("common.export")
            }}<el-icon class="el-icon--right"> <Share /> </el-icon
          ></el-button>
        </el-form-item>
      </el-form>
      <!-- 表格 -->
      <hmx-table
        :table-data="tableData"
        :options="tableOptions"
        :columns="tableColumn"
        @command="handleAction"
        @size-change="handlerPageSize"
        @current-change="handlerPageIndex"
      >
        <template #materialList="{ row }">
          <el-button type="primary" text @click="handleDetail(row)">{{
            $t("common.detail")
          }}</el-button>
        </template>
      </hmx-table>
      <!-- 文件弹框 -->
      <div>
        <input v-show="false" ref="fileRef" type="file" @change="getFile" />
      </div>
    </div>
    <div class="top-chart">
      <div class="top-chart-item">
        <div class="title">
          {{ $t("sparePartsWearingParts.consumptionTop") }}
        </div>
        <BarRank class="rank" :option="consumptionQuantity" />
      </div>
      <div class="top-chart-item">
        <div class="title">
          {{ $t("sparePartsWearingParts.avgReplacementTime") }}
        </div>
        <BarRank class="rank" :option="averageUsedTime" />
      </div>
      <div class="top-chart-item">
        <div class="title">
          {{ $t("sparePartsWearingParts.avgServiceLife") }}
        </div>
        <BarRank class="rank" :option="averageUsedDistance" />
      </div>
    </div>
  </div>
  <preview-dialog :options="previewOptions" @onCancel="closePreviewDialog" />
  <!-- 详情 -->
  <DetailDialog
    :isShow="isDetailShow"
    @onClose="handleCloseDetailDialog"
    :currentItem="currentDetailItem"
  />
  <!-- 更换记录 -->
  <ReplaceDialog
    :isShow="isReplaceShow"
    @onClose="handleCloseReplaceDialog"
    :currentItem="currentReplaceItem"
  />
</template>

<script setup name="UserManageHome">
import { reactive, ref, onMounted, toRefs, computed } from "vue"
import { ElMessage } from "element-plus"
import useTable from "@/hooks/useTable"
import { formatTime } from "@/utils"
import HmxTable from "@/components/hmx-table/index.vue"
import ReplaceDialog from "./components/replace-record-dialog.vue"
import DetailDialog from "./components/detail-dialog.vue"
import BarRank from "./components/barRank.vue"
import { useI18n } from "@xfe/locale"
import { config } from "@/config"
import {
  exportExcel,
  importExcel,
  putInventory,
  putStatus,
} from "@/api/admin/config/quickWearPart"
import { getSparePartsRanking } from "@/api/front/spareParts"

import dayjs from "dayjs"
import exportByBlob from "@/utils/exportByBlob"
import PreviewDialog from "./components/preview-dialog.vue"

const homeServerPrefix = config.base_url.homeServerPrefix

const { t: $t } = useI18n()

const { list, total, loading, getList, addFun, updateFun, deleteFun } =
  useTable(`${homeServerPrefix}/SparePartsAssembly`)

const formRef = ref()

const tableColumn = [
  { type: "index", width: "50", label: "No.", align: "center" },
  {
    prop: "barcode",
    label: $t("sparePartsWearingParts.barcode"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "name",
    label: $t("sparePartsWearingParts.name"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "usedDistance",
    label: $t("sparePartsWearingParts.usageDistance"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "alarmDistance",
    label: $t("sparePartsWearingParts.alarmThreshold"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "stopDistance",
    label: $t("sparePartsWearingParts.shutdownThreshold"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "replaceTime",
    label: $t("sparePartsWearingParts.replacementTime"),
    align: "center",
    showOverflowTooltip: true,
  },
  {
    prop: "materialList",
    label: $t("sparePartsWearingParts.relatedMaterials"),
    align: "center",
    showOverflowTooltip: true,
    slot: "materialList",
  },
  {
    width: "140",
    label: $t("common.operate"),
    align: "center",
    fixed: "right",
    buttons: [
      {
        name: $t("common.edit"),
        type: "text",
        command: "edit",
        permission: ["production:quickWear:edit"],
      },
      {
        name: $t("common.delText"),
        type: "text",
        command: "delete",
        permission: ["production:quickWear:del"],
      },
      {
        name: $t("sparePartsWearingParts.replacementRecords"),
        type: "text",
        command: "replace",
      },
    ],
  },
]

const data = reactive({
  // 绑定数据
  form: {
    name: "", // 备件/易损件名称
  },
  page: {
    pageSize: 10,
    pageIndex: 1,
  },
  options: {
    show: false,
    type: "", // 用于判断是编辑还是删除 add edit
    curUser: null,
  },
  previewOptions: {
    show: false,
    imgUrl: null,
  },
})

const tableData = computed(() => {
  let arr = []
  arr = list.value
  return arr
})

const tableOptions = computed(() => {
  return {
    loading: loading.value,
    showPagination: true,
    border: false,
    paginationConfig: {
      total: total.value,
      currentPage: data.page.pageIndex,
      pageSize: data.page.pageSize,
    },
  }
})

const params = computed(() => {
  let form = JSON.parse(JSON.stringify(data.form))
  Object.keys(form).forEach(key => {
    if (form[key] === null) {
      form[key] = ""
    }
  })
  return {
    ...form,
    ...data.page,
  }
})
// 操作事件
const handleAction = (command, row) => {
  switch (command) {
    case "edit":
      handlerEdit(row)
      break
    case "delete":
      handlerDel(row.id)
      break
    case "replace":
      handleReplace(row)
    default:
      break
  }
}
// 点击查询
const checkList = () => {
  data.page.pageIndex = 1
  getList(params.value)
}
// 表请求条数改变
const handlerPageSize = pageSize => {
  data.page.pageSize = pageSize
  data.page.pageIndex = 1
  getList(params.value)
}
// 表格页数改变
const handlerPageIndex = pageIndex => {
  data.page.pageIndex = pageIndex
  getList(params.value)
}

// 新增
const handlerAdd = () => {
  data.options = {
    show: true,
    type: "add", // 用于判断是编辑还是删除 add edit
    curUser: null,
  }
}
// 编辑
const handlerEdit = async item => {
  data.options = {
    show: true,
    type: "edit", // 用于判断是编辑还是删除 add edit
    curUser: item,
  }
}
// 删除
const handlerDel = async item => {
  deleteFun([item])
}

// 更换
const isReplaceShow = ref(false)
const currentReplaceItem = ref({})
const handleReplace = async item => {
  isReplaceShow.value = true
  currentReplaceItem.value = item
}
const handleCloseReplaceDialog = () => {
  isReplaceShow.value = false
}

// 详情
const isDetailShow = ref(false)
const currentDetailItem = ref({})
const handleDetail = item => {
  isDetailShow.value = true
  currentDetailItem.value = item
}
const handleCloseDetailDialog = () => {
  isDetailShow.value = false
}

const closePreviewDialog = () => {
  data.previewOptions = {
    show: false,
    imgUrl: null,
  }
}

// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  exportLoading.value = true
  const res = await exportExcel().catch(() => {
    exportLoading.value = false
  })
  const date = dayjs(new Date().getTime())
    .format("YYYY-MM-DD HH:mm:ss")
    .toString()
  await exportByBlob(
    res,
    date + $t("sparePartsWearingParts.SparePartsWarehouseData")
  )
  exportLoading.value = false
}
// 导入
const fileRef = ref(null)
const getFile = async e => {
  let formDate = new FormData()
  formDate.append("file", e.target.files[0])
  await importExcel(formDate)
  ElMessage({
    message: $t("prompt.prompt_10"),
    type: "success",
    duration: 1000,
  })
  getList(params.value)
}

// 重置
const handleReset = () => {
  data.form.name = ""
  checkList()
}

// 排行榜列表
const consumptionQuantity = ref({ yAxisData: [], seriesData: [] }) // 保持与 averageUsedTime 一致的结构
const averageUsedTime = ref({ yAxisData: [], seriesData: [] }) // 初始化时使用空数组
const averageUsedDistance = ref({})

onMounted(async () => {
  const res = await getSparePartsRanking()
  consumptionQuantity.value = {
    yAxis: [
      {
        data: res?.consumptionQuantity?.map(d => d.name) || [],
      },
    ],
    series: [
      {
        data:
          res?.consumptionQuantity?.map(d => ({
            type: "quantity",
            value: d.quantity,
          })) || [],
      },
    ],
  }
  averageUsedTime.value = {
    yAxis: [
      {
        data: res?.averageUsedTime?.map(d => d.name) || [],
      },
    ],
    series: [
      {
        data:
          res?.averageUsedTime?.map(d => ({
            type: "usedTime",
            value: d.usedTime,
          })) || [],
      },
    ],
  }
  averageUsedDistance.value = {
    yAxis: [
      {
        data: res?.averageUsedDistance?.map(d => d.name) || [],
      },
    ],
    series: [
      {
        type: "bar",
        data: res?.averageUsedDistance?.map(d => d.usedDistance) || [],
        itemStyle: {},
        color: "#68bbc4",
        label: {
          show: true,
          position: "inside",
          color: "#fff",
          formatter: function (params) {
            return params.value
          },
        },
      },
      {
        type: "bar",
        data: res?.averageUsedDistance?.map(d => d.usedTime) || [],
        itemStyle: {},
        color: "#0084FE",
        label: {
          show: true,
          position: "inside",
          color: "#fff",
          formatter: function (params) {
            return params.value
          },
        },
      },
    ],
  }
})

const { form, options, previewOptions } = toRefs(data)
</script>
<style lang="scss" scoped>
.spareParts-box {
  display: flex;
  width: 100%;
  height: 100%;
  .spareParts-form {
    width: 75%;
    height: 100%;
  }
  .top-chart {
    display: flex;
    flex-direction: column;
    width: 25%;
    height: 100%;
    &-item {
      flex: 1;
      .title {
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
        font-weight: bold;
      }
      .rank {
        width: 100%;
        height: calc(100% - 30px);
      }
    }
  }
}
</style>
