import { request } from "@xfe/request"
import { config } from "@/config"
const base = config.base_url.homeServerPrefix

export function Info_WorkstationApiModel() {
  return request.get({
    url: `${base}/AnalyseWorkstation/InfoWorkstation`,
  })
}

// 新版获取信息框位置
export function getStationSite(params) {
  return request.get({
    url: `${base}/StationSite/${params}`,
  })
}

// 新版创建信息框位置
export function setStationSite(data) {
  return request.post({
    url: `${base}/StationSite/reset`,
    data: data,
  })
}

// 上传工序背景图
export function uploadFileImage(data) {
  return request.post({
    url: `${base}/ExternalData/UploadFile`,
    headers: { "Content-Type": "multipart/form-data" },
    data: data,
  })
}

export function downloadImage(data) {
  return request.get({
    url: `${base}/ExternalData/DownloadFile/${data}`,
    headers: { "Content-Type": "application/x-download" },
    responseType: "blob",
  })
}
