//@ts-nocheck
import { createApp } from "vue"
import App from "./App.vue"
import store from "./store"
import router from "./router"

// import "element-plus/dist/index.css"
import "@/assets/styles/style.scss"
import "@/assets/styles/reset-element.scss"
import "@/assets/styles/common.scss"
import "@/assets/styles/index.css"
import "@/assets/styles/element/index.scss"
import "@/assets/fonts/style.css" // 导入字体图标
import "@/assets/icons" // 导入图标资源
import * as ElIcons from "@element-plus/icons-vue"
import SvgIcon from "@/components/svg-icon/index.vue" // svg component
import VCalendar from "v-calendar"
import "v-calendar/style.css"
import { RecycleScroller } from "vue-virtual-scroller"
import "vue-virtual-scroller/dist/vue-virtual-scroller.css"

// Use plugin with optional defaults

import dayjs from "dayjs"
import "dayjs/locale/zh-cn"
import ElementPlus from "element-plus"

//引入自定义指令
import directives from "@/directives/index"

import { useMessage } from "@/hooks/web/useMessage"
import { setMsg, setNoice, setConfirm } from "@xfe/request"

import localeFile from "@/locale/index"

import { setupI18n, useLocale } from "@xfe/locale"
import { initApplication } from "./init-application" // setupComponents

// 路由拦截
import bootStrapPermission from "@/core/useAuth/permission"

import initSocketClient from "./initSignalr"

const { getLocale } = useLocale()

if (getLocale.value == "en-US") {
  dayjs.locale("en")
} else {
  dayjs.locale("zh-cn")
}

const { createMessage, notification, createConfirm } = useMessage()
;(async () => {
  const app = createApp(App)

  // 全局注册自定义指令
  Object.keys(directives).forEach(key => {
    app.directive(key, directives[key])
  })

  for (const name in ElIcons) {
    app.component(name, ElIcons[name])
  }

  app.config.globalProperties.$dayjs = dayjs // 全局引入dayjs方法

  await initApplication()
  // 国际化资源文件对象
  let i18nLocale = await localeFile

  // 注册i18n
  await setupI18n(app, i18nLocale)

  setMsg(createMessage)
  setNoice(notification)
  setConfirm(createConfirm)
  app.use(store)
  app.use(router)
  app.use(VCalendar, {})
  app.component("RecycleScroller", RecycleScroller)
  // setupComponents(app)
  app
    .use(ElementPlus)
    // 可传三个参数  {requestMethod = request, requestKeys = {}, locale = 'zh-CN'}
    // requestMethod 请求方法默认已用@xfe/request
    // requestKeys 表格分页配置和数据项显示的key 默认值如下：
    /**
     * {
          dataKey: 'items', 返回的数据项的key
          totalKey: 'totalCount',
          pageKey: 'pageIndex',
          sizeKey: 'pageSize'
        }
     */
    .component("svg-icon", SvgIcon)
  bootStrapPermission()
  initSocketClient()
  router.isReady().then(() => app.mount("#app"))
})()
