<template>
  <dialog-card
    dialogWidth="40%"
    :dialogTitle="planOpt.title"
    :isVisable="addShow"
    :append-to-body="false"
    @closeDialog="closeFun(false)"
    @openDialog="openFun"
    top="6%"
  >
    <el-form
      label-width="140"
      class="form"
      ref="planRef"
      :model="planForm"
      :rules="rules"
    >
      <el-form-item
        :label="$t('common.date')"
        class="date-time"
        prop="startTime"
      >
        <el-date-picker
          v-model="planForm.startTime"
          type="datetime"
          :placeholder="$t('common.date')"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
          :readonly="planOpt.type == 'look'"
        />
      </el-form-item>
      <el-form-item :label="$t('table.logTask.description')" prop="description">
        <el-input
          v-model="planForm.description"
          :rows="4"
          type="textarea"
          :readonly="planOpt.type == 'look'"
        />
      </el-form-item>
      <el-form-item class="btns">
        <el-button type="primary" @click="save" v-if="planOpt.type != 'look'">{{
          $t("common.okText")
        }}</el-button>
        <el-button type="primary" @click="cancel">{{
          $t("common.cancelText")
        }}</el-button>
      </el-form-item>
    </el-form>
  </dialog-card>
</template>
<script setup>
import { reactive, ref } from "vue"
import { addRepairPlan, putRepairPlan } from "@/api/front/repairOrder.js"
import dialogCard from "@/components/hmx-dialog.vue"
import { formatTime } from "@/utils"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()
let props = defineProps({
  addShow: Boolean,
  planOpt: {
    type: Object,
    default: () => {},
  },
})
let emit = defineEmits(["closeDialog"])

let planForm = reactive({
  id: "",
  startTime: new Date(),
  description: "",
})
let planRef = ref()
let rules = {
  startTime: [
    {
      required: true,
      message: $t("repairWorkOrder.validation.chooseDate"),
      trigger: "change",
    },
  ],
  description: [
    {
      required: true,
      message: $t("repairWorkOrder.validation.enterDescription"),
      trigger: "blur",
    },
  ],
}

function closeFun(val = false) {
  emit("closeDialog", val)
}

function openFun() {
  if (props.planOpt.type == "add") {
    planForm.startTime = new Date()
    planForm.description = ""
    planForm.id = ""
  } else {
    for (let key in planForm) {
      planForm[key] = props.planOpt.data[key] || ""
    }
  }
}

async function save() {
  planRef.value.validate(async valid => {
    if (valid) {
      planForm.startTime = formatTime(planForm.startTime)
      if (props.planOpt.type == "edit") {
        await putRepairPlan(planForm.id, planForm)
        ElMessage.success($t("repairWorkOrder.messages.updatePlanSuccess"))
        closeFun(true)
      } else {
        await addRepairPlan(planForm)
        ElMessage.success($t("repairWorkOrder.messages.createPlanSuccess"))
        closeFun(true)
      }
    }
  })
}

function cancel() {
  closeFun(false)
}
</script>
<style scoped lang="scss">
.form {
  width: 80%;

  .date-time {
    :deep(.el-input__wrapper) {
      width: 100%;
    }
  }
  .btns {
    :deep(.el-form-item__content) {
      justify-content: flex-end;
    }
  }
}
</style>
