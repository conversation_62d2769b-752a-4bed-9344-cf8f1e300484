<template>
  <!-- 增改用户弹框 -->
  <hmx-dialog
    dialogWidth="60%"
    :dialogTitle="$t('sparePartsWearingParts.materialDetails')"
    customClass="hymson-dialog detail-dialog"
    :isVisable="isShow"
    @closeDialog="closeFun"
    top="50px"
  >
    <el-form :model="form" :inline="true" class="pro-select-box">
      <el-form-item :label="`${$t('sparePartsWearingParts.name')}：`">
        <el-text>{{ currentItem.name }}</el-text>
      </el-form-item>
      <el-form-item :label="`${$t('jobDirection.fileName')}：`">
        <el-input
          v-model="form.fileName"
          class="search-form-input"
          clearable
          :placeholder="$t('jobDirection.fileName')"
        />
      </el-form-item>
      <el-form-item
        :label="`${$t('jobDirection.fileCategory')}：`"
        prop="fileType"
      >
        <el-select
          v-model="form.fileType"
          class="form-input"
          :placeholder="$t('jobDirection.selectFilePlaceholder')"
          clearable
        >
          <el-option
            v-for="item in usableType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="checkList">{{
          $t("common.queryText")
        }}</el-button>
        <el-button type="primary" @click="handleReset">{{
          $t("common.resetText")
        }}</el-button>
      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <hmx-table
      :table-data="tableData"
      :options="tableOptions"
      :columns="tableColumn"
      @command="handleAction"
    >
      <template #fileType="{ row }">
        <span v-if="row.fileType == 1">
          {{ $t("jobDirection.toolMaterials") }}
        </span>
        <span v-if="row.fileType == 2">
          {{ $t("jobDirection.modelChangeMaterials") }}
        </span>
        <span v-if="row.fileType == 3">
          {{ $t("jobDirection.maintenanceMaterials") }}
        </span>
      </template>
      <template #fileName="{ row }">
        <div class="fileBox">
          <img
            class="t-img"
            :src="wordImg"
            v-if="isWdord(row.fileContentType)"
          />
          <img class="t-img" :src="pdfImg" v-if="isPdf(row.fileContentType)" />
          <img
            class="t-img"
            :src="excelImg"
            v-if="isExcel(row.fileContentType)"
          />
          <img
            class="t-img"
            :src="videoImg"
            v-if="isVideo(row.fileContentType)"
          />
          <img
            class="t-img"
            :src="row.fileUrl"
            alt=""
            v-if="isImage(row.fileContentType)"
          />
          <span>
            {{ row.fileName }}
          </span>
        </div>
      </template>
      <template #fileSize="{ row }">
        <span>
          {{ row.fileSize + "M" }}
        </span>
      </template>
      <template #quickWearPart="{ row }">
        <el-button type="primary" text @click="handleDetail(row)">{{
          $t("common.detail")
        }}</el-button>
      </template>
    </hmx-table>
  </hmx-dialog>
  <videoDialog
    :isVisable="isVideoDialogShow"
    :checkOpt="checkOpt"
    @close-dialog="closeVideo"
  />
  <previewFile
    :show="isPreviewFileShow"
    :curFile="curFile"
    @onClose="closePreviewFile"
  />
  <preview-img :options="previewImgOptions" @onCancel="closePreviewImgDialog" />
</template>

<script setup name="DetailDialog">
import { reactive, ref, toRefs, watch, computed, onMounted } from "vue"
import HmxDialog from "@/components/hmx-dialog.vue"
import { useI18n } from "@xfe/locale"
import videoDialog from "@/views/home/<USER>/components/videoDialog.vue"
import previewFile from "@/views/home/<USER>/components/preview-file.vue"
import pdfImg from "@/assets/jobDirection/pdf2.png"
import wordImg from "@/assets/jobDirection/word2.png"
import excelImg from "@/assets/jobDirection/excel2.png"
import videoImg from "@/assets/jobDirection/video.png"
import PreviewImg from "@/views/home/<USER>/components/preview-img.vue"
import { getFilesDetails } from "@/api/front/spareParts"
import { useMessage } from "@/hooks/web/useMessage"
import downloadFile from "@/utils/downloadFile"
import checkExcelHasImg from "@/utils/checkExcelHasImg"

const { createMessage } = useMessage()

const { t: $t } = useI18n()
const emit = defineEmits(["onClose"])

const props = defineProps({
  // 是否展示弹窗
  isShow: {
    type: Boolean,
    default: false,
  },
  // 当前选中备件/易损件
  currentItem: {
    type: Object,
    default: () => ({}),
  },
})

// 对应的备件名称
const sparePartName = computed(() => props.currentItem.serialNumber)

let tableColumn = [
  {
    type: "index",
    width: "50",
    label: "No.",
    align: "center",
    fixed: true,
  },
  {
    prop: "fileName",
    label: $t("jobDirection.fileName"),
    align: "left",
    slot: "fileName",
    showOverflowTooltip: true,
  },
  {
    prop: "fileType",
    label: $t("sparePartsWearingParts.materialType"),
    align: "center",
    slot: "fileType",
  },
  {
    width: "170",
    label: $t("common.operate"),
    align: "center",
    buttons: [
      {
        command: "preView",
        type: "text",
        name: $t("common.preview"),
      },
      {
        name: $t("common.download"),
        type: "text",
        command: "downLoad",
      },
    ],
  },
]

const data = reactive({
  form: {
    fileName: "",
    fileType: "",
  },
  previewImgOptions: {
    show: false,
    imgUrl: null,
  },
})

const tableData = ref([])

const tableOptions = computed(() => {
  return {
    showPagination: false,
    border: false,
  }
})

// 点击查询
const checkList = async () => {
  tableData.value = await getFilesDetails({
    fileName: data.form.fileName,
    fileType: data.form.fileType,
    name: sparePartName.value,
  })
}

// 重置
const handleReset = () => {
  data.form.fileName = ""
  data.form.fileType = ""
  checkList()
}

// 操作事件
const handleAction = (command, row) => {
  switch (command) {
    case "preView":
      filePreview(row)
      break
    case "downLoad":
      handlerDownLoad(row)
      break
    default:
      break
  }
}

let curFile = ref({ fileType: "", filePath: "" })

// 是否开启预览
let isPreviewFileShow = ref(false)

function filePreview(file) {
  if (file.fileContentType == "mp4") {
    handleVidioPlay(file)
  } else if (
    file.fileContentType == "png" ||
    file.fileContentType == "jpg" ||
    file.fileContentType == "webp"
  ) {
    data.previewImgOptions.imgUrl = file.fileUrl
    data.previewImgOptions.show = true
  } else if (
    file.fileContentType == "xls" ||
    file.fileContentType == "xlsx"
  ){
    //处理excel 内容含图片的提示  不含的给预览
        checkExcelHasImg(file?.fileUrl).then(check => {
          if (check) {
            // 提示用户文件含有图片，请下载查看
            createMessage($t("jobDirection.hasImgPlsDown"), "error")
          } else {
            isPreviewFileShow.value = true
            curFile.value.fileType = file?.fileContentType
            curFile.value.filePath = file?.fileUrl
          }
        }).catch((Error) => {
          createMessage(Error, "error")
        })
  }else {
    isPreviewFileShow.value = true
    curFile.value.fileType = file?.fileContentType
    curFile.value.filePath = file?.fileUrl
  }
}

// 是否开启视频弹框
let isVideoDialogShow = ref(false)

let checkOpt = ref({
  title: "",
  url: "",
})

// 点击播放
const handleVidioPlay = item => {
  checkOpt.value.title = item?.fileName
  checkOpt.value.url = item?.fileUrl
  isVideoDialogShow.value = true
}

// 关闭播放
const closeVideo = () => {
  isVideoDialogShow.value = false
  checkOpt.value.title = ""
  checkOpt.value.url = ""
}

// 关闭预览
const closePreviewFile = () => {
  closeVideo()
  isPreviewFileShow.value = false
  curFile.value.filePath = ""
  curFile.value.fileType = ""
}

// 关闭图片预览
const closePreviewImgDialog = () => {
  data.previewImgOptions.imgUrl = ""
  data.previewImgOptions.show = false
}

function isVideo(file) {
  return ["mp4", "wav", "flv"].includes(file)
}
function isImage(file) {
  return ["png", "jpg", "webp"].includes(file)
}
function isWdord(file) {
  return ["docx", "txt", "doc"].includes(file)
}
function isExcel(file) {
  return ["xlsx", "xls"].includes(file)
}
function isPdf(file) {
  return ["pdf"].includes(file)
}

// 关闭弹框
const closeFun = () => {
  data.form = {
    fileName: "",
    fileType: "",
  }
  emit("onClose")
}

// 下载
// const downloadFile = async (url, filename) => {
//   try {
//     const response = await fetch(url)
//     if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)

//     const blob = await response.blob()
//     // 修复文件类型识别问题
//     const fileType = url.split(".").pop().toLowerCase()
//     const mimeType = fileType === "jpg" ? "image/jpeg" : blob.type

//     const correctedBlob = new Blob([blob], { type: mimeType })
//     const objectURL = URL.createObjectURL(correctedBlob)

//     const link = document.createElement("a")
//     link.href = objectURL
//     // 强制指定文件扩展名
//     link.download = filename.includes(".")
//       ? filename
//       : `${filename}.${fileType}`
//     document.body.appendChild(link)
//     link.click()

//     // 清理资源
//     URL.revokeObjectURL(objectURL)
//     document.body.removeChild(link)
//   } catch (error) {
//     console.error($t("sparePartsWearingParts.downloadFailure"), error)
//     createMessage($t("sparePartsWearingParts.downloadFailure"), "error")
//   }
// }

const handlerDownLoad = row => {
  const { fileName, fileUrl } = row
  downloadFile(fileUrl, fileName, {
    errormsg : $t("sparePartsWearingParts.downloadFailure")
  }).catch(() => {
    console.error($t("sparePartsWearingParts.downloadFailure"))
    createMessage($t("sparePartsWearingParts.downloadFailure"), "error")
  })
}

// 可用类型
const usableType = [
  { value: 1, label: $t("jobDirection.toolMaterials") },
  { value: 2, label: $t("jobDirection.modelChangeMaterials") },
  { value: 3, label: $t("jobDirection.maintenanceMaterials") },
]

const { form, previewImgOptions } = toRefs(data)

watch(
  () => props.isShow,
  async val => {
    if (val) {
      tableData.value = await getFilesDetails({
        serialNumber: sparePartName.value,
      })
    }
  }
)
</script>

<style lang="scss">
.detail-dialog.el-dialog {
  .add-user-ruleForm {
    padding: 0 20px 0 0;

    .el-form-item__label {
      flex: none;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
    }

    .el-form-item__content {
      .el-select,
      .el-input,
      .el-input-number {
        flex: none;
        width: 100%;
      }
    }
  }
  .hymson-table {
    height: 400px;
  }
}
</style>

<style scoped lang="scss">
.form {
  width: 100%;
  height: 100%;
  .form-left {
    width: 100%;

    .reference-life {
      display: flex;

      .reference-life-left {
        width: 75%;
      }

      .reference-life-right {
        width: 25%;
      }
    }
  }

  .form-right {
    width: 48%;
    .three-d {
      display: flex;
      height: 100px;
      margin-bottom: 10px;
      .three-d-left {
        width: 65%;
      }
      .three-d-right {
        background-color: #bfa;
        width: 30%;
        display: flex;
        justify-content: center;
        align-items: center;
        .t-img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

::v-deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.three-d {
  display: flex;
  height: 100px;
  margin-bottom: 10px;
  .three-d-left {
    width: 65%;
  }
  .three-d-right {
    background-color: #bfa;
    width: 30%;
    display: flex;
    justify-content: center;
    align-items: center;
    .t-img {
      width: 100%;
      height: 100%;
    }
  }
}

.fileBox {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  img {
    width: 30px;
    height: 30px;
    margin-right: 10px;
  }
}

::v-deep(.el-input.is-disabled .el-input__inner) {
  -webkit-text-fill-color: #fff !important;
}
::v-deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: rgb(29, 31, 34) !important;
}
::v-deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: rgb(29, 31, 34) !important;
}
</style>
