<template>
  <el-dropdown trigger="click">
    <el-button class="btn" type="primary" icon="Setting" size="default">
      {{ $t("card.title.columnSetting") }}
    </el-button>
    <template #dropdown>
      <el-dropdown-menu class="column-dropdown-menu">
        <el-dropdown-item>
          <span class="title">{{ $t("card.title.columnSetting") }}</span>
          <Draggable
            class="t_table_column_setting_dropdown"
            v-model="state.columnSet"
            item-key="prop"
          >
            <template #item="{ element, index }">
              <el-checkbox
                :checked="!element.hidden"
                @click.native.stop
                :disabled="element.checkBoxDisabled"
                @change="checked => checkChanged(checked, index)"
              >
                {{ element.label }}
              </el-checkbox>
            </template>
          </Draggable>
          <!-- <el-checkbox
            v-for="(element, index) in state.columnSet"
            :key="element.label"
            :checked="!element.hidden"
            @click.native.stop
            :disabled="element.checkBoxDisabled"
            @change="checked => checkChanged(checked, index)"
          >
            {{ element.label }}
          </el-checkbox> -->
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup name="ColumnSet">
import { watch, onMounted, reactive, toRaw } from "vue"
import Draggable from "vuedraggable" // 拖拽排序
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()

const props = defineProps({
  columns: {
    type: Array,
    default: () => [],
  },
  title: {
    type: String,
    default: "",
  },
  name: {
    type: String,
    default: "",
  },
})
// 初始化
const initColumnSet = () => {
  const columnSet = toRaw(props.columns).map((col, index) => ({
    ...col,
    hidden: false,
    checkBoxDisabled: false,
  }))
  return columnSet
}
// 抛出事件
const emits = defineEmits(["columnSetting"])
const state = reactive({
  columnSet: [],
})
onMounted(() => {
  state.columnSet = initColumnSet()
  console.log(state.columnSet, "state.columnSet")
})
let num = 0
watch(
  () => props.columns,
  v => {
    if (!num) {
      state.columnSet = v.map((col, index) => ({
        ...col,
        hidden: false,
        checkBoxDisabled: false,
      }))
    }
    num += 1
  },
  { deep: true }
)
watch(
  () => state.columnSet,
  (val, old) => {
    let arr = val.filter(item => !item.hidden)
    emits("columnSetting", arr)
  },
  { deep: true }
)
// checkbox改变选中状态
const checkChanged = (checked, index) => {
  state.columnSet[index].hidden = !checked
  let obj = {}
  state.columnSet.map(val => {
    val.hidden in obj || (obj[val.hidden] = [])
    obj[val.hidden].push(val.hidden)
  })
  if (obj.false.length < 3) {
    state.columnSet.map((val, key) => {
      if (!val.hidden) {
        state.columnSet[key].checkBoxDisabled = true
      }
    })
  } else {
    state.columnSet.map((val, key) => {
      if (!val.hidden) {
        state.columnSet[key].checkBoxDisabled = false
      }
    })
  }
}
</script>
<style lang="scss">
.column-dropdown-menu.el-dropdown-menu {
  padding: 10px;
  font-size: 14px;
  .el-dropdown-menu__item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    &:hover {
      background: transparent !important;
    }
    .el-checkbox {
      padding: 5px 0;
    }
    .el-checkbox__label {
      font-size: 14px;
    }
    .el-checkbox,
    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: white;
      font-size: 14px;
      &:hover {
        color: #59aff9;
      }
      .el-checkbox__inner {
        background-color: rgba(35, 47, 73);
      }
    }
    .title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    .t_table_column_setting_dropdown {
      display: flex;
      flex-direction: column;
      // max-height: 300px;
      overflow-y: auto;
    }
  }
}
.el-dropdown-menu,
.el-dropdown-menu__item:not(.is-disabled):focus {
  background-color: var(--params-background) !important;
  // color: #59aff9;
}
</style>

<style lang="scss" scoped>
.btn {
  background-color: var(--btn-background);
  margin: 0 8px;
}
</style>
