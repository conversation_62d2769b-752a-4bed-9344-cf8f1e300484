<template>
  <el-breadcrumb class="app-breadcrumb" separator="">
    <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
      <span
        v-if="
          item.redirect === 'noRedirect' || index === breadcrumbs.length - 1
        "
        class="no-redirect"
      >
        {{ $t(item.meta.title) }}
      </span>
      <a v-else @click.prevent="handleLink(item)" class="no-path">
        {{ $t(item.meta.title) }}
      </a>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script>
import { ref, watch, onMounted, defineComponent } from "vue"
import { useRoute, useRouter } from "vue-router"
import { compile } from "path-to-regexp"
import { useI18n } from "@xfe/locale"

export default defineComponent({
  name: "Breadcrumb",
  setup() {
    const { t: $t } = useI18n()
    const route = useRoute()
    const router = useRouter()

    const breadcrumbs = ref([])

    const getBreadcrumb = async () => {
      breadcrumbs.value = route.matched.filter(item => {
        return item.meta && item.meta.title && item.meta.breadcrumb !== false
      })
    }

    const pathCompile = path => {
      const { params } = route
      const toPath = compile(path)
      return toPath(params)
    }

    const handleLink = item => {
      // const { redirect, path } = item
      // if (redirect) {
      //   router.push(redirect)
      //   return
      // }
      // router.push(pathCompile(path))
    }

    const handleToPage = async item => {
      router.push({ name: item })
    }

    onMounted(() => {
      getBreadcrumb()
    })

    watch(
      () => route.path,
      v => {
        if (v.startsWith("/redirect/")) {
          return
        }
        getBreadcrumb()
      },
      { deep: true }
    )

    return {
      breadcrumbs,
      handleLink,
      handleToPage,
    }
  },
})
</script>

<style lang="scss" scoped>
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  color: #fff !important;
  font-weight: 400 !important;
}

.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: var(--v3-navigationbar-height);
  margin-left: 8px;
  .no-redirect {
    color: #fff;
    cursor: text;
    a {
      color: #303133;
    }
    .icon {
      font-weight: bold;
      margin: 0 10px;
      color: var(--el-text-color-placeholder);
    }
  }
  :deep(.el-breadcrumb__separator) {
    width: 20px;
    height: 20px;
    background: url("@/assets/images/common/next-level.svg");
    background-size: 100% 100%;
  }
  .no-path {
    cursor: default;
  }
}
</style>
