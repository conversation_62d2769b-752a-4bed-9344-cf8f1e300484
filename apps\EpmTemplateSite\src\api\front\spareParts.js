import { request } from "@xfe/request"
import { config } from "@/config"
const base = config.base_url.homeServerPrefix

/**
 * 获取关联资料详情
 * @param {*} data
 * @returns
 */
export function getFilesDetails(data) {
  return request.post(
    {
      url: `${base}/SparePartsAssembly/Files`,
      data,
    },
    { joinParamsToUrl: true }
  )
}

/**
 * 获取更换记录
 * @param {*} data
 * @returns
 */
export function getReplacement(data) {
  return request.post(
    {
      url: `${base}/SparePartsAssembly/Replacement`,
      data,
    },
    { joinParamsToUrl: true }
  )
}

/**
 * 获取易损件排行榜
 * @param {*} data
 * @returns
 */
export function getSparePartsRanking() {
  return request.get({
    url: `${base}/SparePartsAssembly/Ranking`,
  })
}
