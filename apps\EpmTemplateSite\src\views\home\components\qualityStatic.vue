<template>
  <section class="q-c">
    <div class="b-chart" ref="outputStatisticRef"></div>
  </section>
</template>

<script setup name="BarCharts">
import {
  ref,
  defineEmits,
  defineProps,
  defineExpose,
  watch,
  nextTick,
} from "vue"
import useEcharts from "@/hooks/useEcharts"
import { qualityStaticEcharts } from "./echartsConfig"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()
const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  isShow: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  "chart-click", // 点击chart
])

let outputStatisticRef = ref(null)
const { resize } = useEcharts(
  outputStatisticRef,
  emits,
  props,
  qualityStaticEcharts
)
defineExpose({
  resize,
})
watch(
  () => props.isShow,
  v => {
    v &&
      nextTick(() => {
        resize()
      })
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.q-c {
  width: 100%;
  height: 100%;
}
.total-static {
  color: #fff;
  display: flex;
  height: 20px;
  margin: 5px;
  font-size: 13px;
  .equie-name {
    width: 4em;
  }
  .equie-tip {
    width: 4em;
  }
  .progress {
    position: relative;
    flex: 1;
    background: linear-gradient(270deg, #54dd13 37%, #fef900 100%);
    .mark {
      position: absolute;
      height: 100%;
      left: 0;
      top: 0;
      background-color: rgba(254, 0, 5, 1);
    }
    .num {
      float: right;
      color: #000;
      margin-right: 5px;
    }
  }
}
.b-chart {
  width: 100%;
  height: calc(100%);
}
</style>
