{
  "compilerOptions": {
    "target": "es5",
    "module": "esnext",
    "baseUrl": "./",
    "moduleResolution": "node",
    "paths": {
      "@/*": ["src/*"],
      "/#/*": ["types/*"]
    },
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"],
    "outDir": "./dist",
    "jsx": "preserve",
    "allowJs": true,
    "allowSyntheticDefaultImports": true
  },
  // 指定编译需要编译的目录
  "include": [
    "components.d.ts",
    "auto-imports.d.ts",
    "env.d.ts",
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "src/main.ts",
    "types/**/*.d.ts",
    "types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "src/hooks/web/useEcharts.ts",
    "src/utils/lib/echarts.ts"
  ]
}
