<template>
  <!-- 编辑知识库单条记录弹窗 -->
  <hmx-dialog
    dialogWidth="50%"
    :dialogTitle="$t('knowledgeBase.editRecord')"
    customClass="hymson-dialog edit-dialog"
    :isVisable="isShow"
    :isFooter="true"
    @closeDialog="closeFun"
    @save="saveDialog(formRef)"
    top="50px"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      status-icon
    >
      <el-form-item :label="currentLabel" prop="content">
        <el-input
          v-model="form.content"
          :placeholder="$t('common.inputText')"
          type="textarea"
          resize="none"
          :rows="1"
        />
      </el-form-item>
    </el-form>
  </hmx-dialog>
</template>

<script setup name="RecordDialog">
import { reactive, ref, computed, watch, toRefs, defineEmits } from "vue"
import HmxDialog from "@/components/hmx-dialog.vue"
import { useI18n } from "@xfe/locale"
import { updateKnowledgeBaseById } from "@/api/front/knowleage"
import { useMessage } from "@/hooks/web/useMessage"

const { createMessage } = useMessage()

const { t: $t } = useI18n()
const emit = defineEmits(["onSure", "onClose"])

const props = defineProps({
  // 当前编辑的对应记录信息
  curRecord: {
    type: Object,
    default: () => {},
  },
  // 是否展示弹窗
  isShow: {
    type: Boolean,
    default: false,
  },
})

const data = reactive({
  form: {
    content: "", // 知识库记录内容
  },
})

const formRef = ref()

const rules = {
  content: [
    {
      required: true,
      message: $t("common.inputText"),
      trigger: "blur",
    },
  ],
}

const recordList = ref([
  {
    label: $t("knowledgeBase.problemPhenomenon"),
    type: "phenomenon",
  },
  {
    label: $t("knowledgeBase.relatedFaultCause"),
    type: "reason",
  },
  {
    label: $t("knowledgeBase.handlingMeasures"),
    type: "solution",
  },
])

const currentLabel = computed(
  () =>
    recordList.value.find(item => item.type === props.curRecord.type)?.label ||
    ""
)

const saveDialog = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      try {
        const id = props?.curRecord?.id || ""
        console.log(id, "id")
        console.log(data.form, "data.form")
        await updateKnowledgeBaseById(id, data.form)
        createMessage(
          `${$t("common.edit")}${currentLabel.value}${$t("common.success")}`,
          "success"
        )
        emit("onSure")
        closeFun()
      } catch (error) {
        console.log(error, "error")
        createMessage(`${$t("common.edit")}${currentLabel.value}${$t("common.failure")}`, "warning")
      }
    }
  })
}

// 关闭弹框
const closeFun = () => {
  data.form = {
    content: "",
  }
  emit("onClose")
}

watch(
  () => props.isShow,
  val => {
    if (val) {
      data.form.content = props?.curRecord?.content || ""
    }
  }
)

const { form } = toRefs(data)
</script>

<style scoped lang="scss">
.form {
  width: 100%;
  height: 100%;
  .form-left {
    width: 100%;

    .reference-life {
      display: flex;

      .reference-life-left {
        width: 75%;
      }

      .reference-life-right {
        width: 25%;
      }
    }
  }

  .form-right {
    width: 48%;
    .three-d {
      display: flex;
      height: 100px;
      margin-bottom: 10px;
      .three-d-left {
        width: 65%;
      }
      .three-d-right {
        background-color: #bfa;
        width: 30%;
        display: flex;
        justify-content: center;
        align-items: center;
        .t-img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

::v-deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.three-d {
  display: flex;
  height: 100px;
  margin-bottom: 10px;
  .three-d-left {
    width: 65%;
  }
  .three-d-right {
    background-color: #bfa;
    width: 30%;
    display: flex;
    justify-content: center;
    align-items: center;
    .t-img {
      width: 100%;
      height: 100%;
    }
  }
}

::v-deep(.el-input.is-disabled .el-input__inner) {
  -webkit-text-fill-color: #fff !important;
}
::v-deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: rgb(29, 31, 34) !important;
}
::v-deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: rgb(29, 31, 34) !important;
}
</style>
