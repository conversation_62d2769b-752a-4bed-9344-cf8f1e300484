import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

// 获取作业指导列表
export function getOPSFiles(params) {
  return request.get({
    url: `${homeServerPrefix}/OPSFiles`,
    params,
  })
}

export function DownloadFileByIdApi(
  id,
  operationType = "Download",
  responseType = "blob"
) {
  return request.get({
    url: `${homeServerPrefix}/excel/Template/xlsx`,
    responseType,
    operationType,
    // params: {
    //   operationType,
    // }, // 是否是下载 下载 download / 预览 preview
  })
}

// 查询作业指导
export function getOPSFilesList(data) {
  return request.get({
    url: `${homeServerPrefix}/OPSFiles?id=${data}`,
  })
}

// 增加作业指导
export function postOPSFilesList(data) {
  return request.post({
    url: `${homeServerPrefix}/OPSFiles`,
    headers: { "Content-Type": "multipart/form-data" },
    data: data,
  })
}

// 修改作业指导
export function putOPSFilesList(data, id) {
  return request.put({
    url: `${homeServerPrefix}/OPSFiles/?id=${id}`,
    headers: { "Content-Type": "multipart/form-data" },
    data: data,
  })
}

// 设置易损件关联
export function setSpareParts(data, id) {
  return request.post({
    url: `${homeServerPrefix}/OPSFiles/SetSpareParts/?id=${id}`,
    data: data,
  })
}

// 获取作业指导视频通过id数组
export function getOPSFilesListByIds(params) {
  return request.get({
    url: `${homeServerPrefix}/OPSFiles/ids${params}`,
  })
}
