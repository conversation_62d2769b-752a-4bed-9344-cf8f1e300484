import { ref } from "vue"

export default function useConfigData() {
  // 产品质量
  let productQualityShow = ref(false)
  // 产量统计
  let outputStatisticShow = ref(false)
  // 实时OEE
  let realOeeConfigShow = ref(false)
  // 工序CT
  let ctConfigShow = ref(false)
  // 预警信息
  let warnConfigShow = ref(false)
  // 产线状态
  let productConfigShow = ref(false)

  //进入配置弹窗
  const handleEdit = i => {
    switch (i) {
      case 1:
        productQualityShow.value = true
        break
      case 2:
        outputStatisticShow.value = true
        break
      case 3:
        realOeeConfigShow.value = true
        break
      case 4:
        ctConfigShow.value = true
        break
      case 5:
        warnConfigShow.value = true
        break
      case 6:
        productConfigShow.value = true
        break
      default:
        break
    }
  }

  // 关闭配置化弹窗
  const handleCloseConfig = i => {
    switch (i) {
      case 1:
        productQualityShow.value = false
        break
      case 2:
        outputStatisticShow.value = false
        break
      case 3:
        realOeeConfigShow.value = false
        break
      case 4:
        ctConfigShow.value = false
        break
      case 5:
        warnConfigShow.value = false
        break
      case 6:
        productConfigShow.value = false
        break
    }
  }

  return {
    productQualityShow,
    outputStatisticShow,
    realOeeConfigShow,
    ctConfigShow,
    warnConfigShow,
    productConfigShow,
    handleEdit,
    handleCloseConfig,
  }
}
