<template>
  <div class="bar-line" ref="chartRef"></div>
</template>
<script setup>
import useCharts from "@/hooks/useCharts"
import { ref, watch } from "vue"

let props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  baseOption: {
    type: Object,
    default: () => {},
  },
})
let emits = defineEmits(["chart-click", "chart-legend"])

let chartRef = ref()
let { updateDraw } = useCharts(chartRef, props.baseOption, emits)

watch(
  () => props.option,
  () => {
    updateDraw(props.option)
  },
  { deep: true }
)
</script>
<style scoped lang="scss">
.bar-line {
  width: 100%;
  height: 100%;
}
</style>
