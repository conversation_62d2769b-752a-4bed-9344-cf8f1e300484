export default {
  homeTitle: {
    title: "WebScada Framework",
  },
  auth: {
    auth: "Auth.",
    user: "User",
    role: "Role",
    menu: "Menu",
  },
  setting: {
    config: "Config",
    baseCondig: "Base config",
    equipment: "Equipment",
    referenceLine: "Baseline",
    params: "Parameter",
    international: "International",
    plcConfig: "PLC",
    mesConfig: "MES",
    spcConfig: "SPC",
    laserCode: "Laser code",
    interactConfig: "Interaction table",
    moduleConfig: "2D module",
    threeModuleConfig: "3D module",
    visualConfig: "Visual config",
  },
  productionConfig: {
    productionManage: "Production manage",
    stopReason: "Stop reason",
    shiftAllocation: "Shift settings",
    vulnerablePart: "Vulnerable part",
  },
  debug: {
    debugTool: "Debug",
    plcDebbug: "PLC debugging",
    runtime: "run-time",
    controlConsole: "control",
    laserCode: "Laser code debug",
  },
  homeRoute: {
    login: "Login",
    error: "Error",
    home: "Home",
    dataAnalysis: "Data analysis",
    operateLog: "Operate log",
    alarmLog: "Alarm log",
    logTaskScheduling: "Log task scheduling",
    digitalStatement: "Digital Statement",
    knowledgeBase: "Knowledge Base",
    jobDirection: "Job Instruction",
    repairWorkOrder: "Maintenance WorkOrder",
    equipSpotCheck: "Equip SpotCheck",
    sparePartsWearingParts: "Spare Parts/Wearing Parts",
    monitor: "Monitoring",
  },
  lowcode: {
    minePage: "My page",
    allPages: "All pages",
    template: "My template",
    components: "Component library",
    dataSource: "Data source",
  },
}
