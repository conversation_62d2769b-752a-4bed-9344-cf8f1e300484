<template>
  <el-dialog
    @open="openFun"
    @close="closeFun"
    :title="dialogTitle"
    :close-on-click-modal="false"
    :model-value="isVisable"
    :destroy-on-close="true"
    :append-to-body="appendToBody"
    :show-close="isClose"
    :width="dialogWidth"
    :class="customClass"
    :style="dialogStyle"
    :top="top"
    :close-on-press-escape="false"
  >
    <slot></slot>
    <template #footer v-if="isFooter">
      <span class="dialog-footer">
        <el-button
          @click="closeFun"
          :size="clientWidth() ? 'small' : ''"
          v-if="isCancel"
          >{{ $t("common.cancelText") }}</el-button
        >
        <el-button
          type="primary"
          :disabled="disabled"
          :loading="loading"
          :size="clientWidth() ? 'small' : ''"
          @click="save"
          >{{ $t("common.okText") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup name="HmxAdminDialog">
import { clientWidth } from "@xfe/utils"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()
const emits = defineEmits(["save", "closeDialog", "openDialog"])

defineProps({
  dialogTitle: {
    //  dialog的标题
    type: String,
    default: "",
  },
  isVisable: {
    // 弹窗是否显示
    type: Boolean,
    default: false,
  },
  isFooter: {
    //  是否显示弹窗底部
    type: Boolean,
    default: false,
  },
  isCancel: {
    // 是否显示取消
    type: Boolean,
    default: true,
  },
  cancelText: {
    // 取消的默认文本
    type: String,
    default: "取 消",
  },
  saveText: {
    // 确定的默认文本
    type: String,
    default: "确 定",
  },
  isClose: {
    // 是否显示关闭按钮  默认true
    type: Boolean,
    default: true,
  },
  dialogWidth: {
    // 弹窗的宽度
    type: [String, Number],
  },
  customClass: {
    // 弹框的自定义类名
    type: String,
    default: "hymson-dialog",
  },
  appendToBody: {
    // 是否挂载在body上  默认true
    type: Boolean,
    default: true,
  },
  dialogStyle: {
    // 弹窗的高度
    type: [Object],
    default: () => {},
  },
  disabled: {
    // 是否禁用按钮
    type: Boolean,
    default: false,
  },
  loading: {
    // 按钮是否等待
    type: Boolean,
    default: false,
  },
  top: {
    // 按钮是否等待
    type: String,
    default: "20vh",
  },
})

const save = () => {
  emits("save") // 保存
}

const closeFun = () => {
  emits("closeDialog") // 关闭弹窗
}

const openFun = () => {
  emits("openDialog") // 开启弹窗
}
</script>
<style scoped lang="scss">
.dialog-footer button:first-child {
  margin-right: 10px;
}
</style>
<style lang="scss">
.hymson-dialog.el-dialog {
  background-size: 100% 100%;
  background-position: center center;
  min-height: 200px;
  // border: 1px solid #0084fe;
  background-color: var(--dialog-body-background);
  .el-dialog__header {
    padding: 16px;
    margin-right: 0;
    .el-dialog__headerbtn {
      top: -4px;
    }
  }
  .el-dialog__title,
  .el-dialog__body {
    color: var(--g-font-color);
    .el-checkbox {
      &__label {
        color: var(--g-font-color);
      }
      &__inner {
        border: 1px solid var(--g-checkbox-border-color);
      }
    }
    .el-tree {
      color: var(--g-font-color);
      max-height: 150px;
      overflow: auto;
      --el-tree-node-hover-bg-color: var(--g-tree-node-hover-bg-color);
      background-color: transparent !important;
    }
    .el-input-number {
      &__decrease,
      &__increase {
        border: none;
      }
    }
  }
  .el-dialog__title {
    font-weight: 600;
    color: var(--g-font-color);
  }
  .el-dialog__body {
    overflow: auto;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: var(--dialog-close-color);
    font-size: 36px;
  }
  .el-dialog__headerbtn:focus .el-dialog__close,
  .el-dialog__headerbtn:hover .el-dialog__close {
    color: var(--dialog-close-color);
  }
  .dialog-form-input {
    width: 100%;
  }
}
</style>
