const { defineConfig } = require("@vue/cli-service")
const path = require("path")
// elementplus 按需自动导入
const AutoImport = require("unplugin-auto-import/webpack")
const Components = require("unplugin-vue-components/webpack")
const { ElementPlusResolver } = require("unplugin-vue-components/resolvers")
const HtmlWebpackTagsPlugin = require("html-webpack-tags-plugin")
const { BundleAnalyzerPlugin } = require("webpack-bundle-analyzer")
const { version } = require("./package.json")

const ISPROD = process.env.NODE_ENV === "production"

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = defineConfig({
  publicPath: "./",
  runtimeCompiler: true,
  transpileDependencies: true,
  productionSourceMap: true,
  lintOnSave: false,
  // 生产环境是否生成 sourceMap 文件
  productionSourceMap: false,
  // css相关配置
  css: {
    // 是否使用css分离插件 ExtractTextPlugin
    // extract: true,
    // 开启 CSS source maps?
    sourceMap: false,
  },
  //开发环境服务配置
  devServer: {
    hot: true,
    port: 40148,
    client: {
      overlay: false,
    },
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    proxy: {
      "/api": {
        target: "http://10.10.78.74:1900", //对应自己的接口
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          "^/api": "",
        },
      },
      "/NoticeHub": {
        target: "ws://10.10.47.161:5228/",
        changeOrigin: true,
        ws: true,
      },
      "/SfWorkTemplate": {
        target: "http://10.10.78.74:19600/api/SfWorkTemplate/", //对应自己的接口
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          "^/SfWorkTemplate": "",
        },
      },
    },
  },
  configureWebpack: {
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
        "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
        "@lowcode": path.resolve(
          __dirname,
          "..",
          "..",
          "packages",
          "lowcode",
          "src"
        ),
      },
      extensions: [".ts", ".tsx", ".js", ".json", ".vue"],
    },
    plugins: [
      // ...
      AutoImport({
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
      ...(process.env.npm_lifecycle_event === "analysis"
        ? [new BundleAnalyzerPlugin()]
        : []),
      ,
    ],
    externals: {
      vue: "Vue",
      "element-plus": "ElementPlus",
      "@element-plus/icons-vue": "ElementPlusIconsVue",
      echarts: "echarts",
    },
    // output: {
    //   // 输出重构  打包编译后的 文件名称  【模块名称.版本号.时间戳】
    //   filename: "static/js/[name].[hash:8].js",
    //   chunkFilename: "static/js/[name].[hash:8].js",
    // },
  },
  chainWebpack: config => {
    config.plugin("define").tap(args => {
      args[0]["process"] = { ...args[0]["process.env"] }
      return args
    })
    config.resolve.extensions.add("ts").add("tsx")
    // set svg-sprite-loader
    config.module.rule("svg").exclude.add(resolve("src/assets/icons")).end()
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end()
    // 生产环境
    if (ISPROD) {
      config.optimization.splitChunks({
        cacheGroups: {
          common: {
            //commons 一般是是个人定义的
            name: "chunk-common", // 打包后的文件名
            chunks: "initial", // all、async、initial
            // 它控制的是每个模块什么时候被抽离出去：当模块被不同entry引用的次数大于等于这个配置值时，才会被抽离出去.默认为1
            minChunks: 2,
            maxInitialRequests: 5,
            minSize: 0,
            priority: 1,
            reuseExistingChunk: true,
          },
          /**
           *  vendors 是捆绑所有不是自己的模块，而是来自其他方的模块的捆绑包，它们称为第三方模块或供应商模块。
           * 通常是来自/node-modudles模块，将他们中的第三方包打包进chunk-vendors.js
           */
          vendors: {
            //vendor 是导入的 npm 包
            name: "chunk-vendors",
            test: /[\\/]node_modules[\\/]/,
            chunks: "initial",
            maxSize: 600000,
            maxInitialRequests: 20,
            priority: 2,
            reuseExistingChunk: true,
            enforce: true,
          },
          elementPlus: {
            // /[\\/]node_modules[\\/](vue|vuex|echarts|lodash|.?element-plus[\\/][^(dist)])/
            //把elementPlus从chunk-vendors.js提取出来。当然我们也可以把mixins，vue.min.js等等也按照类似配置提取出来
            name: "chunk-element-plus",
            test: /[\\/]node_modules[\\/]element-plus[\\/][^(dist)]/,
            chunks: "initial",
            priority: 3,
            maxSize: 600000,
            reuseExistingChunk: true,
            enforce: true,
          },
        },
      })
      config
        .plugin("html")
        .tap(args => {
          args[0].template = "./public/index.html"
          return args
        })
        .end()
      config
        .plugin("HtmlWebpackTagsPlugin")
        .use(HtmlWebpackTagsPlugin, [
          {
            links: ["/favicon.ico"],
            tags: [
              {
                path: "/_app.config.js",
                attributes: {
                  // 添加的属性
                  type: "text/javascript",
                  src: `/_app.config.js?v=${version}-${new Date().getTime()}`, // 在这里指定完整的 URL 和版本号
                },
                append: false, // 设置为 false，避免重复添加
              },
            ],
            append: false,
          },
        ])
        .after("html")
    }
  },
})
