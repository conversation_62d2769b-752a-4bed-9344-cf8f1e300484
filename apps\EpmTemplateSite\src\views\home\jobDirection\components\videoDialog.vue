<template>
  <dialog-card
    dialogWidth="50%"
    :dialogTitle="checkOpt.title"
    :isVisable="isVisable"
    :appendToBody="false"
    @closeDialog="closeFun"
  >
    <div class="video-box">
      <video controls autoplay class="video">
        <source :src="checkOpt?.url" type="video/mp4" />
        {{ $t("jobDirection.browserNotSupportVideo") }}
      </video>
    </div>
  </dialog-card>
</template>

<script setup>
import dialogCard from "@/components/hmx-dialog.vue"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()

let emit = defineEmits(["close-dialog"])
let props = defineProps({
  isVisable: {
    type: Boolean,
    default: false,
  },
  checkOpt: {
    type: Object,
    default: () => {},
  },
})

function closeFun() {
  emit("close-dialog", false)
}
</script>
<style lang="scss" scoped>
.video-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .video {
    width: 100%;
    height: 500px;
    object-fit: fill;
  }
}
</style>
