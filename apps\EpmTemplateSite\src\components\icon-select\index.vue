<template>
  <div class="icon-body">
    <el-input
      v-model="name"
      style="position: relative"
      clearable
      placeholder="请输入图标名称"
      @clear="filterIcons"
      @input.native="filterIcons"
    >
      <i slot="suffix" class="el-icon-search el-input__icon" />
    </el-input>
    <div class="icon-list">
      <div
        class="icon-item"
        v-for="(item, index) in iconList"
        :key="index"
        @click="selectedIcon(item)"
      >
        <svg-icon :name="item" :size="16" />
        <span>{{ item }}</span>
      </div>
    </div>
  </div>
</template>

<script setup name="IconSelect">
import { reactive, toRefs } from "vue";
import icons from "./requireIcons";

const emit = defineEmits(["selected"]);

const data = reactive({
  name: "",
  iconList: icons,
});
const filterIcons = () => {
  data.iconList = icons;
  if (data.name) {
    data.iconList = data.iconList.filter(item => item.includes(data.name));
  }
};
const selectedIcon = name => {
  emit("selected", name);
};

// eslint-disable-next-line no-unused-vars
const reset = () => {
  data.name = "";
  data.iconList = icons;
};

const { name, iconList } = toRefs(data);
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.icon-body {
  width: 100%;
  padding: 10px;
  .icon-list {
    margin-top: 10px;
    max-height: 200px;
    overflow-y: scroll;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
    .icon-item {
      height: 30px;
      line-height: 30px;
      padding: 5px 0;
      cursor: pointer;
      width: 33%;
      display: flex;
      align-items: center;
      span {
        overflow: hidden;
        padding-left: 4px;
        font-size: 16px;
      }
    }
  }
}
</style>
