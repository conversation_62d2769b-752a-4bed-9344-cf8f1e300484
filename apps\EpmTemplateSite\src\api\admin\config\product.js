import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 * 生产管理
 */
// 获取设备信息
export function GetMachineConfig() {
  return request.get({
    url: `${homeServerPrefix}/SystemSetting/GetMachineConfig`,
  })
}

// 修改设备信息
export function UpdateMachineConfig(data) {
  return request.post({
    url: `${homeServerPrefix}/SystemSetting/UpdateMachineConfig`,
    data,
  })
}
