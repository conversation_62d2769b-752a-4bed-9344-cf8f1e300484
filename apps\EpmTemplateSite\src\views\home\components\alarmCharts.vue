<template>
  <div class="p-chart" ref="pieRef"></div>
</template>

<script setup name="AlarmCharts">
import {
  ref,
  defineEmits,
  defineProps,
  defineExpose,
  watch,
  nextTick,
} from "vue"
import useEcharts from "@/hooks/useEcharts"
import { alarmStatistics } from "./echartsConfig"
const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  isShow: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  "chart-click", // 点击chart
])
let pieRef = ref()

const { resize } = useEcharts(pieRef, emits, props, alarmStatistics)
defineExpose({
  resize,
})

watch(
  () => props.isShow,
  v => {
    if (v) {
      nextTick(() => {
        resize()
      })
    }
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.p-chart {
  width: 100%;
  height: 90%;
}
</style>
