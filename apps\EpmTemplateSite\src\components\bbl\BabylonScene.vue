<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, defineExpose } from "vue"
import "babylonjs"
import "babylonjs-loaders"
import "babylonjs-materials"
import "babylonjs-gui"

import { SceneManager } from "../../utils/bbl/SceneManager"
import { ModelLoader } from "../../utils/bbl/ModelLoader"
import { AlarmManager } from "../../utils/bbl/AlarmManager"
import {
  ModelStatusManager,
  type ModelStatusData,
  type ModelParamData,
  type IconClickCallback,
} from "../../utils/bbl/ModelStatusManager"
import { LabelManager, type ParameterLabelData } from "../../utils/bbl/LableManager"
import type { AlarmStatus } from "../../utils/bbl/AlarmManager"
import type { FlyToModelResult } from "../../utils/bbl/api/modelStatusService"

// 场景容器引用
const canvasRef = ref<HTMLCanvasElement | null>(null)

// 功能管理器
let sceneManager: SceneManager | null = null
let modelLoader: ModelLoader | null = null
let alarmManager: AlarmManager | null = null
let modelStatusManager: ModelStatusManager | null = null
let labelManager: LabelManager | null = null

// 定义属性
const props = defineProps<{
  width?: string
  height?: string
}>()

// 定义事件
const emit = defineEmits<{
  "icon-click": [meshId: string, modelName: string, params: ModelParamData]
}>()

// 初始化BabylonJS场景
onMounted(() => {
  if (!canvasRef.value) return

  // 初始化场景管理器
  sceneManager = new SceneManager()
  sceneManager.initScene(canvasRef.value)

  // 设置环境贴图和天空盒
  sceneManager
    .setEnvironment(
      "./static/bbl_file/environment.env",
      "./static/bbl_file/environmentSpecular.env"
    )
    .catch(error => {
      console.error("设置环境贴图失败:", error)
    })

  // 初始化模型加载器
  modelLoader = new ModelLoader(sceneManager)

  // 初始化告警管理器
  alarmManager = new AlarmManager(sceneManager, modelLoader)

  // 初始化模型状态管理器
  modelStatusManager = new ModelStatusManager(
    sceneManager,
    modelLoader,
    alarmManager
  )

  // 初始化标签管理器
  labelManager = new LabelManager(sceneManager, modelLoader)

  // 设置图标点击事件回调
  if (modelStatusManager) {
    modelStatusManager.addIconClickCallback((meshId, modelName, params) => {
      console.log(`图标点击: ${meshId}, ${modelName}`, params)
      emit("icon-click", meshId, modelName, params)
    })
  }
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
  if (labelManager) {
    labelManager.dispose()
  }
  if (modelStatusManager) {
    modelStatusManager.dispose()
  }
  if (sceneManager) {
    sceneManager.dispose()
  }
})

// 导入模型
const importModel = async (
  fileUrl: string,
  fileData?: File | ArrayBuffer,
  clearPrevious = true
): Promise<boolean> => {
  if (!modelLoader) return false
  const result = await modelLoader.importModel(fileUrl, clearPrevious)

  // 如果模型加载成功，重新设置环境贴图（确保环境效果正确）
  if (result && sceneManager) {
    await sceneManager
      .setEnvironment(
        "./static/bbl_file/environmentSpecular.env",
        "./static/bbl_file/environment.env"
      )
      .catch(error => {
        console.error("重新设置环境贴图失败:", error)
      })

    // 自动初始化默认动画和效果
    setTimeout(() => {
      if (sceneManager) {
        sceneManager.initializeDefaultEffects()
      }
    }, 1000) // 延迟1秒确保模型完全加载
  }

  return result
}

// 设置模型告警状态
const setModelAlarm = (status: AlarmStatus): void => {
  if (!alarmManager) return
  alarmManager.setModelAlarm(status)
}

// 更新模型状态
const updateModelStatus = (statusDataList: ModelStatusData[]): void => {
  if (!modelStatusManager) return
  modelStatusManager.updateModelStatus(statusDataList)
}

// 添加图标点击事件回调
const addIconClickCallback = (callback: IconClickCallback): void => {
  if (!modelStatusManager) return
  modelStatusManager.addIconClickCallback(callback)
}

// 获取模型参数
const getModelParams = (id: string): ModelParamData => {
  if (!modelStatusManager) return {}
  return modelStatusManager.getModelParams(id)
}

// 更新模型参数
const updateModelParams = (
  id: string,
  params: Partial<ModelParamData>
): void => {
  if (!modelStatusManager) return
  modelStatusManager.updateModelParams(id, params)
}

// 自适应视角
const adjustCameraToModel = (animationDuration = 1800): void => {
  if (!modelLoader) return
  modelLoader.adjustCameraToModel(animationDuration)
}

// 获取模型的网格名称列表
const getMeshNames = (): string[] => {
  if (!modelLoader) return []
  return modelLoader.getMeshNames()
}

// 根据ID获取网格信息
const getMeshInfo = (id: string) => {
  if (!modelLoader) return null
  return modelLoader.getMeshInfo(id)
}

// 获取模型的层次结构树
const getMeshTree = () => {
  if (!modelLoader) {
    console.warn("模型加载器未初始化，无法获取结构树")
    return []
  }

  try {
    const tree = modelLoader.getMeshTree()
    // console.log('组件获取到的模型树:', tree);
    return tree
  } catch (error) {
    console.error("获取模型结构树时出错:", error)
    return []
  }
}

// 获取场景信息
const getSceneInfo = () => {
  if (!sceneManager || !modelLoader) return null

  const scene = sceneManager.getScene()
  if (!scene) return null

  return {
    totalMeshes: modelLoader.getImportedMeshes().length,
    totalVertices: modelLoader
      .getImportedMeshes()
      .reduce((sum, mesh) => sum + mesh.getTotalVertices(), 0),
    totalFaces: modelLoader
      .getImportedMeshes()
      .reduce((sum, mesh) => sum + mesh.getTotalIndices() / 3, 0),
    fps: sceneManager.getEngine()?.getFps() || 0,
    drawCalls: scene.getActiveMeshes().length,
    activeCamera: sceneManager.getCamera()?.name || "",
    activeLights: scene.lights.length,
  }
}

// 设置中心按钮点击回调
const setCenterButtonClickCallback = (callback: (meshId: string) => void) => {
  if (!alarmManager) return
  alarmManager.addCenterButtonClickListener(callback)
}

// 飞行定位到指定模型
const flyToModel = async (modelId: string): Promise<FlyToModelResult> => {
  if (!modelStatusManager) {
    return {
      success: false,
      message: "模型状态管理器未初始化",
      modelId,
    }
  }
  return modelStatusManager.flyToModel(modelId)
}

// 切换wkall节点的可见性
const toggleWkallVisibility = (visible: boolean): boolean => {
  if (!sceneManager) {
    console.error("场景管理器未初始化，无法操作模型可见性")
    return false
  }
  return sceneManager.toggleWkallVisibility(visible)
}

// 初始化默认动画和效果
const initializeDefaultEffects = (): void => {
  if (!sceneManager) {
    console.error("场景管理器未初始化，无法初始化默认效果")
    return
  }
  sceneManager.initializeDefaultEffects()
}

// 停止所有动画和效果
const stopAllEffects = (): void => {
  if (!sceneManager) {
    console.error("场景管理器未初始化，无法停止效果")
    return
  }
  sceneManager.stopAllEffects()
}

// 添加hongdeng灯光闪烁效果
const addHongdengLightFlashing = (): boolean => {
  if (!sceneManager) {
    console.error("场景管理器未初始化，无法添加hongdeng效果")
    return false
  }
  return sceneManager.addHongdengLightFlashing()
}

// 添加旋转动画
const addRotationAnimations = (modelIds: string[]): boolean => {
  if (!sceneManager) {
    console.error("场景管理器未初始化，无法添加旋转动画")
    return false
  }
  return sceneManager.addRotationAnimations(modelIds)
}

// 添加jiguang闪烁效果
const addJiguangFlashing = (): boolean => {
  if (!sceneManager) {
    console.error("场景管理器未初始化，无法添加jiguang效果")
    return false
  }
  return sceneManager.addJiguangFlashing()
}

// 更新参数标签
const updateParameterLabels = (parameterData: ParameterLabelData[]): void => {
  if (!labelManager) {
    console.error("标签管理器未初始化，无法更新参数标签")
    return
  }
  labelManager.updateParameterLabels(parameterData)
}

// 更新单个参数值
const updateParameterValue = (parameterCode: string, newValue: string | number, unit?: string): void => {
  if (!labelManager) {
    console.error("标签管理器未初始化，无法更新参数值")
    return
  }
  labelManager.updateParameterValue(parameterCode, newValue, unit)
}

// 设置标签可见性
const setLabelsVisible = (visible: boolean): void => {
  if (!labelManager) {
    console.error("标签管理器未初始化，无法设置标签可见性")
    return
  }
  labelManager.setLabelsVisible(visible)
}

// 清除所有标签
const clearAllLabels = (): void => {
  if (!labelManager) {
    console.error("标签管理器未初始化，无法清除标签")
    return
  }
  labelManager.clearAllLabels()
}

// 根据真实参数数据更新标签
const updateParameterLabelsFromRealTimeData = (realTimeParametersData: any[]): void => {
  if (!labelManager) {
    console.error("标签管理器未初始化，无法更新参数标签")
    return
  }
  labelManager.updateParameterLabelsFromRealTimeData(realTimeParametersData)
}

// 暴露给父组件的方法
defineExpose({
  importModel,
  setModelAlarm,
  updateModelStatus,
  adjustCameraToModel,
  getMeshNames,
  getMeshInfo,
  getMeshTree,
  getSceneInfo,
  setCenterButtonClickCallback,
  addIconClickCallback,
  getModelParams,
  updateModelParams,
  flyToModel,
  toggleWkallVisibility,
  initializeDefaultEffects,
  stopAllEffects,
  addHongdengLightFlashing,
  addRotationAnimations,
  addJiguangFlashing,
  // 标签管理器方法
  updateParameterLabels,
  updateParameterValue,
  setLabelsVisible,
  clearAllLabels,
  updateParameterLabelsFromRealTimeData,
})
</script>

<template>
  <canvas
    ref="canvasRef"
    :style="{ width: props.width || '100%', height: props.height || '100%' }"
  ></canvas>
</template>

<style scoped>
canvas {
  outline: none;
  touch-action: none;
}
</style>
