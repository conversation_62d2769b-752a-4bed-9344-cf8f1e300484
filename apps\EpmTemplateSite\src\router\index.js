import { createRouter, createWebHashHistory } from "vue-router"
// import { ElMessage } from "element-plus";
import Layout from "@/layouts/content.vue"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()

// 动态路由通过菜单code来匹配

export const rootRoute = [
  {
    path: "/",
    component: Layout,
    name: "Root",
    redirect: "/home",
    meta: { layout: "blank", title: "homeTitle.title" },
  },
  {
    path: "/login",
    name: "Login",
    component: () =>
      import(/* webpackChunkName: "Login" */ "@/views/login/index.vue"),
    meta: {
      layout: "blank",
      title: "homeRoute.login",
    },
  },
  // 解决动态加载的路由 控制台报警告 [Vue Router warn]: No match found for location with path
  // 若跳转的路由不存在，则报错误页面
  {
    path: "/404",
    hidden: true,
    name: "error",
    component: () =>
      import(/* webpackChunkName: "error" */ "@/views/Error.vue"),
    meta: {
      layout: "blank",
      title: "homeRoute.error",
    },
  },
]

// 菜单的路由时动态加载控制的，在页面 菜单管理 中配置

// 其他非菜单路由如需用到可写在下面
export const homeRoutes = [
  // 未登录也需要在首页，需提前定义
  {
    path: "/home",
    component: () =>
      import(/* webpackChunkName: "Home" */ "@/views/home/<USER>"),
    name: "Home",
    meta: {
      title: "homeTitle.title",
    },
    redirect: "/home/<USER>",
    children: [
      {
        path: "/home/<USER>",
        component: () =>
          import(
            /* webpackChunkName: "HomeIndex" */ "@/views/home/<USER>"
          ),
        name: "HomeIndex",
        meta: {
          title: "homeRoute.home",
        },
      },
      {
        path: "/home/<USER>",
        component: () =>
          import(
            /* webpackChunkName: "DigitalStatement" */ "@/views/home/<USER>/index.vue"
          ),
        name: "DigitalStatement",
        meta: {
          title: "homeRoute.digitalStatement",
        },
      },
      {
        path: "/home/<USER>",
        component: () =>
          import(
            /* webpackChunkName: "knowledgeBase" */ "@/views/home/<USER>/index.vue"
          ),
        name: "knowledgeBase",
        meta: {
          title: "homeRoute.knowledgeBase",
        },
      },
      {
        path: "/home/<USER>",
        component: () =>
          import(
            /* webpackChunkName: "jobDirection" */ "@/views/home/<USER>/index.vue"
          ),
        name: "jobDirection",
        meta: {
          title: "homeRoute.jobDirection",
        },
      },
      {
        path: "/home/<USER>",
        component: () =>
          import(
            /* webpackChunkName: "repairWorkOrder" */ "@/views/home/<USER>/index.vue"
          ),
        name: "repairWorkOrder",
        meta: {
          title: "homeRoute.repairWorkOrder",
        },
      },
      {
        path: "/home/<USER>",
        component: () =>
          import(
            /* webpackChunkName: "SpareParts-WearingParts" */ "@/views/home/<USER>/index.vue"
          ),
        name: "SpareParts-WearingParts",
        meta: {
          title: "homeRoute.SpareParts-WearingParts",
        },
      },
    ],
  },
]

export const customRoute = [...homeRoutes]

let router = createRouter({
  history: createWebHashHistory(),
  routes: [...rootRoute, ...homeRoutes],
  scrollBehavior: () => ({ left: 0, top: 0 }),
})

router.beforeEach((to, _, next) => {
  // 设置页面标题
  document.title = `设备智能化管理系统-(${to.meta.title})`
  next()
})

router.onError(error => {
  // window.location.reload()
  console.error("[路由错误]", error)
  throw new Error("路由出错")
})

export default router
