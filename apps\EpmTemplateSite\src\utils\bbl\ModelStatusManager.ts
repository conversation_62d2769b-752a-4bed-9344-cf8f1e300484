import * as BABYLON from 'babylonjs';
import * as G<PERSON> from 'babylonjs-gui';
import { SceneManager } from './SceneManager';
import { ModelLoader } from './ModelLoader';
import { AlarmManager } from './AlarmManager';
import { LanguageService } from './LanguageService';
import type { FlyToModelResult, RealTimeAlarmData } from './api/modelStatusService';

/**
 * 模型状态数据接口
 */
export interface ModelStatusData {
  ID: string;       // 模型ID
  modelName: string; // 模型名称
  isAlarming: boolean; // 是否处于报警状态
  showCoordinates?: boolean; // 是否显示坐标
  alarmInfo?: RealTimeAlarmData; // 报警详细信息
}

/**
 * 模型参数数据接口
 */
export interface ModelParamData {
  temperature?: number;    // 温度
  pressure?: number;       // 压力
  speed?: number;          // 速度
  voltage?: number;        // 电压
  current?: number;        // 电流
  power?: number;          // 功率
  runtime?: number;        // 运行时间
  status?: string;         // 状态描述
  lastMaintenance?: string; // 上次维护时间
  nextMaintenance?: string; // 下次维护时间
  alarmContent?: string;    // 报警内容
  alarmGroup?: string;      // 报警分组
  [key: string]: number | string | undefined; // 其他自定义参数
}

/**
 * 图标点击回调函数类型
 */
export type IconClickCallback = (meshId: string, modelName: string, params: ModelParamData) => void;

/**
 * 模型状态管理器
 * 负责处理模型的状态显示和交互
 */
export class ModelStatusManager {
  private sceneManager: SceneManager;
  private modelLoader: ModelLoader;
  private alarmManager: AlarmManager;
  private advancedTexture: GUI.AdvancedDynamicTexture | null = null;
  private statusIcons: Map<string, GUI.Control> = new Map();
  private iconClickCallbacks: IconClickCallback[] = [];
  private modelParamsMap: Map<string, ModelParamData> = new Map();
  private flyToAnimationDuration: number = 1500; // 飞行动画持续时间（毫秒）

  // 图标SVG路径
  private readonly alarmIconSvg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="#ff0000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
    <line x1="12" y1="9" x2="12" y2="13"></line>
    <line x1="12" y1="17" x2="12.01" y2="17"></line>
  </svg>`;

  private readonly locationIconSvg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="#00aaff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
    <circle cx="12" cy="10" r="3"></circle>
  </svg>`;

  /**
   * 构造函数
   * @param sceneManager 场景管理器实例
   * @param modelLoader 模型加载器实例
   * @param alarmManager 报警管理器实例
   */
  constructor(sceneManager: SceneManager, modelLoader: ModelLoader, alarmManager: AlarmManager) {
    this.sceneManager = sceneManager;
    this.modelLoader = modelLoader;
    this.alarmManager = alarmManager;
    this.initGUI();
    this.generateMockModelParams();
  }

  /**
   * 初始化GUI系统
   */
  private initGUI(): void {
    const scene = this.sceneManager.getScene();
    if (!scene) return;

    // 创建全屏GUI
    this.advancedTexture = GUI.AdvancedDynamicTexture.CreateFullscreenUI("statusUI", true, scene);
  }

  /**
   * 为单个模型ID生成模拟参数
   * @returns 生成的模型参数
   */
  private generateModelParams(): ModelParamData {
    const languageService = LanguageService.getInstance();
    const params: ModelParamData = {
      temperature: Math.round((Math.random() * 50 + 20) * 10) / 10, // 20-70°C
      pressure: Math.round((Math.random() * 5 + 1) * 100) / 100, // 1-6 MPa
      speed: Math.round(Math.random() * 1000 + 100), // 100-1100 RPM
      voltage: Math.round((Math.random() * 200 + 220) * 10) / 10, // 220-420V
      current: Math.round((Math.random() * 10 + 1) * 100) / 100, // 1-11A
      power: Math.round((Math.random() * 5 + 0.5) * 100) / 100, // 0.5-5.5 kW
      runtime: Math.round(Math.random() * 10000), // 0-10000小时
      status: Math.random() > 0.2 ? languageService.translate('normal') : languageService.translate('maintenance'),
      lastMaintenance: this.getRandomDate(true),
      nextMaintenance: this.getRandomDate(false)
    };
    return params;
  }

  /**
   * 生成模拟的模型参数数据
   */
  private generateMockModelParams(): void {
    const modelIds = [
      "AUnwindingAxis",
      "BUnwindingAxis",
      "UnwindingSpliceRotationAxis",
      "UnwindingSpliceSlitterAxis",
      "Alarm_UnwindingGuidingWidthAdjustAxis",
      "zmjqj",
      "bmjqj",
      "jgqjp",
      "MainAxis",
      "zmfd",
      "内部.3015",
      "fqdmz",
      "BrushUpAAxis",
      "BrushDownAAxis",
      "Laser1WidthAdjustAxis",
      "Laser2WidthAdjustAxis",
      "UpBRewindingAxis",
      "DownBRewindingAxis",
      "UpARewindingAxis",
      "DownARewindingAxis",
      "DownRewindingRubberizingQAixs",
      "UpRewindingRubberizingQAixs"
    ];

    // 为每个模型ID生成模拟参数
    modelIds.forEach(id => {
      const params = this.generateModelParams();
      this.modelParamsMap.set(id, params);
    });
  }

  /**
   * 生成随机日期字符串
   * @param isPast 是否是过去的日期
   * @returns 日期字符串
   */
  private getRandomDate(isPast: boolean): string {
    const now = new Date();
    const offset = isPast
      ? -Math.round(Math.random() * 180) // 过去0-180天
      : Math.round(Math.random() * 180 + 30); // 未来30-210天

    const date = new Date();
    date.setDate(now.getDate() + offset);

    return date.toLocaleDateString('zh-CN');
  }

  /**
   * 更新模型状态
   * @param statusDataList 模型状态数据列表
   */
  public updateModelStatus(statusDataList: ModelStatusData[]): void {
    // 先清除所有现有的状态图标
    this.clearAllStatusIcons();

    // 获取报警状态的模型ID列表，用于设置高亮效果
    const alarmingMeshIds = statusDataList
      .filter(data => data.isAlarming)
      .map(data => data.ID);

    // 如果有报警状态的模型，设置高亮效果
    if (alarmingMeshIds.length > 0) {
      this.alarmManager.setModelAlarm({
        meshIds: alarmingMeshIds,
        isAlarming: true,
        color: { r: 1, g: 0, b: 0 },
        showCenterButton: false
      });
    }

    // 为每个模型添加状态图标
    statusDataList.forEach(data => {
      // 检查是否需要为该模型生成参数
      if (!this.modelParamsMap.has(data.ID)) {
        // console.log(`为非固定列表中的模型ID生成参数: ${data.ID}`);
        const params = this.generateModelParams();
        this.modelParamsMap.set(data.ID, params);
      }

      const mesh = this.modelLoader.findMeshById(data.ID);
      if (mesh) {
        this.addStatusIcon(mesh, data);
      } else {
        console.warn(`未找到ID为${data.ID}的模型`);
      }
    });
  }

  /**
   * 为模型添加状态图标
   * @param mesh 目标网格
   * @param statusData 模型状态数据
   */
  private addStatusIcon(mesh: BABYLON.AbstractMesh, statusData: ModelStatusData): void {
    if (!this.advancedTexture) return;

    // 创建图标容器
    const iconContainer = new GUI.Container(`iconContainer_${statusData.ID}`);
    iconContainer.width = "40px";
    iconContainer.height = "40px";
    iconContainer.isPointerBlocker = true; // 确保可以接收点击事件

    // 创建图标
    const iconImage = new GUI.Image(`icon_${statusData.ID}`, this.createIconDataUrl(statusData.isAlarming));
    iconImage.width = "30px";
    iconImage.height = "30px";
    iconContainer.addControl(iconImage);

    // 添加图标到UI并链接到网格
    this.advancedTexture.addControl(iconContainer);
    iconContainer.linkWithMesh(mesh);

    // 创建背景矩形
    const tooltipBackground = new GUI.Rectangle(`tooltipBg_${statusData.ID}`);
    tooltipBackground.width = "auto";
    tooltipBackground.height = "20px";
    tooltipBackground.cornerRadius = 3;
    tooltipBackground.color = "black";
    tooltipBackground.thickness = 0;
    tooltipBackground.background = "black";
    tooltipBackground.alpha = 0.7;
    tooltipBackground.isVisible = false;

    // 添加文本
    const tooltip = new GUI.TextBlock(`tooltip_${statusData.ID}`, statusData.modelName);
    tooltip.color = "white";
    tooltip.fontSize = 12;
    tooltip.textWrapping = true;
    tooltip.resizeToFit = true;
    tooltip.paddingLeft = "5px";
    tooltip.paddingRight = "5px";
    tooltip.paddingTop = "2px";
    tooltip.paddingBottom = "2px";

    // 添加文本到背景矩形
    tooltipBackground.addControl(tooltip);

    // 添加背景矩形到容器
    iconContainer.addControl(tooltipBackground);
    tooltipBackground.linkOffsetY = -20;

    // 添加鼠标悬停事件
    iconContainer.onPointerEnterObservable.add(() => {
      tooltipBackground.isVisible = true;
    });

    iconContainer.onPointerOutObservable.add(() => {
      tooltipBackground.isVisible = false;
    });

    // 添加点击事件
    iconContainer.onPointerClickObservable.add(() => {
      // 获取模型参数
      const params = this.modelParamsMap.get(statusData.ID) || {};

      // 调用所有回调函数
      this.iconClickCallbacks.forEach(callback => {
        callback(statusData.ID, statusData.modelName, params);
      });
    });

    // 存储图标引用以便后续清理
    this.statusIcons.set(statusData.ID, iconContainer);
  }

  /**
   * 创建图标的Data URL
   * @param isAlarming 是否处于报警状态
   * @returns 图标的Data URL
   */
  private createIconDataUrl(isAlarming: boolean): string {
    const svg = isAlarming ? this.alarmIconSvg : this.locationIconSvg;
    return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
  }

  /**
   * 添加图标点击事件回调函数
   * @param callback 回调函数
   */
  public addIconClickCallback(callback: IconClickCallback): void {
    this.iconClickCallbacks.push(callback);
  }

  /**
   * 移除图标点击事件回调函数
   * @param callback 回调函数
   */
  public removeIconClickCallback(callback: IconClickCallback): void {
    const index = this.iconClickCallbacks.indexOf(callback);
    if (index !== -1) {
      this.iconClickCallbacks.splice(index, 1);
    }
  }

  /**
   * 获取模型参数
   * @param id 模型ID
   * @returns 模型参数
   */
  public getModelParams(id: string): ModelParamData {
    return this.modelParamsMap.get(id) || {};
  }

  /**
   * 更新模型参数
   * @param id 模型ID
   * @param params 模型参数
   */
  public updateModelParams(id: string, params: Partial<ModelParamData>): void {
    const currentParams = this.modelParamsMap.get(id) || {};
    this.modelParamsMap.set(id, { ...currentParams, ...params });
  }

  /**
   * 清除所有状态图标
   */
  private clearAllStatusIcons(): void {
    if (!this.advancedTexture) return;

    this.statusIcons.forEach(icon => {
      this.advancedTexture?.removeControl(icon);
    });

    this.statusIcons.clear();

    // 清除报警高亮效果
    this.alarmManager.clearAllAlarms();
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.clearAllStatusIcons();
    if (this.advancedTexture) {
      this.advancedTexture.dispose();
      this.advancedTexture = null;
    }
    this.iconClickCallbacks = [];
    this.modelParamsMap.clear();
  }

  /**
   * 飞行定位到指定模型
   * @param modelId 模型ID
   * @returns 飞行定位结果
   */
  public async flyToModel(modelId: string): Promise<FlyToModelResult> {
    try {
      const mesh = this.modelLoader.findMeshById(modelId);
      if (!mesh) {
        return {
          success: false,
          message: `未找到ID为${modelId}的模型`,
          modelId
        };
      }

      // 获取相机和场景
      const camera = this.sceneManager.getCamera();
      const scene = this.sceneManager.getScene();
      if (!camera || !scene) {
        return {
          success: false,
          message: '相机或场景未初始化',
          modelId
        };
      }

      // 获取网格边界信息
      const boundingInfo = mesh.getBoundingInfo();
      if (!boundingInfo) {
        return {
          success: false,
          message: `无法获取模型${modelId}的边界信息`,
          modelId
        };
      }

      // 计算模型中心点
      const modelCenter = boundingInfo.boundingBox.centerWorld;

      // 获取当前相机状态
      const currentTarget = camera.target.clone();
      const currentRadius = camera.radius;

      // 计算适当的相机距离
      const extendSize = boundingInfo.boundingBox.extendSizeWorld;
      const diagonalLength = extendSize.length() * 2;

      // 根据模型大小计算合适的相机距离，确保模型完全可见
      const targetRadius = Math.max(diagonalLength * 1.2, 5);

      // console.log(`飞行定位到模型 ${modelId}: 位置=${modelCenter.toString()}, 距离=${targetRadius.toFixed(2)}`);

      // 创建相机动画：目标点动画
      const targetAnimation = new BABYLON.Animation(
        "cameraTargetAnimation",
        "target",
        60,
        BABYLON.Animation.ANIMATIONTYPE_VECTOR3,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );

      // 创建关键帧
      const targetKeyFrames = [
        { frame: 0, value: currentTarget },
        { frame: this.flyToAnimationDuration / 16.67, value: modelCenter }
      ];
      targetAnimation.setKeys(targetKeyFrames);

      // 创建相机动画：半径（距离）动画
      const radiusAnimation = new BABYLON.Animation(
        "cameraRadiusAnimation",
        "radius",
        60,
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );

      // 创建关键帧
      const radiusKeyFrames = [
        { frame: 0, value: currentRadius },
        { frame: this.flyToAnimationDuration / 16.67, value: targetRadius }
      ];
      radiusAnimation.setKeys(radiusKeyFrames);

      // 使用平滑动画效果
      const ease = new BABYLON.CircleEase();
      ease.setEasingMode(BABYLON.EasingFunction.EASINGMODE_EASEINOUT);
      targetAnimation.setEasingFunction(ease);
      radiusAnimation.setEasingFunction(ease);

      // 停止当前动画
      scene.stopAnimation(camera);

      // 清除当前动画并添加新动画
      camera.animations = [];
      camera.animations.push(targetAnimation);
      camera.animations.push(radiusAnimation);

      // 创建一个Promise，在动画完成时解析
      return new Promise<FlyToModelResult>((resolve) => {
        // 开始动画并在完成时解析Promise
        scene.beginAnimation(camera, 0, radiusKeyFrames[1].frame, false, 1, () => {
          // 可选：高亮显示模型
          this.highlightModel(modelId);

          resolve({
            success: true,
            message: `已定位到模型${modelId}`,
            modelId
          });
        });
      });
    } catch (error) {
      console.error(`飞行定位到模型${modelId}时出错:`, error);
      return {
        success: false,
        message: `定位失败: ${error instanceof Error ? error.message : '未知错误'}`,
        modelId
      };
    }
  }

  /**
   * 高亮显示指定模型
   * @param modelId 模型ID
   * @param duration 高亮持续时间（毫秒），0表示持续高亮直到清除
   */
  private highlightModel(modelId: string, duration: number = 3000): void {
    const mesh = this.modelLoader.findMeshById(modelId);
    if (!mesh) return;

    // 使用报警管理器进行高亮
    this.alarmManager.setModelAlarm({
      meshIds: [modelId],
      isAlarming: true,
      color: { r: 0, g: 0.8, b: 1 }, // 使用蓝色高亮，区别于红色报警
      showCenterButton: false
    });

    // 如果指定了持续时间，在指定时间后清除高亮
    if (duration > 0) {
      setTimeout(() => {
        // 清除所有高亮，因为AlarmManager中没有清除特定告警的方法
        // 这里需要注意可能会清除其他报警状态，实际项目中应该在AlarmManager中添加清除特定告警的方法
        this.alarmManager.clearAllAlarms();

        // 重新恢复之前的报警状态
        // 这里简化处理，实际项目中应该保存之前的报警状态并恢复
      }, duration);
    }
  }

  /**
   * 设置飞行动画持续时间
   * @param duration 持续时间（毫秒）
   */
  public setFlyToAnimationDuration(duration: number): void {
    this.flyToAnimationDuration = Math.max(duration, 500); // 最小500毫秒
  }
}
