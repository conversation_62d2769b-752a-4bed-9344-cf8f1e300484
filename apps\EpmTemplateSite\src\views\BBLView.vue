<script setup lang="ts">
import {
  ref,
  onMounted,
  reactive,
  onBeforeUnmount,
  onActivated,
  onDeactivated,
  computed,
  watch,
  defineEmits,
} from "vue"
import { useStore } from "vuex"

import BabylonScene from "../components/bbl/BabylonScene.vue"
import ObjectTreePanel from "../components/bbl/ObjectTreePanel.vue"
import AlarmControlPanel from "../components/bbl/AlarmControlPanel.vue"
import type { AlarmStatus } from "../utils/bbl/AlarmManager"
import { IndexedDBManager } from "../utils/bbl/IndexedDBManager"
import { ModelStatusService } from "../utils/bbl/api/modelStatusService"
import type { ModelStatusData } from "../utils/bbl/ModelStatusManager"
import type {
  FlyToModelResult,
  RealTimeAlarmData,
} from "../utils/bbl/api/modelStatusService"
import type { ModelParamData } from "../utils/bbl/ModelStatusManager"
import type { ParameterLabelData } from "../utils/bbl/LableManager"
import { LanguageService, type Language } from "../utils/bbl/LanguageService"

// 对象树节点类型定义
type MeshTreeNode = {
  id: string
  name: string
  children: MeshTreeNode[]
  level: number
  vertices: number
  isVisible: boolean
  parentId?: string
}

// 网格信息类型定义
interface MeshInfo {
  id: string
  name: string
  position: { x: number; y: number; z: number }
  rotation: { x: number; y: number; z: number }
  scaling: { x: number; y: number; z: number }
  vertices: number
  faces: number
  parentId?: string
  children: string[]
  isVisible: boolean
  materialName?: string
}

// 场景信息类型定义
interface SceneInfo {
  totalMeshes: number
  totalVertices: number
  totalFaces: number
  fps: number
  drawCalls: number
  activeCamera: string
  activeLights: number
}

// 场景组件暴露的方法接口
interface BabylonSceneInstance {
  importModel: (
    fileUrl: string,
    fileData?: File | ArrayBuffer,
    clearPrevious?: boolean
  ) => Promise<boolean>
  setModelAlarm: (status: AlarmStatus) => void
  adjustCameraToModel: (animationDuration?: number) => void
  getMeshNames: () => string[]
  getMeshInfo: (id: string) => MeshInfo | null
  getMeshTree: () => MeshTreeNode[]
  getSceneInfo: () => SceneInfo | null
  setCenterButtonClickCallback: (callback: (meshId: string) => void) => void
  updateModelStatus: (data: ModelStatusData[]) => void
  addIconClickCallback: (
    callback: (
      meshId: string,
      modelName: string,
      params: ModelParamData
    ) => void
  ) => void
  flyToModel: (modelId: string) => Promise<FlyToModelResult>
  toggleWkallVisibility: (visible: boolean) => boolean
  initializeDefaultEffects: () => void
  stopAllEffects: () => void
  addHongdengLightFlashing: () => boolean
  addRotationAnimations: (modelIds: string[]) => boolean
  addJiguangFlashing: () => boolean
  // 标签管理器方法
  updateParameterLabels: (parameterData: ParameterLabelData[]) => void
  updateParameterValue: (parameterCode: string, newValue: string | number, unit?: string) => void
  setLabelsVisible: (visible: boolean) => void
  clearAllLabels: () => void
  createMockParameterData: () => ParameterLabelData[]
}

// 报警数据接口类型定义
interface AlarmData {
  alarmCode: string
  alarmContent: string
  startTime: string
  durationSeconds: number
  moduleCode: string
  isSparePart: boolean
  alarmGroup: string | null
}

// 引用BabylonScene组件
const babylonSceneRef = ref<BabylonSceneInstance | null>(null)
const indexedDBManager = new IndexedDBManager()
const modelStatusService = new ModelStatusService()

// 模型文件URL，在实际应用中可以从API或上传获取
const sampleModels = reactive([
  { name: "模型1", url: "/static/bbl_file/models/zhuti.glb" },
  { name: "模型2", url: "/static/bbl_file/models/ccj.glb" },
  { name: "模型3", url: "/static/bbl_file/models/flx.glb" },
  { name: "模型4", url: "/static/bbl_file/models/csbccj.glb" },
])

// 模型加载状态
const modelStatus = reactive({
  loading: false,
  error: "",
  currentModel: null as string | null,
})

// 模型网格列表
const meshNames = ref<string[]>([])

// 网格ID到名称的映射
const meshNameMap = computed(() => {
  const map = new Map<string, string>()
  meshNames.value.forEach(name => {
    map.set(name, name)
  })
  return map
})

// 模型对象树
const meshTree = ref<MeshTreeNode[]>([])

// 模型是否已加载
const modelLoaded = computed(() => modelStatus.currentModel !== null)

// 侧边栏折叠状态
const sidebarCollapsed = ref(false)

// 当前活动标签
const activeTab = ref("control")

// 场景信息
const sceneInfo = ref<SceneInfo | null>(null)

// 选中的对象ID
const selectedObjectId = ref("")

// 选中的对象信息
const selectedObjectInfo = ref<MeshInfo | null>(null)

// 告警选项
const alarmOptions = reactive({
  targetMeshIds: [] as string[],
  isAlarming: false,
  color: { r: 1, g: 0, b: 0 },
  showCenterButton: false,
})

// 缓存相关状态
const currentModelDataForCaching = ref<{
  url: string
  data: ArrayBuffer
} | null>(null)
const cacheStatusMessage = ref<string>("")
const availableCachedModels = ref<
  { url: string; name: string; timestamp: number }[]
>([])
const isLoadingFromCache = ref<boolean>(false)

// 模型状态数据
const modelStatusData = ref<ModelStatusData[]>([])
const modelStatusLoading = ref(false)

// 模型参数弹框状态
const paramDialogVisible = ref(false)
const currentModelParams = ref<{
  id: string
  name: string
  params: ModelParamData
} | null>(null)

// 获取语言服务实例
const languageService = LanguageService.getInstance()
const currentLanguage = ref<Language>(languageService.getCurrentLanguage())

// 切换语言
const store = useStore()
const language = computed(() => store.getters["notice/language"])
const toggleLanguage = async (targetLanguage?: Language) => {
  let newLanguage: Language

  if (targetLanguage) {
    // 如果传入了目标语言，直接设置
    languageService.setLanguage(targetLanguage)
    newLanguage = targetLanguage
  } else {
    // 如果没有传入目标语言，则切换
    newLanguage = languageService.toggleLanguage()
  }

  currentLanguage.value = newLanguage

  // 语言切换后重新获取模型状态数据以更新模型名称
  if (modelLoaded.value) {
    await fetchModelStatusData()
    // 更新选中的模型名称
    updateSelectedModelName()
  }
}
watch(
  () => language.value,
  async v => {
    if (v) {
      // 根据store中的语言值切换语言
      await toggleLanguage(v as Language)
    }
  },
  { immediate: true }
)

// 模组名称到模型ID的映射
const moduleToModelIds: Record<string, string[]> = {
  放卷模组: ["AUnwindingAxis", "BUnwindingAxis"],
  主驱模组: ["MainAxis"],
  收卷模组: [
    "UpBRewindingAxis",
    "DownBRewindingAxis",
    "UpARewindingAxis",
    "DownARewindingAxis",
  ],
}

// 实时参数数据存储
const realTimeParametersData = ref<Record<string, any>>({})

// 实时参数
const realTimeParameters = computed(
  () => store.getters["notice/realTimeParameters"]
)
watch(
  () => realTimeParameters.value,
  v => {
    if (v && Array.isArray(v)) {
      // 处理实时参数数据，按模型ID存储
      const newData: Record<string, any> = {}

      v.forEach((moduleData: any) => {
        const moduleName = moduleData.moudle || moduleData.module
        const modelIds = moduleToModelIds[moduleName]

        if (modelIds && moduleData.parameters) {
          // 将参数数组转换为对象格式
          const params: Record<string, string> = {}
          moduleData.parameters.forEach((param: any) => {
            params[param.parameter] = param.value
          })

          // 为每个对应的模型ID存储参数
          modelIds.forEach(modelId => {
            newData[modelId] = {
              moduleName,
              parameters: params,
              originalData: moduleData,
            }
          })
        }
      })

      realTimeParametersData.value = newData
    }
  },
  { deep: true }
)

// 翻译辅助函数
const translate = (key: string): string => {
  return languageService.translate(key)
}

// 切换侧边栏展开/折叠状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 获取文件名或简单名称的辅助函数
const getModelNameFromUrl = (url: string) => {
  const parts = url.split("/")
  const fileName = parts[parts.length - 1]
  return fileName.replace(/\.(glb|gltf)$/i, "")
}

// 获取可用缓存模型列表
const fetchAvailableCachedModels = async () => {
  try {
    const cachedInfo = await indexedDBManager.getAllCachedModelInfo()
    availableCachedModels.value = cachedInfo.map(item => ({
      ...item,
      name: getModelNameFromUrl(item.url),
    }))
    if (cachedInfo.length > 0) {
      cacheStatusMessage.value = `发现 ${cachedInfo.length} 个已缓存模型。`
    } else {
      cacheStatusMessage.value = "没有发现已缓存的模型。"
    }
  } catch (error) {
    cacheStatusMessage.value = "获取缓存列表失败。"
    availableCachedModels.value = []
  }
}

// 获取模型状态数据
const fetchModelStatusData = async () => {
  if (!babylonSceneRef.value || !modelLoaded.value) return

  modelStatusLoading.value = true
  try {
    const data = await modelStatusService.getModelStatusData()
    modelStatusData.value = data
    babylonSceneRef.value.updateModelStatus(data)
  } catch (error) {
    // 获取模型状态数据失败
  } finally {
    modelStatusLoading.value = false
  }
}

// 处理报警数据的核心方法
const handleAlarmData = async (alarmDataArray: AlarmData[]) => {
  if (!babylonSceneRef.value || !modelLoaded.value) {
    return
  }

  try {
    // 转换为RealTimeAlarmData格式
    const realTimeAlarmData: RealTimeAlarmData[] = alarmDataArray.map(item => ({
      alarmCode: item.alarmCode || "",
      alarmContent: item.alarmContent || "",
      startTime: item.startTime || new Date().toISOString(),
      durationSeconds: item.durationSeconds || 0,
      moduleCode: item.moduleCode || "",
      isSparePart: item.isSparePart || false,
      alarmGroup: item.alarmGroup || null,
    }))

    const updatedData = await modelStatusService.handleRealTimeAlarmData(
      realTimeAlarmData
    )
    modelStatusData.value = updatedData
    babylonSceneRef.value.updateModelStatus(updatedData)

    // 处理报警数据
  } catch (error) {
    // 处理报警数据失败
  }
}

// 清除所有报警状态
const clearAllAlarms = async () => {
  if (!babylonSceneRef.value || !modelLoaded.value) return

  try {
    const currentData = await modelStatusService.getModelStatusData()
    const clearedData = currentData.map(item => ({
      ...item,
      isAlarming: false,
      alarmInfo: undefined,
    }))
    modelStatusData.value = clearedData
    babylonSceneRef.value.updateModelStatus(clearedData)
  } catch (error) {
    // 清除报警状态失败
  }
}

// 暴露给外部调用的报警接口
const setAlarmData = (alarmDataArray: AlarmData[]) => {
  // console.log("接收到的报警数据12212:", alarmDataArray)
  handleAlarmData(alarmDataArray)
}

// 暴露给外部调用的清除报警接口
const clearAlarms = () => {
  clearAllAlarms()
}

// 飞行定位到模型
const flyToModel = async (modelId: string) => {
  if (!babylonSceneRef.value || !modelLoaded.value) {
    return {
      success: false,
      message: "模型未加载或场景未初始化",
      modelId,
    }
  }

  try {
    const result = await babylonSceneRef.value.flyToModel(modelId)
    return result
  } catch (error) {
    return {
      success: false,
      message: `飞行定位失败: ${
        error instanceof Error ? error.message : "未知错误"
      }`,
      modelId,
    }
  }
}

// 处理模型加载成功后的操作
const handleModelLoaded = async () => {
  if (!babylonSceneRef.value) return

  await fetchModelStatusData()
  modelStatusService.setFlyToModelCallback(flyToModel)
  babylonSceneRef.value.addIconClickCallback(handleIconClick)

  // 自动显示参数标签
  if (showParameterLabels.value) {
    const mockData = babylonSceneRef.value.createMockParameterData()
    babylonSceneRef.value.updateParameterLabels(mockData)
  }
}

// 处理模型选择（从网络加载）
const handleModelSelect = async (modelUrl: string) => {
  if (!babylonSceneRef.value) return
  if (modelStatus.loading || isLoadingFromCache.value) return

  // 如果组件已经初始化且当前模型已经是目标模型，则不重新加载
  if (isComponentInitialized.value && modelStatus.currentModel === modelUrl) {
    return
  }

  modelStatus.loading = true
  modelStatus.error = ""
  cacheStatusMessage.value = "开始从网络加载模型..."
  meshNames.value = []
  meshTree.value = []
  sceneInfo.value = null
  selectedObjectId.value = ""
  selectedObjectInfo.value = null
  alarmOptions.targetMeshIds = []
  currentModelDataForCaching.value = null

  try {
    console.log(`从网络加载: ${modelUrl}`)
    const response = await fetch(modelUrl)
    if (!response.ok) {
      throw new Error(`网络请求失败: ${response.statusText}`)
    }
    const arrayBuffer = await response.arrayBuffer()
    currentModelDataForCaching.value = {
      url: modelUrl,
      data: arrayBuffer.slice(0),
    }

    const modelFile = new File(
      [arrayBuffer],
      getModelNameFromUrl(modelUrl) +
        (modelUrl.endsWith(".glb") ? ".glb" : ".gltf")
    )
    const success = await babylonSceneRef.value.importModel(modelUrl, modelFile)

    if (success) {
      modelStatus.currentModel = modelUrl
      meshNames.value = babylonSceneRef.value.getMeshNames()
      meshTree.value = babylonSceneRef.value.getMeshTree()
      refreshSceneInfo()
      cacheStatusMessage.value = "模型已从网络加载。可点击下方按钮缓存。 "

      const existingCache = await indexedDBManager.getModel(modelUrl)
      if (existingCache) currentModelDataForCaching.value = null

      await handleModelLoaded()
    } else {
      modelStatus.error = "模型加载失败。"
      cacheStatusMessage.value = "模型加载失败。"
    }
  } catch (err: unknown) {
    const errorMessage = err instanceof Error ? err.message : "未知错误"
    modelStatus.error = `模型加载错误: ${errorMessage}`
    cacheStatusMessage.value = `加载错误: ${errorMessage}`
    console.error(err)
  } finally {
    modelStatus.loading = false
  }
}

// 加载所有模型 - 优先从缓存加载，缓存不存在时才从网络加载
const emit = defineEmits(["model-ready"])
const loadAllModels = async () => {
  if (!babylonSceneRef.value) return

  await new Promise(resolve => setTimeout(resolve, 500))

  let loadedModelCount = 0
  cacheStatusMessage.value = `正在加载所有模型，请稍等...`

  console.log(`开始加载 ${sampleModels.length} 个模型，优先从缓存加载`)

  for (let i = 0; i < sampleModels.length; i++) {
    const model = sampleModels[i]
    const isFirstModel = i === 0

    if (modelStatus.loading || isLoadingFromCache.value) {
      console.log("跳过模型加载，因为另一个模型正在加载中")
      continue
    }

    try {
      console.log(
        `准备加载模型: ${model.name} (${model.url}) - ${
          isFirstModel ? "第一个模型" : "追加模型"
        }`
      )
      modelStatus.loading = true

      const cachedData = await indexedDBManager.getModel(model.url)
      let success = false

      if (cachedData) {
        console.log(`模型已缓存，从缓存加载: ${model.name}`)
        const modelFile = new File(
          [cachedData],
          model.name + (model.url.endsWith(".glb") ? ".glb" : ".gltf")
        )
        success = await babylonSceneRef.value.importModel(
          model.url,
          modelFile,
          isFirstModel
        )

        if (success) {
          console.log(`从缓存成功加载模型: ${model.name}`)
        } else {
          console.warn(`从缓存加载模型失败: ${model.name}，尝试从网络加载`)
          success = await loadModelFromNetwork(model, isFirstModel)
        }
      } else {
        console.log(`模型未缓存，从网络加载: ${model.name}`)
        success = await loadModelFromNetwork(model, isFirstModel)
      }

      if (success) {
        modelStatus.currentModel = model.url
        loadedModelCount++
        meshNames.value = babylonSceneRef.value.getMeshNames()
        meshTree.value = babylonSceneRef.value.getMeshTree()
        refreshSceneInfo()
        cacheStatusMessage.value = `已加载 ${loadedModelCount}/${sampleModels.length} 个模型`
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "未知错误"
      console.error(`加载模型 ${model.name} 失败:`, errorMessage)
      cacheStatusMessage.value = `加载 ${model.name} 失败: ${errorMessage}`
    } finally {
      modelStatus.loading = false
    }
  }

  if (babylonSceneRef.value && modelStatus.currentModel) {
    console.log("所有模型加载完成，调整视角")
    babylonSceneRef.value.adjustCameraToModel(1200)
    cacheStatusMessage.value = `已成功加载所有 ${loadedModelCount} 个模型`
    emit("model-ready")
  }
}

// 从网络加载模型辅助函数
const loadModelFromNetwork = async (
  model: { name: string; url: string },
  isFirstModel: boolean
): Promise<boolean> => {
  if (!babylonSceneRef.value) return false

  try {
    const response = await fetch(model.url)
    if (!response.ok) {
      throw new Error(`网络请求失败: ${response.statusText}`)
    }

    const arrayBuffer = await response.arrayBuffer()
    const modelFile = new File(
      [arrayBuffer],
      model.name + (model.url.endsWith(".glb") ? ".glb" : ".gltf")
    )
    const success = await babylonSceneRef.value.importModel(
      model.url,
      modelFile,
      isFirstModel
    )

    if (success) {
      console.log(`从网络成功加载模型: ${model.name}`)
      console.log(`自动缓存模型: ${model.name}`)
      await indexedDBManager.saveModel(model.url, arrayBuffer.slice(0))
      currentModelDataForCaching.value = {
        url: model.url,
        data: arrayBuffer.slice(0),
      }
      return true
    } else {
      console.error(`从网络加载模型失败: ${model.name}`)
      return false
    }
  } catch (error) {
    console.error(`网络加载模型出错: ${model.name}`, error)
    return false
  }
}

// 从缓存加载指定模型
const loadModelFromCache = async (modelToLoad: {
  url: string
  name: string
}) => {
  if (!babylonSceneRef.value) return
  if (modelStatus.loading || isLoadingFromCache.value) return

  // 如果组件已经初始化且当前模型已经是目标模型，则不重新加载
  if (isComponentInitialized.value && modelStatus.currentModel === modelToLoad.url) {
    return
  }

  isLoadingFromCache.value = true
  modelStatus.loading = true
  modelStatus.error = ""
  cacheStatusMessage.value = `正在从缓存加载 ${modelToLoad.name}...`
  meshNames.value = []
  meshTree.value = []
  sceneInfo.value = null
  selectedObjectId.value = ""
  selectedObjectInfo.value = null
  alarmOptions.targetMeshIds = []
  currentModelDataForCaching.value = null

  try {
    const cachedData = await indexedDBManager.getModel(modelToLoad.url)
    if (cachedData) {
      const modelFile = new File(
        [cachedData],
        modelToLoad.name + (modelToLoad.url.endsWith(".glb") ? ".glb" : ".gltf")
      )
      const success = await babylonSceneRef.value.importModel(
        modelToLoad.url,
        modelFile
      )
      if (success) {
        modelStatus.currentModel = modelToLoad.url
        meshNames.value = babylonSceneRef.value.getMeshNames()
        meshTree.value = babylonSceneRef.value.getMeshTree()
        refreshSceneInfo()
        cacheStatusMessage.value = `模型 ${modelToLoad.name} 已从缓存加载。`
        await handleModelLoaded()
      } else {
        throw new Error("从缓存数据解析模型失败。")
      }
    } else {
      throw new Error(`在缓存中未找到模型 ${modelToLoad.name}。`)
    }
  } catch (err: unknown) {
    const errorMessage = err instanceof Error ? err.message : "未知错误"
    modelStatus.error = `从缓存加载错误: ${errorMessage}`
    cacheStatusMessage.value = `缓存加载错误: ${errorMessage}`
    console.error(err)
    await fetchAvailableCachedModels()
  } finally {
    isLoadingFromCache.value = false
    modelStatus.loading = false
  }
}

// 缓存当前从网络加载的模型
const cacheCurrentModel = async () => {
  if (
    !currentModelDataForCaching.value ||
    !currentModelDataForCaching.value.data
  ) {
    cacheStatusMessage.value = "没有从网络加载的模型可供缓存。"
    return
  }

  cacheStatusMessage.value = "正在缓存模型..."
  try {
    await indexedDBManager.saveModel(
      currentModelDataForCaching.value.url,
      currentModelDataForCaching.value.data
    )
    currentModelDataForCaching.value = null
    cacheStatusMessage.value = "模型已成功缓存！"
    await fetchAvailableCachedModels()
  } catch (error) {
    cacheStatusMessage.value = "缓存模型失败。"
    console.error("缓存模型失败:", error)
  }
}

// 清除 IndexedDB 缓存
const clearModelCache = async () => {
  cacheStatusMessage.value = "正在清除所有模型缓存..."
  try {
    await indexedDBManager.clearCache()
    currentModelDataForCaching.value = null
    cacheStatusMessage.value = "所有模型缓存已清除。"
    await fetchAvailableCachedModels()
  } catch (error) {
    cacheStatusMessage.value = "清除缓存失败。"
    console.error("清除缓存失败:", error)
  }
}

// 自适应视角
const adjustCamera = () => {
  if (!babylonSceneRef.value || !modelStatus.currentModel) return
  console.log("手动调整视角")
  babylonSceneRef.value.adjustCameraToModel(1800)
}

// 处理告警设置
const triggerAlarm = () => {
  if (
    !babylonSceneRef.value ||
    !modelStatus.currentModel ||
    alarmOptions.targetMeshIds.length === 0
  )
    return

  const alarmStatus: AlarmStatus = {
    meshIds: alarmOptions.targetMeshIds,
    isAlarming: alarmOptions.isAlarming,
    color: alarmOptions.color,
    showCenterButton: alarmOptions.showCenterButton,
  }

  babylonSceneRef.value.setModelAlarm(alarmStatus)
}

// 刷新场景信息
const refreshSceneInfo = () => {
  if (!babylonSceneRef.value) return
  sceneInfo.value = babylonSceneRef.value.getSceneInfo()
}

// 处理对象ID变更
const handleObjectIdUpdate = async (id: string) => {
  selectedObjectId.value = id
  if (id && babylonSceneRef.value) {
    selectedObjectInfo.value = babylonSceneRef.value.getMeshInfo(id)
  } else {
    selectedObjectInfo.value = null
  }
}

// 处理告警目标切换
const handleToggleAlarmTarget = (id: string) => {
  const index = alarmOptions.targetMeshIds.indexOf(id)
  if (index === -1) {
    alarmOptions.targetMeshIds.push(id)
  } else {
    alarmOptions.targetMeshIds.splice(index, 1)
  }
}

// 移除告警目标
const removeAlarmTarget = (id: string) => {
  const index = alarmOptions.targetMeshIds.indexOf(id)
  if (index !== -1) {
    alarmOptions.targetMeshIds.splice(index, 1)
  }
}

// 清空所有告警目标
const clearAlarmTargets = () => {
  alarmOptions.targetMeshIds = []
  if (alarmOptions.isAlarming) {
    alarmOptions.isAlarming = false
    triggerAlarm()
  }
}

// 处理告警状态变更
const handleAlarmingUpdate = (value: boolean) => {
  alarmOptions.isAlarming = value
  triggerAlarm()
}

// 处理告警颜色变更
const handleColorUpdate = (value: { r: number; g: number; b: number }) => {
  alarmOptions.color = value
  if (alarmOptions.isAlarming) {
    triggerAlarm()
  }
}

// 处理中心按钮显示状态变更
const handleCenterButtonUpdate = (value: boolean) => {
  alarmOptions.showCenterButton = value
  if (alarmOptions.isAlarming) {
    triggerAlarm()
  }
}

// 设置自动刷新场景信息
let infoRefreshInterval: number | null = null

// KeepAlive 状态管理
const isComponentInitialized = ref(false)

// 确保模型加载后检查对象树是否为空
const ensureTreeData = () => {
  // 只有在组件已初始化且模型已加载的情况下才检查对象树
  if (
    isComponentInitialized.value &&
    meshTree.value.length === 0 &&
    modelStatus.currentModel &&
    babylonSceneRef.value &&
    modelLoaded.value
  ) {
    meshTree.value = babylonSceneRef.value.getMeshTree()
  }
}

// 处理图标点击事件
const handleIconClick = (
  meshId: string,
  modelName: string,
  _params: ModelParamData // 保留参数但不使用，避免参数不匹配
) => {
  const alarmData = modelStatusData.value.find(item => item.ID === meshId)
  const realTimeData = realTimeParametersData.value[meshId]

  // 只使用实时参数数据，不使用模拟数据
  let finalParams: ModelParamData = {}

  if (
    realTimeData &&
    realTimeData.parameters &&
    Object.keys(realTimeData.parameters).length > 0
  ) {
    // 有后台实时数据时，使用实时数据
    Object.keys(realTimeData.parameters).forEach(paramName => {
      finalParams[paramName] = realTimeData.parameters[paramName]
    })
  }
  // 删除了模拟数据的使用，如果没有实时数据，finalParams 保持为空对象

  if (alarmData && alarmData.alarmInfo) {
    currentModelParams.value = {
      id: meshId,
      name: alarmData.alarmInfo.alarmCode || modelName,
      params: {
        ...finalParams,
        alarmContent: alarmData.alarmInfo.alarmContent || "",
        alarmGroup: alarmData.alarmInfo.alarmGroup || "",
      },
    }
  } else {
    currentModelParams.value = {
      id: meshId,
      name: modelName,
      params: finalParams,
    }
  }

  paramDialogVisible.value = true
}

// 关闭参数弹框
const closeParamDialog = () => {
  paramDialogVisible.value = false
}

// 模型下拉框相关状态
const isDropdownOpen = ref(false)
const modelSearchText = ref("")
const selectedModelId = ref<string | null>(null)
const selectedModelName = ref<string | null>(null)
const searchInput = ref<HTMLInputElement | null>(null)

// 设备显示模式状态（true: 整机设备, false: 内部设备）
const isWholeDeviceMode = ref(true)

// 过滤后的模型列表
const filteredModelList = computed(() => {
  if (!modelSearchText.value) {
    return modelStatusData.value
  }

  const searchText = modelSearchText.value.toLowerCase()
  return modelStatusData.value.filter(
    item =>
      item.modelName.toLowerCase().includes(searchText) ||
      item.ID.toLowerCase().includes(searchText)
  )
})

// 切换设备显示模式
const toggleDeviceMode = () => {
  if (!babylonSceneRef.value || !modelLoaded.value) {
    console.warn("模型未加载，无法切换设备显示模式")
    return
  }

  isWholeDeviceMode.value = !isWholeDeviceMode.value

  try {
    const success = babylonSceneRef.value.toggleWkallVisibility(
      isWholeDeviceMode.value
    )

    if (success) {
      console.log(
        `成功切换到${isWholeDeviceMode.value ? "整机设备" : "内部设备"}模式`
      )
    } else {
      console.warn("切换设备显示模式失败")
      isWholeDeviceMode.value = !isWholeDeviceMode.value
    }
  } catch (error) {
    console.error("切换设备显示模式时发生错误:", error)
    isWholeDeviceMode.value = !isWholeDeviceMode.value
  }
}

// 切换下拉框显示状态
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value

  if (isDropdownOpen.value) {
    setTimeout(() => {
      searchInput.value?.focus()
    }, 100)
  }
}

// 处理下拉框中的模型选择
const handleDropdownModelSelect = async (modelId: string) => {
  if (!modelId) return

  isDropdownOpen.value = false
  selectedModelId.value = modelId
  const model = modelStatusData.value.find(item => item.ID === modelId)
  selectedModelName.value = model ? model.modelName : modelId

  try {
    const result = await flyToModel(modelId)
    if (result.success) {
      console.log(`成功定位到模型: ${modelId}`)
    } else {
      console.warn(`定位到模型失败: ${result.message}`)
    }
  } catch (error) {
    console.error(`定位到模型时出错: ${error}`)
  }
}

// 更新选中模型名称（在语言切换后调用）
const updateSelectedModelName = () => {
  if (selectedModelId.value) {
    const model = modelStatusData.value.find(
      item => item.ID === selectedModelId.value
    )
    selectedModelName.value = model ? model.modelName : selectedModelId.value
  }
}

// 检查当前模型是否有实时参数
const hasRealTimeParams = computed(() => {
  if (!currentModelParams.value) return false
  const modelId = currentModelParams.value.id
  const realTimeData = realTimeParametersData.value[modelId]
  return (
    realTimeData &&
    realTimeData.parameters &&
    Object.keys(realTimeData.parameters).length > 0
  )
})

// 获取当前模型的实时参数
const getRealTimeParams = () => {
  if (!currentModelParams.value) return {}
  const modelId = currentModelParams.value.id
  const realTimeData = realTimeParametersData.value[modelId]
  return realTimeData ? realTimeData.parameters : {}
}

// 当前报警数据
const alarmLogs = computed(() => store.state.notice.alarmInfo)

// 初始化组件的核心逻辑
const initializeComponent = async () => {
  if (isComponentInitialized.value) {
    return // 如果已经初始化过，直接返回
  }

  await fetchAvailableCachedModels()
  await loadAllModels()

  // 模拟报警
  // setAlarmData([
  //   {
  //     alarmCode: "ALARM0007",
  //     alarmContent: "模拟数据：X512主控急停按钮被按下，请注意！",
  //     startTime: "2025-05-28T16:02:39",
  //     durationSeconds: 5733,
  //     moduleCode: "Alarm_Root",
  //     isSparePart: false,
  //     alarmGroup: null,
  //   },
  //   {
  //     alarmCode: "ALARM0005",
  //     alarmContent: "模拟数据：X510放卷急停按钮被按下，请注意！",
  //     startTime: "2025-05-28T16:11:17",
  //     durationSeconds: 5215,
  //     moduleCode: "Alarm_Root",
  //     isSparePart: false,
  //     alarmGroup: null,
  //   },
  //   {
  //     alarmCode: "ALARM0003",
  //     alarmContent: "模拟数据：放卷摆杆角度超上限报警，紧急停机。",
  //     startTime: "2025-05-28T17:28:39",
  //     durationSeconds: 573,
  //     moduleCode: "DownARewindingPushAxis",
  //     isSparePart: false,
  //     alarmGroup: null,
  //   },
  // ])
  watch(
    () => alarmLogs.value,
    val => {
      setAlarmData(val)
    },
    { immediate: true, deep: true }
  )

  if (modelLoaded.value) {
    handleModelLoaded()
  }

  if (babylonSceneRef.value) {
    babylonSceneRef.value.setCenterButtonClickCallback((meshId: string) => {
      console.log(`告警对象中心按钮点击: ${meshId}`)
    })
  }

  document.addEventListener("click", event => {
    const target = event.target as HTMLElement
    if (!target.closest(".model-dropdown")) {
      isDropdownOpen.value = false
    }
  })

  isComponentInitialized.value = true
}

// 启动定时器
const startRefreshInterval = () => {
  if (infoRefreshInterval) return // 如果定时器已经存在，直接返回

  infoRefreshInterval = window.setInterval(() => {
    if (
      activeTab.value === "scene" &&
      !sidebarCollapsed.value &&
      babylonSceneRef.value
    ) {
      refreshSceneInfo()
    }

    if (activeTab.value === "control" && modelLoaded.value) {
      ensureTreeData()
    }
  }, 1000)
}

// 停止定时器
const stopRefreshInterval = () => {
  if (infoRefreshInterval) {
    clearInterval(infoRefreshInterval)
    infoRefreshInterval = null
  }
}

onMounted(async () => {
  await initializeComponent()
  startRefreshInterval()
})

onBeforeUnmount(() => {
  stopRefreshInterval()
})

// KeepAlive 生命周期钩子
onActivated(() => {
  // 组件被激活时，只有在已经初始化的情况下才重新启动定时器
  if (isComponentInitialized.value) {
    startRefreshInterval()
    // 更新语言相关的显示
    updateSelectedModelName()

    // 如果参数标签是显示状态，重新显示
    if (showParameterLabels.value && babylonSceneRef.value) {
      const mockData = babylonSceneRef.value.createMockParameterData()
      babylonSceneRef.value.updateParameterLabels(mockData)
    }
  }
})

onDeactivated(() => {
  // 组件被停用时，停止定时器以节省资源
  stopRefreshInterval()
})

// 参数标签显示状态 - 默认显示
const showParameterLabels = ref(true)

// 切换参数标签显示
const toggleParameterLabels = () => {
  showParameterLabels.value = !showParameterLabels.value

  if (!babylonSceneRef.value) {
    console.warn("场景未初始化，无法切换参数标签")
    return
  }

  if (showParameterLabels.value) {
    // 显示参数标签，使用模拟数据
    const mockData = babylonSceneRef.value.createMockParameterData()
    babylonSceneRef.value.updateParameterLabels(mockData)
    console.log("显示参数标签", mockData)
  } else {
    // 隐藏参数标签
    babylonSceneRef.value.clearAllLabels()
    console.log("隐藏参数标签")
  }
}

// 更新参数标签数据（基于实时参数）
const updateParameterLabelsFromRealTimeData = () => {
  if (!babylonSceneRef.value || !showParameterLabels.value) return

  // 从实时参数数据中提取参数标签数据
  const parameterLabels: ParameterLabelData[] = []

  Object.entries(realTimeParametersData.value).forEach(([modelId, data]) => {
    if (data.parameters) {
      Object.entries(data.parameters).forEach(([paramName, paramValue]) => {
        // 检查参数名是否在我们的映射表中
        const parameterMapping = [
          'fjzljc', 'fjzl', 'fjjj', 'flgdfs', 'zgdfs',
          'ssjA', 'csbccfs', 'ssjzl', 'ssjjj', 'xsjjj',
          'xsjzljc', 'xsjA', 'gdfs1', 'ccfs1'
        ]

        if (parameterMapping.includes(paramName)) {
          parameterLabels.push({
            parameterCode: paramName,
            parameterName: paramName,
            parameterValue: String(paramValue), // 转换为字符串类型
            meshId: modelId
          })
        }
      })
    }
  })

  if (parameterLabels.length > 0) {
    babylonSceneRef.value.updateParameterLabels(parameterLabels)
  }
}

// 监听实时参数数据变化，更新标签
watch(
  () => realTimeParametersData.value,
  () => {
    if (showParameterLabels.value) {
      updateParameterLabelsFromRealTimeData()
    }
  },
  { deep: true }
)

// 暴露给父组件的方法
defineExpose({
  flyToModel,
  setAlarmData,
  clearAlarms,
  toggleLanguage,
  toggleParameterLabels,
})
</script>

<template>
  <main class="home-container">
    <!-- 3D场景显示区域 -->
    <div class="scene-container">
      <BabylonScene
        ref="babylonSceneRef"
        height="100%"
        @icon-click="handleIconClick"
      />
    </div>

    <!-- 模型快速定位下拉框 -->
    <div class="model-dropdown">
      <div class="dropdown-header" @click="toggleDropdown">
        <svg
          class="dropdown-icon"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 1024 1024"
        >
          <path
            fill="currentColor"
            d="M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 0 0 0 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3c7.7-16.2 7.7-35 0-51.5M512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258s279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766m-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176s176-78.8 176-176s-78.8-176-176-176m0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112s112 50.1 112 112s-50.1 112-112 112"
          />
        </svg>
        <span>{{ selectedModelName || translate("selectModel") }}</span>
        <i class="dropdown-arrow" :class="{ open: isDropdownOpen }"></i>
      </div>
      <div class="dropdown-menu" v-if="isDropdownOpen">
        <div class="dropdown-search">
          <input
            type="text"
            v-model="modelSearchText"
            :placeholder="translate('searchModel')"
            @click.stop
            ref="searchInput"
          />
        </div>
        <div class="dropdown-list">
          <div
            v-for="item in filteredModelList"
            :key="item.ID"
            class="dropdown-item"
            :class="{ alarming: item.isAlarming }"
            @click="handleDropdownModelSelect(item.ID)"
          >
            {{ item.modelName }}
            <span v-if="item.isAlarming" class="alarm-indicator"></span>
          </div>
          <div v-if="filteredModelList.length === 0" class="dropdown-empty">
            {{ translate("noModelsFound") }}
          </div>
        </div>
      </div>
    </div>

    <!-- 设备显示模式切换按钮 -->
    <div class="device-mode-toggle">
      <button
        @click="toggleDeviceMode"
        :disabled="!modelLoaded"
        class="device-mode-btn"
      >
        {{
          isWholeDeviceMode
            ? translate("wholeDevice")
            : translate("internalDevice")
        }}
      </button>
    </div>

    <!-- 参数标签切换按钮 -->
    <div class="parameter-labels-toggle" v-if="false">
      <button
        @click="toggleParameterLabels"
        :disabled="!modelLoaded"
        class="parameter-labels-btn"
        :class="{ active: showParameterLabels }"
      >
        {{ showParameterLabels ? "隐藏参数标签" : "显示参数标签" }}
      </button>
    </div>

    <!-- 语言切换按钮 -->
    <div class="language-switch" v-if="false">
      <button @click="() => toggleLanguage()">
        {{
          translate(
            currentLanguage === "zh-CN" ? "switchToEnglish" : "switchToChinese"
          )
        }}
      </button>
    </div>

    <!-- 悬浮控制台 -->
    <div class="floating-sidebar" v-if="false">
      <div class="sidebar-header">
        <h2>模型控制台</h2>
        <button class="toggle-btn" @click="toggleSidebar">
          {{ sidebarCollapsed ? "展开" : "收起" }}
        </button>
      </div>

      <div class="sidebar-content" :class="{ collapsed: sidebarCollapsed }">
        <!-- 信息面板切换 -->
        <div class="tab-container">
          <div
            class="tab"
            :class="{ active: activeTab === 'control' }"
            @click="activeTab = 'control'"
          >
            控制
          </div>
          <div
            class="tab"
            :class="{ active: activeTab === 'scene' }"
            @click="activeTab = 'scene'"
          >
            场景信息
          </div>
          <div
            class="tab"
            :class="{ active: activeTab === 'status' }"
            @click="activeTab = 'status'"
          >
            模型状态
          </div>
        </div>

        <!-- 控制面板 -->
        <div class="panel-container" v-show="activeTab === 'control'">
          <!-- 模型选择 (网络加载) -->
          <div class="control-section">
            <h3>从网络加载模型</h3>
            <div class="model-list">
              <div
                v-for="model in sampleModels"
                :key="model.url"
                class="model-item"
                :class="{
                  active:
                    modelStatus.currentModel === model.url &&
                    !isLoadingFromCache,
                }"
                @click="handleModelSelect(model.url)"
              >
                {{ model.name }}
              </div>
            </div>
          </div>

          <!-- 加载已缓存模型 -->
          <div class="control-section" v-if="availableCachedModels.length > 0">
            <h3>从缓存加载模型</h3>
            <div class="model-list cache-list">
              <div
                v-for="cachedModel in availableCachedModels"
                :key="cachedModel.url"
                class="model-item cached-item"
                :class="{
                  active:
                    modelStatus.currentModel === cachedModel.url &&
                    isLoadingFromCache,
                }"
                @click="loadModelFromCache(cachedModel)"
              >
                {{ cachedModel.name }} ({{
                  new Date(cachedModel.timestamp).toLocaleDateString()
                }})
              </div>
            </div>
          </div>

          <!-- 模型加载与缓存状态 -->
          <div
            v-if="modelStatus.loading || cacheStatusMessage"
            class="status-messages"
          >
            <div
              v-if="modelStatus.loading && !isLoadingFromCache"
              class="status loading"
            >
              网络加载中...
            </div>
            <div v-if="isLoadingFromCache" class="status loading">
              缓存加载中...
            </div>
            <div
              v-if="
                cacheStatusMessage &&
                !modelStatus.loading &&
                !isLoadingFromCache
              "
              class="status info"
            >
              {{ cacheStatusMessage }}
            </div>
            <div v-if="modelStatus.error" class="status error">
              {{ modelStatus.error }}
            </div>
          </div>

          <!-- 对象树和信息面板 -->
          <ObjectTreePanel
            v-if="modelLoaded"
            :mesh-tree="meshTree"
            :selected-object-id="selectedObjectId"
            :selected-object-info="selectedObjectInfo"
            :alarm-target-ids="alarmOptions.targetMeshIds"
            @update:selected-object-id="handleObjectIdUpdate"
            @toggle-alarm-target="handleToggleAlarmTarget"
          />

          <!-- 缓存操作 -->
          <div class="control-section">
            <h3>缓存管理</h3>
            <div class="cache-controls">
              <button
                @click="cacheCurrentModel"
                :disabled="
                  !modelLoaded ||
                  !currentModelDataForCaching ||
                  isLoadingFromCache
                "
              >
                缓存当前(网络)模型
              </button>
              <button @click="clearModelCache" class="clear-cache-btn">
                清除所有缓存
              </button>
            </div>
            <button
              @click="fetchAvailableCachedModels"
              style="margin-top: 10px"
            >
              刷新缓存列表
            </button>
          </div>

          <!-- 视角控制 -->
          <div class="control-section">
            <h3>视角控制</h3>
            <button @click="adjustCamera" :disabled="!modelStatus.currentModel">
              自适应视角
            </button>
          </div>

          <!-- 告警控制面板 -->
          <AlarmControlPanel
            v-if="modelLoaded"
            :alarm-target-ids="alarmOptions.targetMeshIds"
            :mesh-names="meshNameMap"
            :model-loaded="modelLoaded"
            :is-alarming="alarmOptions.isAlarming"
            @remove-alarm-target="removeAlarmTarget"
            @clear-alarm-targets="clearAlarmTargets"
            @update:is-alarming="handleAlarmingUpdate"
            @update:color="handleColorUpdate"
            @update:show-center-button="handleCenterButtonUpdate"
          />
        </div>

        <!-- 场景信息面板 -->
        <div class="panel-container" v-show="activeTab === 'scene'">
          <div class="info-section">
            <h3>场景信息</h3>
            <button class="refresh-btn" @click="refreshSceneInfo">刷新</button>

            <div class="info-content" v-if="sceneInfo">
              <div class="info-item">
                <span class="info-label">网格数量:</span>
                <span class="info-value">{{ sceneInfo?.totalMeshes }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">顶点数量:</span>
                <span class="info-value">{{
                  sceneInfo?.totalVertices?.toLocaleString()
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">面数量:</span>
                <span class="info-value">{{
                  sceneInfo?.totalFaces?.toLocaleString()
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">FPS:</span>
                <span class="info-value">{{ sceneInfo?.fps?.toFixed(0) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">绘制调用:</span>
                <span class="info-value">{{ sceneInfo?.drawCalls }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">活动相机:</span>
                <span class="info-value">{{ sceneInfo?.activeCamera }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">光源数量:</span>
                <span class="info-value">{{ sceneInfo?.activeLights }}</span>
              </div>
            </div>
            <div v-else class="info-empty">请先加载模型</div>
          </div>
        </div>

        <!-- 简化的模型状态控制面板 -->
        <div v-if="activeTab === 'status' && modelLoaded" class="tab-content">
          <div class="status-panel">
            <h3>模型状态监控</h3>

            <!-- 显示Store监听状态 -->
            <div class="auto-monitoring-status">
              <div class="status-indicator active"></div>
              <span>已启用Store数据监听，自动接收报警数据</span>
            </div>

            <div class="status-list" v-if="modelStatusData.length > 0">
              <div
                v-for="item in modelStatusData"
                :key="item.ID"
                class="status-item"
              >
                <span :class="{ alarming: item.isAlarming }">{{
                  item.modelName
                }}</span>
                <div class="status-actions">
                  <div
                    class="status-badge"
                    :class="{
                      alarm: item.isAlarming,
                      normal: !item.isAlarming,
                    }"
                  >
                    {{ item.isAlarming ? "报警中" : "正常" }}
                  </div>
                  <button
                    class="locate-btn"
                    @click="flyToModel(item.ID)"
                    :title="translate('flyToModel')"
                  >
                    <span>定位</span>
                  </button>
                </div>
              </div>
            </div>

            <div v-else class="no-data">暂无模型状态数据</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模型参数弹框 -->
    <div
      class="param-dialog"
      v-if="paramDialogVisible && currentModelParams"
      @click.self="closeParamDialog"
    >
      <div class="param-dialog-content">
        <div class="param-dialog-header">
          <h3>{{ currentModelParams.name }} {{ translate("paramDetails") }}</h3>
          <button class="close-btn" @click="closeParamDialog">×</button>
        </div>
        <div class="param-dialog-body">
          <!-- 报警信息显示 -->
          <div
            class="alarm-info"
            v-if="
              currentModelParams.params.alarmContent ||
              currentModelParams.params.alarmGroup
            "
          >
            <div
              class="alarm-content"
              v-if="currentModelParams.params.alarmContent"
            >
              <span class="alarm-label">{{ translate("alarmContent") }}:</span>
              <span class="alarm-value">{{
                currentModelParams.params.alarmContent
              }}</span>
            </div>
            <div
              class="alarm-group"
              v-if="currentModelParams.params.alarmGroup"
            >
              <span class="alarm-label">{{ translate("alarmGroup") }}:</span>
              <span class="alarm-value alarm-group-value">{{
                currentModelParams.params.alarmGroup
              }}</span>
            </div>
            <div class="alarm-divider"></div>
          </div>

          <!-- 实时参数显示 -->
          <div v-if="hasRealTimeParams" class="realtime-params-section">
            <div class="realtime-params-divider"></div>
            <h4 class="realtime-params-title">
              {{ translate("realTimeParams") }}
            </h4>
            <div
              v-for="(value, paramName) in getRealTimeParams()"
              :key="paramName"
              class="param-item realtime-param"
            >
              <span class="param-label">{{ paramName }}:</span>
              <span class="param-value realtime-value">{{ value }}</span>
            </div>
          </div>

          <!-- 当没有实时参数时显示的提示 -->
          <div
            v-if="!hasRealTimeParams"
            class="no-params-message"
          >
            {{ translate("noRealTimeParams") }}
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<style scoped>
.home-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.scene-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.floating-sidebar {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  background-color: rgba(245, 245, 245, 0.92);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  width: 300px;
  max-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar-header h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.toggle-btn {
  background-color: transparent;
  border: 1px solid #4c84ff;
  color: #4c84ff;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s;
}

.toggle-btn:hover {
  background-color: #4c84ff;
  color: white;
}

.sidebar-content {
  padding: 10px 20px;
  overflow-y: auto;
  max-height: calc(100vh - 120px);
  transition: all 0.3s ease;
}

.sidebar-content.collapsed {
  height: 0;
  padding: 0 20px;
  overflow: hidden;
}

/* 标签式导航 */
.tab-container {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.tab {
  padding: 8px 12px;
  cursor: pointer;
  flex: 1;
  text-align: center;
  font-size: 0.9rem;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.tab.active {
  color: #4c84ff;
  border-bottom: 2px solid #4c84ff;
  font-weight: 500;
}

.panel-container {
  margin-bottom: 10px;
}

h3 {
  font-size: 1rem;
  margin-bottom: 10px;
  color: #555;
}

.control-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.control-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.model-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-item {
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.model-item:hover {
  background-color: rgba(221, 221, 221, 0.9);
}

.model-item.active {
  background-color: #4c84ff;
  color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

button {
  padding: 8px 12px;
  background-color: #4c84ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

button:hover:not(:disabled) {
  background-color: #3a6fd9;
}

button:disabled {
  background-color: rgba(204, 204, 204, 0.7);
  cursor: not-allowed;
}

.status {
  margin-top: 10px;
  padding: 8px;
  border-radius: 4px;
}

.loading {
  background-color: rgba(248, 249, 250, 0.7);
  color: #6c757d;
}

.error {
  background-color: rgba(248, 215, 218, 0.7);
  color: #721c24;
}

/* 场景信息面板样式 */
.info-section {
  position: relative;
}

.refresh-btn {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4px 8px;
  font-size: 0.8rem;
  background-color: #6c757d;
}

.info-content {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
}

.info-item {
  margin-bottom: 8px;
  font-size: 0.9rem;
  display: flex;
  flex-direction: column;
}

.info-label {
  font-weight: 500;
  color: #555;
}

.info-value {
  color: #333;
  word-break: break-word;
}

.info-empty {
  text-align: center;
  padding: 20px 0;
  color: #6c757d;
  font-style: italic;
}

@media (max-width: 768px) {
  .floating-sidebar {
    width: 280px;
    right: 10px;
    top: 10px;
  }

  .sidebar-header {
    padding: 12px 15px;
  }

  .sidebar-content {
    padding: 8px 15px;
  }
}

.status-messages {
  margin-top: 10px;
}

.status.info {
  background-color: rgba(23, 162, 184, 0.1);
  color: #004085;
  border: 1px solid rgba(23, 162, 184, 0.2);
}

.cache-controls {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.clear-cache-btn {
  background-color: #dc3545;
  /* 红色表示清除操作 */
}

.clear-cache-btn:hover:not(:disabled) {
  background-color: #c82333;
}

.cache-list .model-item.cached-item {
  background-color: rgba(23, 162, 184, 0.05);
  /* 淡蓝色背景提示是缓存项 */
  border-left: 3px solid #17a2b8;
}

.cache-list .model-item.cached-item:hover {
  background-color: rgba(23, 162, 184, 0.15);
}

.cache-list .model-item.cached-item.active {
  background-color: #17a2b8;
  /* 激活时用深一点的蓝色 */
  color: white;
}

.status-panel {
  padding: 10px;
}

.status-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.status-controls button {
  flex: 1;
  padding: 8px;
  border: none;
  border-radius: 4px;
  background-color: #4c84ff;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
}

.status-controls button:hover {
  background-color: #3a6fd9;
}

.status-controls button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.status-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
}

.status-item:last-child {
  border-bottom: none;
}

.status-item .alarming {
  color: #ff4d4f;
  font-weight: bold;
}

.status-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}

.status-actions button {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.locate-btn {
  background-color: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: all 0.3s;
}

.locate-btn:hover {
  background-color: #40a9ff;
  transform: scale(1.05);
}

.alarm-btn {
  background-color: #ff4d4f;
  color: white;
}

.normal-btn {
  background-color: #1890ff;
  color: white;
}

.normal-btn:hover {
  background-color: #40a9ff;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #999;
}

.simulate-btn {
  background-color: #722ed1;
}

.simulate-btn:hover {
  background-color: #5b21b6;
}

.realtime-control {
  display: flex;
  align-items: center;
  margin: 15px 0;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.realtime-control button {
  background-color: #52c41a;
  margin-right: 10px;
}

.realtime-control button:hover {
  background-color: #389e0d;
}

.realtime-control button.active {
  background-color: #f5222d;
}

.realtime-control button.active:hover {
  background-color: #cf1322;
}

.realtime-status {
  font-size: 0.9rem;
  color: #8c8c8c;
}

.realtime-status.active {
  color: #52c41a;
  font-weight: bold;
}

.auto-monitoring-status {
  display: flex;
  align-items: center;
  margin: 15px 0;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #8c8c8c;
  margin-right: 10px;
  position: relative;
}

.status-indicator.active {
  background-color: #52c41a;
  box-shadow: 0 0 0 4px rgba(82, 196, 26, 0.2);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4);
  }

  70% {
    box-shadow: 0 0 0 6px rgba(82, 196, 26, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

.status-badge {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.normal {
  background-color: #52c41a;
  color: white;
}

.status-badge.alarm {
  background-color: #f5222d;
  color: white;
  animation: blink 1s infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }

  100% {
    opacity: 1;
  }
}

/* 参数弹框样式 */
.param-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.param-dialog-content {
  width: 400px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: dialog-fade-in 0.3s ease;
}

@keyframes dialog-fade-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.param-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.param-dialog-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #999;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.close-btn:hover {
  color: #333;
}

.param-dialog-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.param-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.param-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.param-label {
  font-weight: 500;
  color: #666;
}

.param-value {
  color: #333;
  font-family: "Courier New", Courier, monospace;
}

.status-warning {
  color: #ff4d4f;
  font-weight: bold;
}

/* 实时参数样式 */
.realtime-params-section {
  margin-top: 15px;
}

.realtime-params-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 15px 0 10px 0;
}

.realtime-params-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 10px;
}

.realtime-param {
  background-color: rgba(24, 144, 255, 0.05);
  border-left: 3px solid #1890ff;
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 0 4px 4px 0;
}

.realtime-value {
  color: #1890ff;
  font-weight: 600;
}

.no-params-message {
  text-align: center;
  padding: 20px;
  color: #999;
  font-style: italic;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  margin-top: 15px;
}

/* 语言切换按钮样式 */
.language-switch {
  position: fixed;
  top: 20px;
  left: 460px;
  z-index: 100;
}

.language-switch button {
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #4c84ff;
  color: #4c84ff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.language-switch button:hover {
  background-color: #4c84ff;
  color: white;
}

.alarm-info {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #fff8f8;
  border-left: 3px solid #ff4d4f;
  border-radius: 4px;
}

.alarm-content {
  margin-bottom: 8px;
}

.alarm-label {
  font-weight: 500;
  color: #666;
  margin-right: 5px;
}

.alarm-value {
  color: #ff4d4f;
  font-weight: 500;
}

.alarm-group-value {
  display: inline-block;
  padding: 2px 8px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 10px;
  font-size: 0.85em;
}

.alarm-divider {
  height: 1px;
  background-color: #ffccc7;
  margin: 10px 0;
}

/* 模型下拉框样式 - 深色主题 */
.model-dropdown {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 280px;
  z-index: 100;
  font-size: 14px;
  font-family: "Microsoft YaHei", Arial, sans-serif;
}

.dropdown-icon {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  color: #3a8ee6;
  vertical-align: middle;
}

@font-face {
  font-family: "iconfont";
  src: url("//at.alicdn.com/t/font_1234567_abcdefg.woff") format("woff");
}

.dropdown-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background-color: rgba(18, 19, 37, 0.95);
  border: 1px solid #252b3d;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  color: #e6e6e6;
  /* 确保与设备显示模式按钮高度一致 */
  height: 40px;
  box-sizing: border-box;
  line-height: 20px;
}

.dropdown-header span {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-arrow {
  border: solid #6f7d9f;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(45deg);
  transition: transform 0.3s;
  margin-left: 10px;
}

.dropdown-arrow.open {
  transform: rotate(-135deg);
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  width: 100%;
  background-color: rgba(18, 19, 37, 0.95);
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  max-height: 400px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: dropdown-fade 0.2s ease;
  border: 1px solid #252b3d;
}

@keyframes dropdown-fade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-search {
  padding: 10px;
  border-bottom: 1px solid #252b3d;
}

.dropdown-search input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #252b3d;
  border-radius: 4px;
  font-size: 14px;
  background-color: rgba(42, 45, 65, 0.8);
  color: #e6e6e6;
}

.dropdown-search input::placeholder {
  color: #6f7d9f;
}

.dropdown-search input:focus {
  outline: none;
  border-color: #3a8ee6;
  box-shadow: 0 0 0 1px rgba(58, 142, 230, 0.3);
}

.dropdown-list {
  max-height: 350px;
  overflow-y: auto;
}

.dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.dropdown-list::-webkit-scrollbar-track {
  background: rgba(42, 45, 65, 0.8);
}

.dropdown-list::-webkit-scrollbar-thumb {
  background-color: #3a8ee6;
  border-radius: 3px;
}

.dropdown-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(37, 43, 61, 0.5);
  color: #e6e6e6;
  position: relative;
}

.dropdown-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 10%;
  height: 80%;
  width: 3px;
  background-color: transparent;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: rgba(42, 45, 65, 0.8);
}

.dropdown-item:hover::before {
  background-color: #3a8ee6;
}

.dropdown-item.alarming {
  color: #ff4d4f;
  font-weight: 500;
}

.dropdown-item.alarming::before {
  background-color: #ff4d4f;
}

.dropdown-empty {
  padding: 15px;
  text-align: center;
  color: #6f7d9f;
  font-style: italic;
}

.alarm-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ff4d4f;
  display: inline-block;
  margin-left: 8px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.4);
  }

  70% {
    box-shadow: 0 0 0 6px rgba(255, 77, 79, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}

/* 设备显示模式切换按钮样式 */
.device-mode-toggle {
  position: absolute;
  top: 20px;
  left: 320px;
  z-index: 100;
}

.device-mode-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(18, 19, 37, 0.95);
  border: 1px solid #252b3d;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  color: #e6e6e6;
  font-size: 14px;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  transition: all 0.3s ease;
  position: relative;
  /* 确保与下拉框高度一致 */
  height: 40px;
  /* 保持内容垂直居中 */
  line-height: 20px;
  /* 保持按钮宽度合适 */
  min-width: 100px;
}

.device-mode-btn::before {
  content: "";
  position: absolute;
  left: 0;
  top: 10%;
  height: 80%;
  width: 3px;
  background-color: transparent;
  transition: background-color 0.2s;
}

.device-mode-btn:hover {
  background-color: rgba(42, 45, 65, 0.8);
}

.device-mode-btn:hover::before {
  background-color: #3a8ee6;
}

.device-mode-btn:disabled {
  background-color: rgba(37, 43, 61, 0.5);
  color: #6f7d9f;
  cursor: not-allowed;
}

.device-mode-btn:disabled::before {
  background-color: transparent;
}

/* 参数标签切换按钮样式 */
.parameter-labels-toggle {
  position: absolute;
  top: 20px;
  left: 450px;
  z-index: 100;
}

.parameter-labels-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(18, 19, 37, 0.95);
  border: 1px solid #252b3d;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  color: #e6e6e6;
  font-size: 14px;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  transition: all 0.3s ease;
  position: relative;
  height: 40px;
  line-height: 20px;
  min-width: 120px;
}

.parameter-labels-btn::before {
  content: "";
  position: absolute;
  left: 0;
  top: 10%;
  height: 80%;
  width: 3px;
  background-color: transparent;
  transition: background-color 0.2s;
}

.parameter-labels-btn:hover {
  background-color: rgba(42, 45, 65, 0.8);
}

.parameter-labels-btn:hover::before {
  background-color: #3a8ee6;
}

.parameter-labels-btn.active {
  background-color: rgba(58, 142, 230, 0.2);
  border-color: #3a8ee6;
  color: #3a8ee6;
}

.parameter-labels-btn.active::before {
  background-color: #3a8ee6;
}

.parameter-labels-btn:disabled {
  background-color: rgba(37, 43, 61, 0.5);
  color: #6f7d9f;
  cursor: not-allowed;
}

.parameter-labels-btn:disabled::before {
  background-color: transparent;
}
</style>
