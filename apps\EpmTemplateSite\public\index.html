<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>HMS.ico" />
    <link rel="stylesheet" href="./static/modules/element-plus/index.css" />
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <script>
      let vueScript =
        `<%= process.env.NODE_ENV %>` === "production"
          ? "./static/modules/vue/vue.global.prod.js"
          : "./static/modules/vue/vue.global.js"
      document.write('<script src="' + vueScript + '"><\/script>')
    </script>
    <!-- built files will be auto injected -->
    <script src="./static/modules/echarts/echarts.min.js"></script>
    <script src="./static/modules/echarts-liquidfill/echarts-liquidfill.min.js"></script>
    <script src="./static/modules/element-plus/index.full.min.js"></script>
    <script src="./static/modules/element-plus/index.iife.min.js"></script>
  </body>
</html>
