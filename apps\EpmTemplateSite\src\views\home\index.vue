<template>
  <div class="h-layout">
    <div class="h-body">
      <router-view v-slot="{ Component }">
        <keep-alive :include="['HomeIndex']">
          <component :is="Component" />
        </keep-alive>
      </router-view>
    </div>
    <footLayout>
      <HomeFooter />
    </footLayout>
  </div>
</template>

<script setup>
import footLayout from "@/components/hmx-footer/index.vue"
import HomeFooter from "./components/homeFooter.vue"
</script>
<style scoped lang="scss">
.h-layout {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--layout-background);
  .h-body {
    width: 100%;
    height: calc(100% - 100px);
    padding: 10px;
  }
}
</style>
