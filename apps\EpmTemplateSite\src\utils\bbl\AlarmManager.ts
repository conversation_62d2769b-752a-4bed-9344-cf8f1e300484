import * as BABYLON from 'babylonjs';
import * as G<PERSON> from 'babylonjs-gui';
import { SceneManager } from './SceneManager';
import { ModelLoader } from './ModelLoader';

/**
 * 告警状态类型定义
 */
export type AlarmStatus = {
  meshIds: string[];  // 告警目标网格ID数组
  isAlarming: boolean;  // 是否处于告警状态
  color: {  // 告警颜色
    r: number;
    g: number;
    b: number;
  };
  showCenterButton: boolean;  // 是否显示中心操作按钮
};

/**
 * 告警管理器
 * 负责处理模型告警效果和交互
 */
export class AlarmManager {
  private sceneManager: SceneManager;
  private modelLoader: ModelLoader;
  private alarmMeshes: Map<string, BABYLON.AbstractMesh> = new Map();
  private centerButtons: Map<string, GUI.Control> = new Map();
  private expandedButtons: Map<string, GUI.Control[]> = new Map(); // 存储扇形展开的按钮
  private advancedTexture: GUI.AdvancedDynamicTexture | null = null;
  private centerButtonListeners: ((meshId: string) => void)[] = [];
  private isMenuExpanded: Map<string, boolean> = new Map(); // 记录每个按钮菜单是否展开

  /**
   * 构造函数
   * @param sceneManager 场景管理器实例
   * @param modelLoader 模型加载器实例
   */
  constructor(sceneManager: SceneManager, modelLoader: ModelLoader) {
    this.sceneManager = sceneManager;
    this.modelLoader = modelLoader;
    this.initGUI();
    this.setupSceneCallbacks();
  }

  /**
   * 初始化GUI系统
   */
  private initGUI(): void {
    const scene = this.sceneManager.getScene();
    if (!scene) return;

    // 创建全屏GUI
    this.advancedTexture = GUI.AdvancedDynamicTexture.CreateFullscreenUI("alarmUI", true, scene);
  }

  /**
   * 设置场景回调
   */
  private setupSceneCallbacks(): void {
    const scene = this.sceneManager.getScene();
    if (!scene) return;
  }

  /**
   * 设置模型告警状态
   * @param alarmStatus 告警状态配置
   */
  public setModelAlarm(alarmStatus: AlarmStatus): void {
    const scene = this.sceneManager.getScene();
    const highlightLayer = this.sceneManager.getHighlightLayer();

    if (!scene || !highlightLayer) return;

    const { meshIds, isAlarming, color, showCenterButton } = alarmStatus;

    // 如果没有指定网格ID，返回
    if (!meshIds || meshIds.length === 0) return;

    // 移除所有之前的高亮效果
    this.clearAllAlarms();

    // 如果不处于告警状态，直接返回
    if (!isAlarming) return;

    // 创建告警颜色
    const alarmColor = new BABYLON.Color3(color.r, color.g, color.b);

    // 为每个目标网格添加高亮效果
    meshIds.forEach(meshId => {
      const targetMesh = this.modelLoader.findMeshById(meshId);

      if (targetMesh) {
        // 将目标网格添加到告警网格映射中
        this.alarmMeshes.set(meshId, targetMesh);

        // 添加高亮效果 - 使用明确的类型断言
        highlightLayer.addMesh(targetMesh as BABYLON.Mesh, alarmColor);

        // 如果需要显示中心按钮，添加按钮
        if (showCenterButton) {
          this.addCenterButton(targetMesh);
        }
      }
    });
  }

  /**
   * 清除所有告警效果
   */
  public clearAllAlarms(): void {
    const highlightLayer = this.sceneManager.getHighlightLayer();
    if (!highlightLayer) return;

    // 移除所有高亮效果
    this.alarmMeshes.forEach(mesh => {
      // 使用明确的类型断言
      highlightLayer.removeMesh(mesh as BABYLON.Mesh);
    });

    // 清空告警网格映射
    this.alarmMeshes.clear();

    // 移除所有中心按钮
    this.removeCenterButtons();
  }

  /**
   * 在网格中心添加操作按钮
   * @param mesh 目标网格
   */
  private async addCenterButton(mesh: BABYLON.AbstractMesh): Promise<void> {
    if (!this.advancedTexture || !mesh) return;

    const scene = this.sceneManager.getScene();
    if (!scene) return;

    try {
      // console.log(`添加中心按钮到网格: ${mesh.id}`);

      // 创建中心按钮容器
      const buttonContainer = new GUI.Container(`buttonContainer_${mesh.id}`);
      buttonContainer.width = "200px"; // 容器尺寸
      buttonContainer.height = "200px"; // 容器尺寸
      buttonContainer.isPointerBlocker = false; // 确保不会阻挡指针事件
      buttonContainer.isFocusInvisible = true; // 不会获取焦点
      buttonContainer.zIndex = 10 + this.centerButtons.size; // 确保每个容器有不同的zIndex

      // 设置按钮容器中的点击检测，确保只有按钮本身才响应点击事件
      buttonContainer.isHitTestVisible = false; // 容器本身不接收点击

      // 添加按钮到主UI并链接到网格
      this.advancedTexture.addControl(buttonContainer);
      buttonContainer.linkWithMesh(mesh);

      // 创建主按钮（Home按钮）
      const mainButtonSize = 54;
      const mainButton = GUI.Button.CreateImageButton(
        `homeButton_${mesh.id}`,  // 名称
        "打开",                   // 文本
        "./static/bbl_file/imgs/btn/home_btn.svg" // 图像URL
      );

      // 设置主按钮样式和属性
      mainButton.width = `${mainButtonSize}px`;
      mainButton.height = `${mainButtonSize + 30}px`; // 为文本留出空间
      mainButton.thickness = 0;
      mainButton.color = "white";
      mainButton.background = "transparent";
      mainButton.horizontalAlignment = GUI.Control.HORIZONTAL_ALIGNMENT_CENTER;
      mainButton.verticalAlignment = GUI.Control.VERTICAL_ALIGNMENT_CENTER;
      mainButton.isPointerBlocker = true; // 确保可以接收点击事件
      mainButton.isHitTestVisible = true; // 确保可以接收点击检测

      // 调整主按钮的图像和文本样式
      if (mainButton.image) {
        mainButton.image.width = "80%";  // 调整为更合适的宽度比例
        mainButton.image.height = "65%"; // 调整高度，留更多空间给文本
        mainButton.image.stretch = GUI.Image.STRETCH_UNIFORM;
        mainButton.image.horizontalAlignment = GUI.Control.HORIZONTAL_ALIGNMENT_CENTER;
        mainButton.image.verticalAlignment = GUI.Control.VERTICAL_ALIGNMENT_TOP;
        mainButton.image.top = "5px";    // 从顶部添加少量间距
      }

      if (mainButton.textBlock) {
        mainButton.textBlock.fontSize = 12;  // 稍微增大字体
        mainButton.textBlock.color = "white";
        mainButton.textBlock.fontFamily = "Arial, Helvetica, sans-serif";
        mainButton.textBlock.textWrapping = true;
        mainButton.textBlock.resizeToFit = false;
        mainButton.textBlock.outlineWidth = 1;  // 增加描边宽度
        mainButton.textBlock.outlineColor = "rgba(0, 0, 0, 0.6)"; // 更深的描边颜色
        mainButton.textBlock.shadowBlur = 3;  // 增加阴影模糊度
        mainButton.textBlock.shadowColor = "black";
        mainButton.textBlock.shadowOffsetX = 1;
        mainButton.textBlock.shadowOffsetY = 1;
        // 设置文本位置在图像下方
        mainButton.textBlock.verticalAlignment = GUI.Control.VERTICAL_ALIGNMENT_BOTTOM;
        mainButton.textBlock.horizontalAlignment = GUI.Control.HORIZONTAL_ALIGNMENT_CENTER;
        mainButton.textBlock.top = "15px"; // 微调底部距离
        mainButton.textBlock.textHorizontalAlignment = GUI.Control.HORIZONTAL_ALIGNMENT_CENTER; // 确保文本居中
        mainButton.textBlock.width = "100%"; // 使用全宽，确保文本居中
        mainButton.textBlock.left = "0px"; // 确保没有水平偏移
        mainButton.textBlock.paddingLeft = "0px"; // 清除左边距
        mainButton.textBlock.paddingRight = "0px"; // 清除右边距
      }

      // 添加主按钮到容器
      buttonContainer.addControl(mainButton);

      // 初始化扇形按钮数组
      const subButtons: GUI.Button[] = [];

      // 创建5个子按钮但初始不可见
      const buttonInfos = [
        { icon: "./static/bbl_file/imgs/btn/btn_01.svg", tooltip: "定位" },
        { icon: "./static/bbl_file/imgs/btn/btn_02.svg", tooltip: "视频" },
        { icon: "./static/bbl_file/imgs/btn/btn_03.svg", tooltip: "详情" },
        { icon: "./static/bbl_file/imgs/btn/btn_04.svg", tooltip: "工具" },
        { icon: "./static/bbl_file/imgs/btn/btn_05.svg", tooltip: "下钻" }
      ];

      for (let i = 0; i < buttonInfos.length; i++) {
        const subButtonSize = 44;
        const subButton = GUI.Button.CreateImageButton(
          `subButton_${i}_${mesh.id}`,  // 名称
          buttonInfos[i].tooltip,        // 文本
          buttonInfos[i].icon            // 图像URL
        );

        // 设置子按钮样式和属性
        subButton.width = `${subButtonSize}px`;
        subButton.height = `${subButtonSize + 30}px`; // 为文本留出空间
        subButton.thickness = 0;
        subButton.color = "white";
        subButton.background = "transparent";
        subButton.isVisible = false;
        subButton.isPointerBlocker = true; // 确保可以接收点击事件
        subButton.isHitTestVisible = true; // 确保可以接收点击检测

        // 调整子按钮的图像和文本样式
        if (subButton.image) {
          subButton.image.width = "80%";  // 与主按钮保持一致
          subButton.image.height = "65%"; // 与主按钮保持一致
          subButton.image.stretch = GUI.Image.STRETCH_UNIFORM;
          subButton.image.horizontalAlignment = GUI.Control.HORIZONTAL_ALIGNMENT_CENTER;
          subButton.image.verticalAlignment = GUI.Control.VERTICAL_ALIGNMENT_TOP;
          subButton.image.top = "5px";   // 与主按钮保持一致
        }

        if (subButton.textBlock) {
          // 根据文本长度自动调整字体大小
          const fontSize = buttonInfos[i].tooltip.length > 3 ? 10 : 11;

          subButton.textBlock.fontSize = fontSize;
          subButton.textBlock.color = "white";
          subButton.textBlock.fontFamily = "Arial, Helvetica, sans-serif";
          subButton.textBlock.textWrapping = true;
          subButton.textBlock.resizeToFit = false;
          subButton.textBlock.outlineWidth = 1;  // 与主按钮保持一致
          subButton.textBlock.outlineColor = "rgba(0, 0, 0, 0.6)"; // 与主按钮保持一致
          subButton.textBlock.shadowBlur = 3;  // 与主按钮保持一致
          subButton.textBlock.shadowColor = "black";
          subButton.textBlock.shadowOffsetX = 1;
          subButton.textBlock.shadowOffsetY = 1;
          // 设置文本位置在图像下方
          subButton.textBlock.verticalAlignment = GUI.Control.VERTICAL_ALIGNMENT_BOTTOM;
          subButton.textBlock.horizontalAlignment = GUI.Control.HORIZONTAL_ALIGNMENT_CENTER;
          subButton.textBlock.top = "15px"; // 与主按钮保持一致
          subButton.textBlock.textHorizontalAlignment = GUI.Control.HORIZONTAL_ALIGNMENT_CENTER;
          subButton.textBlock.width = "100%"; // 使用全宽，确保文本居中
          subButton.textBlock.left = "0px"; // 确保没有水平偏移
          subButton.textBlock.paddingLeft = "0px"; // 清除左边距
          subButton.textBlock.paddingRight = "0px"; // 清除右边距
        }

        buttonContainer.addControl(subButton);
        subButtons.push(subButton);

        // 为每个子按钮添加点击事件
        subButton.onPointerClickObservable.add(() => {
          // console.log(`子按钮 ${i + 1} (${buttonInfos[i].tooltip}) 被点击`);
          this.handleCenterButtonClick(mesh.id, i + 1);
        });
      }

      // 存储扇形按钮
      this.expandedButtons.set(mesh.id, subButtons);
      this.isMenuExpanded.set(mesh.id, false);

      // 为主按钮添加点击事件（展开/收起扇形菜单）
      mainButton.onPointerClickObservable.add(() => {
        const isExpanded = this.isMenuExpanded.get(mesh.id) || false;

        if (isExpanded) {
          // 收起菜单
          this.collapseMenu(mesh.id, subButtons);
        } else {
          // 展开菜单
          this.expandMenu(mesh.id, subButtons);
        }

        this.isMenuExpanded.set(mesh.id, !isExpanded);
      });

      // 添加悬停效果
      mainButton.onPointerEnterObservable.add(() => {
        if (mainButton.image) {
          mainButton.image.alpha = 0.8;
        }
      });

      mainButton.onPointerOutObservable.add(() => {
        if (mainButton.image) {
          mainButton.image.alpha = 1.0;
        }
      });

      // 为子按钮添加悬停效果
      subButtons.forEach((button) => {
        button.onPointerEnterObservable.add(() => {
          if (button.image) {
            button.image.alpha = 0.8;
          }
        });

        button.onPointerOutObservable.add(() => {
          if (button.image) {
            button.image.alpha = 1.0;
          }
        });
      });

      // 保存主按钮引用
      this.centerButtons.set(mesh.id, buttonContainer);

      // console.log(`成功添加中心按钮到网格: ${mesh.id}`);
    } catch (error) {
      console.error(`添加中心按钮失败:`, error);
    }
  }

  /**
   * 展开扇形菜单
   * @param meshId 网格ID
   * @param buttons 子按钮数组
   */
  private expandMenu(meshId: string, buttons: GUI.Button[]): void {
    // 优化扇形布局为围绕中心的环形菜单
    const totalButtons = buttons.length;

    // 计算完整圆环的分布和间距 - 稍微调整角度范围，避免使按钮太靠近
    const fullCircleMode = true; // 使用完整圆环布局
    const startAngle = fullCircleMode ? -Math.PI / 2 : -Math.PI * 0.3 - Math.PI * 0.6 / 2;
    const endAngle = fullCircleMode ? 2 * Math.PI - Math.PI / 2 : -Math.PI * 0.3 + Math.PI * 0.6 / 2;
    const arcRange = endAngle - startAngle;
    const angleStep = arcRange / totalButtons;

    // 增大半径，确保按钮之间有足够间距
    const radius = 55; // 增大半径，让按钮之间有更多空间，也给文本留出更多空间

    // 更新主按钮的文本为"关闭"
    const mainButtonId = `homeButton_${meshId}`;
    const buttonContainer = this.centerButtons.get(meshId);
    if (buttonContainer && buttonContainer instanceof GUI.Container) {
      // 遍历容器中的控件，找到主按钮
      for (let i = 0; i < buttonContainer.children.length; i++) {
        const control = buttonContainer.children[i];
        if (control.name === mainButtonId && control instanceof GUI.Button) {
          // 找到主按钮后，查找其中的文本块
          this.updateButtonText(control, "关闭");
          break;
        }
      }
    }

    // 以动画方式展开按钮
    buttons.forEach((button, index) => {
      // 计算角度 - 围绕中心均匀分布，使用扇区中心位置
      const angle = startAngle + angleStep * (index + 0.5); // 加0.5使按钮位于扇区中心

      // 计算位置
      const x = radius * Math.cos(angle);
      const y = radius * Math.sin(angle);

      // 应用位置
      button.left = `${x}px`;
      button.top = `${y}px`;
      button.isVisible = true;

      // 保存按钮的中心位置信息，用于布局
      button.metadata = {
        ...(button.metadata || {}),
        position: { x, y }
      };

      // 创建弹出动画 - 从中心点向外弹出效果
      button.alpha = 0;
      button.scaleX = 0;
      button.scaleY = 0;

      // 创建alpha动画
      const alphaAnim = new BABYLON.Animation(
        `buttonAlphaAnim_${index}`,
        "alpha",
        30,
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      alphaAnim.setKeys([
        { frame: 0, value: 0 },
        { frame: 10, value: 1 }
      ]);

      // 创建弹出动画 - 使用弹性效果
      const easeFunc = new BABYLON.CubicEase(); // 改用更平滑的缓动函数
      easeFunc.setEasingMode(BABYLON.EasingFunction.EASINGMODE_EASEOUT);

      // 创建缩放动画
      const scaleAnim = new BABYLON.Animation(
        `buttonScaleAnim_${index}`,
        "scaleX",
        30,
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      scaleAnim.setKeys([
        { frame: 0, value: 0 },
        { frame: 15, value: 1.0 }
      ]);
      scaleAnim.setEasingFunction(easeFunc);

      const scaleYAnim = new BABYLON.Animation(
        `buttonScaleYAnim_${index}`,
        "scaleY",
        30,
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      scaleYAnim.setKeys([
        { frame: 0, value: 0 },
        { frame: 15, value: 1.0 }
      ]);
      scaleYAnim.setEasingFunction(easeFunc);

      // 应用所有动画
      button.animations = [];
      button.animations.push(alphaAnim);
      button.animations.push(scaleAnim);
      button.animations.push(scaleYAnim);

      // 初始位置为中心
      button.left = "0px";
      button.top = "0px";

      // 创建位置动画
      const leftAnim = new BABYLON.Animation(
        `buttonLeftAnim_${index}`,
        "left",
        30,
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      leftAnim.setKeys([
        { frame: 0, value: 0 },
        { frame: 15, value: x }
      ]);
      leftAnim.setEasingFunction(easeFunc);

      const topAnim = new BABYLON.Animation(
        `buttonTopAnim_${index}`,
        "top",
        30,
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      topAnim.setKeys([
        { frame: 0, value: 0 },
        { frame: 15, value: y }
      ]);
      topAnim.setEasingFunction(easeFunc);

      button.animations.push(leftAnim);
      button.animations.push(topAnim);

      // 设定动画时间偏移，创建环形展开效果
      const delay = index * 2;

      const scene = this.sceneManager.getScene();
      if (scene) {
        setTimeout(() => {
          scene.beginAnimation(button, 0, 15, false);
        }, delay * 30); // 依次延迟展开
      }
    });

    // console.log(`展开网格${meshId}的菜单`);
  }

  /**
   * 收起扇形菜单
   * @param _meshId 网格ID
   * @param buttons 子按钮数组
   */
  private collapseMenu(_meshId: string, buttons: GUI.Button[]): void {
    // 更新主按钮的文本为"打开"
    const mainButtonId = `homeButton_${_meshId}`;
    const buttonContainer = this.centerButtons.get(_meshId);
    if (buttonContainer && buttonContainer instanceof GUI.Container) {
      // 遍历容器中的控件，找到主按钮
      for (let i = 0; i < buttonContainer.children.length; i++) {
        const control = buttonContainer.children[i];
        if (control.name === mainButtonId && control instanceof GUI.Button) {
          // 找到主按钮后，更新其文本
          this.updateButtonText(control, "打开");
          break;
        }
      }
    }

    buttons.forEach((button, index) => {
      // 保存原始位置，用于动画
      const originalLeft = parseFloat(button.left.toString());
      const originalTop = parseFloat(button.top.toString());

      // 创建收缩动画
      const alphaAnim = new BABYLON.Animation(
        `buttonCollapseAlphaAnim_${index}`,
        "alpha",
        30,
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      alphaAnim.setKeys([
        { frame: 0, value: 1 },
        { frame: 10, value: 0 }
      ]);

      // 创建缩放动画 - 快速收缩效果
      const scaleAnim = new BABYLON.Animation(
        `buttonCollapseScaleAnim_${index}`,
        "scaleX",
        30,
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      scaleAnim.setKeys([
        { frame: 0, value: 1.0 },
        { frame: 10, value: 0 }
      ]);

      const scaleYAnim = new BABYLON.Animation(
        `buttonCollapseScaleYAnim_${index}`,
        "scaleY",
        30,
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      scaleYAnim.setKeys([
        { frame: 0, value: 1.0 },
        { frame: 10, value: 0 }
      ]);

      // 创建向中心收缩的位置动画，使用缓动函数
      const easeFunc = new BABYLON.QuadraticEase();
      easeFunc.setEasingMode(BABYLON.EasingFunction.EASINGMODE_EASEIN);

      const leftAnim = new BABYLON.Animation(
        `buttonCollapseLeftAnim_${index}`,
        "left",
        30,
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      leftAnim.setKeys([
        { frame: 0, value: originalLeft },
        { frame: 10, value: 0 }
      ]);
      leftAnim.setEasingFunction(easeFunc);

      const topAnim = new BABYLON.Animation(
        `buttonCollapseTopAnim_${index}`,
        "top",
        30,
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );
      topAnim.setKeys([
        { frame: 0, value: originalTop },
        { frame: 10, value: 0 }
      ]);
      topAnim.setEasingFunction(easeFunc);

      // 应用所有动画
      button.animations = [];
      button.animations.push(alphaAnim);
      button.animations.push(scaleAnim);
      button.animations.push(scaleYAnim);
      button.animations.push(leftAnim);
      button.animations.push(topAnim);

      // 设定动画时间偏移，创建连锁收起效果
      const delay = index * 2;

      const scene = this.sceneManager.getScene();
      if (scene) {
        setTimeout(() => {
          scene.beginAnimation(button, 0, 10, false, 1, () => {
            button.isVisible = false;
            // 重置位置，以便下次展开
            button.left = "0px";
            button.top = "0px";
          });
        }, delay * 30); // 依次延迟收起
      }
    });

    // console.log(`收起网格${_meshId}的菜单`);
  }

  /**
   * 移除所有中心按钮
   */
  private removeCenterButtons(): void {
    if (!this.advancedTexture) return;

    // console.log(`开始移除所有中心按钮...`);

    // 移除所有按钮
    this.centerButtons.forEach((button) => {
      // 移除按钮本身
      // console.log(`移除按钮: ${button.name}`);
      try {
        this.advancedTexture?.removeControl(button);
      } catch (e) {
        console.error(`移除按钮出错:`, e);
      }
    });

    // 清空按钮集合
    this.centerButtons.clear();
    this.expandedButtons.clear();

    // console.log(`成功移除所有中心按钮`);
  }

  /**
   * 处理中心按钮点击事件
   * @param meshId 网格ID
   * @param buttonIndex 按钮索引（若是子按钮）
   */
  private handleCenterButtonClick(meshId: string, buttonIndex?: number): void {
    // console.log(`中心按钮点击: ${meshId}${buttonIndex ? `, 子按钮: ${buttonIndex}` : ''}`);

    // 通知所有监听器
    this.centerButtonListeners.forEach(listener => {
      try {
        listener(meshId);
      } catch (error) {
        console.error('调用按钮点击监听器时出错:', error);
      }
    });
  }

  /**
   * 添加中心按钮点击事件监听器
   * @param callback 回调函数
   */
  public addCenterButtonClickListener(callback: (meshId: string) => void): void {
    // 将回调添加到监听器列表
    this.centerButtonListeners.push(callback);

    const scene = this.sceneManager.getScene();
    if (!scene) return;

    scene.onPointerObservable.add((eventData) => {
      if (eventData.type === BABYLON.PointerEventTypes.POINTERPICK &&
        eventData.pickInfo?.hit &&
        eventData.pickInfo.pickedMesh) {
        const meshId = eventData.pickInfo.pickedMesh.id;
        if (this.centerButtons.has(meshId)) {
          callback(meshId);
        }
      }
    });
  }

  /**
   * 更新按钮的文本内容
   * @param button 要更新的按钮
   * @param text 新的文本内容
   */
  private updateButtonText(button: GUI.Button, text: string): void {
    // 递归查找按钮中的文本块
    const findAndUpdateText = (control: GUI.Control): boolean => {
      // 如果是文本块，直接更新文本
      if (control instanceof GUI.TextBlock) {
        control.text = text;
        return true;
      }

      // 如果是容器，递归查找其子元素
      if ('children' in control && Array.isArray(control.children)) {
        for (let i = 0; i < control.children.length; i++) {
          if (findAndUpdateText(control.children[i])) {
            return true;
          }
        }
      }

      return false;
    };

    // 开始在按钮中查找文本块
    findAndUpdateText(button);
  }
}
