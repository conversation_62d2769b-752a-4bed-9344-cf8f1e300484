<template>
  <div class="pie" ref="pieRef"></div>
</template>
<script setup>
import useCharts from "@/hooks/useCharts"
import { ref, watch, computed } from "vue"
import { useStore } from "vuex"
import { useI18n } from "@xfe/locale"

const store = useStore()
let themeColor = computed(() => store.state.theme.themeColor)
const { t: $t } = useI18n()

let props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  pieState: {
    type: Array,
    default: () => [],
  },
})

let baseOption = {
  title: {
    text: "pieChart",
    show: false,
  },
  tooltip: {
    trigger: "item",
    formatter: function (a) {
      return (
        a.data.name +
        "：" +
        a.data.value +
        `${$t("common.times")}，` +
        a.data.time +
        $t("common.minutes")
      )
    },
  },
  color: ["#0084FE", "#FF0060", "#E44EF4", "#00DFA2", "#F6FA70"],
  legend: {
    orient: "vertical",
    selectedMode: false,
    x: "right",
    bottom: "5%",
    textStyle: {
      color: "#fff",
    },
    data: [
      $t("digitalStatement.resetStatus"),
      $t("digitalStatement.alarmStatus"),
      $t("digitalStatement.initializationStatus"),
      $t("digitalStatement.runningStatus"),
      $t("digitalStatement.downtimeStatus"),
    ],
    formatter: function (name, b) {
      let time = 0,
        count = 0
      if (name == $t("digitalStatement.resetStatus")) {
        time = props.pieState[0]?.time
        count = props.pieState[0]?.value
      } else if (name == $t("digitalStatement.alarmStatus")) {
        time = props.pieState[1]?.time
        count = props.pieState[1]?.value
      } else if (name == $t("digitalStatement.initializationStatus")) {
        time = props.pieState[2]?.time
        count = props.pieState[2]?.value
      } else if (name == $t("digitalStatement.runningStatus")) {
        time = props.pieState[3]?.time
        count = props.pieState[3]?.value
      } else if (name == $t("digitalStatement.downtimeStatus")) {
        time = props.pieState[4]?.time
        count = props.pieState[4]?.value
      }
      return (
        name +
        "  " +
        count +
        $t("common.times") +
        "  " +
        +time +
        $t("common.minutes")
      )
    },
  },
  series: [
    {
      type: "pie",
      center: ["40%", "30%"],
      radius: ["0%", "50%"],
      label: {
        show: true,
        // position: "outside",
        normal: {
          formatter: function (a) {
            return a.value + $t("common.times") + "\n" + a.percent + "%"
          },
          color: "#0084FE ",
          lineHeight: 18,
        },
      },
      labelLine: {
        normal: {
          length: 20,
          length2: 30,
          lineStyle: {
            width: 1,
          },
        },
      },
      data: [],
    },
  ],
}

const DARK_COLOR = "#fff"
const LIGHT_COLOR = "#606266"

watch(
  () => themeColor.value,
  v => {
    if (v === "light") {
      baseOption.legend.textStyle.color = LIGHT_COLOR
    } else {
      baseOption.legend.textStyle.color = DARK_COLOR
    }
  },
  { immediate: true }
)

let pieRef = ref()
let { updateDraw } = useCharts(pieRef, baseOption)

watch(
  () => props.option,
  () => {
    updateDraw(props.option)
  },
  { deep: true }
)
</script>
<style scoped lang="scss">
.pie {
  width: 100%;
  height: 100%;
}
</style>
