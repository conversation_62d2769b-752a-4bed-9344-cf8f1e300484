<template>
  <div class="repair p-z2">
    <tabPane :head="head" v-model="activeTab" />
  </div>
</template>
<script setup>
import tabPane from "@/components/tab-pane/index.vue"
import Order from "./order/index.vue"
import Plan from "./plan/index.vue"
import { ref } from "vue"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()
let head = [
  {
    id: "order",
    text: $t("repairWorkOrder.repairWorkOrder"),
    component: Order,
  },
  { id: "plan", text: $t("repairWorkOrder.maintenancePlan"), component: Plan },
]
let activeTab = ref("order")
</script>
<style scoped lang="scss">
.repair {
  height: 100%;
  width: 100%;
  background: var(--front-background);
}
</style>
