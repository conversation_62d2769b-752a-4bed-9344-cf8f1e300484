/**
 * 运行数据项key对照表 （示例 用于后端运行数据项对照，其他有用到的地方就import引入该对象）
 * 若后端改了key，我们统一在这改一次就行
 */
const runDataNotice = {
  // plcEquStatus: 'TM.PLC.EquipmentStatus', // PLC设备状态
  // plcConStatus: 'TM.PLC.ConnnectStatus', // PLC连接状态
  // plcStratTensionSetVal: 'TM.PLC.DB1000.DBD90', // 放卷张力设定值
  // plcStratTensionRealVal: 'TM.PLC.DB1000.DBD170', // 放卷张力实际值
  // plcEndTensionSetVal: 'TM.PLC.DB1000.DBD98', // 收卷张力设定值
};

export default runDataNotice;
