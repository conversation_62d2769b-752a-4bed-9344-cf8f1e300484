<template>
  <el-input class="preview-txt" type="textarea" placeholder="" v-model="txtPre" readonly> </el-input>
</template>

<script setup name="PreviewDocx">
import { onMounted, reactive, toRefs } from 'vue'

const props = defineProps({
  file: {
    type: Object,
    default: () => {},
    required: true,
  },
})

const data = reactive({
  loading: false,
  file: {},
  txtPre: {},
})

onMounted(() => {
  console.log(props.file,'props.file');
  const reader = new FileReader()
  reader.onload = function () {
    data.txtPre = reader.result //获取的数据data
  }
  reader.readAsText(props.file)
})

const { txtPre } = toRefs(data)
</script>

<style lang="scss" scoped>
.preview-txt {
  height: 100%;
  :deep(.el-textarea__inner) {
    height: 100%;
  }
}
</style>
