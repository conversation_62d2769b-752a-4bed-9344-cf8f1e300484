import * as BABYLON from 'babylonjs';
import { SceneManager } from './SceneManager';

/**
 * 网格信息接口定义
 */
export interface MeshInfo {
  id: string;
  name: string;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  scaling: { x: number; y: number; z: number };
  vertices: number;
  faces: number;
  parentId?: string;
  children: string[];
  isVisible: boolean;
  materialName?: string;
}

/**
 * 网格树节点接口定义
 */
export interface MeshTreeNode {
  id: string;
  name: string;
  level: number;
  children: MeshTreeNode[];
  vertices: number;
  isVisible: boolean;
  parentId?: string;
}

/**
 * 模型加载器
 * 负责加载和管理3D模型
 */
export class ModelLoader {
  private sceneManager: SceneManager;
  private importedMeshes: BABYLON.AbstractMesh[] = [];

  /**
   * 构造函数
   * @param sceneManager 场景管理器实例
   */
  constructor(sceneManager: SceneManager) {
    this.sceneManager = sceneManager;
  }

  /**
   * 导入模型
   * @param fileUrl 模型文件URL
   * @param clearPrevious 是否清除之前导入的模型，默认为true
   * @returns 是否成功导入
   */
  public async importModel(fileUrl: string, clearPrevious = true): Promise<boolean> {
    const scene = this.sceneManager.getScene();
    if (!scene) return false;

    try {
      // 根据参数决定是否清除之前导入的模型
      if (clearPrevious) {
        this.clearImportedMeshes();
      }

      // 导入模型
      const result = await BABYLON.SceneLoader.ImportMeshAsync('', fileUrl, '', scene);

      // 如果clearPrevious为true，直接替换importedMeshes
      // 如果clearPrevious为false，将新导入的模型添加到importedMeshes
      if (clearPrevious) {
        this.importedMeshes = result.meshes;
      } else {
        // 将新模型添加到数组中，不添加偏移量
        this.importedMeshes = [...this.importedMeshes, ...result.meshes];
      }

      // 不再自动调用自适应视角，让外部明确调用
      if (clearPrevious) {
        this.adjustCameraToModel();
      }

      return true;
    } catch (error) {
      console.error('模型导入失败:', error);
      return false;
    }
  }

  /**
   * 清除已导入的模型
   */
  public clearImportedMeshes(): void {
    if (this.importedMeshes.length > 0) {
      this.importedMeshes.forEach(mesh => {
        try {
          mesh.dispose();
        } catch (e) {
          console.warn("清除网格时出错:", e);
        }
      });
      this.importedMeshes = [];
    }
  }

  /**
   * 获取已导入的模型网格
   */
  public getImportedMeshes(): BABYLON.AbstractMesh[] {
    return this.importedMeshes;
  }

  /**
   * 获取模型的网格名称列表
   */
  public getMeshNames(): string[] {
    return this.importedMeshes
      .filter(mesh => mesh.getTotalVertices() > 0)
      .map(mesh => mesh.id || mesh.name);
  }

  /**
   * 通过ID查找网格
   * @param id 网格ID或名称
   */
  public findMeshById(id: string): BABYLON.AbstractMesh | undefined {
    return this.importedMeshes.find(mesh => mesh.id === id || mesh.name === id);
  }

  /**
   * 根据ID获取网格信息
   * @param id 网格ID
   */
  public getMeshInfo(id: string): MeshInfo | null {
    const mesh = this.findMeshById(id);
    if (!mesh) return null;

    // 获取父网格
    const parentId = mesh.parent instanceof BABYLON.AbstractMesh
      ? (mesh.parent as BABYLON.AbstractMesh).id
      : undefined;

    // 获取子网格
    const children = this.importedMeshes
      .filter(m => m.parent === mesh)
      .map(m => m.id);

    return {
      id: mesh.id,
      name: mesh.name,
      position: {
        x: parseFloat(mesh.position.x.toFixed(3)),
        y: parseFloat(mesh.position.y.toFixed(3)),
        z: parseFloat(mesh.position.z.toFixed(3))
      },
      rotation: {
        x: parseFloat(mesh.rotation.x.toFixed(3)),
        y: parseFloat(mesh.rotation.y.toFixed(3)),
        z: parseFloat(mesh.rotation.z.toFixed(3))
      },
      scaling: {
        x: parseFloat(mesh.scaling.x.toFixed(3)),
        y: parseFloat(mesh.scaling.y.toFixed(3)),
        z: parseFloat(mesh.scaling.z.toFixed(3))
      },
      vertices: mesh.getTotalVertices(),
      faces: mesh.getTotalIndices() / 3,
      parentId,
      children,
      isVisible: mesh.isVisible,
      materialName: mesh.material ? mesh.material.name : undefined
    };
  }

  /**
   * 获取模型的层次结构树
   */
  public getMeshTree(): MeshTreeNode[] {
    // console.log(`开始构建对象树，导入的网格数量: ${this.importedMeshes.length}`);

    // 如果没有导入的网格，返回空数组
    if (this.importedMeshes.length === 0) {
      console.warn('没有导入的网格，返回空对象树');
      return [];
    }

    // 先创建id到mesh的映射
    const meshMap = new Map<string, BABYLON.AbstractMesh>();

    // 过滤有效网格并创建映射
    const validMeshes = this.importedMeshes.filter(mesh => {
      const isValid = mesh && mesh.getTotalVertices && mesh.getTotalVertices() > 0;
      if (!isValid) {
        // console.log(`忽略无效网格: ${mesh?.id || '未知'}`);
      }
      return isValid;
    });
    // console.log(`有效网格数量: ${validMeshes.length}`);

    // 如果没有有效网格，返回空数组
    if (validMeshes.length === 0) {
      console.warn('没有找到有效网格（包含顶点的网格），返回空对象树');
      return [];
    }

    validMeshes.forEach(mesh => {
      if (mesh && mesh.id) {
        meshMap.set(mesh.id, mesh);
      }
    });

    // 找出根网格（没有父网格或父级不是网格的网格）
    const rootMeshes = validMeshes.filter(mesh =>
      !mesh.parent || !(mesh.parent instanceof BABYLON.AbstractMesh)
    );

    // console.log(`根网格数量: ${rootMeshes.length}`);
    if (rootMeshes.length === 0) {
      console.warn('未找到根网格，但有有效网格。将所有有效网格作为根网格处理。');
      // 如果没有找到根网格但有有效网格，将所有有效网格作为根网格
      return validMeshes.map(mesh => ({
        id: mesh.id,
        name: mesh.name || mesh.id,
        level: 0,
        children: [],
        vertices: mesh.getTotalVertices(),
        isVisible: mesh.isVisible,
        parentId: undefined
      }));
    }

    // 递归构建树结构
    const buildTree = (mesh: BABYLON.AbstractMesh, level: number): MeshTreeNode => {
      try {
        // 找出所有以当前网格为父级的子网格
        const children = validMeshes
          .filter(m => m && m.parent === mesh)
          .map(childMesh => buildTree(childMesh, level + 1));

        return {
          id: mesh.id,
          name: mesh.name || mesh.id, // 确保始终有名称
          level,
          children,
          vertices: mesh.getTotalVertices(),
          isVisible: mesh.isVisible,
          parentId: mesh.parent instanceof BABYLON.AbstractMesh
            ? (mesh.parent as BABYLON.AbstractMesh).id
            : undefined
        };
      } catch (error) {
        console.error(`构建树节点时出错 [ID:${mesh?.id || '未知'}]:`, error);
        // 发生错误时返回一个基本节点
        return {
          id: mesh?.id || `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
          name: mesh?.name || mesh?.id || '错误节点',
          level,
          children: [],
          vertices: mesh?.getTotalVertices() || 0,
          isVisible: mesh?.isVisible || false,
          parentId: undefined
        };
      }
    };

    try {
      // 构建树结构
      const tree = rootMeshes.map(mesh => buildTree(mesh, 0));

      // 按名称排序
      tree.sort((a, b) => (a.name || '').localeCompare(b.name || ''));

      // console.log(`构建完成，树根节点数量: ${tree.length}`);

      // 确保返回的不是空数组
      if (tree.length === 0 && validMeshes.length > 0) {
        console.warn('树构建异常，根节点为空但有有效网格。使用备用方法。');
        return validMeshes.map(mesh => ({
          id: mesh.id,
          name: mesh.name || mesh.id,
          level: 0,
          children: [],
          vertices: mesh.getTotalVertices(),
          isVisible: mesh.isVisible,
          parentId: undefined
        }));
      }

      return tree;
    } catch (error) {
      console.error('构建模型树结构时出错:', error);
      // 发生错误时使用备用方法
      return validMeshes.map(mesh => ({
        id: mesh.id,
        name: mesh.name || mesh.id,
        level: 0,
        children: [],
        vertices: mesh.getTotalVertices(),
        isVisible: mesh.isVisible,
        parentId: undefined
      }));
    }
  }

  /**
   * 自适应视角，使模型完整显示在视图中
   * @param animationDuration 动画持续时间（毫秒）
   */
  public adjustCameraToModel(animationDuration = 1800): void {
    const camera = this.sceneManager.getCamera();
    if (!camera || this.importedMeshes.length === 0) {
      console.warn('无法调整相机视角: 相机或模型不存在');
      return;
    }

    // 获取场景
    const scene = this.sceneManager.getScene();
    if (!scene) {
      console.warn('无法调整相机视角: 场景不存在');
      return;
    }

    // 计算所有导入模型的边界框
    const boundingInfo = this.sceneManager.calculateBoundingBox(this.importedMeshes);
    if (!boundingInfo) {
      console.warn('无法调整相机视角: 无法计算边界框');
      return;
    }

    // 边界框中心点 - 模型的目标位置
    const targetCenter = boundingInfo.boundingBox.centerWorld;

    // 获取当前相机目标位置
    const currentTarget = camera.target.clone();

    // 计算模型的对角线长度，作为尺寸参考
    const extendSize = boundingInfo.boundingBox.extendSize;
    const diagonalLength = Math.sqrt(
      extendSize.x * extendSize.x * 4 +
      extendSize.y * extendSize.y * 4 +
      extendSize.z * extendSize.z * 4
    );

    // 边界框的最大尺寸
    const maxSize = Math.max(
      extendSize.x,
      extendSize.y,
      extendSize.z
    );

    // 计算合适的相机距离，确保模型完全可见且大小适中
    // 使用更小的系数，使模型显示更大一些
    const factorByDiagonal = 0.3; // 减小对角线长度的因子
    const factorByMaxSize = 1.5;   // 减小最大尺寸的因子
    const baseRadius = Math.max(
      diagonalLength * factorByDiagonal,
      maxSize * factorByMaxSize
    );

    // 设置最小和最大半径，确保相机不会太近或太远
    const minRadius = 6; // 最小半径
    const maxRadius = Math.max(baseRadius * 1.5, 100); // 最大半径
    const targetRadius = Math.min(Math.max(baseRadius, minRadius), maxRadius);

    // console.log(`调整相机视角: 目标中心点=${targetCenter.toString()}, 目标半径=${targetRadius.toFixed(2)}, 当前半径=${camera.radius.toFixed(2)}`);
    // console.log(`模型数据: 对角线长度=${diagonalLength.toFixed(2)}, 最大尺寸=${maxSize.toFixed(2)}`);

    // 先停止所有相机动画，防止多个动画叠加导致不稳定
    scene.stopAnimation(camera);

    // 是否使用动画
    if (animationDuration > 0) {
      // 创建相机目标点动画（使相机看向的位置平滑移动）
      const targetAnimation = new BABYLON.Animation(
        "cameraTargetAnimation",
        "target",
        60, // 帧率
        BABYLON.Animation.ANIMATIONTYPE_VECTOR3,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );

      // 创建关键帧
      const targetKeyFrames = [
        { frame: 0, value: currentTarget },
        { frame: animationDuration / 16.67, value: targetCenter } // 16.67ms是60fps的帧间隔
      ];
      targetAnimation.setKeys(targetKeyFrames);

      // 创建radius动画
      const radiusAnimation = new BABYLON.Animation(
        "cameraRadiusAnimation",
        "radius",
        60, // 帧率
        BABYLON.Animation.ANIMATIONTYPE_FLOAT,
        BABYLON.Animation.ANIMATIONLOOPMODE_CONSTANT
      );

      // 创建关键帧
      const radiusKeyFrames = [
        { frame: 0, value: camera.radius },
        { frame: animationDuration / 16.67, value: targetRadius } // 16.67ms是60fps的帧间隔
      ];
      radiusAnimation.setKeys(radiusKeyFrames);

      // 使用SineEase缓动函数，提供平滑的动画效果
      const ease = new BABYLON.SineEase();
      ease.setEasingMode(BABYLON.EasingFunction.EASINGMODE_EASEINOUT);
      targetAnimation.setEasingFunction(ease);
      radiusAnimation.setEasingFunction(ease);

      // 清除当前动画并开始新动画
      camera.animations = [];
      camera.animations.push(targetAnimation);
      camera.animations.push(radiusAnimation);
      scene.beginAnimation(camera, 0, radiusKeyFrames[1].frame, false); // false表示只播放一次
    } else {
      // 不使用动画，直接设置相机距离和目标位置
      camera.radius = targetRadius;
      camera.setTarget(targetCenter);
    }
  }
}
