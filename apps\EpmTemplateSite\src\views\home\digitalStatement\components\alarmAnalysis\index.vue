<template>
  <div class="fault">
    <Analysis
      :tableColumn="tableColumn"
      url="DigitalReport/AlarmPagedList"
      exportUrl="FaultAnalysis/export"
      :name="$t('digitalStatement.alarmAnalysis')"
    ></Analysis>
  </div>
</template>
<script setup>
import Analysis from "../analysis/index.vue"
import { ref, computed } from "vue"
import { useI18n } from "@xfe/locale"
import { useStore } from "vuex"

const { t: $t } = useI18n()
const store = useStore()
const language = computed(() => store.getters["notice/language"])
let tableColumn = ref([
  {
    type: "index",
    label: "No.",
    align: "center",
    fixed: "left",
    width: "50",
    show: true,
  },
  {
    prop: "date",
    label: $t("digitalStatement.date"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "teamTime",
    label: $t("digitalStatement.shift"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "alarmTime",
    label: $t("digitalStatement.alarmTime"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "alarmGroup",
    label: $t("digitalStatement.alarmType"),
    align: "center",
    showOverflowTooltip: true,
    show: language.value === "en-US" ? false : true,
  },
  {
    prop: "alarmCode",
    label: $t("digitalStatement.alarmCode"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "alarmContent",
    label: $t("digitalStatement.alarmDescription"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "alarmDuration",
    label: `${$t("digitalStatement.alarmDuration")}(${$t(
      "digitalStatement.minute"
    )})`,
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
])
</script>
<style scoped>
.fault {
  width: 100%;
  height: 100%;
}
</style>
