<template>
  <div class="status-box">
    <div class="s-box-top">
      <div class="left">
        <div class="content">
          <div class="top">
            <div class="content_title">
              {{ $t("echarts.station.deviceName") }}
            </div>
            <div class="content_status name-color">
              {{ store.state.common.sysName ?? $t("homeTitle.title") }}
            </div>
          </div>
          <div class="line"></div>
          <div class="bottom">
            <div class="content_title">
              {{ $t("echarts.station.deviceStatus") }}
            </div>
            <div class="content_status status-color">
              <div v-for="(opt, idx) of stateAtatistics.option" :key="idx">
                {{ $t(store.getters["notice/deveiceState"]) }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="content">
          <div class="top">
            <div class="content_title">
              {{ $t("echarts.station.mesStatus") }}
            </div>
            <div class="content_status">
              {{ connectEnum[handlerConStatus(props.status.mes)] }}
            </div>
          </div>
          <div class="line"></div>
          <div class="bottom">
            <div class="content_title">
              {{ $t("echarts.station.plcStatus") }}
            </div>
            <div class="content_status">
              {{ connectEnum[handlerConStatus(props.status.plc)] }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="s-box-bottom">
      <div
        class="status-panel"
        v-for="(opt, idx) in stateAtatistics.status"
        :key="idx"
      >
        <div class="img">
          <img src="@/assets/images/box/running-time.svg" alt="" />
          <div class="content">
            <div class="opt">{{ opt || "-" }}</div>
            <div class="idx">{{ idx }}</div>
            <div class="miniute">({{ $t("common.minutes") }})</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from "@xfe/locale"
import { useStore } from "vuex"

const store = useStore()
const { t: $t } = useI18n()
let props = defineProps({
  stateAtatistics: {
    type: Object,
    required: true,
  },
  status: {
    type: Object,
    default: () => {
      return {
        plc: 0, // PLC连接状态
        ccd: 0, // CCD连接状态
        laser: 0, // 激光器连接状态
        mes: 0, // mes连接状态
      }
    },
  },
  // 连接状态是否存在
  displayFlag: {
    type: Object,
    default: () => {
      return {
        PLCStatus: false, // PLC
        MESStatus: false, //MES
        CCDStatus: false, // CCD
        laserStatus: false, // 激光器
      }
    },
  },
})

const handlerStatus = status => {
  switch (true) {
    case /1|运行时间/.test(status):
      return {
        class: "r-color",
        name: $t("echarts.station.run"),
        i: 1,
      }
    case /2|故障时间/.test(status):
      return {
        class: "f-color",
        name: $t("echarts.station.failure"),
        i: 2,
      }
    case /3|维护时间/.test(status):
      return {
        class: "m-color",
        name: $t("echarts.station.maintenance"),
        i: 3,
      }
    case /4|停机时间/.test(status):
      return {
        class: "s-color",
        name: $t("echarts.station.stop"),
        i: 4,
      }
    default:
      return {
        class: "s-color",
        name: $t("echarts.station.stop"),
        i: 0,
      }
  }
}

const connectEnum = [
  $t("echarts.station.notConnect"),
  $t("echarts.station.run"),
]

const handlerConStatus = status => {
  switch (status) {
    case 0:
      return 0
    case 1:
      return 1
    default:
      return 0
  }
}
</script>
<style scoped lang="scss">
.status-box {
  height: 100%;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: var(--card-title);
  padding: 0 18px;
  .s-box-top {
    flex: 1;
    display: flex;
    justify-content: space-between;
    > div {
      width: 45%;
      height: 80%;
      background: url("~@/assets/images/box/link-panel.svg") no-repeat;
      background-size: cover;
    }
    .content {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      padding: 5px 20px;
      .name-color {
        color: #b6dcff;
      }
      .status-color {
        color: #ff9900;
      }
      .line {
        height: 1px;
        border: 1px solid rgba(0, 254, 239, 0.15);
        margin: 5px 0;
      }
      &_title {
        font-size: 24px;
      }
      &_status {
        text-align: center;
        font-size: 28px;
        font-weight: 800;
        color: #00feef;
        > div {
          font-weight: 800;
        }
      }
    }
  }
  .s-box-bottom {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .status-panel {
      width: 22%;
      .img {
        position: relative;
        width: 100%;
        height: 100%;
        font-size: 12px;
        img {
          width: 100%;
          height: 100%;
        }
        .content {
          width: 100%;
          position: absolute;
          text-align: center;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          font-size: 24px;
          .opt {
            color: #fef900;
            font-weight: bold;
          }
        }
      }
    }
  }
}
.flx {
  display: flex;
}
.flex-v-c {
  display: flex;
  align-items: center;
}

.mr6 {
  margin-right: 6px;
  white-space: nowrap;
}
</style>
