<template>
  <div class="equipmentStatus">
    <div class="header">
      <div class="left">
        <!-- 新增颜色图例 -->
        <template v-for="(item, index) in colorList" :key="index">
          <div
            class="color-legend"
            :style="{ backgroundColor: item.color }"
          ></div>
          <div class="color-text">{{ item.text }}</div>
        </template>
      </div>
      <div class="center" :style="{ color: currentStatus.color }">
        {{ currentStatus.text }}
      </div>
      <div class="right">
        <div>{{ $t("monitor.initializationTime") }}{{ initializeTime }}</div>
        <div>{{ $t("monitor.runningTime") }}{{ runningTime }}</div>
        <div>{{ $t("monitor.resetTime") }}{{ resetTime }}</div>
        <div>{{ $t("monitor.downtime") }}{{ stopTime }}</div>
        <div>{{ $t("monitor.alarmContinueTime") }}{{ alarmTime }}</div>
      </div>
      <div class="unit">{{ $t("monitor.unitMinutes") }}</div>
    </div>
    <div class="container">
      <div class="b-chart" ref="equipmentStatusRef"></div>
    </div>
  </div>
</template>

<script setup name="BarCharts">
import {
  ref,
  computed,
  defineEmits,
  defineProps,
  defineExpose,
  watch,
  nextTick,
} from "vue"
import useEcharts from "@/hooks/useEcharts"
import { useI18n } from "@xfe/locale"
import { equipmentStatus } from "../echartsConfig.js"

const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        currentStatus: "", // 当前状态
        runningTime: 0, // 运行时长
        stopTime: 0, // 停机时长
        alarmTime: 0, // 报警时长
        resetTime: 0, // 复位时长
        initializeTime: 0, // 初始化时长
      }
    },
  },
  option: {
    type: Object,
    default: () => {},
  },
  isShow: {
    type: Boolean,
    default: false,
  },
})

const { t: $t } = useI18n()

const emits = defineEmits([
  "chart-click", // 点击chart
])

const colorList = [
  { text: $t("monitor.initialization"), color: "#E44EF4" },
  { text: $t("monitor.running"), color: "#00DFA2" },
  { text: $t("monitor.reset"), color: "#0079FF" },
  { text: $t("monitor.downtimeStatus"), color: "#F6FA70" },
  { text: $t("monitor.alarmStatus"), color: "#FF0060" },
]

const currentStatus = computed(() => {
  return {
    text: props.data.currentStatus,
    color: colorList.find(item => item.text === props.data.currentStatus)
      ?.color,
  }
})

const runningTime = computed(() => {
  return props.data.runningTime ?? "-"
})

const stopTime = computed(() => {
  return props.data.stopTime ?? "-"
})

const alarmTime = computed(() => {
  return props.data.alarmTime ?? "-"
})

const resetTime = computed(() => {
  return props.data.resetTime ?? "-"
})

const initializeTime = computed(() => {
  return props.data.initializeTime ?? "-"
})

let equipmentStatusRef = ref(null)
const { resize } = useEcharts(equipmentStatusRef, emits, props, equipmentStatus)
defineExpose({
  resize,
})
watch(
  () => props.isShow,
  v => {
    v &&
      nextTick(() => {
        resize()
      })
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.equipmentStatus {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 30px;
    .left {
      width: 500px;
      display: flex; // 新增flex布局
      align-items: center; // 新增垂直居中
      gap: 10px; // 新增间距

      .color-legend {
        width: 15px;
        height: 15px;
        border: 1px solid #fff;
      }
      .color-text {
        font-size: 16px;
      }
    }
    .center {
      flex: 1;
      text-align: center;
      font-weight: bold;
    }
    .right {
      // width: 650px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      > div {
        margin-right: 5px;
        // flex: 1;
        font-size: 15px;
        text-align: right;
      }
    }
    .unit {
      font-size: 16px;
    }
  }
  .container {
    width: 100%;
    flex: 1;
    .b-chart {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
