<template>
  <hmx-dialog
    dialogWidth="760px"
    :dialogTitle="$t('monitor.equipmentAlarm')"
    :isVisable="modelValue"
    :draggable="true"
    @closeDialog="closeFun"
    customClass="equip-dialog"
  >
    <div class="alarm-box">
      <div class="header">
        <div class="text">
          {{ $t("monitor.total") }}{{ productionAlarmList.length
          }}{{ $t("monitor.items") }}
        </div>
      </div>
      <el-scrollbar height="100%">
        <div
          class="content"
          v-for="(item, index) in currentAlarmList"
          :key="index"
        >
          <div class="item">
            <div class="top">
              <div class="reason">
                <div class="image">
                  <img src="@/assets/images/box/icon-alarmInfo.svg" alt="" />
                </div>
                <div class="text">
                  <el-tooltip
                    effect="dark"
                    :content="`${item.alarmContent}【${item.alarmCode}】`"
                    placement="top"
                  >
                    {{ item.alarmContent }}【{{ item.alarmCode }}】
                  </el-tooltip>
                </div>
                <div class="btn">
                  <div class="reason-btn blue" @click="handleViewReason(item)">
                    {{ $t("monitor.viewReason") }}
                  </div>
                  <div class="reason-btn blue" @click="handleViewModule(item)">
                    {{ $t("monitor.locateToModule") }}
                  </div>
                </div>
              </div>
              <div class="time" v-show="!item.isSparePart">
                <div class="alarm-time">
                  {{ $t("monitor.alarmTime") }}{{ formatTime(item.startTime) }}
                </div>
                <div class="total-time">
                  {{ $t("monitor.totalDuration")
                  }}<span class="red">{{
                    formatSeconds(item.durationSeconds)
                  }}</span>
                </div>
              </div>
            </div>
            <div class="bottom" v-show="isFold[index]">
              <div class="title">{{ $t("monitor.faultGuide") }}</div>
              <div class="operate">
                <div
                  class="operate-btn blue"
                  @click="handleViewSparepart(item)"
                >
                  1、{{ $t("monitor.checkAvailableSpareParts") }}
                </div>
                <div class="operate-btn blue" @click="handleRepairWorkOrder">
                  2、{{ $t("monitor.fillOutMaintenanceWorkOrder") }}
                </div>
              </div>
            </div>
            <div class="fold-btn" @click="handleFold(index)">
              <!-- 传入当前item的index -->
              <div class="fold-btn-content" v-show="!isFold[index]">
                <!-- 根据isFold数组对应位置的值判断展开和收起的显示 -->
                <el-icon><ArrowDown /></el-icon>
                <div class="text">{{ $t("monitor.expand") }}</div>
              </div>
              <div class="fold-btn-content" v-show="isFold[index]">
                <el-icon><ArrowUp /></el-icon>
                <div class="text">{{ $t("monitor.collapse") }}</div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </hmx-dialog>
  <!-- 故障备件弹窗 -->
  <FaultyDialog
    v-model="sparePartShow"
    :data="sparePartData"
    :imgList="imageList"
    :videoList="videoList"
    :moduleData="moduleData"
    :loading="spareLoading"
  />
  <!-- 设备维修工单弹窗 -->
  <AddOrder
    :addShow="addShow"
    :orderOpt="orderOpt"
    @closeDialog="closeDialog"
  />
</template>

<script setup name="AlarmDialog">
import { ref, reactive, computed } from "vue"
import { useStore } from "vuex"
import { formatTime, formatSeconds } from "@/utils"
import { useRouter } from "vue-router"
import { useI18n } from "@xfe/locale"
import HmxDialog from "@/components/hmx-dialog.vue"
import xfeTabs from "@/components/xfe-tabs"
import FaultyDialog from "../FaultySpareparts/index.vue"
import AddOrder from "@/views/home/<USER>/order/addOrder.vue"
import useEchartsData from "../../hooks/useEchartsData"

const store = useStore()

const { t: $t } = useI18n()

const router = useRouter()
const emit = defineEmits(["update:modelValue", "handlePosition"])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  alarmList: {
    type: Array,
    default: () => [],
  },
  moduleRef: {
    type: HTMLElement,
    default: null,
  },
})
const {
  sparePartShow,
  sparePartData,
  imageList,
  videoList,
  spareLoading,
  moduleData,
  handleViewSparepart,
} = useEchartsData()

const productionAlarmList = computed(() => props.alarmList)
// 已处理列表
const processedList = computed(() =>
  props.alarmList.filter(item => item.status === 1)
)
// 未处理列表
const untreatedList = computed(() =>
  props.alarmList.filter(item => item.status === 0)
)

// 添加冷却时间方法
const startCooldown = () => {
  store.commit("SET_IS_COOL_DOWN", true)
  setTimeout(() => {
    store.commit("SET_IS_COOL_DOWN", false)
  }, 60000)
}

const closeFun = () => {
  emit("update:modelValue", false)
  startCooldown()
}

const tabs = [
  {
    label: "全部",
    index: 0,
    status: 2,
  },
  {
    label: "已处理",
    index: 1,
    status: 1,
  },
  {
    label: "未处理",
    index: 2,
    status: 0,
  },
]

const currentAlarmIndex = ref(2)
const handleChangeTab = item => {
  currentAlarmIndex.value = item.status
}
const currentAlarmList = computed(() => {
  // 根据 index 计算并返回相应的结果
  // 例如：
  if (currentAlarmIndex.value === 0) {
    return untreatedList.value
  } else if (currentAlarmIndex.value === 1) {
    return processedList.value
  } else {
    return productionAlarmList.value
  }
})

const isFold = ref([]) // 改为数组

const handleFold = index => {
  // 传入当前item的index
  isFold.value[index] = !isFold.value[index] // 根据index修改isFold数组对应位置的值
}

// 查看原因
const handleViewReason = item => {
  router.push({
    path: "/home/<USER>",
    query: {
      phenomenon: item.alarmContent,
    },
  })
}

// 定位到模组
const handleViewModule = async item => {
  const val = item.moduleCode
  emit("handlePosition", val)
  emit("update:modelValue", false)
}

// 填报维修工单
let addShow = ref(false)
let orderOpt = reactive({
  type: "add",
  data: {},
})
const handleRepairWorkOrder = () => {
  orderOpt.type = "add"
  addShow.value = true
}
const closeDialog = () => {
  addShow.value = false
}
</script>

<style scoped lang="scss">
$item-bg: #1b1b1b;
$item-border: #ee4e4e;
.alarm-box {
  height: 500px;
  color: #fff;
  .header {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    .tab {
      display: flex;
      align-items: center;
      .title {
        min-width: 70px;
        margin-right: 20px;
      }
    }
  }
  .content {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .item {
      padding: 10px 20px;
      width: 100%;
      border: 1px solid $item-border;
      background-color: $item-bg;
      border-radius: 5px;
      .top {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
        height: 100px;
        .reason {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          .image {
            width: 18px;
            height: 18px;
            margin-right: 10px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .text {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
          }
          .btn {
            display: flex;
            width: 200px;
            > div {
              cursor: pointer;
              margin-left: 20px;
            }
          }
        }
        .time {
          display: flex;
          font-size: 14px;
          .total-time {
            margin-left: 20px;
          }
        }
      }
      .fold-btn {
        cursor: pointer;
        width: 100%;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #0084fe;
        background-color: rgba(0, 132, 254, 0.1);
        &-content {
          display: flex;
          align-items: center;
          justify-content: center;
          .text {
            margin-left: 5px;
          }
        }
      }
      .bottom {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100px;
        border-top: 1px solid $item-border;
        .operate {
          &-btn {
            cursor: pointer;
            margin-top: 5px;
          }
        }
      }
    }
  }
  .orange {
    color: #eecb4e;
  }
  .red {
    color: #ee4e4e;
  }
}
</style>

<style lang="scss">
.equip-dialog {
  .el-dialog__header {
    // border: solid 10px green;
    background-color: #004b90;
    margin: 0;
    .el-dialog__title {
      color: #fff;
      font-weight: 600;
    }
    .el-dialog__headerbtn {
      .el-dialog__close {
        font-size: 24px;
        color: #fff;
      }
    }
  }
  .el-dialog__body {
    // border: solid red 1px;
    background-color: rgba(0, 0, 0, 1);
  }
}
</style>
