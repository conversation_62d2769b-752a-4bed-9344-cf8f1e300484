<template>
  <!-- 新增：数据为空时显示提示 -->

  <div class="box">
    <div v-if="isEmpty" ref="barRef" class="no-data">
      {{ $t("common.noData") }}
    </div>
    <div class="chart" v-else>
      <div class="bar" ref="barRef"></div>
    </div>
  </div>
</template>
<script setup>
import useCharts from "@/hooks/useCharts"
import { ref, watch, computed, nextTick } from "vue"
import { useStore } from "vuex"
import { useI18n } from "@xfe/locale"

const store = useStore()
let themeColor = computed(() => store.state.theme.themeColor)

const { t: $t } = useI18n()

let props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
})

let baseOption = {
  tooltip: {
    show: true,
    confine: true,
    trigger: "axis",
    formatter: function (params) {
      console.log(params[0], "a1234")
      if (params.length > 1) {
        return (
          params[0].name +
          "：" +
          params[0].value +
          `${$t("common.times")}，` +
          params[1].value +
          `${$t("common.minutes")}`
        )
      } else {
        if (params[0].data.type === "usedTime") {
          return params[0].name + "：" + params[0].value + $t("common.minutes")
        } else {
          return params[0].name + "：" + params[0].value + $t("common.times")
        }
      }
    },
  },
  dataZoom: [
    {
      type: "slider",
      show: true,
      yAxisIndex: [0],
      left: "96%",
      width: 10,
      maxValueSpan: 10,
      minValueSpan: 1,
      backgroundColor: "#616573",
      borderColor: "none",
      fillerColor: "#2B98FF",
      textStyle: {
        color: "#fff",
      },
    },
  ],
  grid: {
    top: "0%",
    left: "3%",
    right: "8%",
    bottom: "10%",
    containLabel: true,
  },
  xAxis: {
    show: true,
    type: "value",
    axisLabel: {
      textStyle: {
        color: "#fff",
      },
    },
    axisLine: {
      show: false,
      lineStyle: {
        type: "solid",
        color: "#292c38",
      },
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: "solid",
        color: "rgba(77, 128, 254, 0.2)",
      },
    },
  },
  yAxis: [
    {
      type: "category",
      inverse: true,
      axisLabel: {
        show: true,
        color: "#fff",
        formatter: function (value) {
          return value.length > 6 ? `${value.substring(0, 6)}...` : value
        },
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      data: props.option?.yAxis?.[0]?.data || [],
    },
  ],
  series: [
    {
      type: "bar",
      data: props.option?.series?.[0]?.data || [],
      itemStyle: {},
      color: "#0084FE",
      label: {
        show: true,
        position: "inside",
        color: "#fff",
        formatter: function (params) {
          return params.value
        },
      },
    },
  ],
}

const DARK_COLOR = "#fff"
const LIGHT_COLOR = "#606266"

// 判断数据是否为空
const isEmpty = computed(() => {
  return props.option?.yAxisData?.length || props.option?.seriesData?.length
})

watch(
  () => themeColor.value,
  v => {
    if (v === "light") {
      baseOption.yAxis[0].axisLabel.color = LIGHT_COLOR
    } else {
      baseOption.yAxis[0].axisLabel.color = DARK_COLOR
    }
  },
  { immediate: true }
)

let barRef = ref()
let { updateDraw } = useCharts(barRef, baseOption)

watch(
  () => props.option,
  () => {
    updateDraw(props.option)
  },
  { deep: true }
)
</script>
<style scoped lang="scss">
.box {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  .chart {
    flex: 1;
    width: 100%;
    .bar {
      width: 100%;
      height: 100%;
    }
  }
  .no-data {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #909399; /* 浅灰色，与常见无数据提示风格一致 */
    font-size: 14px;
  }
}
</style>
