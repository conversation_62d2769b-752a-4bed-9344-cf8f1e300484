import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 * 参数配置和mes配置
 */

/**
 *  参数配置同步配置
 */
export function SyncDataSetting() {
  return request.get({
    url: `${homeServerPrefix}/ProductDataSetting/SyncDataSetting`,
  })
}

// 获取参数类型列表
export function GetParamList() {
  return request.get({
    url: `${homeServerPrefix}/MesInterfaceParamSetting/GetParamTypeList`,
  })
}

// 获取mes类型列表
export function getMesTypeList() {
  return request.get({
    url: `${homeServerPrefix}/MesData/GetMesTypeList`,
  })
}

// 设置当前MES类型
export function setCurrentMesType(data) {
  return request.post({
    url: `${homeServerPrefix}/MesInterfaceParamSetting/SetCurrentMesType?mesType=${data}`,
  })
}

// 获取设备列表
export function GetEquipTypeList() {
  return request.get({
    url: `${homeServerPrefix}/MesInterfaceParamSetting/GetEquipTypeList`,
  })
}

// mes调试
export function mesDebug(params, data) {
  return request.post({
    url: `${homeServerPrefix}/MesData/InvokeMethod`,
    params,
    data,
  })
}

// 获取mes调试的json
export function getMesByJson(params) {
  return request.get({
    url: `${homeServerPrefix}/MesInterfaceParamSettingDetail`,
    params,
  })
}

// 密码验证
export function verifyParamsPwd(params) {
  return request.get({
    url: `${homeServerPrefix}/User/password/verify/${params}`,
  })
}
