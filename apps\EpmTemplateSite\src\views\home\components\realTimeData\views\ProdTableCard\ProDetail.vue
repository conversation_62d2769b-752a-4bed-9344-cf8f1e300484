<template>
  <div class="pro-detail">
    <div class="detail-title">
      <span>{{ $t("card.dialogContent.cellCoding") }}：</span>
      <span>{{
        props.proInfo?.barCode != "" ? props.proInfo?.barCode : "null"
      }}</span>
    </div>
    <!-- 工位 -->
    <div class="stations-box">
      <div class="station" v-for="station in detailInfo" :key="station.key">
        <p class="station-tit">{{ station.key }}</p>
        <template v-if="station.value.length">
          <div
            class="data"
            v-for="datas in station.value"
            :key="datas.dataName"
          >
            <span class="data-name">{{ datas.dataName }}：</span>
            <span class="data-val">{{ datas.dataValue }}</span>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup name="ProDetail">
import { defineProps, reactive, toRefs, onMounted } from "vue"

const props = defineProps({
  proInfo: {
    type: Object,
    requied: true,
    default: () => {
      return {}
    },
  },
})
const data = reactive({
  detailInfo: [],
})

onMounted(() => {
  data.detailInfo = props.proInfo.list
})

const { detailInfo } = toRefs(data)
</script>

<style lang="scss" scoped>
.pro-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .detail-title {
    width: 80%;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    border: 1px solid #1b7ef2;
    border-radius: 4px;
    color: #fff;
    background-color: #1c1e23;
    > span {
      padding: 0 8px;
    }
  }
  .stations-box {
    width: 100%;
    overflow: auto;
    margin-top: 30px;
    padding: 20px;
    flex: none;
    display: flex;
    .station {
      flex: none;
      margin: 0 8px;
      padding: 10px;
      min-width: 180px;
      max-height: 350px;
      overflow-y: auto;
      background: #1c1e23;
      border-radius: 2px;
      border: 1px solid #000;
      color: #fff;
      .station-tit {
        font-size: 14px;
        padding-bottom: 16px;
        text-align: center;
      }
      .data {
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 20px;
        &:hover {
          background-color: #33fefe;
          color: #333;
          cursor: pointer;
        }
        .data-name {
          // flex: none;
          // width: 60%;
          text-align: right;
          white-space: nowrap;
        }
        .data-val {
          // flex: none;
          // width: 40%;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
<style lang="scss" scoped>
@media screen and (max-width: 1024px) {
  .pro-detail {
    .stations-box {
      .station {
        .data {
          line-height: 20px;
        }
      }
    }
  }
}
</style>
