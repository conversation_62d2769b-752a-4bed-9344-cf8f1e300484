<template>
  <section :class="{ police: true, detClass: detClass }">
    <div class="chart">
      <!-- <span class="tip">单位(条)</span> -->
      <div class="b-chart" ref="tgfPolicePieRef"></div>
    </div>
    <ul class="info-list">
      <li v-for="list of wlist" :key="list.name">
        <div class="info-type">
          <span class="font-icon" :style="{ background: list.color }">!</span
          >{{ list.name }}
        </div>
        <div class="info-detail">
          <div class="type">{{ $t("echarts.title.pending") }}</div>
          <div class="num">{{ list.handle }}</div>
        </div>
        <div class="info-detail">
          <div class="type">{{ $t("echarts.title.processed") }}</div>
          <div class="num">{{ list.showing }}</div>
        </div>
      </li>
    </ul>
  </section>
</template>

<script setup>
import {
  ref,
  defineEmits,
  defineProps,
  defineExpose,
  watch,
  nextTick,
  computed,
} from "vue"
import useEcharts from "@/hooks/useEcharts"
import { policePieEcharts } from "./echartsConfig"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()
const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  isShow: {
    type: Boolean,
    default: false,
  },
  detClass: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  "chart-click", // 点击chart
])
let wlist = computed(() => {
  return [
    {
      name: $t("echarts.title.narmal"),
      color: "#0084FE",
      handle: props.option?.narmal?.handle ?? 0,
      showing: props.option?.narmal?.showing ?? 0,
    },
    {
      name: $t("echarts.title.important"),
      color: "#FF9900 ",
      handle: props.option?.warn?.handle ?? 0,
      showing: props.option?.warn?.showing ?? 0,
    },
    {
      name: $t("echarts.title.emergent"),
      color: "#FE0005",
      handle: props.option?.alarm?.handle ?? 0,
      showing: props.option?.alarm?.showing ?? 0,
    },
  ]
})
let tgfPolicePieRef = ref(null)
const { resize } = useEcharts(
  tgfPolicePieRef,
  emits,
  { option: {} },
  policePieEcharts
)
defineExpose({
  resize,
})
watch(
  () => props.isShow,
  v => {
    v &&
      nextTick(() => {
        resize()
      })
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.police {
  padding-top: 5px;
  width: 100%;
  height: 100%;
  display: flex;
  color: #fff;
  font-size: 13px;

  .chart {
    position: relative;
    width: 50%;
    height: 85%;
    .tip {
      position: absolute;
      top: 0;
      left: 0;
    }
    .b-chart {
      width: 100%;
      height: 100%;
    }
  }

  .info-list {
    flex: 1;
    font-size: 9px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    li {
      background-color: rgba(25, 59, 100, 1);
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      .info-detail {
        margin-left: 5px;
        width: 7em;
        text-align: center;
        .type {
          transform: scale(1);
        }
        .num {
          font-weight: bolder;
        }
      }
      .info-type {
        .font-icon {
          display: inline-block;
          text-align: center;
          font-size: 16px;
          font-weight: bolder;
          clip-path: polygon(
            30% 0%,
            70% 0%,
            100% 30%,
            100% 70%,
            70% 100%,
            30% 100%,
            0% 70%,
            0% 30%
          );
          width: 20px;
          height: 20px;
          background-color: red;
          color: rgba(25, 59, 100, 1);
        }
      }
    }
  }
}
.detClass {
  .info-list {
    font-size: 18px;
    li {
      margin-bottom: 20px;
      padding-left: 10px;
    }
  }
}
</style>
