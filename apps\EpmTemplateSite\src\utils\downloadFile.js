const downloadFile = async (url, filename) => {
    console.log("开始下载文件...",url, filename)
  try {
    const response = await fetch(url)
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)

    const blob = await response.blob()
    // 修复文件类型识别问题
    const fileType = url.split(".").pop().toLowerCase()
    const mimeType = fileType === "jpg" ? "image/jpeg" : blob.type

    const correctedBlob = new Blob([blob], { type: mimeType })
    const objectURL = URL.createObjectURL(correctedBlob)

    const link = document.createElement("a")
    link.href = objectURL
    // 强制指定文件扩展名
    link.download = filename.includes(".")
      ? filename
      : `${filename}.${fileType}`
    document.body.appendChild(link)
    link.click()

    // 清理资源
    URL.revokeObjectURL(objectURL)
    document.body.removeChild(link)
  } catch (error) {
    throw new Error(error)
  }
}

export default downloadFile