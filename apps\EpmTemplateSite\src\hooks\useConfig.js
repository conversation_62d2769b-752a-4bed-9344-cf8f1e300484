/**
 * 全局配置项
 */
import { ref } from "vue"

export default function useConfig() {
  // 产能/产量详情表头
  const ppmDetailTable = typeName => {
    let tableHead = ref([
      { type: "index", width: "50", label: "No.", align: "center" },
      { prop: "dataValue", label: typeName, align: "center" },
      { prop: "createTime", label: "时间", align: "center" },
    ])
    return tableHead.value
  }
  // 产品列表表头 isAssembly 是否是组盘机 isProductType 是否有产品类型 list 其余扩展字段
  const productionTableHead = (
    isProductType = false,
    isAssembly = false,
    list = []
  ) => {
    const arr1_1 = [
      { type: "index", label: "No.", align: "center", fixed: "left" },
      {
        prop: "barCode",
        label: "电芯条码",
        align: "center",
        fixed: "left",
        width: "340",
        showOverflowTooltip: true,
      },
      {
        prop: "productCode",
        label: "产品型号",
        align: "center",
        showOverflowTooltip: true,
      },
      {
        prop: "result",
        // width: "120",
        label: "生产结果",
        slot: "result",
        align: "center",
      },
    ]
    const arr1_2 = [
      { type: "index", label: "No.", align: "center", fixed: "left" },
      {
        prop: "barCode",
        label: "托盘条码",
        align: "center",
        fixed: "left",
        width: "340",
        showOverflowTooltip: true,
      },
      {
        prop: "productCode",
        label: "产品型号",
        align: "center",
        showOverflowTooltip: true,
      },
    ]
    const arr1 = isAssembly ? arr1_2 : arr1_1
    const arr2 = isProductType
      ? [
          {
            prop: "productType",
            // width: "100",
            label: "生产类型",
            slot: "productType",
            align: "center",
          },
        ]
      : []
    const arr3 = [
      {
        prop: "createTime",
        label: "时间",
        align: "center",
        showOverflowTooltip: true,
      },
      {
        // width: "120",
        label: "操作",
        align: "center",
        fixed: "right",
        buttons: [{ name: "详 情", type: "default", command: "detail" }],
      },
    ]

    let tableHead = [...arr1, ...arr2, ...list, ...arr3]
    return tableHead
  }

  // 子站单独部署后的setting菜单  apply 哪一个子站
  const settingPath = (apply, firstMenu) => {
    const arr = [
      {
        path: `/apps/${apply}/config/real-time-data`,
        name: "实时数据配置",
        icon: "TrendCharts",
        size: "24px",
        permission: true,
      },
      {
        path: `/apps/${apply}/config/production-data`,
        name: "生产数据配置",
        icon: "Platform",
        size: "24px",
        permission: true,
      },
      {
        path: `/apps/${apply}/config/params-config`,
        name: "参数配置",
        icon: "DocumentCopy",
        size: "24px",
        permission: firstMenu.includes("setting:parameter"),
      },
      {
        path: `/apps/${apply}/config/warn-data-config`,
        name: "报警数据配置",
        icon: "",
        size: "24px",
        permission: true,
      },
      // {
      //   path: "setStorage/timeout",
      //   name: "数据储存时间配置",
      //   icon: "",
      //   size: "",
      //   permission: true,
      // },
      // 暂未开通配方管理 页面需求清楚后开放 /apps/${apply}/config/formula-manage
      {
        path: "",
        name: "配方管理",
        icon: "DocumentCopy",
        size: "24px",
        permission: true,
      },
      {
        path: `/apps/${apply}/config/plc-debug-config`,
        name: "plc调试配置",
        icon: "",
        size: "24px",
        permission: true,
      },
    ]
    return arr
  }

  return {
    ppmDetailTable,
    productionTableHead,
    settingPath,
  }
}
