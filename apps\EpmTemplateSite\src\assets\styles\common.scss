/**
 * 全局公共样式
 */

.table-form {
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
  color: var(--title-color);
  .el-form-item__label,
  .el-pagination__goto,
  .el-pagination__total {
    color: var(--title-color);
  }
}

.btn-query {
  width: 80px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  background: #004b90;
  opacity: 1;
  border: 1px solid;
  border-image: linear-gradient(
      180deg,
      rgba(72, 167, 255, 1),
      rgba(0, 132, 254, 1)
    )
    1 1;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}

.fold-table {
  position: relative;
  right: 42px;
  width: 40px;
  height: 80px;
  background: #004b90;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  border: 1px solid #0084fe;
  top: -100%;
  transform: translateX(-50%);
  cursor: pointer;
}
.fold-left {
  position: relative;
  height: 30px;
  width: 3px;
  background: #fef900;
  // &::after {
  //   position: absolute;
  //   top: 3px;
  //   left: 10px;
  //   content: "";
  //   width: 20px;
  //   height: 20px;
  //   background-color: transparent; /* 模块背景为透明 */
  //   border-color: #fef900;
  //   border-style: solid;
  //   border-width: 0 0 3px 3px;
  //   transform: rotate(45deg);
  // }
}

.ml20 {
  margin-left: 20px;
}
.ml10 {
  margin-left: 10px;
}
.ml50 {
  margin-left: 50px;
}
.mb16 {
  margin-bottom: 16px;
}
.mb8 {
  margin-bottom: 8px;
}
.mt10 {
  margin-top: 10px;
}

.cursor-pointer {
  cursor: pointer;
}
.flex-c-c {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-a-c {
  display: flex;
  align-items: center;
}
.flex-between {
  display: flex;
  justify-content: space-between;
}
.flex-end {
  display: flex;
  justify-content: flex-end;
}

.flx {
  display: flex;
}

.p-r {
  position: relative;
}

.p-z2 {
  position: relative;
  z-index: 2;
}

.w420 {
  width: 420px;
}

.text-overfole {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 表格状态icon显示
.state-icon {
  width: 11px;
  height: 11px;
  border-radius: 50%;
}
.state-start {
  background: #6cb71b;
}
.state-stop {
  background: #ff5335;
}
