{"name": "po-zcxh", "version": "2.0.3", "private": true, "description": "中创新航三合一", "scripts": {"serve": "vue-cli-service serve", "mock:serve": "vue-cli-service serve --mode mock", "build:pro": "vue-cli-service build --mode production && esno ../../scripts/build/config/postBuild.ts", "build:release": "vue-cli-service build --mode release && esno ../../scripts/build/config/postBuild.ts", "build:dev": "vue-cli-service build --mode development && esno ../../scripts/build/config/postBuild.ts", "lint": "vue-cli-service lint", "analysis": "vue-cli-service build --mode development --report"}, "dependencies": {"@element-plus/icons-vue": "2.1.0", "@microsoft/signalr": "^7.0.3", "@popperjs/core": "^2.11.8", "@vue-office/docx": "^1.3.0", "@vue-office/excel": "^1.1.5", "@vue/cli-plugin-typescript": "^5.0.8", "@vueuse/core": "^9.13.0", "@xfe/locale": "workspace:*", "@xfe/request": "workspace:*", "@xfe/utils": "workspace:*", "axios": "^1.3.4", "babylonjs": "^8.6.1", "babylonjs-gui": "^8.6.1", "babylonjs-loaders": "^8.6.1", "babylonjs-materials": "^8.6.1", "core-js": "^3.8.3", "dayjs": "^1.11.7", "echarts": "5.4.3", "echarts-liquidfill": "^3.1.0", "element-plus": "2.3.3", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "qs": "^6.11.0", "screenfull": "^6.0.2", "svg-sprite-loader": "^6.0.11", "typescript": "^4.9.5", "v-calendar": "^3.1.2", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vue-types": "^5.0.2", "vuedraggable": "^4.1.0", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@types/codemirror": "^5.60.7", "@typescript-eslint/eslint-plugin": "^5.55.0", "@typescript-eslint/parser": "^5.56.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^11.0.2", "@xfe/types": "workspace:*", "eslint": "^7.32.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.0.0", "html-webpack-tags-plugin": "^3.0.2", "mockjs": "^1.1.0", "postcss-px-to-viewport": "^1.1.1", "prettier": "^2.8.4", "sass": "^1.60.0", "sass-loader": "^13.2.0", "style-loader": "^3.3.1", "ts-loader": "^9.4.2", "unplugin-auto-import": "^0.15.1", "unplugin-vue-components": "^0.24.1", "vue-virtual-scroller": "2.0.0-beta.8", "webpack-bundle-analyzer": "^4.9.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}