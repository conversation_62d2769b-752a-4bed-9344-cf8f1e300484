import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 * 获取NGCode列表
 */
export function getOutputNGDataList() {
  return request.get({
    url: `${homeServerPrefix}/DatumLineConfigWorkstation/GetOutputNGDataList`,
  })
}

/**
 * 获取产品出站信息数据
 */
export function getOutputProductDataList() {
  return request.get({
    url: `${homeServerPrefix}/DatumLineConfigWorkstation/GetOutputProductDataList`,
  })
}

/**
 * 新增基准线配置
 */
export function addDatumLineConfig(data) {
  return request.post({
    url: `${homeServerPrefix}/DatumLineConfigWorkstation/NewAddDatumLineConfigWorkstation`,
    data,
  })
}
