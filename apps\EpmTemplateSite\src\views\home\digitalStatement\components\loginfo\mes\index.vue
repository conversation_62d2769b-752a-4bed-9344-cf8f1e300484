<template>
  <baseCard
    :tableColumn="tableColumn"
    url="log/connect"
    name="MES日志"
    exportUrl=""
  />
</template>
<script setup>
import baseCard from "../base/index.vue"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()

let tableColumn = [
  { type: "index", width: "50", label: "No.", align: "center" },
  {
    prop: "userName",
    label: $t("table.operateQuery.userName"),
    //align: "center",
  },
  {
    prop: "message",
    label: $t("table.operateQuery.content"),
    //align: "center",
  },
  {
    prop: "creationTime",
    label: $t("table.operateQuery.creationTime"),
    //align: "center",
    slot: "creationTime",
  },
  {
    prop: "remark",
    label: $t("table.operateQuery.remark"),
    //align: "center",
  },
]
</script>
