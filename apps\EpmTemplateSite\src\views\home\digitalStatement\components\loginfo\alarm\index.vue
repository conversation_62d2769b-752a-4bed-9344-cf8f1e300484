<template>
  <div class="alarm">
    <baseCard
      :tableColumn="tableColumn"
      url="Log/warn"
      name="报警日志"
      exportUrl="Log/ExportWarnLog"
      @row-opera="rowOpera"
    />
    <alarmDialog
      :dialog="showDetail"
      :alarm-info="alarmInfo"
      @onCancel="showDetail = false"
    />
  </div>
</template>
<script setup>
import baseCard from "../base/index.vue"
import { useI18n } from "@xfe/locale"
import alarmDialog from "./alarmDialog.vue"
import { ref } from "vue"
import { formatTime } from "@/utils"

const { t: $t } = useI18n()

let tableColumn = [
  { type: "index", width: "50", label: "No.", align: "center" },
  {
    prop: "warningCode",
    label: $t("table.alarmQuery.alarmCode"),
    //align: "center",
  },
  {
    prop: "message",
    width: "300",
    label: $t("table.alarmQuery.content"),
    //align: "center",
  },
  {
    prop: "continueTime",
    label: $t("table.alarmQuery.alarmDuration"),
    //align: "center",
  },
  {
    prop: "warningNum",
    label: $t("table.alarmQuery.alarmTimes"),
    //align: "center",
  },
  {
    prop: "errorMsg",
    label: $t("table.alarmQuery.alarmInformation"),
    //align: "center",
  },
  // {
  //   prop: "serverName",
  //   label: $t("table.alarmQuery.serviceName"),
  //   //align: "center",
  // },
  {
    prop: "moduleName",
    label: $t("table.alarmQuery.moduleName"),
    //align: "center",
  },
  // { prop: "kind", width: "120", label: "错误等级", align: "center" },
  {
    prop: "suggest",
    label: $t("table.alarmQuery.operationSuggestion"),
    //align: "center",
  },
  {
    prop: "remark",
    label: $t("table.alarmQuery.remarks"),
    //align: "center",
  },
  {
    width: "100",
    label: $t("table.alarmQuery.operation"),
    //align: "center",
    buttons: [
      {
        command: "detail",
        type: "primary",
        name: $t("table.alarmQuery.details"),
      },
    ],
  },
]

let alarmInfo = ref({})
let showDetail = ref(false)
function rowOpera(data) {
  if (data.type == "detail") {
    let { row, form } = data
    let startTime = formatTime(form.time[0])
    let endTime = formatTime(form.time[1])
    alarmInfo.value = {
      warningCode: row.warningCode,
      startTime,
      endTime,
    }
    showDetail.value = true
  }
}
</script>
<style scoped lang="scss">
.alarm {
  width: 100%;
  height: 100%;
}
</style>
