import dayjs from "dayjs"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()

/**
 * 将json字符串转换成换行格式
 * @param {Object} text_value
 */
export function formartJsonStr(text_value) {
  if (text_value == "") {
    alert("不能为空")
    return ""
  } else {
    let res = ""
    for (let i = 0, j = 0, k = 0, ii, ele; i < text_value.length; i++) {
      //k:缩进，j:""个数
      ele = text_value.charAt(i)
      if (j % 2 == 0 && ele == "}") {
        k--
        for (ii = 0; ii < k; ii++) ele = "    " + ele
        ele = "\n" + ele
      } else if (j % 2 == 0 && ele == "{") {
        ele += "\n"
        k++
        // debugger;
        for (ii = 0; ii < k; ii++) ele += "    "
      } else if (j % 2 == 0 && ele == ",") {
        ele += "\n"
        for (ii = 0; ii < k; ii++) ele += "    "
      } else if (ele == '"') j++
      res += ele
    }
    return res
  }
}

//设置防抖，保证无论拖动窗口大小，只执行一次获取浏览器宽高的方法
export const debounce = (fun, delay) => {
  let timer
  return function () {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fun()
    }, delay)
  }
}

// 获取屏幕可视宽度（是否小于等于1024）
export const clientWidth = () => {
  return document.body.clientWidth <= 1024
}

// 检查当前设备mobile or pc
export const checkEquipment = () => {
  if (
    navigator.userAgent.match(
      /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
    )
  ) {
    return "mobile"
  } else {
    return "pc" //电脑
  }
}

export const emptyId = "00000000-0000-0000-0000-000000000000"

/**
 * @param {Object} json
 * @param {Object} type： 默认不传 ==>全部小写;传1 ==>全部大写;传2 ==>首字母大写;传3 ==>首字母小写
 * 将json的key值进行大小写转换
 */
export const jsonKeysToCase = (json, type) => {
  if (typeof json == "object") {
    var tempJson = JSON.parse(JSON.stringify(json))
    toCase(tempJson)
    return tempJson
  } else {
    return json
  }

  function toCase(json) {
    if (typeof json == "object") {
      if (Array.isArray(json)) {
        json.forEach(function (item) {
          toCase(item)
        })
      } else {
        for (var key in json) {
          var item = json[key]
          if (typeof item == "object") {
            toCase(item)
          }
          delete json[key]
          switch (type) {
            case 1:
              //key值全部大写
              json[key.toLocaleUpperCase()] = item
              break
            case 2:
              //key值首字母大写，其余小写
              json[
                key.substring(0, 1).toLocaleUpperCase() +
                  key.substring(1).toLocaleLowerCase()
              ] = item
              break
            case 3:
              //key值首字母小写
              json[key.substring(0, 1).toLocaleLowerCase() + key.substring(1)] =
                item
              break
            default:
              //默认key值全部小写
              json[key.toLocaleLowerCase()] = item
              break
          }
        }
      }
    }
  }
}

// 时间过滤方法
export const formatTime = (time, type = "YYYY-MM-DD HH:mm:ss") => {
  if (typeof time === "number" && time.length === 10) {
    // 解析 Unix 时间戳 (秒) 1666617034
    // dayjs.unix().format('YYYY-MM-DD')
    // 时间戳 (秒)转为毫秒
    time = time * 1000
  }
  // 解析 Date 对象实例 new Date()
  // 解析 Unix 时间戳 (毫秒) 1666617034000
  // 解析符合 ISO 8601 格式的日期字符串 '2022-10-24T20:00:00.000Z'
  return dayjs(time).format(type)
}

// 用于echarts柱状图文字过长换行显示
export const wrapText = (params, num = 5) => {
  let newParamsName = "" // 最终拼接成的字符串
  let paramsNameNumber = params.length // 实际标签的个数
  let provideNumber = num // 每行能显示的字的个数
  let rowNumber = Math.ceil(paramsNameNumber / provideNumber) // 换行的话，需要显示几行，向上取整
  /**
   * 判断标签的个数是否大于规定的个数， 如果大于，则进行换行处理 如果不大于，即等于或小于，就返回原标签
   */
  // 条件等同于rowNumber>1
  if (paramsNameNumber > provideNumber) {
    /** 循环每一行,p表示行 */
    for (let p = 0; p < rowNumber; p++) {
      let tempStr = "" // 表示每一次截取的字符串
      let start = p * provideNumber // 开始截取的位置
      let end = start + provideNumber // 结束截取的位置
      // 此处特殊处理最后一行的索引值
      if (p === rowNumber - 1) {
        // 最后一次不换行
        tempStr = params.substring(start, paramsNameNumber)
      } else {
        // 每一次拼接字符串并换行
        tempStr = params.substring(start, end) + "\n"
      }
      newParamsName += tempStr // 最终拼成的字符串
    }
    //超出2行显示...
    //if(rowNumber>2){
    //  newParamsName+='...'
    //}
  } else {
    // 将旧标签的值赋给新标签
    newParamsName = params
  }
  //将最终的字符串返回
  return newParamsName
}

//转换数据,0是相等，1是模糊查询
export const GetDictionaryFromObject = (
  object,
  isByPage,
  isInt = false,
  isAsc = true
) => {
  if (isByPage) {
    let paramPage = {
      index: object.pageIndex,
      size: object.pageSize,
      parameters: [],
      orderBys: ["createTime"],
      isAsc: isAsc,
    }

    let newData = JSON.parse(JSON.stringify(object))
    delete newData.pageIndex
    delete newData.pageSize
    let newList = [
      Object.keys(newData).map(val => {
        let comObj = {
          key:
            val.indexOf("time") != -1
              ? "createTime"
              : val.indexOf("op") != -1 // 操作日志操作人和操作信息都是用的LogMessage
              ? "LogMessage"
              : val,
          value: isInt ? object[val] : object[val].toString(), // object[val].toString()
          type: 1,
        }
        return comObj
      }),
    ]
    //过滤封装
    let num = 1
    newList[0].forEach(item => {
      if (item.value.length > 0 || typeof item.value == "number") {
        if (item.key == "isDeleted") {
          item.type = 0
        }
        if (item.key == "isPublish") {
          item.type = 0
        }
        if (item.key == "createTime") {
          if (num === 1) {
            item.type = 2
            num++
          } else {
            item.type = 4
          }
        }
        if (item.key == "LogType") item.type = 0
        if (item.key == "logDate") item.type = 0
        if (item.key == "logMonth") item.type = 0
        if (item.key == "ServerName") item.type = 0
        if (item.key == "Level") item.type = 0
        if (item.key == "productType") item.type = 0
        if (item.key == "CTCode") item.type = 0
        if (item.key == "alarmCode") item.type = 0
        paramPage.parameters.push(item)
      }
    })
    num = null
    return paramPage
  } else {
    let params = {
      parameters: [],
      orderBys: [],
      isAsc: true,
    }
    let thisList = [
      Object.keys(object).map(val => {
        return {
          key: val,
          value: object[val],
          type: 1,
        }
      }),
    ]
    thisList[0].forEach(item => {
      if (item.value.length > 0) {
        if (item.key == "isDeleted") {
          item.type = 0
        }
        params.parameters.push(item)
      }
    })

    return params
  }
}

// 数组对象去重  arr 数组，key 根据什么属性去重

export function distinct(arr, key) {
  let hash = {}
  return arr.reduce((acc, cru, index) => {
    if (!hash[cru[key]]) {
      hash[cru[key]] = { index: acc.length }
      acc.push(cru)
    } else {
      acc.splice(hash[cru[key]]["index"], 1, cru)
    }
    return acc
  }, [])
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== "object") {
    throw new Error("error arguments", "deepClone")
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === "object") {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * 生成树形数据
 * @param {Object} data 原始数据
 * @param {Object} pid  父ID
 * @param {Object} opt  配置项
 * @param {Object} returnKeys 要返回的字段，数组
 */
export function createTreeData(data, pid, opt, returnKeys) {
  if (!opt["idKey"]) {
    opt["idKey"] = "id"
  }
  if (!opt["pidKey"]) {
    opt["pidKey"] = "pid"
  }
  if (!opt["textKey"]) {
    opt["textKey"] = "name"
  }
  if (!opt["valueKey"]) {
    opt["valueKey"] = "value"
  }
  let idKey = opt["idKey"]
  let pidKey = opt["pidKey"]
  let textKey = opt["textKey"]
  let valueKey = opt["valueKey"]
  let result = [],
    temp
  for (let i = 0; i < data.length; i++) {
    if (data[i][pidKey] == pid) {
      let obj = data[i]
      obj["text"] = data[i][textKey]
      obj["name"] = data[i][textKey]
      obj["value"] = data[i][valueKey]
      obj["id"] = data[i][idKey]
      obj["pid"] = data[i][pidKey]
      if (opt["ishaveChild"]) {
        obj["haveChild"] = true
      }
      let res = {}
      if (returnKeys && returnKeys.length > 0) {
        for (let j = 0; j < returnKeys.length; j++) {
          res[returnKeys[j]] = obj[returnKeys[j]]
          if (obj["children"]) {
            res["children"] = obj["children"]
          }
        }
      } else {
        res = obj
      }
      temp = createTreeData(data, data[i][idKey], opt, returnKeys)
      if (temp.length > 0) {
        obj.children = temp
      }
      result.push(res)
    }
  }
  return result
}

/**
 * 根据树形结构生成JSON串
 */
export function getJsonData(obj, data) {
  if (data && data.length > 0) {
    for (let i = 0; i < data.length; i++) {
      switch (data[i].dataType) {
        case "0": //Int
          obj[data[i].paramCode] = 0
          break
        case "1": //Float
          obj[data[i].paramCode] = 0
          break
        case "2": //String
          obj[data[i].paramCode] = `"${data[i].paramValue}"`
          break
        case "3": //Array
          obj[data[i].paramCode] = []
          if (data[i].children && data[i].children.length > 0) {
            if (data[i].children.length == 0) {
              if (data[i].children[0].dataType == 0) {
                obj[data[i].paramCode].push(0)
              } else {
                obj[data[i].paramCode].push("")
              }
            } else {
              let arrObj = {}
              obj[data[i].paramCode].push(arrObj)
              getJsonData(arrObj, data[i].children)
            }
          }
          break
        case "4": //Object
          obj[data[i].paramCode] = {}
          if (data[i].children && data[i].children.length > 0) {
            getJsonData(obj[data[i].paramCode], data[i].children)
          }
          break
        case "5": // Boolean
          obj[data[i].paramCode] = false
          break
        default:
          return obj
      }
    }
    return obj
  } else {
    return obj
  }
}

/**
 * 将json字符串转换成换行格式
 * @param {Object} text_value
 * @param {Array} list  遍历给字符串后面添加备注
 */
export function formartJsonStr_1(text_value, list) {
  let str = text_value.substring(0, text_value.indexOf("}")) + ",}"
  if (text_value == "") {
    alert("不能为空")
    return ""
  } else {
    let res = ""
    for (let i = 0, j = 0, k = 0, ii, ele, index = 0; i < str.length; i++) {
      //k:缩进，j:""个数`
      ele = str.charAt(i)
      if (j % 2 == 0 && ele == "}") {
        k--
        for (ii = 0; ii < k; ii++) ele = "    " + ele
        ele = "\n" + ele
      } else if (j % 2 == 0 && ele == "{") {
        ele += "\n"
        k++
        // debugger;
        for (ii = 0; ii < k; ii++) ele += "    "
      } else if (j % 2 == 0 && ele == ",") {
        if (list) {
          list.forEach((el, idx) => {
            if (index == idx) {
              ele += "// " + el.name
            }
          })
        }
        index++
        ele += "\n"
        for (ii = 0; ii < k; ii++) ele += "    "
      } else if (ele == '"') j++
      res += ele
    }
    return res
  }
}
// mes使用文档格式化
export function getJsonStr(treeData, list) {
  let objStr = ""
  objStr += "{\n"
  for (let key in treeData) {
    let temp = treeData[key]
    if (typeof temp == "object") {
      if (temp instanceof Array) {
        objStr += " " + key + ":"
        if (temp.length <= 1) {
          if (typeof temp[0] == "object") {
            objStr += "[{" + getRemark(key, list) + "\n"
          } else {
            objStr += "[" + getRemark(key, list) + "\n"
          }
        } else {
          objStr += "[{" + getRemark(key, list) + "\n"
        }
        if (temp.length > 0) {
          let arrTemp = temp[0]
          if (typeof arrTemp == "object") {
            for (let key1 in arrTemp) {
              let temp1 = arrTemp[key1]
              if (temp1 instanceof Array) {
                objStr += "    " + key1 + ":"
                //objStr+='[{'+getRemark(key1,list)+'\n';===========================
                if (temp1.length <= 1) {
                  if (typeof temp1[0] == "object") {
                    objStr += "[{" + getRemark(key1, list) + "\n"
                  } else {
                    objStr += "[" + getRemark(key1, list) + "\n"
                  }
                } else {
                  objStr += "[{" + getRemark(key1, list) + "\n"
                }
                //============================================================
                if (temp1.length > 0) {
                  let arrTemp1 = temp1[0]
                  if (typeof arrTemp1 == "object") {
                    for (let key2 in arrTemp1) {
                      let temp2 = arrTemp1[key2]
                      if (temp2 === "" || temp2 === undefined) {
                        objStr +=
                          "      " +
                          key2 +
                          ':"",' +
                          getRemark(key2, list) +
                          "\n"
                      } else {
                        objStr +=
                          "      " +
                          key2 +
                          ":" +
                          temp2 +
                          "," +
                          getRemark(key2, list) +
                          "\n"
                      }
                    }
                  } else {
                    objStr += "      " + '""\n'
                  }
                }
                //objStr+='    }],\n';========================
                if (temp1.length <= 1) {
                  if (typeof temp1[0] == "object") {
                    objStr += "  }],\n"
                  } else {
                    objStr += "  ],\n"
                  }
                } else {
                  objStr += "  }],\n"
                }
                //=============================
              } else {
                if (temp1 === "" || temp1 === undefined) {
                  objStr +=
                    "    " + key1 + ':"",' + getRemark(key1, list) + "\n"
                } else {
                  objStr +=
                    "    " +
                    key1 +
                    ":" +
                    temp1 +
                    "," +
                    getRemark(key1, list) +
                    "\n"
                }
              }
            }
          } else {
            objStr += "    " + '""\n'
          }
        }
        if (temp.length <= 1) {
          if (typeof temp[0] == "object") {
            objStr += "  }],\n"
          } else {
            objStr += "  ],\n"
          }
        } else {
          objStr += "  }],\n"
        }
      } else {
        objStr += "  " + key + ":"
        objStr += "{" + getRemark(key, list) + "\n"
        for (let key1 in temp) {
          let temp1 = temp[key1]
          if (temp1 === "" || temp1 === undefined) {
            objStr += "    " + key1 + ':"",' + getRemark(key1, list) + "\n"
          } else {
            objStr +=
              "    " + key1 + ":" + temp1 + "," + getRemark(key1, list) + "\n"
          }
        }
        objStr += "  },\n"
      }
    } else {
      if (temp === "" || temp === undefined) {
        objStr += " " + key + ':"",' + getRemark(key, list) + "\n"
      } else {
        objStr += " " + key + ":" + temp + "," + getRemark(key, list) + "\n"
      }
    }
  }
  objStr += "}\n"
  return objStr
}

export function getRemark(key, list) {
  let temp = list.find(item => item.paramCode == key)
  if (temp) {
    return " //" + temp.paramName + "," + temp.remark
  } else {
    return ""
  }
}

// 将数组转换为树形结构
export function buildTree(arr, parentId = null) {
  const tree = []

  // 遍历数组
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].parentId === parentId) {
      const node = { ...arr[i] }

      // 递归构建子树
      const children = buildTree(arr, node.id)
      if (children.length) {
        node.children = children
      }

      tree.push(node)
    }
  }

  return tree
}

/**
 * 字符串首字母转小写
 * @param string 传入的字符串
 */
export function toLowerCaseFirstLetter(str) {
  return str.charAt(0).toLowerCase() + str.slice(1)
}
/**
 * 字符串首字母转大写
 * @param string 传入的字符串
 */
export function toUpperCaseFirstLetter(str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

export function setShortcuts(format = "YYYY-MM-DD", list = []) {
  return [
    {
      text: $t("common.today"),
      value: () => {
        const start = dayjs().startOf("day").format(format)
        const end = dayjs().endOf("day").format(format)
        return [start, end]
      },
    },
    {
      text: $t("common.last3Days"),
      value: () => {
        const start = dayjs().subtract(3, "day").startOf("day").format(format)
        const end = dayjs().endOf("day").format(format)
        return [start, end]
      },
    },
    {
      text: $t("common.last7Days"),
      value: () => {
        const start = dayjs().subtract(6, "day").startOf("day").format(format)
        const end = dayjs().endOf("day").format(format)
        return [start, end]
      },
    },
    ...list,
  ]
}

/**
 * 传入两个字符串，筛选出两者的差异并返回
 * @param {字符串1} str1
 * @param {字符串2} str1
 */
export function compareStrings(str1, str2) {
  const differentChars = []

  const len1 = str1.length
  const len2 = str2.length
  const maxLen = Math.max(len1, len2)

  for (let i = 0; i < maxLen; i++) {
    const char1 = str1[i]
    const char2 = str2[i]

    if (char1 !== char2) {
      if (!differentChars.includes(char1)) {
        differentChars.push(char1)
      }

      if (!differentChars.includes(char2)) {
        differentChars.push(char2)
      }
    }
  }

  return differentChars.join("")
}

/**
 * 秒数转时间方法
 * @param {*} seconds 秒数
 * @returns
 */
export function formatSeconds(seconds) {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60
  let formattedString = ""
  if (seconds === 0 || seconds === null) {
    return "0"
  }
  if (hours > 0) {
    formattedString += `${hours}${$t("common.hour")}`
  }
  if (minutes > 0) {
    formattedString += `${minutes}${$t("common.minute")}`
  }
  if (remainingSeconds > 0) {
    formattedString += `${remainingSeconds}${$t("common.second")}`
  }
  return formattedString
}
