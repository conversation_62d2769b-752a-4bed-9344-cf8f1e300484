<template>
  <div class="fault">
    <Analysis
      :tableColumn="tableColumn"
      url="FaultAnalysis/FaultStatistic"
      exportUrl="FaultAnalysis/export"
      :name="$t('digitalStatement.faultAnalysis')"
    ></Analysis>
  </div>
</template>
<script setup>
import Analysis from "../analysis/index.vue"
import { ref } from "vue"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()

let tableColumn = ref([
  {
    type: "index",
    label: "No.",
    align: "center",
    fixed: "left",
    width: "50",
    show: true,
  },
  {
    prop: "faultDate",
    label: $t("digitalStatement.date"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "teamTime",
    label: $t("digitalStatement.shift"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "devName",
    label: $t("digitalStatement.equipmentName"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "totalTime",
    label: `${$t("digitalStatement.faultDuration")}(${$t(
      "digitalStatement.minute"
    )})`,
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "totalCount",
    label: $t("digitalStatement.faultCount"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "faultRate",
    label: `${$t("digitalStatement.faultRate")}(%)`,
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "repairTime",
    label: `${$t("digitalStatement.maintenanceDuration")}(${$t(
      "digitalStatement.minute"
    )})`,
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "mttr",
    label: `MTTR(${$t("digitalStatement.minute")})`,
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "mtbf",
    label: `MTBF(${$t("digitalStatement.minute")})`,
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "oee",
    label: "OEE",
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
])
</script>
<style scoped>
.fault {
  width: 100%;
  height: 100%;
}
</style>
