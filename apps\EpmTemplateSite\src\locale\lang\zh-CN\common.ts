export default {
  common: {
    okText: "确 认",
    close: "关 闭",
    cancelText: "取 消",
    submitText: "提 交",
    loadingText: "加载中...",
    saveText: "保存",
    add: "新增",
    edit: "编辑",
    preview: "预览",
    delText: "删除",
    startText: "启动",
    restartText: "重启",
    stopText: "停止",
    resetText: "重置",
    resetpdText: "重置密码",
    searchText: "搜索",
    queryText: "查询",
    time: "时间",
    times: "次",
    date: "日期",
    unit: "单位",
    a: "个",
    seconds: "秒",
    status: "状态",
    minutes: "分钟",
    noData: "暂无数据",
    to: "至",
    view: "查看",
    publish: "发布",

    day: "今",
    week: "周",
    month: "月",

    userName: "用户名",
    baseLine: "基准线",
    detail: "详情",
    baseValue: "基准值",
    ctGood: "CT良好",
    ctBad: "CT异常",

    inputText: "请输入",
    chooseText: "请选择",

    redo: "刷新",
    back: "返回",

    light: "亮色主题",
    dark: "黑暗主题",

    startTime: "开始时间",
    endTime: "结束时间",
    alarm: "报警",
    alarmTime: "报警时间",
    alarmAnalysis: "报警分析",
    alarmQuery: "报警查询",
    alarmDetail: "报警详情",
    logTaskScheduling: "日志任务调度",
    logKeyWord: "关键字",
    warningCode: "报警码",
    operate: "操作",
    operateTime: "操作时间",
    operateAnalysis: "操作分析",
    operateQuery: "操作查询",
    equipStatus: "设备状态",
    logType: "日志类型",
    scale: "刻度",

    click: "点击",
    enter: "录入",
    barCode: "条码",

    export: "导出",
    fullTableExport: "全表导出",
    import: "导入",
    oneKeyImport: "一键导入",
    oneKeyExport: "一键导出",
    sync: "同步",
    enable: "启用",
    disable: "停用",
    yes: "是",
    no: "否",
    show: "显示",
    hide: "隐藏",
    prompt: "提示",
    maximize: "最大化",
    minimize: "最小化",
    read: "读取",

    all: "全部",
    normal: "正常",
    selectAll: "全选",
    isDelete: "是否删除",

    success: "成功",
    failure: "失败",

    download: "下载",

    reasonTitle: "上报停机原因",
    submitReason: "提交停机原因",

    fileOversize: "文件大小超过限制，最大允许",
    noFileSelected: "未选择文件",

    today: "今天",
    last3Days: "最近三天",
    last7Days: "最近七天",

    timePeriod: "时段",
    hour: "时",
    minute: "分",
    second: "秒",
  },
  prompt: {
    errMsg504: "网络超时",
    errMsg500: "网络连接错误",
    prompt_1: "接口路径找不到",
    prompt_2: "你已被登出，请重新登录",
    prompt_3: "退出成功!",
    prompt_4: "退出登录失败，是否强制退出？",
    prompt_5: "登录失败，用户无菜单权限",
    prompt_6: "未读取到卡号，请重试",
    prompt_7: "新增成功！",
    prompt_8: "修改成功！",
    prompt_9: "删除成功！",
    prompt_10: "导入成功！",
    prompt_11: "产品历史数据",
    prompt_12: "待启动",
    prompt_13: "同步成功!",
    prompt_14: "国际化资源配置表!",
    prompt_15: "是否重置当前用户密码？",
    prompt_16: "重置密码成功",
    prompt_17: "加载中",
    prompt_18: "请到后台发布可视化配置",
    prompt_19: "发布中",
    prompt_20: "请上传背景图",
    prompt_21: "图片上传失败",
    prompt_22: "保存成功",
    prompt_23: "保存失败",
    prompt_24: "CT良好",
    prompt_25: "CT异常",
    prompt_26: "配置化成功",
    prompt_27: "未选中",
    prompt_28: "运行时间",
    prompt_29: "停机时间",
    prompt_30: "故障时间",
    prompt_31: "维护时间",
    prompt_32: "请选择参数类型",
    prompt_33: "请选择MES类型",
    prompt_34: "设置成功",
    prompt_35: "交互表",
    prompt_36:
      "请输入对应的key和value值的键值对,然后点击确认参数即可显示在上方",
    prompt_37: "日志历史数据",
    prompt_38: "暂无产品数据，请稍后再试！",
    prompt_39: "发布成功！",
    prompt_40: "暂无权限~",
    prompt_41: "导入文件出错",
    prompt_42: "导出数据中，请稍等...",
  },
  echarts: {
    oee: {
      synthesis: "OEE",
      available: "可用性",
      performance: "表现性",
      quality: "质量指数",
    },
    station: {
      none: "无",
      offLine: "离线",
      run: "运行",
      failure: "故障",
      standby: "待机",
      stop: "停机",
      idle: "空闲",
      maintenance: "维修",
      notConnect: "未连接",
      deviceName: "设备名称",
      scanStatus: "扫码设备",
      mesStatus: "MES状态",
      deviceStatus: "设备状态",
      plcStatus: "PLC状态",
      alarmClearing: "报警清除",
      beStarting: "启动",
      alarm: "报警",
      resetting: "复位中",
      waitForInitialization: "等待初始化",
    },
    title: {
      productQuality: "质量统计",
      outputCount: "产量统计",
      alarmInfo: "报警信息",
      productTask: "生产任务",
      CT: "CT工序表",
      realTimeCt: "实时CT（单位：秒）",
      warnPrompt: "预警信息",
      oee: "实时OEE",
      output: "产量",
      outputYield: "产量趋势",
      baseCapacity: "基准产能",
      capacity: "产能",
      count: "统计",
      logInfo: "日志信息",
      productInfo: "产品信息",
      yield: "良率",
      noYield: "不良率",
      total: "总数",
      narmal: "一般",
      important: "重要",
      emergent: "紧急",
      pending: "待处理",
      processed: "已处理",
      runningState: "运行状态",
      productionAlarm: "生产报警",
      statusStatistics: "状态统计",
      connectionMonitoring: "连接监控",
      currentShiftProduction: "当前班次生产",
      equipEnergyAndEnviron: "设备能耗&环境",
      equipmentMaintenance: "设备维护",
    },
    dialogTitle: {
      productQuality: "产品质量趋势分析",
      outputCount: "产量统计趋势分析",
      CT: "CT工序表趋势分析",
      CtDetail: "CT表详情",
      warnPrompt: "预警信息趋势分析",
      oee: "实时OEE趋势分析",
      qualityConfig: "产品质量配置",
      outputConfig: "产量统计配置",
      ctConfig: "CT工序表配置",
      realTimeCtConfig: "实时CT配置",
      warnPromptConfig: "预警信息配置",
      oeeConfig: "实时OEE配置",
      outputCapacityAnalysis: "产能/产量趋势分析",
      outputCapacityConfig: "产能/产量趋势配置化",
      outputCapacityDetail: "详情-产量/产能统计",
      qualityStatistics: "质量统计",
      qualityStatisticsAnalysis: "质量统计趋势分析",
      qualityStatisticsConfig: "质量统计配置化",
      statusStatistics: "状态统计",
      statusStatisticsAnalysis: "状态统计趋势分析",
      productionTask: "生产任务",
      earlyWarningInfo: "预警",
      infoDetails: "信息详情",
      hstoricalLog: "日志信息",
    },
    table: {
      num: "序号",
      time: "时间",
      alarmInfo: "报警信息",
      taskNum: "任务号",
      taskName: "任务名称",
      content: "交互内容",
      warnPrompt: "预警信息",
    },
    tab: {
      oknum: "合格数",
      ngnum: "不合格数",
      outputCount: "产量统计",
      capacityCount: "产能统计",
      available: "可用性",
      performance: "表现性",
      quality: "质量指数",
    },
    log: {
      alarmStatistics: "报警统计",
      alarmDistribution: "报警分布",
      alarmArrangement: "报警排布",
      operationStatistics: "操作统计",
      operationLayout: "操作排布",
      operationDistribution: "操作分布",
      systemStatistics: "系统统计",
      systemDistribution: "系统分布",
      systemArrangement: "系统排布",
      mesStatistics: "MES统计",
      mesDistribution: "MES分布",
      mesArrangement: "MES排布",
      statistics: "统计",
      distribution: "分布",
      total: "报警总数",
      displayMode: "显示模式",
      fiveDaysData: "近五日统计信息",
      hstoricalAlarmLog: "历史报警日志",
      hstoricalOperationLog: "历史操作日志",
    },
    content: {
      taskName: "任务名称",
      interactiveContent: "交互内容",
      time: "时间",
      warnInfo: "预警信息",
    },
    spc: {
      xbar: "XBar图",
      r: "R图",
      average: "平均值",
      upperLimit: "控制上限",
      lowerLimit: "控制下限",
      centerLimit: "控制中心限",
      rangeValue: "极差值",
      parameter: "SPC参数",
      subgroupCapacity: "子组容量",
      a2: "A2值",
      d3: "D3值",
      d4: "D4值",
      generateGraph: "生成SPC图形",
      noData: "暂无SPC参数",
    },
    product: {
      classes: "班次",
      productStatistics: "产品统计",
      productQuality: "产品良率",
      productData: "产品数据",
      productNg: "产品NG原因分布",
    },
  },
  card: {
    title: {
      columnSetting: "列设置",
      clearData: "清空数据",
      hstoricalData: "历史数据",
      alarmLog: "报警日志",
      systemLog: "系统日志",
      operationLog: "操作日志",
      mesLog: "MES日志",
      fold: "折叠",
      spread: "展开",
      baseInfor: "基本信息",
      processFormula: "工艺配方",
      mesConfig: "MES配置",
      workOrderConfig: "工单配置",
    },
    dialogTitle: {
      logDetails: "日志详情",
      details: "详情",
      historicalData: "历史数据",
      editWorkInfo: "编辑工位信息",
      aluminumShell1: "铝壳上料工位1",
      aluminumShell2: "铝壳上料工位2",
      productDetails: "产品信息详情",
    },
    dialogContent: {
      cellCoding: "电芯条码",
    },
    table: {
      alarmLog: {
        alarmTime: "报警时间",
        moduleName: "模组名称",
        alarmInformation: "报警信息",
        operationSuggestion: "操作建议",
        operation: "操作",
        details: "详情",
      },
      systemLog: {
        time: "时间",
        information: "信息",
      },
      operationLog: {
        operateTime: "操作时间",
        operateTor: "操作人",
        operateType: "操作类型",
        operateDetails: "操作详情",
        operation: "操作",
        details: "详情",
      },
      mesLog: {
        time: "时间",
        information: "信息",
      },
      productList: {
        productionResult: "生产结果",
        productionType: "生产类型",
        operation: "操作",
        details: "详情",
        ngReason: "NG原因",
        regularParts: "常规件",
        firstPiece: "首件",
        challengePiece: " 挑战件",
      },
      historicalData: {
        productBarCode: "产品条码",
        productModel: "产品型号",
        productionTime: "生产时间",
        startTime: "开始时间",
        endTime: "结束时间",
        query: "查询",
        export: "导出",
        time: "时间",
        operation: "操作",
        details: "详情",
        result: "产品结果",
        ngReason: "NG原因",
        offlineUpload: "离线上传",
        record: "数据补录",
        queryTip: "查询时间不能超过7天",
      },
    },
  },
  table: {
    alarmQuery: {
      alarmCode: "报警码",
      content: "内容",
      startTime: "开始时间",
      alarmDuration: "报警持续时间(秒)",
      alarmTimes: "报警次数",
      alarmInformation: "报警信息",
      serviceName: "服务名称",
      moduleName: "模组名称",
      operationSuggestion: "操作建议",
      remarks: "备注",
      operation: "操作",
      details: "详情",
    },
    logTask: {
      taskName: "任务名称",
      taskGroupName: "任务组名",
      startTime: "开始时间",
      lastExecutionTime: "上次执行时间",
      endTime: "结束时间",
      executionsNumber: "执行次数",
      failuresNumber: "失败次数",
      failureCause: "失败原因",
      jobStatus: "Job状态",
      triggerType: "触发器类型",
      description: "描述",
      operation: "操作",
    },
    operateQuery: {
      userName: "用户名称",
      serviceName: "服务名称",
      interfacePath: "接口路径",
      logLevel: "操作类型",
      content: "内容",
      request: "发送内容",
      creationTime: "创建时间",
      remark: "备注",
    },
    equipManage: {
      deviceName: "设备名称",
      serviceName: "服务名称",
      remark: "备注",
      serialNumber: "序号",
    },
    referenceLineConfig: {
      coding: "编码",
      secondCode: "二级标识编码",
      value: "值",
      remark: "备注",
      creationTime: "创建时间",
      updateTime: "更新时间",
      productInfoParams: "产品信息参数",
      relevantFailNG: "关联故障NG",
      upperLimitConfig: "设置上限",
      floorLimitConfig: "设置下限",
      selectType: "选择类型",
    },
    paramsConfig: {
      paramTypeName: "参数类型名称",
      paramType: "参数类型",
      paramKey: "参数key",
      paramValue: "参数值",
      remark: "备注",
      newParamType: "新增参数类型",
      currentParamType: "当前参数类型",
      newParam: "新增参数",
      syncConfig: "同步配置",
    },
    nationConfig: {
      key: "键",
      value: "值",
      language: "语言",
    },
    plcConfig: {
      connectionName: "连接名称",
      connectionCode: "连接码",
      protocolType: "协议类型",
      readPolicy: "读策略",
      writePolicy: "写策略",
      numFailedRetries: "失败重试次数",
      description: "描述",
      readWriteSeparation: "读写分离",
      failedRetryTime: "失败重试时间",
      connectionParameters: "连接参数",
    },
    interactConfig: {
      deviceName: "设备名称",
      deviceNumber: "设备编号",
      stationName: "工站名称",
      dataNumber: "数据编号",
      stationNumber: "工站编号",
      dataName: "数据名称",
      arrayNumber: "数组编号",
      arrayIndex: "数组索引",
      dataType: "数据类型",
      dataLength: "数据长度",
      PLCAddress: "PLC地址",
      collectionFrequency: "采集频率",
      SpcOrNot: "是否SPC",
      classification: "分类",
      orderBy: "序号",
    },
    userManagement: {
      name: "姓名",
      accountName: "账户名",
      nickname: "昵称",
      idCard: "ID卡号",
      role: "角色",
      gender: "性别",
      age: "年龄",
      phoneNumber: "手机号码",
      email: "邮箱",
      introduction: "简介",
      status: "状态",
      createTime: "创建时间",
      remark: "备注",
      male: "男",
      female: "女",
      unknown: "保密",
    },
    roleManagement: {
      roleName: "角色名称",
      roleNumber: "角色编号",
      displayOrder: "显示顺序",
      automaticLogout: "自动注销",
      logoutTime: "注销时间(分钟)",
      status: "状态",
      createTime: "创建时间",
      remark: "备注",
    },
    menuManagement: {
      menuName: "菜单名称",
      menuIcon: "菜单图标",
      menuOrder: "排序",
      permissionCode: "权限标识",
      routePath: "路由地址",
      componentPath: "组件路径",
      menuType: "菜单类型",
      displayStatus: "显示状态",
      isExternalLink: "是否外链",
      menuStatus: "菜单状态",
      createTime: "创建时间",
      remark: "备注",
      content: "目录",
      menu: "菜单",
      button: "按钮",
      lowcode: "低代码页面",
      pageSelect: "页面选择",
    },
    mesTable: {
      interfaceName: "接口名称",
      interfaceType: "接口类型",
      equipType: "设备类型",
      mesType: "MES类型",
      version: "版本号",
      remark: "备注",
      add: "新增MES接口类型",
      addParams: "新增参数配置",
      currentMesType: "当前MES类型",
      paramType: "参数类型",
      paramName: "参数名称",
      paramCode: "参数编号",
      paramValue: "参数值",
      dataType: "数据类型",
      setCurrentMesType: "设置当前MES类型",
      debug: "调试",
    },
    shiftTable: {
      config: "设置",
    },
    mudoleConfig: {
      moduleName: "模组名称",
      showModule: "模组显示",
      showData: "数据常显",
      setImg: "背景图设置",
      backgroundImg: "背景图",
      selectImg: "点击选择背景图",
      moduleListSetting: "模组列表设置",
    },
    visual: {
      haveReleased: "已发布",
      Instructions: "使用说明",
      Instructions_1:
        "1、交互表数据更新，已经无法匹配原配置项的数据，配置项将作废。",
      Instructions_2: "2、限制配置项数目分别为4个。",
      Instructions_3: "3、双击配置项，可进入画布编辑配置项内容",
      configList: "配置项列表",
      addConfig: "新建配置项",
      configName: "配置项名称",
      configType: "配置项类型",
      cover: "封面",
      config_prompt_1: "图片大小请保持在20k以内",
      config_prompt_2: "2D模组最多不能超过四个",
      config_prompt_3: "3D模组最多不能超过四个",
      config_prompt_4: "封面大小不能超过",
      config_prompt_5: "双击进入",
    },
  },
  dialog: {
    remarkInfo: "请输入备注",
    task: {
      newTask: "新增任务",
      editTask: "编辑任务",
      taskName: "任务名称",
      taskGroupName: "任务组名",
      triggerType: "触发器类型",
      executionInterval: "执行间隔时间",
      executionsNumber: "执行次数",
      description: "描述",
      cycle: "(默认无限循环)",
    },
    equipManage: {
      newEquip: "新增设备",
      editEquip: "编辑设备",
      deviceInfo: "请输入设备名称(非数字)",
      serviceInfo: "请输入服务名称",
      serialInfo: "请输入序号",
    },
    referenceLineConfig: {
      newReference: "新增基准线配置",
      editReference: "编辑基准线配置",
      codingInfo: "请输入编码",
      secondCodeInfo: "请输入二级标识码",
      valueInfo: "请输入值",
      productInfo: "请选择产品信息参数",
      relevantFailInfo: "请选择关联故障NG",
      upperLimitInfo: "请设置上限",
      floorLimitInfo: "请设置下限",
      selectTypeInfo: "请选择类型",
    },
    paramsConfig: {
      newParamColumns: "新增参数类型",
      editParamColumns: "编辑参数类型",
      parameColumnName: "参数类型名称",
      parameNameInfo: "请输入参数类型名称",
      parameColumnCode: "参数类型编码",
      parameCodeInfo: "请输入参数类型编码",
      parameColumnRemark: "参数类型备注",
      parameRemarkInfo: "请输入参数类型备注",
      newParam: "新增参数",
      editParam: "编辑参数",
      paramValueRemark: "参数值备注",
      paramKeyInfo: "请输入参数key",
      paramValueInfo: "请输入参数值",
      paramRemarkInfo: "请输入参数值备注",
    },
    nationConfig: {
      newNationConfig: "新增国际化配置",
      editNationConfig: "编辑国际化配置",
      keyInfo: "请输入键",
      valueInfo: "请输入值",
      langInfo: "请输入语言",
    },
    plcConfig: {
      newEquip: "新增PLC配置",
      editEquip: "编辑PLC配置",
      confirmParam: "确认参数",
      nameInfo: "请输入连接名称",
      codeInfo: "请输入连接码",
      codeError: "连接码已存在请重新输入",
      protocolInfo: "请输入协议类型",
      readInfo: "请输入读策略",
      writeInfo: "请输入写策略",
      selectInfo: "请选择",
      descriptionInfo: "请输入描述",
      connectParamInfo: "请输入连接参数",
      connectParamError: "连接参数必须为 JSON 对象",
      connectParamRule: "连接参数应为合法的 JSON 格式",
      keyInfo: "请输入key",
      keyError: "key值不能为空",
      keyRule: "key值中不能包含分号(;)",
      valueInfo: "请输入value",
      valueError: "value值不能为空",
      valueRule: "value值中不能包含分号(;)",
    },
    interactConfig: {
      newInteractConfig: "新增交互表配置",
      editInteractConfig: "编辑交互表配置",
      deviceNumberInfo: "请输入设备编号",
      deviceNameInfo: "请输入设备名称",
      stationNumberInfo: "请输入工站编号",
      stationNameInfo: "请输入工站名称",
      dataNumberInfo: "请输入数据编号",
      dataNameInfo: "请输入数据名称",
      dataLengthInfo: "请输入数据长度",
      dataTypeInfo: "请输入数据类型",
      classificationInfo: "请输入分类",
      collectionFrequencyInfo: "请输入采集频率",
      PLCAddressInfo: "请输入PLC地址",
      arrayNumberInfo: "请输入数组编号",
      arrayIndexInfo: "请输入数组索引",
      spcInfo: "请选择SPC",
      orderInfo: "请输入序号",
    },
    userManagement: {
      userPassword: "用户密码",
      accountNumber: "账号",
      resetPassword: "重置密码",
      newUser: "新增用户",
      editUser: "编辑用户",
      nameInfo: "请输入姓名",
      nickInfo: "请输入用户昵称",
      phoneInfo: "请输入手机号码",
      emailInfo: "请输入邮箱",
      accountInfo: "请输入账号",
      passwordInfo: "请输入用户密码",
      cardInfo: "请输入ID卡号",
      selectInfo: "请选择",
      introInfo: "请输入简介",
      newPdInfo: "的新密码",
      enterInfo: "请输入",
      resetPdInfo: "请输入重置密码",
    },
    roleManagement: {
      newRole: "新增角色",
      editRole: "编辑角色",
      nameInfo: "请输入角色名称",
      codeInfo: "请输入角色编码",
      menuPermission: "菜单权限",
    },
    menuManagement: {
      newMenu: "新增菜单",
      editMenu: "编辑菜单",
      rootMenu: "根类目",
      displaySort: "显示排序",
      permissionChar: "权限字符",
      routeParameter: "路由参数",
      superiorMenu: "上级菜单",
      superiorInfo: "选择上级菜单",
      iconInfo: "点击选择图标",
      nameInfo: "请输入菜单名称",
      routeURLInfo: "请输入路由地址",
      nameError: "菜单名称不能为空",
      orderError: "菜单顺序不能为空",
      routeError: "路由地址不能为空",
      compoInfo: "请输入组件路径",
      authInfo: "请输入权限标识",
      routeParamInfo: "请输入路由参数",
    },
    configForm: {
      dataNumber: "数据编号",
      standardLineCode: "标准线code",
      PLCAddress: "PLC地址",
      Baseline: "基准线",
      PLCInfo: "请输入PLC地址",
      referInfo: "请输入基准线",
    },
    shiftTable: {
      shiftName: "班次名称",
      shiftInfo: "请输入班次名称",
      startTime: "开始时间",
      startInfo: "请输入开始时间",
      endTime: "结束时间",
      endInfo: "请输入结束时间",
      timesInfo: "请输入班次时间",
      successInfo: "设置班次成功!",
    },
    mesDialog: {
      add: "新增MES接口类型",
      edit: "编辑MES接口类型",
      addParams: "新增参数配置",
      editParams: "编辑参数配置",
      interfaceName: "接口名称",
      interfaceType: "接口类型",
      equipType: "设备类型",
      mesType: "MES类型",
      version: "版本号",
      remark: "备注",
      paramType: "参数类型",
      paramName: "参数名称",
      paramCode: "参数编号",
      paramValue: "参数值",
      dataType: "数据类型",
    },
  },
  title: {
    equipManage: "设备管理",
    referenceLineConfig: "基准线配置",
    paramConfig: "参数配置",
    nationConfig: "国际化配置",
    plcConfig: "PLC配置",
    interactConfig: "交互表配置",
    shiftConfig: "班次配置",
    mesConfig: "MES配置",
  },
  tooltip: {
    common: {
      compoTooltip: "点击该按钮即可切换首页组件化配置",
    },
    menuManagement: {
      linkTooltip: "选择是外链则路由地址需要以`http(s)://`开头",
      routeURLTooltip:
        "访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",
      compoTooltip:
        "访问的组件路径，如：`system/user/index`，默认在`views`目录下",
      permissionTooltip:
        "控制器中定义的权限字符，(`如：system:user:add，添加用户权限，用于权限控制`)",
      routeParamTooltip:
        '访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`',
      displayTooltip: "选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",
      menuTooltip: "选择停用则路由将不会出现在侧边栏，也不能被访问",
    },
    interactConfig: {
      categoryTooltip: "存在多个分类请用英文逗号分割",
    },
  },
  vision: {
    versionInformation: "版本信息",
    projectName: "项目名称",
    backEndVersion: "后端版本",
    releaseDate: "发布日期",
    frontEndVersion: "前端版本",
    upperComputer: "上位机",
  },
}
