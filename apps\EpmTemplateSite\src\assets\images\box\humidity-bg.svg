<svg width="124" height="50" viewBox="0 0 124 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Img/Humidity">
<path id="Union" d="M25 1H121V49H25C11.7452 49 1 38.2548 1 25C1 11.7452 11.7452 1 25 1Z" fill="url(#paint0_linear_2022_2729)"/>
<circle id="Ellipse 536" cx="23" cy="23" r="23.5" transform="matrix(-1 0 0 1 48 2)" stroke="url(#paint1_linear_2022_2729)"/>
<circle id="Ellipse 538" cx="19" cy="19" r="19" transform="matrix(-1 0 0 1 44 6)" fill="#1B524B"/>
<path id="Union_2" d="M124 1H25C11.7452 1 1 11.7452 1 25C1 38.2548 11.7452 49 25 49H124" stroke="url(#paint2_linear_2022_2729)"/>
<g id="&#229;&#164;&#167;&#230;&#176;&#148;&#230;&#185;&#191;&#229;&#186;&#166; 2" clip-path="url(#clip0_2022_2729)">
<path id="Vector" d="M28.4571 26.3235V16.4575C28.4571 15.5405 28.0929 14.6611 27.4444 14.0127C26.796 13.3643 25.9166 13 24.9996 13C24.0827 13 23.2032 13.3643 22.5548 14.0127C21.9064 14.6611 21.5421 15.5405 21.5421 16.4575V26.3235C20.5323 27.0543 19.7798 28.0862 19.3929 29.2711C19.006 30.4561 19.0044 31.7332 19.3885 32.9191C19.7725 34.105 20.5224 35.1387 21.5305 35.8719C22.5386 36.6051 23.7531 37 24.9996 37C26.2462 37 27.4606 36.6051 28.4688 35.8719C29.4769 35.1387 30.2268 34.105 30.6108 32.9191C30.9949 31.7332 30.9933 30.4561 30.6064 29.2711C30.2194 28.0862 29.467 27.0543 28.4571 26.3235ZM24.9996 35.372C24.0979 35.3708 23.2196 35.0841 22.4908 34.5532C21.7619 34.0222 21.2197 33.2742 20.942 32.4163C20.6643 31.5583 20.6653 30.6345 20.9448 29.7771C21.2243 28.9198 21.768 28.1729 22.498 27.6435L23.1692 27.1554V16.4575C23.1692 15.972 23.362 15.5064 23.7053 15.1632C24.0486 14.8199 24.5142 14.6271 24.9996 14.6271C25.4851 14.6271 25.9507 14.8199 26.2939 15.1632C26.6372 15.5064 26.8301 15.972 26.8301 16.4575V27.1533L27.5012 27.6435C28.2312 28.1729 28.7749 28.9198 29.0544 29.7771C29.334 30.6345 29.3349 31.5583 29.0572 32.4163C28.7795 33.2742 28.2374 34.0222 27.5085 34.5532C26.7796 35.0841 25.9014 35.3708 24.9996 35.372Z" fill="url(#paint3_linear_2022_2729)"/>
<path id="Vector_2" d="M25.8132 28.585V20.7284H24.1862V28.585C23.5869 28.7788 23.0765 29.1802 22.7468 29.7169C22.4171 30.2536 22.2899 30.8903 22.388 31.5125C22.4861 32.1347 22.8031 32.7014 23.2819 33.1106C23.7607 33.5198 24.3699 33.7447 24.9997 33.7447C25.6296 33.7447 26.2387 33.5198 26.7175 33.1106C27.1963 32.7014 27.5133 32.1347 27.6114 31.5125C27.7095 30.8903 27.5823 30.2536 27.2526 29.7169C26.923 29.1802 26.4125 28.7788 25.8132 28.585ZM35.9376 21.9263C34.1336 19.8071 33.9648 19.0383 33.9485 18.9346C33.9343 19.0403 33.7655 19.8071 31.9615 21.9263C29.8524 24.4035 31.0727 27.501 33.9343 27.6434H33.9648C36.8243 27.499 38.0446 24.4035 35.9376 21.9263ZM34.3105 26.5492C34.2929 26.6288 34.2486 26.7001 34.185 26.7511C34.1214 26.8021 34.0423 26.8299 33.9607 26.8298C33.9337 26.8328 33.9064 26.8328 33.8794 26.8298C33.2531 26.7009 32.6966 26.3451 32.3167 25.8308C31.9368 25.3165 31.7603 24.68 31.8211 24.0435C31.8254 23.9969 31.8388 23.9516 31.8606 23.9102C31.8824 23.8688 31.9121 23.832 31.9481 23.8021C31.9841 23.7722 32.0256 23.7496 32.0703 23.7357C32.115 23.7218 32.162 23.7169 32.2086 23.7212C32.2552 23.7254 32.3005 23.7388 32.3419 23.7606C32.3833 23.7824 32.4201 23.8121 32.45 23.8481C32.4799 23.8841 32.5025 23.9256 32.5164 23.9703C32.5303 24.015 32.5352 24.062 32.5309 24.1086C32.5309 24.1798 32.415 25.7682 34.038 26.1424C34.1265 26.1629 34.2038 26.2164 34.254 26.2921C34.3043 26.3677 34.3238 26.4597 34.3085 26.5492H34.3105ZM18.8718 15.6703C17.7878 14.3992 17.6881 13.9375 17.678 13.8765C17.678 13.9395 17.5681 14.3992 16.4861 15.6703C15.2211 17.157 15.9513 19.0159 17.678 19.1013H17.6963C19.4026 19.0139 20.1348 17.157 18.8718 15.6703ZM15.034 22.4185C15.0238 22.4876 14.912 23.0002 13.71 24.4116C12.3026 26.0631 13.1161 28.1295 15.034 28.223H15.0543C16.9621 28.1274 17.7756 26.0631 16.3702 24.4116C15.156 23.0002 15.0442 22.4876 15.034 22.4185Z" fill="url(#paint4_linear_2022_2729)"/>
</g>
</g>
<defs>
<linearGradient id="paint0_linear_2022_2729" x1="1" y1="49" x2="121" y2="49" gradientUnits="userSpaceOnUse">
<stop stop-color="#3EE5DB"/>
<stop offset="0.144496" stop-color="#2DA5A5"/>
<stop offset="1" stop-color="#2696A5" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_2022_2729" x1="47" y1="22.5" x2="-2.3343e-06" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#92EBFF"/>
<stop offset="1" stop-color="#4EEEDB"/>
</linearGradient>
<linearGradient id="paint2_linear_2022_2729" x1="1" y1="49" x2="111" y2="49" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E998B" stop-opacity="0"/>
<stop offset="0.529929" stop-color="#3EE5E5"/>
<stop offset="1" stop-color="#1E998A" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_2022_2729" x1="24.9996" y1="13" x2="24.9996" y2="37" gradientUnits="userSpaceOnUse">
<stop stop-color="#C1E975"/>
<stop offset="1" stop-color="#3EE5E5"/>
</linearGradient>
<linearGradient id="paint4_linear_2022_2729" x1="25.0001" y1="13.8765" x2="25.0001" y2="33.7447" gradientUnits="userSpaceOnUse">
<stop stop-color="#C1E975"/>
<stop offset="1" stop-color="#3EE5E5"/>
</linearGradient>
<clipPath id="clip0_2022_2729">
<rect width="24" height="24" fill="white" transform="translate(13 13)"/>
</clipPath>
</defs>
</svg>
