<template>
  <!-- 表格 -->
  <el-table
    class="pro-table"
    v-loading="options.loading"
    :data="tableData"
    v-bind="_options"
    :row-class-name="tableRowClassName"
    header-row-class-name="tableheader"
    header-cell-class-name="pro-header-table-cell"
    cell-class-name="pro-table-cell"
    @selection-change="handleSelectionChange"
    @row-click="handleRowClick"
    @cell-click="handleCellClick"
  >
    <template v-for="(col, index) in columns" :key="index">
      <!---复选框, 序号 (START)-->
      <el-table-column
        v-if="
          col.type === 'index' ||
          col.type === 'selection' ||
          col.type === 'expand'
        "
        :align="col.align"
        :label="$t(col.label)"
        :type="col.type"
        :index="indexMethod"
        :width="col.width"
        :fixed="col.fixed"
      />
      <!---复选框, 序号 (END)-->
      <!-- 自定义slot (START) -->
      <el-table-column
        :show-overflow-tooltip="col.showOverflowTooltip"
        v-else-if="col.slot"
        :align="col.align"
        :label="$t(col.label)"
        :width="col.width"
        :fixed="col.fixed"
      >
        <template #default="scope">
          <slot :name="col.slot" :row="scope.row" :index="scope.$index"></slot>
        </template>
      </el-table-column>
      <!-- 自定义slot (END) -->
      <!-- 如果传递按钮数组，就展示按钮组 START-->
      <el-table-column
        v-else-if="col.buttons?.length"
        :align="col.align"
        :label="$t(col.label)"
        :width="col.width"
        :fixed="col.fixed || fixed"
      >
        <template #default="scope">
          <el-button-group>
            <el-button
              v-for="(btn, index) in col.buttons"
              size="small"
              :key="index"
              :type="btn.type"
              @click="handleAction(btn.command, scope)"
              >{{ $t(btn.name) }}</el-button
            >
          </el-button-group>
        </template>
      </el-table-column>
      <!-- 如果传递按钮数组，就展示按钮组 END-->
      <!-- 默认渲染列 (START) -->
      <el-table-column
        :show-overflow-tooltip="col.showOverflowTooltip"
        v-else
        :label="$t(col.label)"
        :prop="col.prop"
        :align="col.align"
        :width="col.width"
        :fixed="col.fixed"
      >
        <template #default="{ row }">
          <span class="fs14">{{ row[col.prop] ?? 0 }}</span>
        </template>
      </el-table-column>
      <!-- 默认渲染列 (END) -->
    </template>
  </el-table>
  <!-- 分页器 -->
  <div v-if="_options.showPagination" class="pro-pagination-box">
    <el-pagination
      class="pro-pagination"
      background
      v-bind="_paginationConfig"
      @size-change="pageSizeChange"
      @current-change="pageIndexChange"
    />
  </div>
</template>
<script setup>
import { computed, defineProps, defineEmits } from "vue"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()

const props = defineProps({
  tableData: {
    // table的数据
    type: Array,
    default: [],
  },
  columns: {
    // 每列的配置项
    // 对应列的类型。
    // type?: Type, 如果设置了selection则显示多选框如果设置了 index 则显示该行的索引（从 1 开始计算）； 如果设置了 expand 则显示为一个可展开的按钮
    // label: string,
    // prop?: string,
    // slot?: string
    // width?: string,
    // align?: Align,
    // showOverflowTooltip?: boolean,
    // buttons?: Array,
    type: Array,
    default: [],
  },
  options: {
    // height?: string | number, // Table 的高度， 默认为自动高度。 如果 height 为 number 类型，单位 px；如果 height 为 string 类型，则这个高度会设置为 Table 的 style.height 的值，Table 的高度会受控于外部样式。
    // stripe?: boolean, // 是否为斑马纹 table
    // maxHeight?: string | number, // Table 的最大高度。 合法的值为数字或者单位为 px 的高度。
    // size?: Size // Table 的尺寸
    // showHeader?: boolean // 是否显示表头,
    // tooltipEffect?: 'dark' | 'light' // tooltip effect 属性
    // loading: boolean, // 表格添加loading
    // showPagination?: boolean, // 是否展示分页器
    // paginationConfig?: Pagination, // 分页器配置项，详情见下方 paginationConfig 属性,
    // rowStyle?: ({ row, rowIndex }) => stirng | object // 行的 style 的回调方法，也可以使用一个固定的 Object 为所有行设置一样的 Style。
    type: Object,
    default: {},
  },
})

// 设置option默认值，如果传入自定义的配置则合并option配置项
const _options = computed(() => {
  const option = {
    loading: false,
    stripe: false,
    tooltipEffect: "dark",
    // height: 170,
    showHeader: true,
    showPagination: false,
    rowStyle: () => "cursor:pointer", // 行样式
  }
  return Object.assign(option, props?.options)
})

// 合并分页配置
const _paginationConfig = computed(() => {
  const config = {
    total: 0,
    pageIndex: 1,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
    layout: "total, sizes, prev, pager, next, jumper",
  }
  return Object.assign(config, _options.value.paginationConfig)
})

const emit = defineEmits([
  "selection-change", // 当选择项发生变化时会触发该事件
  "row-click", // 当某一行被点击时会触发该事件
  "cell-click", // 当某个单元格被点击时会触发该事件
  "command", // 按钮组事件
  "size-change", // pageSize事件
  "current-change", // pageIndex按钮组事件
  "pagination-change", // pageIndex或者pageSize改变触发
])
// 自定义索引
const indexMethod = index => {
  const tabIndex =
    index +
    (_paginationConfig.value.pageIndex - 1) * _paginationConfig.value.pageSize +
    1
  return tabIndex
}
// 切换pageSize
const pageSizeChange = pageSize => {
  emit("size-change", pageSize)
  emit("pagination-change", 1, pageSize)
}
// 切换pageIndex
const pageIndexChange = pageIndex => {
  emit("current-change", pageIndex)
  emit("pagination-change", pageIndex, _paginationConfig.value.pageSize)
}
// 按钮组事件
const handleAction = (command, scope) => {
  emit("command", command, scope.row)
}
// 多选事件
const handleSelectionChange = val => {
  emit("selection-change", val)
}
// 当某一行被点击时会触发该事件
const handleRowClick = (row, column, event) => {
  emit("row-click", row, column, event)
}
// 当某个单元格被点击时会触发该事件
const handleCellClick = (row, column, cell, event) => {
  emit("cell-click", row, column, cell, event)
}
// 偶数行颜色变化
const tableRowClassName = ({ row, rowIndex }) => {
  if (rowIndex % 2 !== 0) {
    return "even-row"
  }
  return ""
}
</script>

<style scoped lang="scss">
.pro-table.el-table {
  transition: all 0.3s;
  background-color: transparent;
  color: #fff;
  :deep(.tableheader) {
    background-color: rgba(12, 52, 88, 1);
    th.el-table__cell {
      background-color: rgba(35, 47, 73);
      color: #fff;
    }
  }

  :deep(.tableheader) {
    .el-table-fixed-column--right {
      background-color: var(--el-table-fixed-background);
    }
  }
  :deep(.pro-table-cell),
  :deep(.pro-header-table-cell) {
    padding: 0;
    height: 28px;
    background-color: var(--log-table-background);
    color: var(--table-color);
  }
  :deep(.el-table__row) {
    background-color: transparent;
  }
  :deep(.el-table__body) {
    tr:hover > td.el-table__cell {
      background-color: var(--el-table__cell);
    }
    .el-table__cell {
      border-right: none !important;
    }
  }
  :deep(.el-loading-spinner) {
    .path {
      stroke: #fff;
    }
    .el-loading-text {
      color: #fff;
    }
  }
  :deep(.el-loading-mask) {
    background-color: rgba(15, 20, 43, 0.6);
  }
}
/* 分页 */
.pro-pagination-box {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  :deep(.pro-pagination) {
    > span {
      color: var(--pro-pagination);
    }
    .el-input__wrapper {
      background-color: transparent;
      box-shadow: none;
      border: 1px solid #0098d3;
    }
    .el-input__inner {
      color: var(--pro-pagination);
    }
    .btn-next:disabled,
    .btn-prev:disabled,
    .btn-next,
    .btn-prev,
    .el-pager li {
      background-color: transparent;
      border: 1px solid #0098d3;
      color: var(--pro-pagination);
    }
  }
}
.fs14 {
  font-size: 14px;
}
</style>
