<template>
  <div class="pie" ref="pieRef"></div>
</template>
<script setup>
import useCharts from "@/hooks/useCharts"
import { ref, watch, computed } from "vue"
import { useStore } from "vuex"

const store = useStore()
let themeColor = computed(() => store.state.theme.themeColor)

let props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  pieState: {
    type: Array,
    default: () => [],
  },
})

let baseOption = {
  title: {
    text: "pieChart",
    show: false,
  },
  tooltip: {
    trigger: "item",
    formatter: function (a) {
      return a.data.name + "：" + a.data.value + "次，" + a.data.time + "分钟"
    },
  },
  color: ["#0084FE", "#4EEEA1", "#EE4E4E", "#EECB4E"],
  legend: {
    orient: "vertical",
    selectedMode: false,
    x: "right",
    bottom: "5%",
    textStyle: {
      color: "#fff",
    },
    data: ["待机", "运行", "故障", "停机"],
    formatter: function (name, b) {
      let time = 0,
        count = 0
      if (name == "待机") {
        time = props.pieState[0]?.time
        count = props.pieState[0]?.value
      } else if (name == "运行") {
        time = props.pieState[1]?.time
        count = props.pieState[1]?.value
      } else if (name == "故障") {
        time = props.pieState[2]?.time
        count = props.pieState[2]?.value
      } else if (name == "停机") {
        time = props.pieState[3]?.time
        count = props.pieState[3]?.value
      }
      return name + "  " + count + "次  " + time + "分钟"
    },
  },
  series: [
    {
      type: "pie",
      center: ["40%", "30%"],
      radius: ["0%", "50%"],
      label: {
        show: true,
        // position: "outside",
        normal: {
          formatter: function (a) {
            return a.value + "次" + "\n" + a.percent + "%"
          },
          color: "#0084FE ",
          lineHeight: 18,
        },
      },
      labelLine: {
        normal: {
          length: 20,
          length2: 30,
          lineStyle: {
            width: 1,
          },
        },
      },
      data: [],
    },
  ],
}

const DARK_COLOR = "#fff"
const LIGHT_COLOR = "#606266"

watch(
  () => themeColor.value,
  v => {
    if (v === "light") {
      baseOption.legend.textStyle.color = LIGHT_COLOR
    } else {
      baseOption.legend.textStyle.color = DARK_COLOR
    }
  },
  { immediate: true }
)

let pieRef = ref()
let { updateDraw } = useCharts(pieRef, baseOption)

watch(
  () => props.option,
  () => {
    updateDraw(props.option)
  },
  { deep: true }
)
</script>
<style scoped lang="scss">
.pie {
  width: 100%;
  height: 100%;
}
</style>
