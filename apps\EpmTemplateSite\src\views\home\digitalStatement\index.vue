<template>
  <div class="digit p-z2">
    <tabPane :head="head" v-model="activeTab" />
  </div>
</template>
<script setup>
import tabPane from "@/components/tab-pane/index.vue"
import productStatis from "./components/productStatis/index.vue"
import deviceStatus from "./components/deviceStatus/index.vue"
import faultAnalysis from "./components/faultAnalysis/index.vue"
import alarmAnalysis from "./components/alarmAnalysis/index.vue"
import { useI18n } from "@xfe/locale"
import { ref } from "vue"

const { t: $t } = useI18n()

let head = [
  {
    id: "productStatis",
    text: $t("digitalStatement.productionStatistics"),
    component: productStatis,
  },
  {
    id: "deviceStatus",
    text: $t("digitalStatement.equipmentStatus"),
    component: deviceStatus,
  },
  {
    id: "faultAnalysis",
    text: $t("digitalStatement.faultAnalysis"),
    component: faultAnalysis,
  },
  {
    id: "alarmAnalysis",
    text: $t("digitalStatement.alarmAnalysis"),
    component: alarmAnalysis,
  },
]
let activeTab = ref("productStatis")
</script>
<style scoped lang="scss">
.digit {
  height: 100%;
  width: 100%;
  background: var(--front-layout-background);
}
</style>
