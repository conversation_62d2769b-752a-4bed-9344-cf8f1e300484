import { reactive, watch, toRefs, onMounted, onUnmounted, ref } from "vue"
import { ElMessage } from "element-plus"
import useAuths from "@/core/useAuth"
import { useRoute } from "vue-router"
import { useI18n } from "@xfe/locale"
import { compareStrings } from "@/utils"

/**
 * @param ruleFormRef  form框校验demp
 * @param  cardRef  刷卡登录dom
 */
export default function useTanleView(ruleFormRef, cardRef) {
  const { t: $t } = useI18n()
  const route = useRoute()

  //加载完成
  onMounted(() => {
    //绑定事件
    window.addEventListener("keydown", keyDown)
  })

  onUnmounted(() => {
    //销毁事件
    window.removeEventListener("keydown", keyDown, false)
  })

  const ruleForm = reactive({
    userName: "",
    password: "",
  })
  const rules = reactive({
    userName: [{ required: true, message: "必填(required)", trigger: "blur" }],
    password: [{ required: true, message: "必填(required)", trigger: "blur" }],
  })

  const data = reactive({
    isTourist: false,
    redirect: "",
    otherQuery: {},
    isCard: false, // 刷卡登录
    collecting: true, // 校验
    cardId: "",
  })

  const { loginFun, loginByCard } = useAuths({ loginReUrl: data.redirect })

  const getOtherQuery = query => {
    return Object.keys(query).reduce((acc, cur) => {
      if (cur !== "redirect" && cur !== "unTourist") {
        acc[cur] = query[cur]
      }
      return acc
    }, {})
  }

  // 游客
  const touristParams = {
    userName: "visitor",
    password: "123456",
  }
  // 密码登录
  const login = async params => {
    try {
      loginFun(params)
    } catch (error) {
      ElMessage({
        message: error.message,
        type: "error",
        duration: 2000,
      })
    }
  }

  // 刷卡登录
  const cardLogin = async cardRef => {
    const value = cardRef.value
    if (!value) {
      ElMessage({
        message: $t("prompt.prompt_6"),
        type: "warning",
        duration: 2000,
      })
      return
    }
    data.collecting = true
    try {
      await loginByCard({ cardId: value })
      data.collecting = false
    } catch {
      data.cardId = ""
    }
  }

  // 点击登录
  const submitForm = async formEl => {
    if (!formEl) return
    await formEl.validate(valid => {
      if (valid) {
        login(ruleForm)
      }
    })
  }

  // 键盘事件处理
  const keyDown = e => {
    //如果是回车则执行登录方法
    if (!data.isCard && e.keyCode == 13) {
      submitForm(ruleFormRef.value)
    }
  }

  const resetForm = formEl => {
    if (!formEl) return
    formEl.resetFields()
  }
  const toFocus = () => {
    if (data.isCard) {
      cardRef.value?.focus()
    }
  }

  watch(
    () => route,
    route => {
      const { query } = route
      const { unTourist } = query
      // unTourist === true 非游客用户
      if (unTourist) {
        data.isTourist = false
      } else {
        data.isTourist = true
      }
      if (query) {
        data.redirect = query.redirect
        data.otherQuery = getOtherQuery(query)
      }
    },
    {
      immediate: true,
    }
  )

  watch(
    () => data.isTourist,
    val => {
      if (val) {
        login(touristParams)
      }
    },
    {
      immediate: false, // 设置为true时默认游客自动登录
    }
  )
  watch(
    () => data.isCard,
    val => {
      if (val) {
        cardRef.value?.focus()
      } else {
        cardRef.value?.blur()
      }
    }
  )

  const { isCard, collecting, cardId } = toRefs(data)

  // 模拟密码框
  const pwdCover = ref("")
  // 可以试下用watch监听值
  function setPassword(val) {
    let reg = /[0-9a-zA-Z,._!@#$%^&*]/g // 只允许输入字母和数字和部分字符
    let nDot = /[^●]/g // 非圆点字符
    let index = -1 // 新输入的字符位置
    let lastChar = 0 // 新输入的字符
    let realArr = ruleForm.password.split("") // 真实密码数组
    let coverArr = val.split("") // 文本框显示密码数组
    let coverLen = val.length // 文本框字符串长度
    let realLen = ruleForm.password.length // 真实密码长度
    let perPwd = ruleForm.password // 记录输入前的密码
    let curPwd = val
    // 找到新输入的字符及位置
    coverArr.forEach((el, idx) => {
      if (nDot.test(el)) {
        index = idx
        lastChar = el // 这里会导致每次只会进入一个值，所以复制进来的数据会出错
      }
    })
    if (curPwd.length - perPwd.length > 1) {
      lastChar = compareStrings(perPwd, curPwd)
    }
    // 判断输入的字符是否符合规范，不符合的话去掉该字符
    if (lastChar && !reg.test(lastChar)) {
      coverArr.splice(index, 1)
      pwdCover.value = coverArr.join("")
      return
    }

    if (realLen < coverLen) {
      // 新增字符
      realArr.splice(index, 0, lastChar)
    } else if (coverLen <= realLen && index !== -1) {
      // 替换字符（选取一个或多个字符直接替换）
      realArr.splice(index, realLen - (coverLen - 1), lastChar)
    } else {
      // 删除字符，因为 val 全是 ● ，没有办法匹配，不知道是从末尾还是中间删除的字符，删除了几个，不好对 password 处理，所以可以通过光标的位置和 val 的长度来判断
      let pos = document.getElementById("pwd").selectionEnd // 获取光标位置
      realArr.splice(pos, realLen - coverLen)
    }
    // 将 pwdCover 替换成 ●
    pwdCover.value = val.replace(/\S/g, "●")
    ruleForm.password = realArr.join("")
  }

  return {
    ruleForm,
    rules,
    isCard,
    collecting,
    cardId,
    touristParams,
    login,
    keyDown,
    toFocus,
    resetForm,
    submitForm,
    cardLogin,
    setPassword,
    pwdCover,
  }
}
