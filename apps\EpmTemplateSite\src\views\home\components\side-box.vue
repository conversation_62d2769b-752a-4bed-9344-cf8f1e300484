<template>
  <div class="box-container">
    <div class="flex-between">
      <div class="title">
        <div class="icon">
          <img src="@/assets/images/box/icon-title.svg" alt="" />
        </div>
        <div class="text">
          <span v-if="!slots.title"> {{ boxTitle }}</span>
          <slot name="title"></slot>
        </div>
      </div>
    </div>
    <div class="content-wrapper">
      <slot></slot>
    </div>
  </div>
</template>

<script setup name="BoxSide">
import { defineProps, defineEmits, useSlots } from "vue"

const emit = defineEmits(["toggleExpand"])

defineProps({
  boxTitle: {
    type: String,
    default: "",
  },
  iconName: {
    type: String,
    default: "",
  },
})

const slots = useSlots()
</script>
<style scoped lang="scss">
.box-container {
  height: 100%;
  min-height: 0; // 允许内容收缩
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  height: 100%;
  min-height: 0;
}

.flex-between {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 28px;
  line-height: 28px;
  border-bottom: 1px solid #0084fe;
  opacity: 1;
}
.title {
  color: var(--g-font-color);
  text-align: left;
  display: flex;
  align-items: center;
  font-size: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  :deep(.icon) {
    width: 20px;
    height: 20px;
    margin-right: 5px;
    position: relative;
    img {
      position: absolute;
      width: 100%;
      height: 100%;
    }
  }
  .jt {
    display: inline-block;
    width: 0;
    height: 0;
    border: 7px solid transparent;
    border-left: 7px solid #2cbeff;
  }
}

.edit-icon {
  color: #01c2ff;
  cursor: pointer;
  margin-right: 10px;
  &:hover {
    transform: scale(1.2);
  }
}

.detail-icon {
  display: flex;
  cursor: pointer;
  margin-right: 10px;
  &:hover {
    transform: scale(1.2);
  }
}
</style>
