<template>
  <Transition :name="direction">
    <slot v-if="show"></slot>
  </Transition>
</template>

<script setup>
import { ref, onMounted, onUnmounted, defineProps } from "vue"

defineProps({
  // slide-fade 从右往左出  left-slide-fade 从左往右出  bottom-slide-fade 从下往上  page-top-slide-fade 页面从上往下
  direction: {
    type: String,
    default: "slide-fade",
  },
})

let show = ref(false)
let tiemr = null

onMounted(() => {
  tiemr = setTimeout(() => {
    show.value = true
  }, 20)
})
onUnmounted(() => {
  tiemr = null
})
</script>
<style scoped lang="scss">
.transition-layout {
  // width: 100%;
  // height: 100%;
}
/**
* left --> right
* 从左往右出
*/
.left-slide-fade-enter-active {
  transition: all 0.4s ease-out;
}

.left-slide-fade-leave-active {
  transition: all 1s cubic-bezier(1, 0.5, 0.8, 1);
}

.left-slide-fade-enter-from,
.left-slide-fade-leave-to {
  transform: translateX(-360px);
  opacity: 0;
}

/**
* right --> left
* 从右往左出
*/
.slide-fade-enter-active {
  transition: all 0.4s ease-out;
}

.slide-fade-leave-active {
  transition: all 1s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(360px);
  opacity: 0;
}

// 页面整体从上滑出
.page-top-slide-fade-enter-active {
  transition: all 0.4s ease-out;
}
.page-top-slide-fade-leave-active {
  transition: all 1s cubic-bezier(1, 0.5, 0.8, 1);
}

.page-top-slide-fade-enter-from,
.page-top-slide-fade-leave-to {
  transform: translateY(-900px);
  opacity: 0;
}

// 从下往上滑出
.bottom-slide-fade-enter-active {
  transition: all 0.4s ease-out;
}
.bottom-slide-fade-leave-active {
  transition: all 1s cubic-bezier(1, 0.5, 0.8, 1);
}

.bottom-slide-fade-enter-from,
.bottom-slide-fade-leave-to {
  transform: translateY(300px);
  opacity: 0;
}
</style>

<style scoped lang="scss">
@media screen and (max-width: 1600px) {
  .page-top-slide-fade-enter-from,
  .page-top-slide-fade-leave-to {
    transform: translateY(-700px);
    opacity: 0;
  }
}
</style>

