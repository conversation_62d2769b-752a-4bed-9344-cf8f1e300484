<template>
  <div class="rubber-module">
    <!-- <xfe-transition direction="bottom-slide-fade"> -->
    <div class="left-container">
      <div class="log-box">
        <div class="module-pro-table">
          <box-side
            :boxTitle="$t('echarts.title.logInfo')"
            :isMore="true"
            @handleHistory="handleHistory(7)"
          >
            <template #icon>
              <img src="@/assets/images/box/log.svg" alt="" />
            </template>
            <LogTableCard
              :logOption="logOption"
              @closedDialog="handleLogDialogShow"
            />
          </box-side>
        </div>
        <div class="module-pro-table">
          <box-side
            :boxTitle="$t('echarts.title.productInfo')"
            :isMore="true"
            @handleHistory="handleHistory(8)"
          >
            <template #icon>
              <img src="@/assets/images/box/productionInfo.svg" alt="" />
            </template>
            <ProdTableCard
              :productList="productList"
              :isVisable="productShow"
              @closedDialog="handleProDialogShow"
            />
          </box-side>
        </div>
      </div>
    </div>
    <!-- </xfe-transition> -->
  </div>
  <!-- 日志信息详情 -->
  <dialog-card
    :dilogAddTitle="$t('echarts.title.logInfo')"
    :isVisable="logShow"
    dialogWidth="96%"
    @closeDialog="logShow = false"
    customClass="hsx-dialog"
  >
    <template #icon>
      <img src="@/assets/images/box/log.svg" alt="" />
    </template>
    <div class="log-info">
      <LogTableCard
        :logOption="logOption"
        :isZoom="logShow"
        @closedDialog="handleLogDialogShow"
      />
    </div>
  </dialog-card>

  <!-- 产品信息详情 -->
  <dialog-card
    :dilogAddTitle="$t('echarts.title.productInfo')"
    :isVisable="productShow"
    top="50px"
    dialogWidth="96%"
    @closeDialog="productShow = false"
    customClass="hsx-dialog"
  >
    <template #icon>
      <img src="@/assets/images/box/productionInfo.svg" alt="" />
    </template>
    <div class="pro-info">
      <ProdTableCard
        :productList="productList"
        :isZoom="productShow"
        @closedDialog="handleProDialogShow"
      />
    </div>
  </dialog-card>
</template>

<script setup>
import { ref, computed, watch } from "vue"
import { useStore } from "vuex"
import ProdTableCard from "./ProdTableCard"
import LogTableCard from "./LogTableCard"
import useEchartsData from "@/views/home/<USER>/useEchartsData"
import boxSide from "@/views/home/<USER>/side-box.vue"
import dialogCard from "@/components/dialog-card.vue"
import xfeTransition from "@/components/xfe-transition"

import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()
const { logOption, productList, handleHistory, logShow, productShow } =
  useEchartsData()

const store = useStore()

const handleProDialogShow = () => {
  productShow.value = false
}

const handleLogDialogShow = () => {
  logShow.value = false
}
</script>

<style lang="scss" scoped>
.rubber-module {
  flex: none;
  width: 100%;
  color: var(--card-title);
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .left-container {
    position: relative;
    flex: 1;
    // margin-right: 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
    overflow: hidden;

    > div {
      flex: 1;
      width: 100%;
      margin-bottom: 10px;

      &:nth-last-child(1) {
        margin-bottom: 0;
      }
    }

    .log-box {
      position: absolute;
      height: 100%;
      display: flex;
      justify-content: space-between;
      left: 0;
      z-index: 10;
    }

    .module-pro-table {
      flex: none;
      width: 49%;
      min-height: 40px;
      // background: linear-gradient(
      //   to top,
      //   rgba(32, 33, 47, 1),
      //   rgba(32, 33, 47, 0.2)
      // );
    }

    .shell-scan-code {
      left: 100px;
    }
  }
}
.log-info {
  height: 550px;
  :deep(.card-title) {
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    height: 10%;
  }
  :deep(.card-content) {
    margin-top: 1%;
    height: 100%;
  }
  :deep(.pro-table-cell),
  :deep(.pro-header-table-cell) {
    height: 40px !important;
  }
}

.pro-info {
  :deep(.pro-table-cell),
  :deep(.pro-header-table-cell) {
    height: 40px !important;
  }
}
</style>
