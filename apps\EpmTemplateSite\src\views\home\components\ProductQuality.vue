<template>
  <div class="p-chart" ref="productQualityEchartsRef"></div>
</template>

<script setup name="PieCharts">
import { ref, defineEmits, defineProps, defineExpose, onMounted, watch, nextTick } from "vue"
import useEcharts from "@/hooks/useEcharts"
import { productQualityEcharts } from "./echartsConfig"
const props = defineProps({
  option: {
    type: Object,
    default: () => { },
  },
  isShow: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits([
  "chart-click", // 点击chart
])
let productQualityEchartsRef = ref()
const { resize } = useEcharts(
  productQualityEchartsRef,
  emits,
  props,
  productQualityEcharts
)
defineExpose({
  resize,
})
// onMounted(() => {
//   console.log(1111111111111,props.isShow);
//   if(props.isShow){
//     resize()
//   }
// })
watch(
  () => props.isShow,
  v => {
    if (v) {
      nextTick(() => {
        resize()
      })
    }

  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.p-chart {
  width: 100%;
  height: 90%;
}
</style>
