<template>
  <tabPane :head="head" v-model="activeTab" borderBottom="" :keepAlive="true">
    <template #left>
      <div class="l-slot">二级：</div>
    </template>
  </tabPane>
</template>
<script setup>
import tabPane from "@/components/tab-pane/index.vue"
import { ref } from "vue"
import alarm from "./alarm/index.vue"
import opera from "./opera/index.vue"
import system from "./system/index.vue"
import mes from "./mes/index.vue"
import params from "./params/index.vue"
let head = [
  { id: "alarm", text: "报警日志", component: alarm },
  { id: "opera", text: "操作日志", component: opera },
  { id: "system", text: "系统日志", component: system },
  { id: "mes", text: "MES日志", component: mes },
  // { id: "params", text: "参数变更日志", component: params },
]
let activeTab = ref("alarm")
</script>
<style lang="scss" scoped>
.l-slot {
  height: 50px;
  line-height: 50px;
  margin-right: 10px;
}
</style>
