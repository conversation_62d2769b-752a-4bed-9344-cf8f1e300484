import { getServerLanguage } from "@/api/front/home"

const notice = {
  namespaced: true,
  state: {
    alarmInfo: [], // 报警信息
    alarmRankInfo: [], // 报警排行信息
    oeeInfo: {}, // Oee信息
    outputStatisticsInfo: [], // 产量统计信息
    sparePartsInfo: [], // 备件信息
    maintenanceInfo: {}, // 维保信息
    equipmentStatusInfo: {
      currentStatus: "", // 当前状态
      runningTime: 0, // 运行时长
      stopTime: 0, // 停机时长
      alarmTime: 0, // 报警时长
      resetTime: 0, // 复位时长
      initializeTime: 0, // 初始化时长
      statusList: [], // 状态列表
    }, // 设备状态信息
    language: "zh-CN", // 语言
    realTimeParameters: [], // 实时参数
  },
  mutations: {
    SET_ALARM_INFO(state, data) {
      state.alarmInfo = data
    },
    SET_ALARM_RANK_INFO(state, data) {
      state.alarmRankInfo = data
    },
    SET_OEE_INFO(state, data) {
      const oeeData = {
        oee: data.oee / 100,
        timeOperateRate: data.timeOperateRate / 100,
        performanceOperateRate: data.performanceOperateRate / 100,
      }
      state.oeeInfo = oeeData
    },
    SET_OUTPUT_STATISTICS_INFO(state, data) {
      state.outputStatisticsInfo = data
    },
    SET_SPARE_PARTS_INFO(state, data) {
      state.sparePartsInfo = data
    },
    SET_MAINTENANCE_INFO(state, data) {
      state.maintenanceInfo = data
    },
    SET_EQUIPMENT_STATUS_INFO(state, data) {
      state.equipmentStatusInfo = data
    },
    SET_LANGUAGE(state, data) {
      state.language = data
    },
    SET_REAL_TIME_PARAMETERS(state, data) {
      state.realTimeParameters = data
    },
  },
  actions: {
    // 获取当前初始化的语言
    getCurrentLanguage({ commit }) {
      getServerLanguage()
        .then(res => {
          commit("SET_LANGUAGE", res)
        })
        .catch(error => {
          console.log(error)
        })
    },
  },
  getters: {
    alarmInfo: state => state.alarmInfo,
    alarmRankInfo: state => state.alarmRankInfo,
    oeeInfo: state => state.oeeInfo,
    outputStatisticsInfo: state => state.outputStatisticsInfo,
    sparePartsInfo: state => state.sparePartsInfo,
    maintenanceInfo: state => state.maintenanceInfo,
    equipmentStatusInfo: state => state.equipmentStatusInfo,
    language: state => state.language,
    realTimeParameters: state => state.realTimeParameters,
  },
}

export default notice
