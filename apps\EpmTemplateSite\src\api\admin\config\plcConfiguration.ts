import { AxiosResponse } from "axios"
import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix
/**
 * 新增/编辑PLC配置参数
 */
export interface plcConnectionInfo {
  name: string
  uniqueCode: string
  protocolType: string
  readStrategy: string
  writeStrategy: string
  retryCount?: string
  description?: string
  isRWSplitting?: string
  retryTimeout?: string
  connectionParam?: any
}

/**
 * 新增/编辑PLC配置参数
 */

/**
 * 新增PLC配置
 */
export function createPlcConfigInfo(
  params: plcConnectionInfo
): Promise<AxiosResponse> {
  return request.post({
    url: `${homeServerPrefix}/PLCConnectionConfigInfo/Create`,
    params,
  })
}

/**
 * 更新PLC配置
 */
export function updatePlcConfigInfo(
  id: string,
  params: plcConnectionInfo
): Promise<AxiosResponse> {
  return request.put({
    url: `${homeServerPrefix}/PLCConnectionConfigInfo/Update/${id}`,
    params,
  })
}

/**
 * 删除PLC配置
 */
export function deletePlcConfigInfo(
  id: string,
  params: plcConnectionInfo
): Promise<AxiosResponse> {
  return request.delete({
    url: `${homeServerPrefix}/PLCConnectionConfigInfo/Remove/${id}`,
    params,
  })
}

/**
 * 获取协议类型
 */
export function getProtocolTypes(): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/PLCConnectionConfigInfo/ProtocolTypes`,
  })
}

/**
 * 获取读策略
 */
export function getReadStrategies(): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/PLCConnectionConfigInfo/ReadStrategies`,
  })
}

/**
 * 获取写策略
 */
export function getWriteStrategies(): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/PLCConnectionConfigInfo/WriteStrategies`,
  })
}

/**
 * PLC连接码
 */
export function checkUniqueCode(uniqueCode: string): Promise<AxiosResponse> {
  return request.post({
    url: `${homeServerPrefix}/PLCConnectionConfigInfo/UniqueCodeVerification/${uniqueCode}`,
  })
}
