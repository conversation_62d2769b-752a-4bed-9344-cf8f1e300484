<template>
  <div class="tab-page">
    <div class="tab-head" :style="{ borderBottom: borderBottom }">
      <slot name="left" class="tab"> </slot>
      <div class="head">
        <div
          v-for="tab in head"
          :key="tab.id"
          :class="[
            'head-item',
            tab.class ? tab.class : '',
            modelValue == tab.id ? 't-active' : '',
            tab.disabled ? 'disabled' : '',
          ]"
          @click="select(tab)"
        >
          {{ tab.text }}
        </div>
      </div>
    </div>
    <div class="tab-body">
      <template v-if="keepAlive">
        <keep-alive :exclude="exclude">
          <component :is="activeComp" v-on="event" />
        </keep-alive>
      </template>
      <template v-else>
        <component :is="activeComp" v-on="event" />
      </template>
    </div>
  </div>
</template>
<script setup>
import { computed } from "vue"

let props = defineProps({
  head: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: String,
    default: "",
  },
  keepAlive: {
    type: Boolean,
    default: false,
  },
  exclude: {
    type: [Array, String],
    default: () => [],
  },
  borderBottom: {
    type: String,
    default: "1px solid rgba(0,132,254,0.75)",
  },
})
let emit = defineEmits(["update:modelValue"])
let activeComp = computed(() => {
  let comp = ""
  props.head.some(t => {
    if (t.id == props.modelValue) {
      comp = t.component
    }
  })
  return comp
})

function select(tab) {
  if (tab.disabled) return
  emit("update:modelValue", tab.id)
}

let event = {
  changeTab: function changeTab(opt) {
    if (opt.type == "change-tab") {
      props.head[opt.index].disabled = false
      emit("update:modelValue", props.head[opt.index].id)
    }
    if (opt.type == "change-disabled") {
      props.head[opt.index].disabled = opt.value
    }
  },
}
</script>
<style lang="scss" scoped>
.tab-page {
  width: 100%;
  height: 100%;
  // background: var(--front-background);
  .tab-head {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-start;
    color: var(--g-font-color);
    font-size: 16px;
    background: var(--front-layout-background);
    padding: 0 10px;

    .l-slot {
      height: 50px;
      line-height: 50px;
      margin-right: 10px;
    }
    .head {
      height: 100%;
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .head-item {
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        min-width: 100px;
        padding: 0 20px;
        text-align: center;
        cursor: pointer;
        font-size: 16px;
      }
      .t-active {
        color: #fff;

        background-color: #0048ff;
      }
      .disabled {
        color: #a8abb2;
        cursor: not-allowed;
      }
    }
  }
  .tab-body {
    width: 100%;
    height: calc(100% - 50px);
    background: var(--front-layout-background);
    padding-top: 10px;
  }
}
</style>
