import * as XLSX from "xlsx"
import axios from "axios"
//检查excel 是否有img
const checkExcelHasImg = async (fileDownloadUrl) => {
  console.log(fileDownloadUrl, 'file');
  let buffer = await axios.get(fileDownloadUrl, { responseType: 'arraybuffer' })
  // 将ArrayBuffer转换为Uint8Array
  const data = new Uint8Array(buffer.data);

  // 3. 使用SheetJS解析数据
  const workbook = XLSX.read(data, { type: 'array' });
  let hasInvalidCells = false;

  // 遍历所有sheet
  workbook.SheetNames.forEach(sheetName => {
    const sheet = workbook.Sheets[sheetName];
    console.log(`\n正在检查工作表: ${sheetName}`);

    // 遍历所有单元格
    Object.keys(sheet).forEach(address => {
      console.log(address, 'address');

      // 检查address值是否等于!objects
      if (address === '!objects') {
        console.log(`[问题] 单元格 ${address} 地址为!objects`);
        hasInvalidCells = true;
        return hasInvalidCells;
      }

      const cell = sheet[address];
      let cellHasIssue = false;
      let issueType = '';

      // 检查单元格V值是否含=DISPIMG(
      if (cell && cell.v && typeof cell.v === 'string' && cell.v.includes('=DISPIMG(')) {
        issueType = '包含=DISPIMG(';
        cellHasIssue = true;
      }

      if (cellHasIssue) {
        console.log(`[问题] 单元格 ${address} ${issueType}`);
        hasInvalidCells = true;
      }
    });
  });

  return hasInvalidCells;
}

export default checkExcelHasImg