<template>
  <div class="log-table-card">
    <div class="card-title">
      <div class="log-tab">
        <div
          v-for="(item, index) of logTypeTab"
          :class="{ active: isTab == index }"
          :key="item.value"
          class="footer-log-tab-item"
          @click="changeTab(item, index)"
        >
          <div class="icon">
            <img
              src="@/assets/images/box/icon-selected.svg"
              alt=""
              v-show="isTab == index"
            />
            <img
              src="@/assets/images/box/icon-unselected.svg"
              alt=""
              v-show="isTab !== index"
            />
          </div>
          <div class="content">{{ $t(item.label) }}</div>
        </div>
      </div>
    </div>
    <div class="card-content">
      <div v-show="isTab == 0">
        <product-table
          :columns="tableColumnWarn"
          :table-data="alarmLogs"
          :options="options"
          @command="handlerCommand"
        >
        </product-table>
      </div>
      <div v-show="isTab == 2">
        <product-table
          :columns="tableColumnSys"
          :table-data="runLogs"
          :options="options"
          @command="handlerCommand"
        >
        </product-table>
      </div>
      <div v-show="isTab == 1">
        <product-table
          :columns="tableColumnOpt"
          :table-data="operationalLogs"
          :options="options"
          @command="handlerCommand"
        >
        </product-table>
      </div>
      <div v-show="isTab == 3">
        <product-table
          :columns="tableColumnSys"
          :table-data="mesLogs"
          :options="options"
          @command="handlerCommand"
        >
        </product-table>
      </div>
    </div>

    <!-- 详情 -->
    <hmx-dialog
      :dialogTitle="logDetails"
      dialogWidth="80%"
      :isVisable="isDetailVisable"
      @closeDialog="isDetailVisable = false"
      :append-to-body="true"
    >
      <log-detail v-if="isDetailVisable" :logInfo="curProInfo" />
    </hmx-dialog>
  </div>
</template>

<script setup name="ProdTableCard">
/* eslint-disable */
import { reactive, ref, toRefs, watch, defineProps, unref, computed } from "vue"
import LogDetail from "./LogDetail.vue"
import {
  logTypeTab,
  tableColumnWarn,
  tableColumnSys,
  tableColumnOpt,
} from "./config"
import { ProductTable } from "../../components/index"
import { useI18n } from "@xfe/locale"
import { useRouter } from "vue-router"
import HmxDialog from "@/components/hmx-dialog.vue"

const router = useRouter()
const { t: $t } = useI18n()
const logDetails = $t("card.dialogTitle.logDetails")

const props = defineProps({
  logOption: {
    type: Object,
    required: true,
    default: {
      runLogs: [],
      operationalLogs: [],
      alarmLogs: [],
      mesLogs: [],
    },
  },
  isDialogShow: {
    type: Boolean,
    default: false,
  },
  // 是否缩放弹窗
  isZoom: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(["closedDialog"])

let loading = ref(false)
// data--------
const data = reactive({
  isDetailVisable: false, // 详情弹出框
  isTableVisable: false, // 历史数据表格弹框
  curProInfo: {}, // 当前产品信息
  isTab: 0,
  // 表格配置
  options: {
    loading: loading.value,
    // showPagination: false,
    // rowStyle: () => "cursor:pointer; height:'30px'", // 行样式
    // maxheight: 140,
    paginationConfig: {
      total: 0,
      pageIndex: 1,
      pageSize: 10,
    },
  },
})

// watch------------

const tableOpt = reactive({
  runLogs: [],
  operationalLogs: [],
  alarmLogs: [],
  mesLogs: [],
})

Object.assign(tableOpt, props.logOption)

const { runLogs, operationalLogs, alarmLogs, mesLogs } = toRefs(tableOpt)
// methods----------
const handlerCommand = (command, row) => {
  if (row.message) {
    data.curProInfo = row.message
  } else {
    data.curProInfo = row
  }
  data.isDetailVisable = true
}

const changeTab = (item, index) => {
  data.isTab = index
}

// 点击查看实时数据逻辑
const isZoom = computed(() => props.isZoom)
watch(
  () => isZoom.value,
  v => {
    if (v) {
      data.options.height = "100%"
    } else {
      // data.options.height = 140
    }
  },
  { immediate: true }
)

const { options, isDetailVisable, isTableVisable, curProInfo, isTab } =
  toRefs(data)
</script>

<style lang="scss" scoped>
.log-table-card {
  background-color: var(--prod-table-card);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  :deep(.el-table) {
    height: calc(100% - 40px);
  }
  .card-title {
    height: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 8px;
  }
  .card-content {
    height: 80%;
    > div {
      height: 100%;
    }
  }
  .btn {
    background-color: var(--btn-background);
  }
}
.footer-log-tab-item {
  cursor: pointer;
  display: flex;
  justify-content: center;
  font-size: 16px;
  line-height: 30px;
  height: 30px;
  width: 130px;
  text-align: center;
  background: url("~@/assets/images/box/button-unselected.svg") no-repeat;
  background-size: 100%;
  color: #d9d9d9;
  border-radius: 2px;
  margin-right: 10px;
  .icon {
    width: 12px;
    height: 12px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .content {
    margin-left: 3px;
  }
}
.log-tab {
  display: flex;
}
.active {
  background: url("~@/assets/images/box/button-selected.svg") no-repeat;
  background-size: 100%;
  color: #fff;
  font-weight: bold;
}
:deep(.el-button-group) > .el-button:first-child:last-child {
  background: transparent;
  border: transparent;
  color: #0084fe;
  font-size: 14px;
}
</style>
