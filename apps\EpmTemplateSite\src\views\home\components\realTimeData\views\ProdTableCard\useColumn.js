import { useI18n } from "@xfe/locale"
/**
 * 配置产品表头项
 */
// 产品列表表头 isProductType 是否有产品类型 list 其余扩展字段
export function useColumn(isProductType = false, list = []) {
  const { t: $t } = useI18n()

  const arr1 = [
    {
      type: "selection",
      label: "No.",
      align: "center",
      fixed: "left",
      width: "50",
    },
    {
      prop: "barCode",
      label: $t("card.table.historicalData.productBarCode"),
      align: "center",
      fixed: "left",
      width: "280",
      showOverflowTooltip: true,
    },
    {
      prop: "result",
      width: "120",
      label: $t("card.table.productList.productionResult"),
      slot: "result",
      align: "center",
    },
    {
      prop: "ngCode",
      width: "80",
      label: "班次",
      align: "center",
    },
    // {
    //   prop: "ngCode",
    //   width: "120",
    //   label: $t("card.table.productList.ngReason"),
    //   align: "center",
    // },
    {
      prop: "productCode",
      width: "120",
      label: $t("card.table.historicalData.productModel"),
      align: "center",
      showOverflowTooltip: true,
    },
  ]
  const arr2 = isProductType
    ? [
        {
          prop: "productType",
          width: "120",
          label: $t("card.table.productList.productionType"),
          slot: "productType",
          align: "center",
        },
      ]
    : []
  const arr3 = [
    {
      prop: "creationTime",
      label: $t("card.table.historicalData.time"),
      width: "200",
      align: "center",
      showOverflowTooltip: true,
    },
    // {
    //   // width: "120",
    //   label: $t("card.table.historicalData.operation"),
    //   align: "center",
    //   fixed: "right",
    //   buttons: [
    //     {
    //       name: $t("card.table.historicalData.details"),
    //       type: "default",
    //       command: "detail",
    //     },
    //   ],
    // },
  ]
  let tableHead = [...arr1, ...arr2, ...list, ...arr3]
  return tableHead
}
