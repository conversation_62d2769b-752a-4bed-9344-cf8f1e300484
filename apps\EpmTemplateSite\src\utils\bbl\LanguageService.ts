/**
 * 语言类型
 */
export type Language = "zh-CN" | "en-US"

/**
 * 翻译映射接口
 */
export interface TranslationMap {
  [key: string]: {
    zh: string
    en: string
  }
}

/**
 * 语言服务
 * 负责管理语言切换和翻译
 */
export class LanguageService {
  private static instance: LanguageService
  private currentLanguage: Language = "zh-CN"

  // 翻译映射表
  private translations: Record<Language, Record<string, string>> = {
    "zh-CN": {
      // 通用
      switchToEnglish: "切换到英文",
      switchToChinese: "切换到中文",
      wholeDevice: "整机设备",
      internalDevice: "内部设备",

      // 参数
      paramDetails: "参数详情",
      temperature: "温度",
      pressure: "压力",
      speed: "速度",
      voltage: "电压",
      current: "电流",
      power: "功率",
      runtime: "运行时间",
      status: "状态",
      lastMaintenance: "上次维护",
      nextMaintenance: "下次维护",
      normal: "正常",
      maintenance: "需要维护",

      // 单位
      celsius: "°C",
      mpa: "MPa",
      rpm: "RPM",
      volt: "V",
      ampere: "A",
      kilowatt: "kW",
      hour: "小时",

      // 模型操作
      flyToModel: "飞行定位",
      selectModel: "选择模型...",
      searchModel: "搜索模型...",
      noModelsFound: "未找到匹配的模型",

      // 报警信息
      alarmContent: "报警内容",
      alarmGroup: "报警分组",

      // 参数显示
      realTimeParams: "实时参数",
      noParamsData: "暂无参数数据",

      // 模型名称
      AUnwindingAxis: "放卷A轴",
      BUnwindingAxis: "放卷B轴",
      UnwindingSpliceRotationAxis: "放卷接带平台",
      UnwindingSpliceSlitterAxis: "放卷贴胶模组",
      Alarm_UnwindingGuidingWidthAdjustAxis: "放卷纠偏模组",
      zmjqj: "正面加强筋",
      bmjqj: "背面加强筋",
      jgqjp: "激光前纠偏模组",
      MainAxis: "主驱模组",
      zmfd: "正面风刀模组",
      bmfd: "背面面风刀模组",
      fqdmz: "分切刀模组",
      BrushUpAAxis: "上毛刷除尘模组",
      BrushDownAAxis: "下毛刷除尘模组",
      Laser1WidthAdjustAxis: "上打标模组",
      Laser2WidthAdjustAxis: "下打标模组",
      UpBRewindingAxis: "上收卷B轴",
      DownBRewindingAxis: "下收卷B轴",
      UpARewindingAxis: "上收卷A轴",
      DownARewindingAxis: "下收卷A轴",
      DownRewindingRubberizingQAixs: "下收卷贴胶模组",
      UpRewindingRubberizingQAixs: "上收卷贴胶模组",
    },
    "en-US": {
      // 通用
      switchToEnglish: "Switch to English",
      switchToChinese: "Switch to Chinese",
      wholeDevice: "Whole Device",
      internalDevice: "Internal Device",

      // 参数
      paramDetails: "Parameter Details",
      temperature: "Temperature",
      pressure: "Pressure",
      speed: "Speed",
      voltage: "Voltage",
      current: "Current",
      power: "Power",
      runtime: "Runtime",
      status: "Status",
      lastMaintenance: "Last Maintenance",
      nextMaintenance: "Next Maintenance",
      normal: "Normal Operation",
      maintenance: "Maintenance Required",

      // 单位
      celsius: "°C",
      mpa: "MPa",
      rpm: "RPM",
      volt: "V",
      ampere: "A",
      kilowatt: "kW",
      hour: "hours",

      // 模型操作
      flyToModel: "Fly to Model",
      selectModel: "Select Model...",
      searchModel: "Search Model...",
      noModelsFound: "No models found",

      // 报警信息
      alarmContent: "Alarm Content",
      alarmGroup: "Alarm Group",

      // 参数显示
      realTimeParams: "Real-time Parameters",
      noParamsData: "No parameter data available",

      // 模型名称
      AUnwindingAxis: "Unwinding A Axis",
      BUnwindingAxis: "Unwinding B Axis",
      UnwindingSpliceRotationAxis: "Unwinding Splice Platform",
      UnwindingSpliceSlitterAxis: "Unwinding Splice Slitter Module",
      Alarm_UnwindingGuidingWidthAdjustAxis:
        "Unwinding Guiding Width Adjust Module",
      zmjqj: "Front Reinforcement",
      bmjqj: "Back Reinforcement",
      jgqjp: "Laser Front Guiding Module",
      MainAxis: "Main Drive Module",
      zmfd: "Front Air Knife Module",
      bmfd: "Back Air Knife Module",
      fqdmz: "Slitting Knife Module",
      BrushUpAAxis: "Upper Brush Dust Removal Module",
      BrushDownAAxis: "Lower Brush Dust Removal Module",
      Laser1WidthAdjustAxis: "Upper Marking Module",
      Laser2WidthAdjustAxis: "Lower Marking Module",
      UpBRewindingAxis: "Upper Rewinding B Axis",
      DownBRewindingAxis: "Lower Rewinding B Axis",
      UpARewindingAxis: "Upper Rewinding A Axis",
      DownARewindingAxis: "Lower Rewinding A Axis",
      DownRewindingRubberizingQAixs: "Lower Rewinding Rubberizing Module",
      UpRewindingRubberizingQAixs: "Upper Rewinding Rubberizing Module",
    },
  }

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {}

  /**
   * 获取单例实例
   * @returns LanguageService实例
   */
  public static getInstance(): LanguageService {
    if (!LanguageService.instance) {
      LanguageService.instance = new LanguageService()
    }
    return LanguageService.instance
  }

  /**
   * 获取当前语言
   * @returns 当前语言
   */
  public getCurrentLanguage(): Language {
    return this.currentLanguage
  }

  /**
   * 设置语言
   * @param language 要设置的语言
   */
  public setLanguage(language: Language): void {
    this.currentLanguage = language
  }

  /**
   * 切换语言
   * @returns 切换后的语言
   */
  public toggleLanguage(): Language {
    this.currentLanguage = this.currentLanguage === "zh-CN" ? "en-US" : "zh-CN"
    return this.currentLanguage
  }

  /**
   * 翻译文本
   * @param key 翻译键
   * @returns 翻译后的文本
   */
  public translate(key: string): string {
    if (
      this.translations[this.currentLanguage] &&
      this.translations[this.currentLanguage][key]
    ) {
      return this.translations[this.currentLanguage][key]
    }
    return key
  }

  /**
   * 获取模型名称
   * @param id 模型ID
   * @param defaultName 默认名称
   * @returns 翻译后的模型名称
   */
  public getModelName(id: string, defaultName: string): string {
    if (
      this.translations[this.currentLanguage] &&
      this.translations[this.currentLanguage][id]
    ) {
      return this.translations[this.currentLanguage][id]
    }
    return defaultName
  }

  /**
   * 添加翻译
   * @param key 翻译键
   * @param zhText 中文文本
   * @param enText 英文文本
   */
  public addTranslation(key: string, zhText: string, enText: string): void {
    this.translations["zh-CN"][key] = zhText
    this.translations["en-US"][key] = enText
  }
}
