import { ref } from "vue"
// 传入小时，并开始倒计时 ，输出格式为00:00:00
export default function useCountDown() {
  let timer = null // 定时器对象
  let backTime = ref("")
  const isEndCountDown = ref<boolean>(true)

  function formatTime(seconds: number) {
    const h = Math.floor(seconds / 3600)
    const m = Math.floor((seconds % 3600) / 60)
    const s = Math.floor(seconds % 60)
    return `${padZero(h)}:${padZero(m)}:${padZero(s)}`
  }

  function padZero(num: number) {
    // 补零函数
    return num.toString().padStart(2, "0")
  }
  const countdownTime = ref(0)
  function startCountDown(hours: number) {
    countdownTime.value = hours * 60 * 60 // 将小时数转换为秒数
    timer = setInterval(() => {
      countdownTime.value--
      if (countdownTime.value < 0) {
        resetTime()
        isEndCountDown.value = true
      } else {
        isEndCountDown.value = false
        backTime.value = formatTime(countdownTime.value)
      }
    }, 1000)
  }

  function resetTime(num?: number) {
    clearInterval(timer) // 清除定时器
    backTime.value = ""
    startCountDown(num)
  }

  function clearTime() {
    clearInterval(timer) // 清除定时器
    backTime.value = ""
    countdownTime.value = 0
  }

  return {
    backTime,
    startCountDown,
    isEndCountDown,
    resetTime,
    countdownTime,
    clearTime,
  }
}
