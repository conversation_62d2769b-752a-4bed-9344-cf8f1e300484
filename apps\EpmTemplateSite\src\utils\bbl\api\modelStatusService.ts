import type { ModelStatusData } from '../ModelStatusManager';
import { LanguageService } from '../LanguageService';

/**
 * 后台报警数据接口（原有格式，保持向后兼容）
 */
export interface BackendAlarmData {
  modeID: string;   // 后台传来的模型ID
  isAlarming: string; // 后台传来的报警状态（字符串形式的"true"或"false"）
}

/**
 * 真实报警数据接口（SignalR推送的格式）
 */
export interface RealTimeAlarmData {
  alarmCode: string;        // 报警代码
  alarmContent: string;     // 报警内容
  startTime: string;        // 开始时间
  durationSeconds: number;  // 持续时间（秒）
  moduleCode: string;       // 模块代码（对应模型ID）
  isSparePart: boolean;     // 是否是易损件
  alarmGroup: string | null; // 报警组
}

/**
 * 飞行定位结果接口
 */
export interface FlyToModelResult {
  success: boolean;
  message: string;
  modelId?: string;
}

/**
 * 模型状态服务
 * 负责获取和处理模型状态数据
 */
export class ModelStatusService {
  private pollingInterval: number | null = null;
  private alarmDataCallback: ((data: BackendAlarmData[]) => void) | null = null;
  private realTimeAlarmCallback: ((data: RealTimeAlarmData[]) => void) | null = null;
  private languageService: LanguageService;
  private flyToModelCallback: ((modelId: string) => Promise<FlyToModelResult>) | null = null;

  constructor() {
    this.languageService = LanguageService.getInstance();
  }

  /**
   * 获取模型状态数据
   * @returns 模型状态数据列表
   */
  public async getModelStatusData(): Promise<ModelStatusData[]> {
    // 这里模拟从后端获取数据
    // 在实际应用中，可以替换为真实的API调用
    return this.getMockData();
  }

  /**
   * 获取模拟数据
   * @returns 模拟的模型状态数据列表
   */
  private getMockData(): ModelStatusData[] {
    const modelIds = [
      "AUnwindingAxis",
      "BUnwindingAxis",
      "UnwindingSpliceRotationAxis",
      "UnwindingSpliceSlitterAxis",
      "Alarm_UnwindingGuidingWidthAdjustAxis",
      "zmjqj",
      "bmjqj",
      "jgqjp",
      "MainAxis",
      "zmfd",
      "bmfd",
      "fqdmz",
      "BrushUpAAxis",
      "BrushDownAAxis",
      "Laser1WidthAdjustAxis",
      "Laser2WidthAdjustAxis",
      "UpBRewindingAxis",
      "DownBRewindingAxis",
      "UpARewindingAxis",
      "DownARewindingAxis",
      "DownRewindingRubberizingQAixs",
      "UpRewindingRubberizingQAixs"
    ];

    return modelIds.map(id => ({
      ID: id,
      modelName: this.languageService.getModelName(id, id),
      isAlarming: false,
      showCoordinates: true // 固定列表中的模型显示坐标
    }));
  }

  /**
   * 更新模型状态数据
   * @param id 模型ID
   * @param isAlarming 是否处于报警状态
   * @returns 更新后的模型状态数据列表
   */
  public async updateModelStatus(id: string, isAlarming: boolean): Promise<ModelStatusData[]> {
    // 获取当前数据
    const currentData = await this.getModelStatusData();

    // 查找并更新指定ID的模型状态
    const updatedData = currentData.map(item => {
      if (item.ID === id) {
        return { ...item, isAlarming };
      }
      return item;
    });

    // 在实际应用中，这里应该调用API将更新后的数据发送到后端
    // 这里只是返回更新后的数据
    return updatedData;
  }

  /**
   * 批量更新模型状态数据
   * @param updates 要更新的模型状态数据列表
   * @returns 更新后的模型状态数据列表
   */
  public async batchUpdateModelStatus(updates: { id: string; isAlarming: boolean }[]): Promise<ModelStatusData[]> {
    // 获取当前数据
    const currentData = await this.getModelStatusData();

    // 创建ID到更新状态的映射
    const updateMap = new Map<string, boolean>();
    updates.forEach(update => {
      updateMap.set(update.id, update.isAlarming);
    });

    // 查找并更新指定ID的模型状态
    const updatedData = currentData.map(item => {
      if (updateMap.has(item.ID)) {
        return { ...item, isAlarming: updateMap.get(item.ID)! };
      }
      return item;
    });

    // 在实际应用中，这里应该调用API将更新后的数据发送到后端
    // 这里只是返回更新后的数据
    return updatedData;
  }

  /**
   * 处理后台发送的报警数据
   * 将后台的报警数据格式转换为内部使用的模型状态数据格式
   * @param backendData 后台发送的报警数据
   * @returns 更新后的模型状态数据列表
   */
  public async handleBackendAlarmData(backendData: BackendAlarmData[]): Promise<ModelStatusData[]> {
    // 获取当前所有模型状态数据
    const currentData = await this.getModelStatusData();

    // 创建固定模型ID列表的集合，用于快速查找
    const fixedModelIds = new Set(this.getMockData().map(model => model.ID));

    // 创建ID到报警状态的映射
    const alarmMap = new Map<string, boolean>();
    backendData.forEach(item => {
      // 将字符串的"true"/"false"转换为布尔值
      const isAlarming = item.isAlarming.toLowerCase() === "true";
      alarmMap.set(item.modeID, isAlarming);
    });

    // 更新模型状态
    const updatedData = currentData.map(item => {
      // 如果后台数据中包含此模型的报警状态，则更新
      if (alarmMap.has(item.ID)) {
        return { ...item, isAlarming: alarmMap.get(item.ID)! };
      }
      // 否则保持原状态不变
      return item;
    });

    // 检查是否有非固定列表中的模型ID，如果有，添加到updatedData中
    backendData.forEach(item => {
      const modelID = item.modeID;
      if (!updatedData.some(data => data.ID === modelID)) {
        // console.log(`添加非固定列表中的模型ID: ${modelID}`);
        const isAlarming = item.isAlarming.toLowerCase() === "true";
        updatedData.push({
          ID: modelID,
          modelName: this.languageService.getModelName(modelID, modelID),
          isAlarming: isAlarming,
          showCoordinates: fixedModelIds.has(modelID) // 只有固定列表中的模型显示坐标
        });
      }
    });

    return updatedData;
  }

  /**
   * 处理真实格式的报警数据（SignalR推送的数据）
   * moduleCode直接对应模型ID
   * @param realTimeAlarmData 真实报警数据数组
   * @returns 更新后的模型状态数据列表
   */
  public async handleRealTimeAlarmData(realTimeAlarmData: RealTimeAlarmData[]): Promise<ModelStatusData[]> {
    // 获取当前所有模型状态数据
    const currentData = await this.getModelStatusData();

    // 创建固定模型ID列表的集合，用于快速查找
    const fixedModelIds = new Set(this.getMockData().map(model => model.ID));

    // 创建moduleCode（即模型ID）到报警状态的映射
    const alarmMap = new Map<string, boolean>();

    // 创建moduleCode到报警详细信息的映射
    const alarmInfoMap = new Map<string, RealTimeAlarmData>();

    // 处理真实报警数据
    realTimeAlarmData.forEach(item => {
      // moduleCode直接对应模型ID
      const modelId = item.moduleCode;
      // 如果报警数据存在且持续时间>=0，认为是活跃报警
      const isAlarming = item.durationSeconds >= 0;
      alarmMap.set(modelId, isAlarming);

      // 保存报警详细信息
      alarmInfoMap.set(modelId, item);

      // console.log(`处理报警数据: moduleCode=${item.moduleCode}, modelId=${modelId}, isAlarming=${isAlarming}, alarmCode=${item.alarmCode}`);
    });

    // 更新模型状态
    const updatedData = currentData.map(item => {
      // 如果报警数据中包含此模型，则更新状态
      if (alarmMap.has(item.ID)) {
        return {
          ...item,
          isAlarming: alarmMap.get(item.ID)!,
          alarmInfo: alarmInfoMap.get(item.ID)
        };
      }
      // 否则保持原状态不变
      return item;
    });

    // 检查是否有新的模型ID（moduleCode），如果有，添加到updatedData中
    realTimeAlarmData.forEach(item => {
      const modelId = item.moduleCode;
      if (!updatedData.some(data => data.ID === modelId)) {
        // console.log(`添加新模型ID: ${modelId} (moduleCode: ${item.moduleCode})`);
        const isAlarming = item.durationSeconds >= 0;
        updatedData.push({
          ID: modelId,
          modelName: this.languageService.getModelName(modelId, item.alarmCode || modelId),
          isAlarming: isAlarming,
          showCoordinates: fixedModelIds.has(modelId), // 只有固定列表中的模型显示坐标
          alarmInfo: item // 保存报警详细信息
        });
      }
    });

    return updatedData;
  }

  /**
   * 设置飞行定位到模型的回调函数
   * @param callback 回调函数，接收模型ID，返回定位结果
   */
  public setFlyToModelCallback(callback: (modelId: string) => Promise<FlyToModelResult>): void {
    this.flyToModelCallback = callback;
  }

  /**
   * 飞行定位到指定模型
   * @param modelId 模型ID
   * @returns 定位结果
   */
  public async flyToModel(modelId: string): Promise<FlyToModelResult> {
    if (this.flyToModelCallback) {
      return this.flyToModelCallback(modelId);
    }
    return {
      success: false,
      message: '飞行定位功能未初始化',
      modelId
    };
  }

  /**
   * 设置真实报警数据回调
   * @param callback 真实报警数据回调函数
   */
  public setRealTimeAlarmCallback(callback: (data: RealTimeAlarmData[]) => void): void {
    this.realTimeAlarmCallback = callback;
  }

  /**
   * 启动真实报警数据监控
   * 注意：实际的SignalR连接应该在initSignalr.js中建立
   * 这里只是设置回调和模拟数据
   * @param callback 收到真实报警数据时的回调函数
   */
  public async startRealTimeAlarmMonitoring(callback: (data: RealTimeAlarmData[]) => void): Promise<boolean> {
    this.realTimeAlarmCallback = callback;

    // console.log('设置报警数据回调，等待initSignalr.js建立连接');

    // 模拟真实报警数据（用于测试）
    this.simulateRealTimeAlarmData();

    return true;
  }

  /**
   * 停止真实报警数据监控
   */
  public async stopRealTimeAlarmMonitoring(): Promise<void> {
    this.realTimeAlarmCallback = null;
    // console.log('停止报警数据监控');
  }

  /**
   * 供外部调用的方法，用于处理从initSignalr.js接收到的报警数据
   * @param alarmData 从SignalR接收到的报警数据
   */
  public handleSignalRAlarmData(alarmData: RealTimeAlarmData[]): void {
    if (this.realTimeAlarmCallback) {
      // console.log('接收到initSignalr.js传来的报警数据:', alarmData);
      this.realTimeAlarmCallback(alarmData);
    }
  }

  /**
   * 模拟真实报警数据（用于测试）
   */
  private simulateRealTimeAlarmData(): void {
    // 模拟接收真实格式的报警数据
    const simulateData = () => {
      if (!this.realTimeAlarmCallback) return;

      // 创建模拟的真实报警数据，moduleCode直接作为模型ID
      const mockRealTimeData: RealTimeAlarmData[] = [
        {
          alarmCode: "下分切刀",
          alarmContent: "下分切刀已到达设定报警距离",
          startTime: new Date().toISOString(),
          durationSeconds: Math.floor(Math.random() * 3600),
          moduleCode: "MainAxis", // 直接使用模型ID
          isSparePart: true,
          alarmGroup: "易损件"
        },
        {
          alarmCode: "放卷摆杆",
          alarmContent: "放卷摆杆角度超上限报警，紧急停机。",
          startTime: new Date().toISOString(),
          durationSeconds: Math.floor(Math.random() * 7200),
          moduleCode: "AUnwindingAxis", // 直接使用模型ID
          isSparePart: false,
          alarmGroup: null
        },
        {
          alarmCode: "上毛刷A",
          alarmContent: "上毛刷A已到达设定报警距离",
          startTime: new Date().toISOString(),
          durationSeconds: Math.floor(Math.random() * 1800),
          moduleCode: "BrushUpAAxis", // 直接使用模型ID
          isSparePart: true,
          alarmGroup: "易损件"
        }
      ];

      // console.log('模拟发送真实报警数据:', mockRealTimeData);
      this.realTimeAlarmCallback(mockRealTimeData);
    };

    // 立即发送一次
    setTimeout(simulateData, 2000);

    // 每10秒发送一次新的报警数据
    setInterval(() => {
      if (Math.random() > 0.3) { // 70%概率发送报警数据
        simulateData();
      }
    }, 10000);
  }

  /**
   * 启动模拟的WebSocket连接或轮询
   * 在实际应用中，这里应该建立WebSocket连接或启动轮询
   * @param callback 收到报警数据时的回调函数
   */
  public startRealtimeAlarmMonitoring(callback: (data: BackendAlarmData[]) => void): void {
    if (this.pollingInterval) {
      this.stopRealtimeAlarmMonitoring();
    }

    this.alarmDataCallback = callback;

    // 生成模拟报警数据的函数
    const generateRandomAlarmData = (): BackendAlarmData[] => {
      const possibleModels = [
        "AUnwindingAxis",
        "BUnwindingAxis",
        "UnwindingSpliceRotationAxis",
        "MainAxis",
        "zmfd",
        "fqdmz",
        "Laser1WidthAdjustAxis",
        "UpBRewindingAxis",
      ];

      // 随机选择1-3个模型进行报警
      const alarmCount = Math.floor(Math.random() * 3) + 1;
      const alarmData: BackendAlarmData[] = [];

      for (let i = 0; i < alarmCount; i++) {
        const randomIndex = Math.floor(Math.random() * possibleModels.length);
        const modeID = possibleModels[randomIndex];

        // 避免重复添加
        if (!alarmData.some(item => item.modeID === modeID)) {
          alarmData.push({
            modeID,
            isAlarming: "true"
          });
        }
      }

      return alarmData;
    };

    // 立即发送一次模拟报警数据
    setTimeout(() => {
      const initialAlarmData = generateRandomAlarmData();
      // console.log('初始模拟后台报警数据:', initialAlarmData);

      if (this.alarmDataCallback && initialAlarmData.length > 0) {
        this.alarmDataCallback(initialAlarmData);
      }
    }, 2000); // 延迟2秒发送，确保模型已加载完成

    // 模拟WebSocket连接或轮询
    // 在实际应用中，这里可能是WebSocket连接或API轮询
    this.pollingInterval = window.setInterval(() => {
      // 随机生成一些报警数据
      if (Math.random() > 0.4) { // 60%的概率触发报警
        const alarmData = generateRandomAlarmData();

        // 调用回调函数
        if (this.alarmDataCallback && alarmData.length > 0) {
          // console.log('模拟后台发送报警数据:', alarmData);
          this.alarmDataCallback(alarmData);
        }
      }

      // 随机生成一些解除报警的数据
      if (Math.random() > 0.6) { // 40%的概率解除报警
        const possibleModels = [
          "AUnwindingAxis",
          "BUnwindingAxis",
          "UnwindingSpliceRotationAxis",
          "MainAxis",
          "zmfd",
        ];

        // 随机选择1-2个模型解除报警
        const clearCount = Math.floor(Math.random() * 2) + 1;
        const clearData: BackendAlarmData[] = [];

        for (let i = 0; i < clearCount; i++) {
          const randomIndex = Math.floor(Math.random() * possibleModels.length);
          const modeID = possibleModels[randomIndex];

          // 避免重复添加
          if (!clearData.some(item => item.modeID === modeID)) {
            clearData.push({
              modeID,
              isAlarming: "false"
            });
          }
        }

        // 调用回调函数
        if (this.alarmDataCallback && clearData.length > 0) {
          // console.log('模拟后台发送解除报警数据:', clearData);
          this.alarmDataCallback(clearData);
        }
      }
    }, 3000); // 每3秒检查一次
  }

  /**
   * 停止模拟的WebSocket连接或轮询
   */
  public stopRealtimeAlarmMonitoring(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
    this.alarmDataCallback = null;
  }
}
