import { AxiosResponse } from "axios"
import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 * 数据分析
 */
export interface dataBase {
  dataCode?: string
}
export interface chartData extends dataBase {
  start: string
  end: string
  interval: number
  serverName?: string
}

export interface serverData {
  creationTime: string
  description: string
  value: string | number
}

export interface BaseLine {
  kCode: string
  SubKCode?: string
}

export interface SpareData {
  alarmCodes: string
}

export type serverDatas = serverData[]

export function getAnalyse(params: chartData): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/AnalyseWorkstation`,
    params,
  })
}

export function getDatumLineConfig(params: BaseLine): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/DatumLineConfigWorkstation`,
    params,
  })
}

export function getCTLists(): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/GlobalSetting/ct`,
  })
}

/**
 * 通过参数设置获取设备系统名称
 */
interface ParamsLabel {
  dictType: string
  dictLabel: string
}

// 通过参数类型获取设备名
export function getSystemName<T extends ParamsLabel>(
  params: T
): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/Dictionary`,
    params: params,
  })
}

// 获取版本相关信息
export function getServerVersion(): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/GlobalSetting/version`,
  })
}

// 任务启停
export function setWorkStatus(status: number): Promise<AxiosResponse> {
  return request.post({
    url: `${homeServerPrefix}/Home/SetWorkStatus?status=${status}`,
  })
}

/**
 * 根据报警码查询备件库信息
 * @param params 报警码
 * @returns
 */
export function getSparePartInfoByCode(
  params: SpareData
): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/SpareParts/alarmcodes`,
    params: params,
  })
}

/**
 * 根据模组分组获取所有易损件
 * @returns
 */
export function getAllVulnerablePartsLife(): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/VulnerablePartsLife/module`,
  })
}

/**
 * 获取全部易损件报警信息
 * @returns
 */
export function getAllPartsWarnInfo(): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/VulnerablePartsLife/warn`,
  })
}

/**
 * 触发首页推送
 * @param params
 * @returns
 */
export function triggerHomePush(): Promise<AxiosResponse> {
  return request.post({
    url: `${homeServerPrefix}/HomePage`,
  })
}

/**
 * 获取包含所有模组信息的列表（制片）
 * @returns
 */
export function getAllVulnerableList(): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/SparePartsAssembly/module`,
  })
}

// 获取当前最新的语言
export function getServerLanguage(): Promise<AxiosResponse> {
  return request.get({
    url: `${homeServerPrefix}/HomePage/Language`,
  })
}
