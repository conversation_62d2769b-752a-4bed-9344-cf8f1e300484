import store from "./store"
import useSocket from "@/hooks/useSocket"
import { config } from "@/config"

export default function initSocketClient() {
  const { registerNoticeSignalr, connectionFun, connectionStop } = useSocket()
  const { base_url } = config
  const webSocketUrl = base_url[process.env.VUE_APP_SCOKET]
  const homeServerPrefix = base_url.homeServerPrefix

  connectionStop()

  function connectSignalr() {
    // 首页建立scoket   TODO  signalr连接可单独提出来在登录后初始化
    registerNoticeSignalr(webSocketUrl, `${homeServerPrefix}/Hub`)
    connectionFun("On", (e, s) => {
      switch (s.category) {
        case "AlarmInfo": // 报警信息
          store.commit("notice/SET_ALARM_INFO", s.message)
          break
        case "AlarmRank": // 报警排行
          store.commit("notice/SET_ALARM_RANK_INFO", s.message)
          break
        case "Oee": // Oee
          store.commit("notice/SET_OEE_INFO", s.message)
          break
        case "OutputStatistic": // 产量统计
          store.commit("notice/SET_OUTPUT_STATISTICS_INFO", s.message)
          break
        case "SpareParts": // 备件信息
          store.commit("notice/SET_SPARE_PARTS_INFO", s.message)
          break
        case "Maintenance": // 维保时间
          store.commit("notice/SET_MAINTENANCE_INFO", s.message)
          break
        case "EquipmentStatus": // 设备状态
          store.commit("notice/SET_EQUIPMENT_STATUS_INFO", s.message)
          break
        case "Language": // 实时参数
          store.commit("notice/SET_LANGUAGE", s.message)
          break
        case "RealTimeParameters": // 实时参数
          store.commit("notice/SET_REAL_TIME_PARAMETERS", s.message)
          break
      }
    })
  }
  connectSignalr()
}
