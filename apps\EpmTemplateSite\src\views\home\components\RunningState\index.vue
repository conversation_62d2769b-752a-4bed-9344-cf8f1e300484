<template>
  <div class="running-state">
    <div class="box">
      <div class="icon">
        <img src="@/assets/images/box/icon-status.svg" alt="" />
      </div>
      <div class="text">当前状态:</div>
      <div class="status">{{ option.currentStatus }}</div>
    </div>
    <div class="chart">
      <RunCharts :isShow="isShow" :option="option" />
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue"
import RunCharts from "./components/RunCharts.vue"

defineProps({
  option: {
    type: Object,
    defaule: () => {},
  },
  isShow: {
    type: <PERSON><PERSON><PERSON>,
    defaule: true,
  },
})
</script>

<style scoped lang="scss">
.running-state {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .box {
    width: 140px;
    height: 35px;
    margin: 10px 0 0 23px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 14px;
    border-radius: 2px;
    color: #fff;
    background-color: rgba(182, 220, 255, 0.15);
    .icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      img {
        width: 100%;
        height: 200%;
      }
    }
    .text {
      margin: 0 5px;
      font-size: 12px;
      zoom: 0.9;
    }
    .status {
      font-size: 18px;
      font-weight: bold;
    }
  }
  .chart {
    flex: 1;
  }
}
</style>

<style scoped lang="scss">
@media screen and (max-width: 1180px) {
  .running-state {
    .box {
      width: 50%;
    }
  }
}
</style>
