<template>
  <el-dialog
    @close="cancelUpload"
    :append-to-body="true"
    fullscreen
    :close-on-click-modal="false"
    class="preview-file-dialog"
    :modelValue="props.show"
    :destroy-on-close="true"
  >
    <!-- <template #header>
      <div>预览文件：{{ curFile.fileName }}（注：在线预览文件可能不完整，可下载到本地查看）</div>
      <el-button type="primary" class="down-load-btn" @click="DownloadFileByIdApi(props.curFile.id)"> 下载 </el-button>
    </template> -->

    <div
      v-loading="loading"
      element-loading-background="rgba(0, 0, 0, 0.6)"
      element-loading-text="Loading..."
      id="preview-area"
    >
      <template v-if="!loading">
        <PreviewPdf
          v-if="['pdf', 'txt'].includes(curFile.fileType)"
          :file="curFile.filePath"
          :fileType="curFile.fileType"
        />
        <DocxPdf
          v-if="['docx', 'doc'].includes(curFile.fileType)"
          :file="curFile.filePath"
        />
        <XlsxPdf
          v-if="['xlsx', 'xls'].includes(curFile.fileType)"
          :file="curFile.filePath"
        />
        <!-- <TxtPdf v-if="['txt'].includes(curFile.fileType)" :file="curFile.filePath" /> -->
      </template>
    </div>
  </el-dialog>
</template>

<script setup name="PreviewFile">
import { reactive, toRefs, watch } from "vue"
import { DownloadFileByIdApi } from "@/api/front/jobDirection" // 默认的请求文件api
import PreviewPdf from "@/components/hymson-preview/pdf.vue"
import DocxPdf from "@/components/hymson-preview/docx.vue"
import XlsxPdf from "@/components/hymson-preview/xlsx.vue"
import TxtPdf from "@/components/hymson-preview/txt.vue"

const emits = defineEmits(["onClose"])

//formdata 初始化
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
    required: true,
  },
  curFile: {
    type: Object,
    default: () => {
      return {}
    },
    required: true,
  },
})

const data = reactive({
  loading: false,
  file: {},
  txtPre: {},
})

// watch(
//   () => props.show,
//   val => {
//     console.log('DownloadFileByIdApi');
//     if (val) {
//       data.loading = true
//       DownloadFileByIdApi(props.curFile.id, 'Preview')
//         .then(res => {
//           if (res) {
//             data.file = res
//             data.loading = false
//           }
//         })
//         .catch(() => {
//           emits('onClose') // 关闭弹窗
//           data.loading = false
//         })
//     }
//   },
//   {immediate: true}
// )

// 取消
const cancelUpload = () => {
  data.file = {}
  data.loading = false
  emits("onClose") // 关闭弹窗
}

const { file, loading } = toRefs(data)
</script>

<style lang="scss">
.preview-file-dialog.el-dialog.is-fullscreen {
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  .el-dialog__title,
  .el-dialog__headerbtn .el-dialog__close,
  .el-dialog__body {
    color: #fff;
  }
  .el-dialog__header {
    padding-right: 80px;
    color: #fff;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  .el-dialog__body {
    flex: 1;
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    #preview-area {
      width: 90%;
      height: 100%;
    }
    .down-load-btn {
      position: absolute;
      top: 20px;
      right: 100px;
    }
  }
  .el-dialog__headerbtn .el-dialog__close {
    font-size: 32px;
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
