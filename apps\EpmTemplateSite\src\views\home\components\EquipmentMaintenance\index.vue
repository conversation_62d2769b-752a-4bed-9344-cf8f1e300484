<template>
  <div class="equip-box">
    <div class="maintenance">
      <div class="days">
        {{ $t("monitor.daysUntilNextMaintenance")
        }}{{
          maintenanceInfo?.interval
            ? maintenanceInfo.interval + $t("monitor.days")
            : $t("monitor.none")
        }}
      </div>
      <div class="date">
        {{ $t("monitor.maintenanceDate")
        }}{{
          maintenanceInfo?.startTime
            ? maintenanceInfo?.startTime
            : $t("monitor.none")
        }}
      </div>
    </div>
    <div class="chart">
      <EquipmentUsed :option="option" @chart-click="handleClickEquip" />
    </div>
    <!-- 易损件状态监控 -->
    <hmx-dialog
      :dialogTitle="$t('monitor.wearablePartsStatusMonitoring')"
      :isVisable="monitorDetailShow"
      dialogWidth="70%"
      @closeDialog="monitorDetailShow = false"
      customClass="monitor-dialog"
    >
      <template #icon>
        <img src="@/assets/images/box/log.svg" alt="" />
      </template>
      <div class="log-info">
        <log-detail v-if="monitorDetailShow" />
      </div>
    </hmx-dialog>
  </div>
</template>

<script setup name="BarCharts">
import { ref, computed, defineProps } from "vue"
import { useStore } from "vuex"
import { useI18n } from "@xfe/locale"
import EquipmentUsed from "./components/EquipmentUsed.vue"
import LogDetail from "./components/LogDetail.vue"

const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
})

const { t: $t } = useI18n()

const store = useStore()
const maintenanceInfo = computed(() => store.getters["notice/maintenanceInfo"])

const monitorDetailShow = ref(false)
const handleClickEquip = () => {
  monitorDetailShow.value = true
}
</script>
<style scoped lang="scss">
.equip-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .maintenance {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
  }
  .chart {
    width: calc(100% - 20px);
    height: 100%;
  }
}
</style>
