import { request } from "@xfe/request"
import { config } from "@/config"
const base = config.base_url.homeServerPrefix

export function getKnowledgeBase(params) {
  return request.get({
    url: `${base}/KnowledgeBase`,
    params,
  })
}

export function getKnowledgeBaseRanking() {
  return request.get({
    url: `${base}/KnowledgeBase/Ranking`,
  })
}

export function searchKnowledgeBase(params) {
  const { knowledgeCategory, ...requestParams } = params
  return request.post({
    url: `${base}/KnowledgeBase/Search?knowledgeCategory=${knowledgeCategory}`,
    params: requestParams,
  })
}

export function getKnowledgeBaseById(id) {
  return request.get({
    url: `${base}/KnowledgeBase/${id}`,
  })
}

export function updateKnowledgeBaseById(id, data) {
  return request.put(
    {
      url: `${base}/KnowledgeBase/${id}`,
      data,
    },
    { joinParamsToUrl: true }
  )
}

export function getlinkKonw(params) {
  return request.get({
    url: `${base}/KnowledgeBase`,
    params,
  })
}

export function importExcel(data) {
  return request.post({
    url: `${base}/KnowledgeBase/Import`,
    headers: { "Content-Type": "multipart/form-data" },
    data,
  })
}

export function exportExcel(data) {
  return request.get({
    url: `${base}/KnowledgeBase/Export`,
    header: {
      headers: { "Content-Type": "application/x-download" },
    },
    responseType: "blob",
  })
}

/**
 * 新增单条知识库关联数据
 * @param {*} data
 * @returns
 */
export function postSingleKnowledgeBase(data) {
  return request.post({
    url: `${base}/KnowledgeBase/Single`,
    data,
  })
}
