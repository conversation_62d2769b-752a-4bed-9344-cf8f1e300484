<template>
  <div class="details">
    <div class="arrowBox">
      <div
        class="iconBox"
        :style="swiperAct == 0 ? 'pointer-events: none' : ''"
        @click="arrowBold('left')"
      >
        <el-icon color="#0084FE" size="20"><CaretLeft /></el-icon>
      </div>
      <div class="imgWrap">
        <div class="imgBox" id="imgBox" ref="imgBoxRef">
          <div
            :class="['img', swiperAct == index ? 'active-img' : '']"
            :id="`img${index}`"
            v-for="(item, index) in imgList"
            :key="index"
            @click="arrowClick(index)"
          >
            <!-- 图片类型使用el-image -->
            <el-image
              v-if="isImage(item.fileType)"
              style="width: 100%; height: 100%"
              :src="item.path"
              @click="handleViewFile(item)"
            />
            <!-- 非图片类型显示图标 -->
            <div v-else class="file-icon-box" @click="handleViewFile(item)">
              <svg-icon
                :name="getFileIcon(item.fileType)"
                :size="32"
                class="file-icon"
              />
              <el-tooltip
                effect="dark"
                :content="item.name"
                placement="top"
                :disabled="item.name.length <= 5"
              >
                <div class="file-name">{{ item.name }}</div>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
      <div
        class="iconBox"
        :style="swiperAct == imgList.length - 1 ? 'pointer-events: none' : ''"
        @click="arrowBold('right')"
      >
        <el-icon color="#0084FE" size="20"><CaretRight /></el-icon>
      </div>
    </div>
    <!-- 视频预览弹窗 -->
    <videoDialog
      :isVisable="isVideoDialogShow"
      :checkOpt="checkOpt"
      @close-dialog="closeVideo"
    />
    <!-- 图片预览弹窗 -->
    <imageDialog
      :options="previewImgOptions"
      @onCancel="closePreviewImgDialog"
    />
    <previewFile
      :show="isPreviewFileShow"
      :curFile="curFile"
      @onClose="closePreviewFile"
    />
  </div>
</template>

<script setup name="SwiperBox">
import { reactive, toRefs, ref, onMounted, onUnmounted, defineProps } from "vue"
import { CaretLeft, CaretRight } from "@element-plus/icons-vue"
import videoDialog from "@/views/home/<USER>/components/videoDialog.vue"
import imageDialog from "@/views/home/<USER>/components/preview-img.vue"
import previewFile from "@/views/home/<USER>/components/preview-file.vue"

const props = defineProps({
  srcList: {
    type: Array,
    default: () => [],
  },
  // 类型,图片还是视频
  boxType: {
    type: String,
    default: "image",
  },
})
const imgBoxRef = ref()

let data = reactive({
  imgList: props.srcList,
  imgType: props.boxType,
  swiperAct: 0,
  isScoll: true,
})

let { imgList, imgType, swiperAct, isScoll } = toRefs(data)
// 鼠标点击事件
let clickHandle = e => {
  if (e.x <= 518) {
    // 调用方法向左滑动
    scrollRight("left")
  }
  if (e.x >= 660) {
    // 调用方法向右滑动
    scrollRight("right")
  }
}
// 选中的图片索引
let arrowClick = index => {
  swiperAct.value = index
}
// 上下切换图片
let arrowBold = type => {
  let value
  if (type === "left") {
    value = swiperAct.value - 1
    scrollRight("left")
  }
  if (type === "right") {
    value = swiperAct.value + 1
    scrollRight("right")
  }
  // 当前现实的图片
  swiperAct.value = value
}
/**
 * 左右移动事件
 * @param type 向左/右移动
 */
let scrollRight = type => {
  if (!isScoll.value) return
  // 节流
  isScoll.value = false
  /**
   * @param 618 可视化宽度 最大的盒子
   * @param 5 可视化中现实数量
   * @param imgList 数组长度
   */
  const allLength = Math.floor((618 / 5) * imgList.value.length)
  // 获取包裹商品卡片的div盒子的宽度
  const boxLength = imgBoxRef.value.clientWidth
  // 数组总长度比大盒子的宽度还要小
  if (allLength < boxLength) return
  // 获取包裹商品卡片的div盒子
  const listEl = imgBoxRef.value
  // 获取定位的 left 值
  const rightMove = Math.abs(parseInt(window.getComputedStyle(listEl)?.left))
  /**
   * @param rightMove 定位的 left 值
   * @param boxLength 包裹商品卡片的div盒子的宽度
   * @param 124 是单个商品卡片的宽度
   * @param allLength 可视化宽度 最大的盒子
   */
  if (rightMove + boxLength + 124 > allLength) {
    if (type == "left") {
      listEl.style.left = "-" + (rightMove - 124) + "px"
    } else {
      listEl.style.left = "-" + (allLength - boxLength) + "px"
    }
  } else {
    if (type == "left") {
      if (rightMove - 124 < 124) {
        listEl.style.left = "0px"
      } else {
        listEl.style.left = "-" + (rightMove - 124) + "px"
      }
    } else {
      listEl.style.left = "-" + (rightMove + 124) + "px"
    }
  }
  setTimeout(() => {
    isScoll.value = true
  }, 500)
}

onMounted(() => {
  // 可视化宽度 最大的盒子 添加点击事件
  imgBoxRef.value.addEventListener("click", clickHandle)
})
onUnmounted(() => {
  // 可视化宽度 最大的盒子 移除点击事件
  if (imgBoxRef.value) {
    imgBoxRef.value.removeEventListener("click", clickHandle)
  }
})

// 是否开启视频弹框
let isVideoDialogShow = ref(false)
let checkOpt = ref({
  title: "",
  url: "",
})
// 点击播放
const handleViewVideo = item => {
  checkOpt.value.title = item?.fileDesc
  checkOpt.value.url = item?.videoPath
  isVideoDialogShow.value = true
}
// 关闭播放
const closeVideo = () => {
  isVideoDialogShow.value = false
  checkOpt.value.title = ""
  checkOpt.value.url = ""
}

// 是否开启图片弹窗
let previewImgOptions = ref({
  show: false,
  imgUrl: null,
})
// 点击预览
const handleViewImage = item => {
  previewImgOptions.value.imgUrl = item.imagePath
  previewImgOptions.value.show = true
}
// 关闭图片预览
const closePreviewImgDialog = () => {
  previewImgOptions.value.imgUrl = ""
  previewImgOptions.value.show = false
}

// 在计算属性后添加文件类型判断方法
const isVideo = file => ["mp4", "wav", "flv"].includes(file)
const isImage = file => ["png", "jpg", "webp"].includes(file)
const isWord = file => ["docx", "txt", "doc"].includes(file)
const isExcel = file => ["xlsx", "xls"].includes(file)
const isPdf = file => ["pdf"].includes(file)

let isPreviewFileShow = ref(false)
let curFile = ref({
  fileType: "",
  filePath: "",
})

// 在点击处理函数中添加文件类型判断
const handleViewFile = item => {
  if (isImage(item.fileType)) {
    previewImgOptions.value.imgUrl = item.path
    previewImgOptions.value.show = true
  } else if (isVideo(item.fileType)) {
    checkOpt.value.title = item.name
    checkOpt.value.url = item.path
    isVideoDialogShow.value = true
  } else if (
    isWord(item.fileType) ||
    isExcel(item.fileType) ||
    isPdf(item.fileType)
  ) {
    isPreviewFileShow.value = true
    curFile.value.fileType = item.fileType
    curFile.value.filePath = item.path
  }
}

// 添加关闭预览方法
const closePreviewFile = () => {
  isPreviewFileShow.value = false
  curFile.value.fileType = ""
  curFile.value.filePath = ""
}

const getFileIcon = fileType => {
  if (isVideo(fileType)) return "video-file"
  if (isWord(fileType)) return "word-file"
  if (isExcel(fileType)) return "excel-file"
  if (isPdf(fileType)) return "pdf-file"
  return "unknown-file"
}
</script>

<style scoped lang="scss">
.details {
  .arrowBox {
    width: 100%;
    height: 90px;
    display: flex;
    align-items: center;
    .imgWrap {
      flex: 1;
      height: 90px;
      position: relative;
      margin: 0 10px;
      overflow: hidden;
      .imgBox {
        width: 100%;
        height: 90px;
        /* 修改为flex布局 */
        display: flex;
        gap: 12px; /* 添加间距控制 */
        position: absolute;
        left: 0px;
        transition: all 0.3s;

        .img {
          cursor: pointer;
          /* 修改为flex项 */
          flex: 0 0 114px; /* 禁止伸缩，固定宽度 */
          height: 90px;
          box-sizing: border-box;
          border: 2px solid #f8f8f8;

          /* 移除原有margin */
          &:not(:last-child) {
            margin-right: 0; /* 已通过gap控制间距 */
          }
        }
        .active-img {
          border: 3px solid #0084fe;
        }
        .file-icon-box {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
        .file-name {
          color: #fff;
          font-size: 12px;
          text-align: center;
          padding: 0 4px;
          margin-top: 10px;
          // 添加以下样式
          max-width: 100px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .iconBox {
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 90px;
      width: 20px;
      height: 100%;
      background: #232836;
      opacity: 0.8;
      color: #fff;
      text-align: center;
      cursor: pointer;
    }
    .iconBox:hover {
      background: #232836;
      opacity: 1;
    }
  }
}
</style>
