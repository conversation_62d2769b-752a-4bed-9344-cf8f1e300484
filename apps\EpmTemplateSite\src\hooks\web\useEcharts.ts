import type { EChartsOption } from "echarts"
import { nextTick, Ref, unref, onActivated, onMounted, onUnmounted } from "vue"
import echarts from "@/utils/lib/echarts"
import { useDebounceFn } from "@vueuse/core"
import { lineOptions, pieOptions, barOptions } from "./config"

/**
 * @param elRef dom节点的ref
 * @param emits 注册事件
 */

// line 表示默认折现   pie表示默认饼图   bar表示默认柱状图  custom表示自定义类型
export enum OptionType {
  line = "line",
  pie = "pie",
  bar = "bar",
  custom = "custom",
}

export interface LoadingStyle {
  text: string
  color: string
  textColor: string
  maskColor: string
  zlevel: number
}

export function useEcharts(elRef: Ref<HTMLDivElement>, emits: any) {
  let chartInstance: echarts.ECharts | null = null //初始化实例
  let resizeFn = resize
  resizeFn = useDebounceFn(resize, 200)

  // 初始化echarts
  function initCharts() {
    const el = unref(elRef)
    if (!el || !unref(el)) {
      return
    }
    chartInstance = echarts.init(el)
    // const { removeEvent } = useEventListener({
    //   el: window,
    //   name: "resize",
    //   listener: resizeFn,
    // });
  }

  // 设置/更新option
  function setOptions(
    options: EChartsOption,
    optionType: OptionType = OptionType.line
  ) {
    if (unref(elRef)?.offsetHeight === 0) {
      // 判断容器高度是否为0
      return
    }
    nextTick(() => {
      if (!chartInstance) {
        initCharts()
        if (!chartInstance) return
      }
      let initOptions =
        optionType === OptionType.line
          ? lineOptions
          : optionType === OptionType.pie
          ? pieOptions
          : optionType === OptionType.bar
          ? barOptions
          : {}
      chartInstance?.setOption(initOptions)
      chartInstance?.setOption(options)
      chartInstance.on("click", function (param) {
        emits("chart-click", param)
      })
    })
  }

  // 更新大小
  function resize() {
    chartInstance?.resize()
  }

  onActivated(() => {
    resize()
  })

  // 显示加载状
  function showLoading(loadingStyle: LoadingStyle) {
    if (!chartInstance) {
      initCharts()
    }
    chartInstance?.showLoading(loadingStyle)
  }
  // 显示加载状
  function hideLoading() {
    if (!chartInstance) {
      initCharts()
    }
    chartInstance?.hideLoading()
  }

  //获取实例
  function getInstance(): echarts.ECharts | null {
    if (!chartInstance) {
      initCharts()
    }
    return chartInstance
  }

  onMounted(() => {
    // chartInstance.on("click", function (param) {
    //   emits("chart-click", param);
    // });
    //自适应不同屏幕时改变图表尺寸
    window.addEventListener("resize", resizeFn)
  })

  onUnmounted(() => {
    chartInstance && chartInstance.dispose()
    window.removeEventListener("resize", resizeFn)
  })
  return {
    resize,
    setOptions,
    getInstance,
    showLoading,
    hideLoading,
  }
}
