<template>
  <div class="b-chart" ref="tensionMonitorRef"></div>
</template>

<script setup name="BarCharts">
import {
  ref,
  defineEmits,
  defineProps,
  defineExpose,
  watch,
  nextTick,
} from "vue"
import useEcharts from "@/hooks/useEcharts"
import { tensionMonitorEcharts } from "../echartsConfig.js"
const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
})
const emits = defineEmits([
  "chart-click", // 点击chart
])

let tensionMonitorRef = ref(null)
const { resize } = useEcharts(
  tensionMonitorRef,
  emits,
  props,
  tensionMonitorEcharts
)
defineExpose({
  resize,
})
</script>
<style scoped lang="scss">
.b-chart {
  width: 100%;
  height: 100%;
}
</style>
