import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 * 激光刻码配置
 */

/**
 *  激光刻码协议
 */
// 获取激光刻码协议列表
export function getLaserProtocolList() {
  return request.get({
    url: `${homeServerPrefix}/LaserCodeProtocol/GetLaserProtocolList`,
  })
}

// 根据协议动态获取参数
export function getListByProtocolType(params) {
  return request.get({
    url: `${homeServerPrefix}/LaserCodeProtocol/GetListByProtocolType`,
    params,
  })
}

// 获取刻码规则列表
export function getRuleLists() {
  return request.get({
    url: `${homeServerPrefix}/LaserCodeRule`,
  })
}

// 根据协议和规则编号获取的刻码规则
export function getRuleListByProtocolCode(params) {
  return request.get({
    url: `${homeServerPrefix}/LaserCodeProtocol/GetRuleListByProtocolCode`,
    params,
  })
}

// 设置刻码规则
export function setRules(data) {
  return request.post({
    url: `${homeServerPrefix}/LaserCodeProtocol/SetRules`,
    data,
  })
}

// 根据ID获取刻码规则参数
export function getListByRelId(params) {
  return request.get({
    url: `${homeServerPrefix}/LaserCodeRulePara/GetListByRelId`,
    params,
  })
}

// 根据刻码规则数据类型
export function getRuleDataTypeList(params) {
  return request.get({
    url: `${homeServerPrefix}/LaserCodeRulePara/GetRuleDataTypeList`,
    params,
  })
}
