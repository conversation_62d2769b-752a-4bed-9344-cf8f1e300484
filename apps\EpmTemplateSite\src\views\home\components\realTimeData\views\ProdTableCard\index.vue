<template>
  <div class="prod-table-card">
    <!-- v-if="allColumns.length" -->
    <div class="operate" v-show="isZoom">
      <column-set
        v-if="allColumns.length"
        name="aluminum-column-set"
        :columns="allColumns"
        @columnSetting="v => (data.tableColumns = v)"
      />
      <el-button
        type="primary"
        class="btn"
        :icon="Close"
        @click="handlerClearTable"
      >
        {{ $t("card.title.clearData") }}
      </el-button>
    </div>
    <product-table
      :columns="tableColumns"
      :table-data="productTableList"
      :options="options"
      @command="handlerCommand"
    >
      <template #result="{ row }">
        <span :style="{ color: handlerColor(row.result) }">
          {{ row.result }}
        </span>
      </template>
    </product-table>
    <!-- 详情 -->
    <hmx-dialog
      :dialogTitle="$t('card.dialogTitle.details')"
      dialogWidth="880px"
      :isVisable="isDetailVisable"
      @closeDialog="isDetailVisable = false"
      :append-to-body="true"
    >
      <pro-detail v-if="isDetailVisable" :proInfo="curProInfo" />
    </hmx-dialog>
    <!-- 历史数据表格弹框 -->
    <hmx-dialog
      :dialogTitle="$t('card.dialogTitle.historicalData')"
      dialogWidth="85%"
      :isVisable="props.isDialogShow"
      @closeDialog="handleClose"
      :append-to-body="true"
    >
      <pro-table
        v-if="props.isDialogShow"
        @command="handlerCommand"
        :proHisColumn="tableColumn"
      />
    </hmx-dialog>
  </div>
</template>

<script setup name="ProdTableCard">
import {
  reactive,
  toRefs,
  watch,
  defineProps,
  ref,
  onMounted,
  toRaw,
  computed,
} from "vue"
import { ArrowDownBold, ArrowUpBold, Close } from "@element-plus/icons-vue"
import ProDetail from "./ProDetail.vue"
import ProTable from "./ProTable.vue"
import { useStore } from "vuex"
import { ProductTable, ColumnSet } from "../../components/index"
import { GetColunmns } from "@/api/front/production"
import { useMessage } from "@/hooks/web/useMessage"
import { useI18n, useLocale } from "@xfe/locale"
import HmxDialog from "@/components/hmx-dialog.vue"

const { createMessage } = useMessage()
const { t: $t } = useI18n()
const { getLocale } = useLocale()

const props = defineProps({
  productList: {
    type: Array,
    required: true,
    default: [],
  },
  isDialogShow: {
    type: Boolean,
    default: false,
  },
  // 是否缩放弹窗
  isZoom: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(["closedDialog"])

const store = useStore()

const colors = {
  success: "#20E6A4",
  error: "#FF1E26",
}

// data--------
const data = reactive({
  isFold: true, // 是否折叠 false 不折叠
  isDetailVisable: false, // 详情弹出框
  isTableVisable: false, // 历史数据表格弹框
  hisProInfo: {}, // 历史产品信息
  curProInfo: {}, // 当前产品信息
  allColumns: [], // 所有列
  // 表格表头重置数据
  tableColumns: [],
  // 表格配置
  options: {
    loading: false,
    // showPagination: false,
    // rowStyle: () => "cursor:pointer; height:'30px'", // 行样式
    // height: 175,
    // border: true,
    paginationConfig: {
      total: 0,
      pageIndex: 1,
      pageSize: 10,
    },
  },
  productTableList: [],
  barCode: "",
})

watch(
  props.productList,
  val => {
    const table = []
    val.forEach(item => {
      const obj = {
        colorFields: [],
        resultColor: "",
      }
      item.forEach(info => {
        obj[info.code] = info.value
        if (info.resultColor) {
          obj.resultColor = info.resultColor
          obj.colorFields.push(info.code)
        }
      })
      table.push(obj)
    })
    data.productTableList = table

    if (val.length > 0 && data.allColumns.length === 0) {
      const allColumns = []
      allColumns.push({
        type: "index",
        label: "No.",
        //align: "center",
        width: "70",
        fixed: "left",
      })
      val[0].forEach(info => {
        let obj = {
          prop: info.code,
          label: info.name,
          //align: "center",
          showOverflowTooltip: true,
          info: info,
          resultColor: "resultColor",
        }
        // if (info.isMain) {
        //   obj.fixed = "left"
        // }
        // if (info.isMain && !info.resultColor) {
        //   obj.width = "300"
        // }
        allColumns.push(obj)
      })
      allColumns.push({
        label: $t("card.table.productList.operation"),
        //align: "center",
        fixed: "right",
        width: "100",
        buttons: [
          {
            name: $t("card.table.productList.details"),
            type: "default",
            command: "detail",
          },
        ],
      })
      data.allColumns = allColumns
    }
  },
  { immediate: true }
)

// watch------------
watch(
  () => data.isFold,
  val => {
    if (val) {
      data.options.height = 240
    } else {
      data.options.height = 0
    }
  }
)
// watch(
//   () => store.getters["qualityStatCard/curQualityResult"],
//   val => {
//     if (val.value) {
//       data.isTableVisable = true
//     }
//   }
// )

// methods----------
const handlerCommand = (command, row, type) => {
  let currentInfo = { list: {} }
  if (!type) {
    let localeCode = ""
    if (getLocale.value == "en-US") {
      localeCode = "Cell code"
    } else {
      localeCode = "电芯条码"
    }
    props.productList.forEach(info => {
      if (info.every(item => item.value === row[item.code])) {
        info.forEach(item => {
          if (item.name === localeCode) currentInfo.barCode = item.value
          if (!item.isMain) {
            if (!currentInfo.list[item.siteCode]) {
              currentInfo.list[item.siteCode] = {
                key: item.siteName,
                value: [],
              }
            }
            currentInfo.list[item.siteCode].value.push({
              dataName: item.name,
              dataValue: item.value,
            })
          }
        })
      }
    })
  } else {
    currentInfo.list = toRaw(row.details)
    currentInfo.barCode = row.barCode
  }
  data.curProInfo = currentInfo
  data.isDetailVisable = true
}

const handleFold = () => {
  if (data.tableColumns.length < 1) {
    createMessage($t("prompt.prompt_38"), "info")
    return
  }
  data.isFold = !data.isFold
}

const handlerColor = status => {
  let color = ""
  switch (status) {
    case "OK":
      color = colors.success
      break
    case "NG":
      color = colors.error
      break
    default:
      break
  }
  return color
}

// 清空列表数据
const handlerClearTable = () => {
  store.commit("notice/CLEAR_PRODUCT_LIST")
}

let tableColumn = ref([])

const computedColumns = () => {
  GetColunmns().then(res => {
    let activeColumns = []
    res &&
      Object.keys(res).forEach(key => {
        activeColumns.push({
          prop: key,
          label: res[key],
          //align: "center",
          showOverflowTooltip: true,
        })
      })

    tableColumn.value = activeColumns
  })
}

onMounted(computedColumns)

const handleClose = () => {
  // console.log(8888888888);
  // props.isDialogShow = false
  emits("closedDialog")
}

// 点击查看实时数据逻辑
const isZoom = computed(() => props.isZoom)
watch(
  () => isZoom.value,
  v => {
    if (v) {
      data.options.height = 500
    } else {
      // data.options.height = 175
    }
  },
  { immediate: true }
)

const {
  isFold,
  options,
  isDetailVisable,
  isTableVisable,
  curProInfo,
  hisProInfo,
  tableColumns,
  allColumns,
  productTableList,
  barCode,
} = toRefs(data)
</script>

<style lang="scss" scoped>
.prod-table-card {
  margin-top: 10px;
  background-color: var(--prod-table-card);
  height: 100%;
  .operate {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
  }
  .card-title {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-bottom: 8px;
    .operation-btns {
      display: flex;
      align-items: center;
    }
  }
  .btn {
    background-color: var(--btn-background);
  }
  // background-color: var(--layout-background);
  :deep(.el-table) {
    height: calc(100% - 40px);
    background-color: var(--layout-background);
  }
}
:deep(.el-button-group) > .el-button:first-child:last-child {
  background: transparent;
  border: transparent;
  color: #0084fe;
  font-size: 14px;
}
</style>
