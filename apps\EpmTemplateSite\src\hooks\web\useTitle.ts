// @ts-nocheck
import { ref, watch, unref } from "vue"
import { useRouter, RouteLocationNormalizedLoaded } from "vue-router"
interface IRouteProps extends RouteLocationNormalizedLoaded {
  query: {
    [key: string]: any
  }
}

export function useTitle() {
  const router = useRouter()
  const currentRoute = router?.currentRoute
  console.log(currentRoute)
  let pageTitle = ref<string>("")
  watch(
    () => unref(currentRoute).path,
    async () => {
      const route = unref(currentRoute) as unknown as IRouteProps
      if (route?.query?.title) {
        // 如果是子站里携带titie进配置
        pageTitle.value = (route?.query?.title as string) ?? ""
      } else {
        pageTitle.value = (route?.meta?.parentTitle as string) ?? ""
      }
    },
    { immediate: true }
  )
  return {
    pageTitle,
  }
}
