<template>
  <div class="real-time-oee">
    <div class="right-oee-top-box">
      <chartGuage :option="options" :isOee="props.isShow" />
    </div>
    <ul class="right-oee-box">
      <li><chartGuage :option="options2" :isOee="props.isShow" /></li>
      <li><chartGuage :option="options3" :isOee="props.isShow" /></li>
      <li><chartGuage :option="options4" :isOee="props.isShow" /></li>
    </ul>
  </div>
</template>

<script setup>
import { ref, defineProps, watch } from "vue"
import chartGuage from "./guageCharts.vue"
import { oeeEcharts, RealOEECharts } from "./echartsConfig"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        available: 0, //可用性
        oee: 0, // oee
        performance: 0, // 表现性
        quality: 0, // 质量指数
      }
    },
  },
  isShow: {
    type: Boolean,
    default: false,
  },
})

let options = ref(JSON.parse(JSON.stringify(RealOEECharts)))

let options2 = ref(JSON.parse(JSON.stringify(oeeEcharts)))
let options3 = ref(JSON.parse(JSON.stringify(oeeEcharts)))
let options4 = ref(JSON.parse(JSON.stringify(oeeEcharts)))

const initOption = params => {
  options.value.series[1].data = [
    {
      name: $t("echarts.oee.synthesis"),
      value: params.oee,
    },
  ]
  options.value.series[1].axisLine.lineStyle.color[0][0] = params.oee / 100
  options2.value.series[1].data = [
    {
      name: $t("echarts.oee.available"),
      value: params.available,
    },
  ]
  options2.value.series[1].axisLine.lineStyle.color[0][0] =
    params.available / 100
  options3.value.series[1].data = [
    {
      name: $t("echarts.oee.performance"),
      value: params.performance,
    },
  ]
  options3.value.series[1].axisLine.lineStyle.color[0][0] =
    params.performance / 100
  options4.value.series[1].data = [
    {
      name: $t("echarts.oee.quality"),
      value: params.quality,
    },
  ]
  options4.value.series[1].axisLine.lineStyle.color[0][0] = params.quality / 100
}

initOption(props.data)

watch(
  () => props.data,
  v => {
    initOption(v)
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.real-time-oee {
  display: flex;
  width: 100%;
  height: calc(100% - 38px);
  align-items: flex-end;
  padding: 5px;
  padding-bottom: 0px;
  .right-oee-top-box {
    width: 30%;
    height: 100%;
  }
  .right-oee-box {
    flex: 1;
    height: 100%;
    display: flex;
    li {
      width: calc(100% / 3);
    }
  }
}
</style>
