/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AlarmControlPanel: typeof import('./src/components/bbl/AlarmControlPanel.vue')['default']
    BabylonScene: typeof import('./src/components/bbl/BabylonScene.vue')['default']
    BreadCrumb: typeof import('./src/components/bread-crumb/index.vue')['default']
    DialogCard: typeof import('./src/components/dialog-card.vue')['default']
    Docx: typeof import('./src/components/hymson-preview/docx.vue')['default']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    FullLoading: typeof import('./src/components/hmx-full-loading/full-loading.vue')['default']
    HmxButton: typeof import('./src/components/hmx-button/HmxButton.vue')['default']
    HmxDialog: typeof import('./src/components/hmx-dialog.vue')['default']
    HmxFooter: typeof import('./src/components/hmx-footer/index.vue')['default']
    HmxFormBox: typeof import('./src/components/hmx-form-box/index.vue')['default']
    HmxProgress: typeof import('./src/components/hmx-progress/index.vue')['default']
    HmxSetColunm: typeof import('./src/components/hmx-set-colunm/index.vue')['default']
    HmxSwitch: typeof import('./src/components/hmx-switch/index.vue')['default']
    HmxTabLayout: typeof import('./src/components/hmx-tab-layout/index.vue')['default']
    HmxTable: typeof import('./src/components/hmx-table/index.vue')['default']
    IconSelect: typeof import('./src/components/icon-select/index.vue')['default']
    LoadingError: typeof import('./src/components/hmx-full-loading/loading-error.vue')['default']
    ObjectTreePanel: typeof import('./src/components/bbl/ObjectTreePanel.vue')['default']
    Pdf: typeof import('./src/components/hymson-preview/pdf.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollContainer: typeof import('./src/components/hmx-container/src/ScrollContainer.vue')['default']
    SvgIcon: typeof import('./src/components/svg-icon/index.vue')['default']
    SwiperBox: typeof import('./src/components/swiper-box/index.vue')['default']
    TableColumn: typeof import('./src/components/hmx-table/TableColumn.vue')['default']
    TabPane: typeof import('./src/components/tab-pane/index.vue')['default']
    TreeNode: typeof import('./src/components/bbl/TreeNode.vue')['default']
    Txt: typeof import('./src/components/hymson-preview/txt.vue')['default']
    VerticalTab: typeof import('./src/components/vertical-tab/index.vue')['default']
    XfeFold: typeof import('./src/components/xfe-fold/index.vue')['default']
    XfeTabs: typeof import('./src/components/xfe-tabs/index.vue')['default']
    XfeTransition: typeof import('./src/components/xfe-transition/index.vue')['default']
    Xlsx: typeof import('./src/components/hymson-preview/xlsx.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
