// .prettierrc.js
module.exports = {
  tabWidth: 2, // 缩进
  printWidth: 80, // 最大长度80个字符
  semi: false, // 行末分号
  singleQuote: false, // 单引号
  jsxSingleQuote: false, // JSX双引号
  trailingComma: "es5", // 尽可能使用尾随逗号（包括函数参数）
  bracketSpacing: true, // 在对象，数组括号与文字之间加空格 "{ foo: bar }"
  jsxBracketSameLine: false, // > 标签放在最后一行的末尾，而不是单独放在下一行
  arrowParens: "avoid", // (x) => {} 箭头函数参数只有一个时是否要有小括号。avoid：省略括号  always:总是有括号
  useTabs: false, // 使用tab还是空格
  endOfLine: "auto", // 行尾换行格式
  HTMLWhitespaceSensitivity: "ignore",
  ignorePath: ".prettierignore", // 不使用prettier格式化的文件填写在项目的.prettierignore文件中
};
