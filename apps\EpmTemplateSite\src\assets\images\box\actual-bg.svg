<svg width="117" height="73" viewBox="0 0 117 73" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Img/Actual">
<g id="Rectangle 2416">
<g filter="url(#filter0_ii_2025_1119)">
<path d="M0 2C0 0.895432 0.895431 0 2 0H33.5L38 4.5H80L84.5 0H115C116.105 0 117 0.895431 117 2V71C117 72.1046 116.105 73 115 73H2C0.89543 73 0 72.1046 0 71V2Z" fill="url(#paint0_linear_2025_1119)" fill-opacity="0.35"/>
</g>
<path d="M37.6464 4.85355L37.7929 5H38H80H80.2071L80.3536 4.85355L84.7071 0.5H115C115.828 0.5 116.5 1.17157 116.5 2V71C116.5 71.8284 115.828 72.5 115 72.5H2C1.17157 72.5 0.5 71.8284 0.5 71V2C0.5 1.17157 1.17157 0.5 2 0.5H33.2929L37.6464 4.85355Z" stroke="#0084FE"/>
</g>
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M35 0H38H80H83L80 3H38L35 0Z" fill="#0084FE"/>
</g>
<defs>
<filter id="filter0_ii_2025_1119" x="-4" y="-4" width="125" height="81" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.517647 0 0 0 0 0.996078 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2025_1119"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.517647 0 0 0 0 0.996078 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2025_1119" result="effect2_innerShadow_2025_1119"/>
</filter>
<linearGradient id="paint0_linear_2025_1119" x1="58.5" y1="0" x2="58.5" y2="73" gradientUnits="userSpaceOnUse">
<stop stop-color="#0084FE" stop-opacity="0.2"/>
<stop offset="1" stop-color="#0084FE"/>
</linearGradient>
</defs>
</svg>
