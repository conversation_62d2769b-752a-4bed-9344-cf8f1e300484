<svg width="117" height="73" viewBox="0 0 117 73" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Img/Plan">
<g id="Rectangle 2415">
<g filter="url(#filter0_ii_2025_1118)">
<path d="M0 2C0 0.895432 0.895431 0 2 0H33.5L38 4.5H80L84.5 0H115C116.105 0 117 0.895431 117 2V71C117 72.1046 116.105 73 115 73H2C0.89543 73 0 72.1046 0 71V2Z" fill="url(#paint0_linear_2025_1118)" fill-opacity="0.35"/>
</g>
<path d="M37.6464 4.85355L37.7929 5H38H80H80.2071L80.3536 4.85355L84.7071 0.5H115C115.828 0.5 116.5 1.17157 116.5 2V71C116.5 71.8284 115.828 72.5 115 72.5H2C1.17157 72.5 0.5 71.8284 0.5 71V2C0.5 1.17157 1.17157 0.5 2 0.5H33.2929L37.6464 4.85355Z" stroke="#4EEEDB"/>
</g>
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M36 0H39H79H82L79 3H39L36 0Z" fill="#4EEEDB"/>
</g>
<defs>
<filter id="filter0_ii_2025_1118" x="-4" y="-4" width="125" height="81" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.305882 0 0 0 0 0.933333 0 0 0 0 0.858824 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2025_1118"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.305882 0 0 0 0 0.933333 0 0 0 0 0.858824 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2025_1118" result="effect2_innerShadow_2025_1118"/>
</filter>
<linearGradient id="paint0_linear_2025_1118" x1="58.5" y1="0" x2="58.5" y2="73" gradientUnits="userSpaceOnUse">
<stop stop-color="#4EEEDB" stop-opacity="0.2"/>
<stop offset="1" stop-color="#4EEEDB"/>
</linearGradient>
</defs>
</svg>
