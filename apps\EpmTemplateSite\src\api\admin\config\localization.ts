import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix
/**
 *  导出国际化配置表
 */
export function exportHomeExcel() {
  return request.post({
    url: `${homeServerPrefix}/Localization/ExportExcelFile`,
    headers: { "Content-Type": "application/x-download" },
    responseType: "blob",
  })
}

/**
 *  导入国际化配置表
 */
export function importHomeExcel(data) {
  return request.post({
    url: `${homeServerPrefix}/Localization/ImportExcelFile`,
    headers: { "Content-Type": "multipart/form-data" },
    data,
  })
}
