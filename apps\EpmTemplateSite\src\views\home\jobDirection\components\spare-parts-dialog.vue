<template>
  <!-- 增改用户弹框 -->
  <hmx-dialog
    dialogWidth="50%"
    :dialogTitle="
      options.type === 'add'
        ? $t('jobDirection.addFile')
        : $t('jobDirection.editFile')
    "
    customClass="hymson-dialog user-dialog"
    :isVisable="options.show"
    :isFooter="true"
    @closeDialog="closeFun"
    @save="saveDialog(formRef)"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="add-user-ruleForm"
      status-icon
      v-loading="isLoading"
    >
      <div class="form">
        <div>
          <el-form-item :label="$t('jobDirection.fileName')" prop="fileName">
            <el-input
              v-model="form.fileName"
              :placeholder="$t('jobDirection.fileNamePlaceholder')"
              type="textarea"
              resize="none"
              :rows="1"
            />
          </el-form-item>
        </div>
        <div>
          <el-form-item
            :label="$t('jobDirection.fileCategory')"
            prop="fileType"
          >
            <el-select
              v-model="form.fileType"
              class="form-input"
              :placeholder="$t('jobDirection.selectFilePlaceholder')"
              clearable
            >
              <el-option
                v-for="item in usableType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="">
            <span style="color: red">{{
              $t("jobDirection.uploadExcelWarning")
            }}</span>
          </el-form-item>
        </div>
        <div>
          <el-form-item prop="file">
            <template #label>
              <el-tooltip
                :content="`${$t(
                  'jobDirection.uploadFileSupportFormat'
                )}doc, docx, txt, xlsx, pdf, png, jpg, webp, mp4`"
                placement="top"
              >
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
              {{ $t("jobDirection.fileUpload") }}
            </template>
            <div class="flex-c-c">
              <div class="cover" @click="handleImport"></div>
              <div v-if="file">
                <div class="file-path">{{ file.name }}</div>
              </div>
              <div v-if="options.type !== 'add' && !file">
                <div class="file-path">{{ form.filePath }}</div>
              </div>
            </div>
          </el-form-item>
        </div>
        <div>
          <el-form-item
            :label="$t('jobDirection.fileDescription')"
            prop="fileDesc"
          >
            <el-input
              v-model="form.fileDesc"
              :placeholder="$t('jobDirection.fileDescriptionPlaceholder')"
              type="textarea"
              resize="none"
              :rows="3"
            />
          </el-form-item>
        </div>
      </div>
    </el-form>
    <input v-show="false" ref="fileRef" type="file" @change="getFile" />
  </hmx-dialog>
</template>

<script setup name="UserDialog">
import { reactive, ref, toRefs, watch } from "vue"
import HmxDialog from "@/components/hmx-dialog.vue"
import { config } from "@/config"
import useHandleFile, { perviewRemoteImg } from "@/hooks/useAdminFile"
import { postOPSFilesList, putOPSFilesList } from "@/api/front/jobDirection"
import { useI18n } from "@xfe/locale"
import { useMessage } from "@/hooks/web/useMessage"

const { createMessage } = useMessage()

const isLoading = ref(false)

const { t: $t } = useI18n()
const emit = defineEmits(["onSure", "onCancel"])

const props = defineProps({
  options: {
    type: Object,
    default: () => {
      return {
        show: false,
        type: "add", // 用于判断是编辑还是删除 add edit
        curFile: null, // 当前编辑的文件
      }
    },
  },
})

const formRef = ref()
const fileRef = ref(null)

const { getFile, file, fileBolb, handleImport } = useHandleFile(
  fileRef,
  true,
  200 * 1024
) // 需限制大小在20kb

const data = reactive({
  form: {
    fileName: "",
    fileType: "",
    fileDesc: "",
    file: "",
    filePath: "",
  },
  rules: {
    fileName: [
      {
        required: true,
        message: $t("jobDirection.fileNamePlaceholder"),
        trigger: "blur",
      },
    ],
    fileType: [
      {
        required: true,
        message: $t("jobDirection.selectFilePlaceholder"),
        trigger: "blur",
      },
    ],
    file: [{ required: true, message: "请上传文件", trigger: "change" }],
  },
})

// 添加文件状态监听
watch(
  () => file.value,
  newVal => {
    // 用于文件上传校验用的
    data.form.file = newVal ? "uploaded" : ""
  },
  { immediate: true }
)

// 通过字符串切片获取后缀名
function getFileExtension(filename) {
  let index = filename?.lastIndexOf(".")
  return filename?.slice(index + 1)
}

// 添加、修改用户
const saveDialog = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      // if (file.value) {
      if (props.options.type === "add") {
        if (file.value) {
          if (getFileExtension(file.value.name) == "xls") {
            createMessage($t("jobDirection.excelFormatTip"), "error", 5000)
            return
          } else if (
            getFileExtension(file.value.name) == "wav" ||
            getFileExtension(file.value.name) == "flv"
          ) {
            createMessage($t("jobDirection.videoFormatTip"), "error", 5000)
            return
          } else {
            let formDate = new FormData()
            formDate.append("file", file.value)
            formDate.append("FileName", form.value.fileName)
            formDate.append("FileType", form.value.fileType)
            formDate.append("FileDesc", form.value.fileDesc)
            isLoading.value = true
            await postOPSFilesList(formDate)
              .then(res => {
                isLoading.value = false
                createMessage($t("prompt.prompt_7"), "success")
              })
              .catch(() => {
                isLoading.value = false
                createMessage($t("prompt.prompt_23"), "error")
              })
          }
        }
      } else {
        if (getFileExtension(file.value?.name) == "xls") {
          createMessage($t("jobDirection.excelFormatTip"), "error", 5000)
          return
        } else if (
          getFileExtension(file.value?.name) == "wav" ||
          getFileExtension(file.value?.name) == "flv"
        ) {
          createMessage($t("jobDirection.videoFormatTip"), "error", 5000)
          return
        } else {
          let formDate = new FormData()
          formDate.append("file", file.value)
          formDate.append("FileName", form.value.fileName)
          formDate.append("FileType", form.value.fileType)
          formDate.append("FileDesc", form.value.fileDesc)
          isLoading.value = true
          await putOPSFilesList(formDate, props.options.curFile?.id)
            .then(res => {
              isLoading.value = false
              createMessage($t("prompt.prompt_8"), "success")
            })
            .catch(() => {
              isLoading.value = false
              createMessage($t("prompt.prompt_23"), "error")
            })
        }
      }
      // }

      emit("onSure", {
        type: props.options.type,
        id: props.options.curFile?.id, // 用于编辑的id
        activeTab: form.value.fileType, // 当前添加文件的对应tab
      })
      closeFun()
    }
  })
}

// 关闭弹框
const closeFun = () => {
  fileBolb.value = ""
  file.value = ""
  data.form = {
    fileName: "",
    fileType: "",
    fileDesc: "",
    file: "",
    filePath: "",
  }
  formRef.value.resetFields()
  emit("onCancel")
}

watch(
  () => props.options,
  val => {
    if (val.type === "edit") {
      const curFile = JSON.parse(JSON.stringify(val.curFile))
      for (let key in data.form) {
        data.form[key] = curFile[key]
      }
      data.form.file = curFile?.fileUrl ? "uploaded" : ""
    }
  },
  { deep: true }
)

// 可用类型
const usableType = [
  { value: 1, label: $t("jobDirection.toolMaterials") },
  { value: 2, label: $t("jobDirection.modelChangeMaterials") },
  { value: 3, label: $t("jobDirection.maintenanceMaterials") },
]

const { form, rules } = toRefs(data)
</script>

<style lang="scss">
.user-dialog.el-dialog {
  .add-user-ruleForm {
    padding: 0 20px 0 0;

    .el-form-item__label {
      flex: none;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
    }

    .el-form-item__content {
      .el-select,
      .el-input,
      .el-input-number {
        flex: none;
        width: 100%;
      }
    }
  }
}
</style>

<style scoped lang="scss">
.form {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .form-left {
    width: 100%;

    .reference-life {
      display: flex;

      .reference-life-left {
        width: 75%;
      }

      .reference-life-right {
        width: 20%;
      }
    }
  }
}

.cover {
  width: 54px;
  height: 54px;
  margin-right: 20px;
  cursor: pointer;
  background: url("@/assets/images/common/cover.png") no-repeat 100%;
  img {
    width: 100%;
    height: 100%;
  }
}
.flex-c-c {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
