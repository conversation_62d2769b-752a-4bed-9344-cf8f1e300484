<template>
  <hmx-dialog
    dialogWidth="760px"
    :dialogTitle="$t('alarmDialog.title')"
    :isVisable="modelValue"
    :draggable="true"
    @closeDialog="closeFun"
    customClass="spare-dialog"
  >
    <div class="spare-box" v-loading="loading">
      <div class="top">
        <div class="title">{{ $t("alarmDialog.basicInfo") }}</div>
        <div class="content">
          <p>{{ $t("alarmDialog.name") }}：{{ spareData.name }}</p>
          <p>{{ $t("alarmDialog.barcode") }}：{{ spareData.barcode }}</p>
          <p>
            {{ $t("alarmDialog.usageDistance") }}：{{ spareData.usedDistance }}
          </p>
          <p>
            {{ $t("alarmDialog.alarmThreshold") }}：{{
              spareData.alarmDistance
            }}
          </p>
          <p>
            {{ $t("alarmDialog.shutdownThreshold") }}：{{
              spareData.stopDistance
            }}
          </p>
        </div>
      </div>
      <div class="bottom">
        <div class="title">{{ $t("alarmDialog.recommendedTools") }}</div>
        <div class="tool">
          <swiper-box
            :srcList="imgList"
            boxType="image"
            v-if="imgList.length"
          ></swiper-box>
          <div class="empty-box" v-else>{{ $t("alarmDialog.noTools") }}</div>
        </div>
        <div class="title">{{ $t("alarmDialog.recommendedMaintenance") }}</div>
        <div class="tool">
          <swiper-box
            :srcList="videoList"
            boxType="video"
            v-if="videoList.length"
          ></swiper-box>
          <div class="empty-box" v-else>
            {{ $t("alarmDialog.noMaterials") }}
          </div>
        </div>
      </div>
    </div>
  </hmx-dialog>
</template>

<script setup name="FaultyDialog">
import { ref, computed, onMounted } from "vue"
import HmxDialog from "@/components/hmx-dialog.vue"
import SwiperBox from "@/components/swiper-box/index.vue"
import { useI18n } from "@xfe/locale"
import { config } from "@/config"
import emitter from "@/utils/mitt"

const { t: $t } = useI18n()

const emit = defineEmits(["update:modelValue"])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const { base_url } = config

const spareData = ref({})
const relevanceList = ref([])

emitter.on("getSparePartData", e => {
  spareData.value = e
})

emitter.on("getRelevanceList", e => {
  relevanceList.value = e
})

const closeFun = () => {
  emit("update:modelValue", false)
}

// 修改imgList和videoList的计算逻辑
const imgList = computed(() =>
  relevanceList.value
    .filter(item => item.fileType === 1)
    .map(item => ({
      type: "file",
      fileType: item.fileContentType,
      // path: getSourceURL(item.filePath),
      path: item.fileUrl,
      name: item.fileName,
    }))
)

const videoList = computed(() =>
  relevanceList.value
    .filter(item => [2, 3].includes(item.fileType))
    .map(item => ({
      type: "file",
      fileType: item.fileContentType,
      // path: getSourceURL(item.filePath),
      path: item.fileUrl,
      name: item.fileName,
    }))
)

const currentURL = base_url[process.env.VUE_APP_BASEPATH].replace(/\/api/, "")
const getSourceURL = url => {
  if (url) {
    return currentURL + "/" + url
  } else {
    return ""
  }
}
</script>

<style scoped lang="scss">
.spare-box {
  color: var(--g-font-color);
  .top {
    display: flex;
    flex-direction: column;
    width: 100%;
    .title {
      margin-bottom: 15px;
    }
    .img {
      width: 270px;
      height: 250px;
      border: 1px solid #0084fe;
    }
    .content {
      width: 100%;
      flex: 1;
      > p {
        margin-bottom: 15px;
      }
      :nth-last-child(1) {
        margin-bottom: 0;
      }
      .remainingLife {
        display: flex;
        align-items: center;
        width: 100%;
        height: 40px;
        .charts {
          width: 80%;
          height: 100%;
        }
        .count {
          width: 20%;
          font-size: 20px;
          margin-left: 10px;
        }
      }
      .warnInfo {
        color: #ee4e4e;
        font-size: 20px;
        font-weight: bold;
      }
    }
  }
  .bottom {
    margin-top: 10px;
    width: 100%;
    .title {
      margin: 10px 0;
    }
    .tool {
      .empty-box {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 90px;
        font-size: 20px;
        font-weight: bold;
        border: 1px solid #232836;
      }
    }
  }
}
</style>

<style lang="scss">
$border-color: red;
.spare-dialog {
  border: 2px solid $border-color;
  .el-dialog__header:first-child {
    background-color: #ee4e4e;
    margin: 0;
    .el-dialog__title {
      font-weight: 600;
    }
    .el-dialog__headerbtn {
      .el-dialog__close {
        font-size: 24px;
        color: #fff;
      }
    }
  }
}
</style>
