import { getGlobalConfig } from "@xfe/utils"

const { apiUrl, scoketUrl, serverName, moduleUrl } = getGlobalConfig(
  process.env
)

export interface StaticConfig {
  base_url: {
    dev: string
    scoket_dev: string
    mock: string
    scoket_mock: string
    pro: string
    scoket_pro: string
    release: string
    scoket_release: string
    homeServerPrefix: string
    bbl: string
    moduleUrl: string
  }
  result_code: string
  request_timeout: number
  default_headers: string
  whiteList: Array<string>
  version: string
}
const config: StaticConfig = {
  /**
   * api请求基础路径
   */
  base_url: {
    // 开发环境接口前缀
    dev:
      process.env.NODE_ENV === "dev"
        ? process.env.VUE_APP_GLOB_API_URL
        : apiUrl,
    scoket_dev:
      process.env.NODE_ENV === "dev"
        ? process.env.VUE_APP_GLOB_APP_SCOKET
        : scoketUrl,

    mock: process.env.VUE_APP_GLOB_APP_MOCK_URL,
    scoket_mock: process.env.VUE_APP_GLOB_APP_MOCK_SCOKET_URL,
    // 打包生产环境接口前缀
    pro:
      process.env.NODE_ENV === "dev"
        ? process.env.VUE_APP_GLOB_API_URL
        : apiUrl,
    scoket_pro:
      process.env.NODE_ENV === "dev"
        ? process.env.VUE_APP_GLOB_APP_SCOKET
        : scoketUrl,
    release:
      process.env.NODE_ENV === "test"
        ? process.env.VUE_APP_GLOB_API_URL
        : apiUrl,
    scoket_release:
      process.env.NODE_ENV === "test"
        ? process.env.VUE_APP_GLOB_APP_SCOKET
        : scoketUrl,
    homeServerPrefix: serverName,
    bbl: process.env.VUE_APP_GLOB_BBL_URL,
    moduleUrl:
      process.env.NODE_ENV != "production"
        ? process.env.VUE_APP_GLOB_3D_URL
        : moduleUrl,
  },

  /**
   * 接口成功返回状态码
   */
  result_code: "0000",

  /**
   * 接口请求超时时间
   */
  request_timeout: 60000,

  /**
   * 默认接口请求类型
   * 可选值：application/x-www-form-urlencoded multipart/form-data
   */
  default_headers: "application/json",
  /**
   * 系统白名单配置
   */
  whiteList: ["/login", "/404", "/lowcode/edit", "/lowcode/preview"],
  version: "*******",
}

export { config }
