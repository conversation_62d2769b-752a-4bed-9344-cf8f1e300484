<template>
  <div ref="realOEE" class="guage"></div>
</template>

<script setup>
import {
  ref,
  defineEmits,
  defineProps,
  defineExpose,
  onMounted,
  watch,
  nextTick,
} from "vue"
import useEcharts from "@/hooks/useEcharts"
import { RealOEECharts } from "./echartsConfig"
const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  isOee: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  "chart-click", // 点击chart
])

let realOEE = ref(null)
const { resize } = useEcharts(realOEE, emits, props, RealOEECharts)
defineExpose({
  resize,
})
// onMounted(() => {
//   resize()
// })

watch(
  () => props.isOee,
  v => {
    if (v) {
      nextTick(() => {
        resize()
      })
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.guage {
  width: 100%;
  height: 100%;
}
</style>
