<template>
  <div class="pie" ref="pieRef"></div>
</template>
<script setup>
import useCharts from "@/hooks/useCharts"
import { ref, watch, computed } from "vue"
import { useStore } from "vuex"

const store = useStore()
let themeColor = computed(() => store.state.theme.themeColor)

let props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  title: {
    type: String,
    default: "",
  },
})

let baseOption = {
  title: {
    text: props.title,
    textStyle: {
      color: "#fff",
      fontSize: 14,
    },
  },
  tooltip: {
    show: true,
    trigger: "axis",
    formatter: "{b}: {c}",
    // axisPointer: {
    //   type: "shadow",
    // },
  },
  dataZoom: [
    {
      type: "slider",
      show: true,
      yAxisIndex: [0],
      left: "96%",
      width: 10,
      maxValueSpan: 10,
      minValueSpan: 1,
      backgroundColor: "#616573",
      borderColor: "none",
      fillerColor: "#2B98FF",
      textStyle: {
        color: "#fff",
      },
    },
  ],
  grid: {
    top: "10%",
    left: "3%",
    right: "8%",
    bottom: "5%",
    containLabel: true,
  },
  xAxis: {
    type: "value",
    data: [],
    axisLabel: {
      show: false,
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255,255,255,0.15)",
        width: 1,
      },
    },
  },
  yAxis: [
    {
      type: "category",
      axisLabel: {
        color: "#fff", // 刻度标签文字的颜色
        fontSize: "12", // 文字字体大小
        interval: 0,
        show: false,
      },
    },
    {
      axisTick: "none",
      axisLine: "none",
      axisLabel: {
        textStyle: {
          color: "#ffffff",
          fontSize: "10",
        },
        verticalAlign: "middle",
        margin: 20,
        formatter: function (params) {
          let resultDom = ""
          if (params.length > 6) {
            resultDom = params.substring(0, 6) + "..."
          } else {
            resultDom = params
          }
          return resultDom
        },
      },
      data: [],
    },
  ],
  series: [
    {
      type: "bar",
      barWidth: "24",
      data: [],
      itemStyle: {},
      color: "#0084FE",
      label: {
        show: true,
        position: "inside",
        color: "#fff",
        formatter: function (params) {
          return params.value
        },
      },
    },
  ],
}

const DARK_COLOR = "#fff"
const LIGHT_COLOR = "#606266"

watch(
  () => themeColor.value,
  v => {
    if (v === "light") {
      baseOption.title.textStyle.color = LIGHT_COLOR
      baseOption.yAxis[0].axisLabel.color = LIGHT_COLOR
      baseOption.yAxis[1].axisLabel.textStyle.color = LIGHT_COLOR
    } else {
      baseOption.title.textStyle.color = DARK_COLOR
      baseOption.yAxis[0].axisLabel.color = DARK_COLOR
      baseOption.yAxis[1].axisLabel.textStyle.color = DARK_COLOR
    }
  },
  { immediate: true }
)

let pieRef = ref()
let { updateDraw } = useCharts(pieRef, baseOption)

watch(
  () => props.option,
  () => {
    updateDraw(props.option)
  },
  { deep: true }
)
</script>
<style scoped lang="scss">
.pie {
  width: 100%;
  height: 100%;
}
</style>
