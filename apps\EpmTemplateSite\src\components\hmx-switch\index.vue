<template>
  <div class="switch flx ml10">
    <div
      class="start"
      :class="{ 'active-tab': active === 1 }"
      @click="handleSwitch(1)"
    >
      {{ firstLabel }}
    </div>
    <div
      class="stop"
      :class="{ 'active-tab': active === 2 }"
      @click="handleSwitch(2)"
    >
      {{ secondLabel }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue"

interface HmxSwitch {
  firstLabel?: string
  secondLabel?: string
  defaultValue?: string | number
  isController?: boolean // 选中逻辑是否由外部控制
  parentActive?: number
}

const props = withDefaults(defineProps<HmxSwitch>(), {
  firstLabel: "启用",
  secondLabel: "停用",
  defaultValue: 1,
  isController: false,
  parentActive: 1,
})

const emits = defineEmits(["handleSwitch"])

const active = ref(props.defaultValue)

const handleSwitch = (i: number) => {
  if (!props.isController) {
    if (active.value === i) return
    active.value = i
  }
  let val = i == 1 ? true : false
  emits("handleSwitch", val)
}

watch(
  () => props.parentActive,
  v => {
    active.value = v
  }
)
</script>
<style scoped lang="scss">
.switch {
  > div {
    width: 96px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: #d9d9d9;
    color: #5a5a5a;
    font-size: 16px;
    cursor: pointer;
  }
  .active-tab {
    background: #54dd13;
    color: #000;
  }
  .start {
    border-radius: 4px 0 0 4px;
  }
  .stop {
    border-radius: 0 4px 4px 0;
  }
}
</style>
