<template>
  <div class="bar" ref="barRef"></div>
</template>
<script setup>
import useCharts from "@/hooks/useCharts"
import { ref, watch, computed } from "vue"
import { useStore } from "vuex"

const store = useStore()
let themeColor = computed(() => store.state.theme.themeColor)

let props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  unit: {
    type: String,
    default: "",
  },
})

const unit = computed(() => props.unit)

let baseOption = {
  tooltip: {
    show: true,
    confine: true,
    trigger: "axis",
    formatter: `{b}: {c}${unit.value || ""}`,
  },
  dataZoom: [
    {
      type: "slider",
      show: true,
      yAxisIndex: [0],
      left: "96%",
      width: 10,
      maxValueSpan: 10,
      minValueSpan: 1,
      backgroundColor: "#616573",
      borderColor: "none",
      fillerColor: "#2B98FF",
      textStyle: {
        color: "#fff",
      },
    },
  ],
  grid: {
    top: "0%",
    left: "3%",
    right: "8%",
    bottom: "10%",
    containLabel: true,
  },
  xAxis: {
    show: true,
    type: "value",
    axisLabel: {
      textStyle: {
        color: "#fff",
      },
    },
    axisLine: {
      show: false,
      lineStyle: {
        type: "solid",
        color: "#292c38",
      },
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: "solid",
        color: "rgba(77, 128, 254, 0.2)",
      },
    },
  },
  yAxis: [
    {
      type: "category",
      inverse: true,
      axisLabel: {
        interval: 0,
        show: true,
        color: "#fff",
        formatter: function (value) {
          return value.length > 6 ? `${value.substring(0, 6)}...` : value
        },
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      data: props.option?.yAxisData || [],
    },
  ],
  series: [
    {
      type: "bar",
      barWidth: "14",
      data: props.option?.seriesData || [],
      itemStyle: {},
      color: "#0084FE",
      label: {
        show: true,
        position: "inside",
        color: "#fff",
        formatter: function (params) {
          return params.value
        },
      },
    },
  ],
}

const DARK_COLOR = "#fff"
const LIGHT_COLOR = "#606266"

watch(
  () => themeColor.value,
  v => {
    if (v === "light") {
      baseOption.yAxis[0].axisLabel.color = LIGHT_COLOR
    } else {
      baseOption.yAxis[0].axisLabel.color = DARK_COLOR
    }
  },
  { immediate: true }
)

let barRef = ref()
let { updateDraw } = useCharts(barRef, baseOption)

watch(
  () => props.option,
  () => {
    updateDraw(props.option)
  },
  { deep: true }
)
</script>
<style scoped lang="scss">
.bar {
  width: 100%;
  height: 100%;
}
</style>
