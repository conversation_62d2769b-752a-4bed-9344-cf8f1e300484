<template>
  <dialog-card
    dialogWidth="80%"
    :dialogTitle="$t('common.alarmDetail')"
    :isVisable="dialog"
    :append-to-body="false"
    @closeDialog="closeFun"
    @openDialog="openFun"
    top="50px"
  >
    <div class="detail-table">
      <hmx-table
        :table-data="list"
        :options="tableOptions"
        :columns="tableColumn"
        @size-change="handlerPageSize"
        @current-change="handlerPageIndex"
      >
      </hmx-table>
    </div>
  </dialog-card>
</template>

<script setup>
import dialogCard from "@/components/hmx-dialog.vue"
import useLogDetail from "@/hooks/useLogDetail"
import { useI18n } from "@xfe/locale"

const { t: $t } = useI18n()
let { list, tableOptions, checkList, handlerPageSize, handlerPageIndex } =
  useLogDetail("warn/details")
const emit = defineEmits(["onCancel"])

const props = defineProps({
  dialog: {
    type: Boolean,
    default: false,
  },
  alarmInfo: {
    type: Object,
    default: {},
  },
})

let tableColumn = [
  { type: "index", width: "50", label: "No.", align: "center" },
  {
    prop: "warningCode",
    label: $t("table.alarmQuery.alarmCode"),
    //align: "center",
  },
  {
    prop: "message",
    width: "300",
    label: $t("table.alarmQuery.content"),
    //align: "center",
  },
  {
    prop: "creationTime",
    label: $t("table.alarmQuery.startTime"),
    //align: "center",
  },
  {
    prop: "continueTime",
    label: $t("table.alarmQuery.alarmDuration"),
    //align: "center",
  },
  {
    prop: "errorMsg",
    label: $t("table.alarmQuery.alarmInformation"),
    //align: "center",
  },
  {
    prop: "serverName",
    label: $t("table.alarmQuery.serviceName"),
    //align: "center",
  },
  {
    prop: "moduleName",
    label: $t("table.alarmQuery.moduleName"),
    //align: "center",
  },
  {
    prop: "suggest",
    label: $t("table.alarmQuery.operationSuggestion"),
    //align: "center",
  },
  {
    prop: "remark",
    label: $t("table.alarmQuery.remarks"),
    //align: "center",
  },
]

const openFun = () => {
  checkList(props.alarmInfo)
}

const closeFun = () => {
  emit("onCancel")
}
</script>
<style scoped>
.detail-table {
  width: 100%;
  height: 500px;
  display: flex;
  flex-direction: column;
}
</style>
<style lang="scss" scoped>
.add-user.el-dialog {
  .el-dialog__body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}
</style>
