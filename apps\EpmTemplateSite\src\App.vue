<template>
  <el-config-provider :locale="elLocaleMap[getLocale]">
    <div class="sys-layout" :data-theme="themeColor">
      <component :is="resolveLayout">
        <router-view></router-view>
      </component>
    </div>
  </el-config-provider>
</template>

<script>
import { computed, onMounted, onUnmounted, ref } from "vue"
import { useRoute } from "vue-router"
// import { router, useRoute } from "@xfe/router"
import { useStore } from "vuex"
import { ElConfigProvider } from "element-plus"
import LayoutBlank from "@/layouts/blank.vue"
import LayoutContent from "@/layouts/content.vue"
import { useLocale } from "@xfe/locale"
import zh_CN from "element-plus/es/locale/lang/zh-cn"
import en from "element-plus/es/locale/lang/en"

export default {
  name: "APP",
  components: {
    ElConfigProvider,
    LayoutBlank,
    LayoutContent,
  },
  setup() {
    const elLocaleMap = {
      "zh-CN": zh_CN,
      "en-US": en,
    }
    const store = useStore()

    const fullScreen = computed(() => {
      return store.getters["fullScreen"]
    })

    const { getLocale } = useLocale()

    // 刷新页面后重置 用于刷新页面后再次建立长连接
    // store.dispatch("setListenDataLoopFlag", false)

    const route = useRoute()

    const resolveLayout = computed(() => {
      // Handles initial route
      let str = "LayoutBlank"
      if (!route.name) return null
      if (route.meta.layout === "blank") {
        str = "LayoutBlank"
      } else {
        str = "LayoutContent"
      }
      return str
    })
    // 用于键盘F11全屏时报错问题
    const KeyDown = event => {
      if (event.keyCode === 122) {
        event.returnValue = false
        if (!fullScreen.value) {
          store.dispatch("updateFullScreen")
        }
      }
    }
    const checkFull = () => {
      let isFull =
        document.fullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement
      // to fix : false || undefined == undefined
      if (isFull === undefined) isFull = false
      return isFull
    }

    let themeColor = computed(() => store.state.theme.themeColor)
    const htmlDom = document.querySelector("html")
    onMounted(() => {
      window.addEventListener("keydown", KeyDown, true) // 监听按键事件
      window.onresize = () => {
        if (!checkFull()) {
          // 要执行的动作
          // store.commit("SET_FULLSCREEN", false);
        }
      }
      // 挂载到根节点
      document
        .querySelector("#app")
        .setAttribute("data-theme", themeColor.value)
      // 挂载到html
      if (themeColor.value === "dark") {
        htmlDom.classList.add("dark")
        htmlDom.setAttribute("data-theme", "dark")
        htmlDom.classList.remove("light")
        htmlDom.classList.remove("black")
      } else if (themeColor.value === "light") {
        htmlDom.setAttribute("data-theme", "light")
        htmlDom.classList.remove("dark")
        htmlDom.classList.remove("black")
      } else {
        htmlDom.classList.add("dark")
        htmlDom.classList.add("black")
        htmlDom.setAttribute("data-theme", "black")
        htmlDom.classList.remove("light")
      }
      // 获取当前国际化语言
      store.dispatch("notice/getCurrentLanguage")
      // 关闭浏览器TAB页时提醒
      window.addEventListener("beforeunload", handleCloseBrowser)
    })
    onUnmounted(() => {
      window.removeEventListener("keydown", KeyDown, true) // 取消销毁监听按键事件
      window.removeEventListener("beforeunload", handleCloseBrowser)
    })

    const handleCloseBrowser = e => {
      // e.preventDefault()
      // // 自定义提示消息
      // var confirmationMessage = "确定要关闭该页面吗？"
      // // 兼容不同浏览器的提示消息设置
      // ;(e || window.event).returnValue = confirmationMessage
      // return confirmationMessage
    }

    return {
      themeColor,
      resolveLayout,
      getLocale,
      elLocaleMap,
    }
  },
}
</script>

<style>
.sys-layout {
  width: 100%;
  height: 100%;
}
</style>
