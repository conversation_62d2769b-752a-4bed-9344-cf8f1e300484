import * as BABYLON from "babylonjs"

// 定义动画组接口，作为BABYLON.AnimationGroup的简化版本
interface AnimationGroupLike {
  stop: () => void
  dispose: () => void
}

/**
 * BabylonJS场景管理器
 * 负责场景初始化、渲染循环和资源管理
 */
export class SceneManager {
  private canvas: HTMLCanvasElement | null = null
  private engine: BABYLON.Engine | null = null
  private scene: BABYLON.Scene | null = null
  private camera: BABYLON.ArcRotateCamera | null = null
  private highlightLayer: BABYLON.HighlightLayer | null = null
  private resizeListener: (() => void) | null = null

  // 存储动画和效果的引用，用于后续管理
  private animationGroups: (BABYLON.AnimationGroup | AnimationGroupLike)[] = []
  private lightAnimations: BABYLON.Animation[] = []
  private materialAnimations: BABYLON.Animation[] = []

  /**
   * 初始化场景
   * @param canvas 渲染目标Canvas
   * @returns 创建的场景对象
   */
  public initScene(canvas: HTMLCanvasElement): BABYLON.Scene | null {
    if (!canvas) return null

    this.canvas = canvas

    // 创建引擎和场景
    this.engine = new BABYLON.Engine(canvas, true)
    this.scene = new BABYLON.Scene(this.engine)
    this.scene.clearColor = new BABYLON.Color4(0.05, 0.05, 0.05, 1)

    BABYLON.DracoCompression.Configuration = {
      decoder: {
        wasmUrl:
          "/static/bbl_file/babylon-draco-files/draco_wasm_wrapper_gltf.js",
        wasmBinaryUrl:
          "/static/bbl_file/babylon-draco-files/draco_decoder_gltf.wasm",
        fallbackUrl:
          "/static/bbl_file/babylon-draco-files/draco_decoder_gltf.js",
      },
    }

    // 创建相机
    this.camera = new BABYLON.ArcRotateCamera(
      "camera",
      -Math.PI / 2,
      Math.PI / 2,
      10,
      new BABYLON.Vector3(0, 0, 0),
      this.scene
    )
    this.camera.attachControl(canvas, true)
    this.camera.wheelPrecision = 50
    this.camera.lowerRadiusLimit = 2
    this.camera.upperRadiusLimit = 50

    // 添加beta角度限制，防止查看底部
    this.camera.lowerBetaLimit = Math.PI / 6 // 最小30度，防止视角太低
    this.camera.upperBetaLimit = Math.PI / 2.2 // 最大约82度，略微仰视

    // 创建光源
    const light1 = new BABYLON.HemisphericLight(
      "light1",
      new BABYLON.Vector3(1, 1, 0),
      this.scene
    )
    light1.intensity = 0.8

    const light2 = new BABYLON.DirectionalLight(
      "light2",
      new BABYLON.Vector3(0, -1, 1),
      this.scene
    )
    light2.intensity = 0.5

    // 创建高亮层用于告警显示
    this.highlightLayer = new BABYLON.HighlightLayer(
      "highlightLayer",
      this.scene
    )

    // 开始渲染循环
    this.startRenderLoop()

    // 响应窗口大小变化
    this.setupResizeListener()

    return this.scene
  }

  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    if (!this.engine || !this.scene) return

    this.engine.runRenderLoop(() => {
      if (this.scene) {
        this.scene.render()
      }
    })
  }

  /**
   * 设置窗口大小变化监听器
   */
  private setupResizeListener(): void {
    if (!this.engine) return

    this.resizeListener = () => {
      if (this.engine) {
        this.engine.resize()
      }
    }

    window.addEventListener("resize", this.resizeListener)
  }

  /**
   * 获取场景对象
   */
  public getScene(): BABYLON.Scene | null {
    return this.scene
  }

  /**
   * 获取相机对象
   */
  public getCamera(): BABYLON.ArcRotateCamera | null {
    return this.camera
  }

  /**
   * 获取引擎对象
   */
  public getEngine(): BABYLON.Engine | null {
    return this.engine
  }

  /**
   * 获取高亮层
   */
  public getHighlightLayer(): BABYLON.HighlightLayer | null {
    return this.highlightLayer
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    if (this.resizeListener) {
      window.removeEventListener("resize", this.resizeListener)
      this.resizeListener = null
    }

    if (this.engine) {
      this.engine.dispose()
      this.engine = null
    }

    this.scene = null
    this.camera = null
    this.highlightLayer = null
    this.canvas = null
  }

  /**
   * 计算网格集合的边界框
   * @param meshes 要计算边界框的网格数组
   * @returns 边界信息对象
   */
  public calculateBoundingBox(
    meshes: BABYLON.AbstractMesh[]
  ): BABYLON.BoundingInfo | null {
    if (meshes.length === 0) return null

    let min = new BABYLON.Vector3(
      Number.MAX_VALUE,
      Number.MAX_VALUE,
      Number.MAX_VALUE
    )
    let max = new BABYLON.Vector3(
      Number.MIN_VALUE,
      Number.MIN_VALUE,
      Number.MIN_VALUE
    )

    meshes.forEach(mesh => {
      if (!mesh.getBoundingInfo) return

      const boundingInfo = mesh.getBoundingInfo()
      const meshMin = boundingInfo.boundingBox.minimumWorld
      const meshMax = boundingInfo.boundingBox.maximumWorld

      min = BABYLON.Vector3.Minimize(min, meshMin)
      max = BABYLON.Vector3.Maximize(max, meshMax)
    })

    return new BABYLON.BoundingInfo(min, max)
  }

  /**
   * 设置场景环境贴图和天空盒
   * @param envSpecularPath 环境反射贴图路径
   * @param envPath 环境贴图路径
   * @returns 是否设置成功
   */
  public async setEnvironment(
    envSpecularPath: string,
    envPath: string
  ): Promise<boolean> {
    if (!this.scene) {
      return false
    }

    try {
      // 创建并设置环境反射贴图
      const hdrTexture = BABYLON.CubeTexture.CreateFromPrefilteredData(
        envSpecularPath,
        this.scene
      )
      this.scene.environmentTexture = hdrTexture
      // 调低环境反射的亮度
      this.scene.environmentIntensity = 0.9 // 调低亮度

      // 创建并设置天空盒
      const boxTexture = BABYLON.CubeTexture.CreateFromPrefilteredData(
        envPath,
        this.scene
      )

      if (this.camera) {
        this.scene.createDefaultSkybox(
          boxTexture,
          true,
          (this.camera.maxZ - this.camera.minZ) / 2,
          0.3,
          false
        )
      }

      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 切换wkall节点的可见性
   * @param visible 是否显示wkall节点下的模型
   * @returns 操作是否成功
   */
  public toggleWkallVisibility(visible: boolean): boolean {
    if (!this.scene) {
      return false
    }

    try {
      // 使用getTransformNodeByName方法获取wkall节点
      const wkallNode = this.scene.getTransformNodeByName("wkall")

      if (wkallNode) {
        // 递归设置wkall节点及其所有子节点的可见性
        const setNodeVisibility = (node: BABYLON.Node, isVisible: boolean) => {
          // 如果是网格，设置其可见性
          if (node instanceof BABYLON.AbstractMesh) {
            node.isVisible = isVisible
          }

          // 递归处理子节点
          if (node.getChildren) {
            node.getChildren().forEach(child => {
              setNodeVisibility(child, isVisible)
            })
          }
        }

        setNodeVisibility(wkallNode, visible)
        return true
      } else {
        return false
      }
    } catch (error) {
      return false
    }
  }

  /**
   * 为hongdeng节点内的所有模型添加灯光闪烁效果
   * @returns 操作是否成功
   */
  public addHongdengLightFlashing(): boolean {
    if (!this.scene) {
      return false
    }

    try {
      const hongdengNode = this.scene.getTransformNodeByName("hongdeng")

      if (hongdengNode) {
        // 为hongdeng节点及其子节点添加闪烁效果
        const addAlarmEffectToNode = (node: BABYLON.Node) => {
          if (node instanceof BABYLON.AbstractMesh) {
            // 为每个模型添加点光源，增强视觉效果
            const light = new BABYLON.PointLight(
              `hongdeng_light_${node.name}`,
              node.getAbsolutePosition(),
              this.scene!
            )
            light.diffuse = new BABYLON.Color3(1, 0, 0) // 红色
            light.specular = new BABYLON.Color3(1, 0, 0)
            light.intensity = 5 // 高亮度
            light.range = 20 // 更大的影响范围

            // 创建光源强度闪烁动画
            const intensityAnimation = new BABYLON.Animation(
              `hongdeng_intensity_${node.name}`,
              "intensity",
              30,
              BABYLON.Animation.ANIMATIONTYPE_FLOAT,
              BABYLON.Animation.ANIMATIONLOOPMODE_CYCLE
            )

            const keys = []
            keys.push({ frame: 0, value: 1 })
            keys.push({ frame: 15, value: 10 }) // 更高的强度波动
            keys.push({ frame: 30, value: 1 })
            intensityAnimation.setKeys(keys)

            light.animations.push(intensityAnimation)
            this.scene!.beginAnimation(light, 0, 30, true)

            // 如果模型有材质，设置为发光材质
            if (node.material) {
              // 保存原始材质
              const originalMaterial = node.material

              // 创建强化的红色发光材质
              const alarmMaterial = new BABYLON.StandardMaterial(
                `alarm_${node.name}`,
                this.scene!
              )
              alarmMaterial.diffuseColor = new BABYLON.Color3(1, 0, 0) // 红色
              alarmMaterial.emissiveColor = new BABYLON.Color3(1, 0, 0) // 强红色发光
              alarmMaterial.specularColor = new BABYLON.Color3(1, 0, 0)
              alarmMaterial.specularPower = 64 // 高光泽度

              // 放大模型以增强视觉效果
              const originalScaling = node.scaling.clone()
              const scaledUp = originalScaling.scale(1.2) // 放大20%

              // 创建材质和缩放的交替切换
              let useAlarmEffect = false
              const alarmInterval = setInterval(() => {
                if (!this.scene) {
                  clearInterval(alarmInterval)
                  return
                }

                useAlarmEffect = !useAlarmEffect

                // 切换材质
                node.material = useAlarmEffect
                  ? alarmMaterial
                  : originalMaterial

                // 切换缩放
                node.scaling = useAlarmEffect ? scaledUp : originalScaling
              }, 300) // 更快的闪烁频率

              // 清理函数
              this.scene!.onDisposeObservable.add(() => {
                clearInterval(alarmInterval)
                // 恢复原始状态
                node.material = originalMaterial
                node.scaling = originalScaling
              })
            } else {
              // 如果没有材质，创建一个红色材质
              const alarmMaterial = new BABYLON.StandardMaterial(
                `alarm_${node.name}`,
                this.scene!
              )
              alarmMaterial.diffuseColor = new BABYLON.Color3(1, 0, 0)
              alarmMaterial.emissiveColor = new BABYLON.Color3(1, 0, 0)
              node.material = alarmMaterial

              // 添加缩放动画
              const scalingAnimation = new BABYLON.Animation(
                `hongdeng_scaling_${node.name}`,
                "scaling",
                30,
                BABYLON.Animation.ANIMATIONTYPE_VECTOR3,
                BABYLON.Animation.ANIMATIONLOOPMODE_CYCLE
              )

              const originalScale = node.scaling.clone()
              const keys = []
              keys.push({ frame: 0, value: originalScale })
              keys.push({ frame: 30, value: originalScale.scale(2) }) // 放大30%
              keys.push({ frame: 60, value: originalScale })
              scalingAnimation.setKeys(keys)

              node.animations.push(scalingAnimation)
              this.scene!.beginAnimation(node, 0, 30, true)
            }

            // 将模型添加到高亮层
            const highlightLayer = this.getHighlightLayer()
            if (highlightLayer) {
              highlightLayer.addMesh(
                node as BABYLON.Mesh,
                BABYLON.Color3.Red(),
                true
              )
            }
          }

          // 递归处理子节点
          if (node.getChildren) {
            node.getChildren().forEach(child => {
              addAlarmEffectToNode(child)
            })
          }
        }

        addAlarmEffectToNode(hongdengNode)
        return true
      } else {
        return false
      }
    } catch (error) {
      return false
    }
  }

  /**
   * 为指定模型ID添加旋转动画
   * @param modelIds 要添加旋转动画的模型ID数组
   * @returns 操作是否成功
   */
  public addRotationAnimations(modelIds: string[]): boolean {
    if (!this.scene) {
      return false
    }

    try {
      // 定义旋转动画辅助函数
      const createAndApplyRotationAnimation = (
        node: BABYLON.TransformNode | BABYLON.AbstractMesh
      ) => {
        // 记录节点初始旋转状态
        const initialRotation = node.rotation
          ? node.rotation.clone()
          : new BABYLON.Vector3(0, 0, 0)

        // 检查是否已设置rotationQuaternion
        const hadQuaternion =
          node.rotationQuaternion !== null &&
          node.rotationQuaternion !== undefined
        const originalQuaternion = hadQuaternion
          ? node.rotationQuaternion!.clone()
          : null

        // 临时清除rotationQuaternion以启用rotation属性
        if (hadQuaternion) {
          node.rotationQuaternion = null
        }

        // 创建旋转动画
        const rotationAnimation = new BABYLON.Animation(
          `yRotation_${node.name}`,
          "rotation.z",
          30, // 每秒30帧
          BABYLON.Animation.ANIMATIONTYPE_FLOAT,
          BABYLON.Animation.ANIMATIONLOOPMODE_CYCLE
        )

        // 设置关键帧 - 一个完整的360度旋转
        const keyFrames = []
        keyFrames.push({ frame: 0, value: node.rotation.y })
        keyFrames.push({ frame: 120, value: node.rotation.y + Math.PI * 2 }) // 4秒完成一圈
        rotationAnimation.setKeys(keyFrames)

        // 确保animations数组存在
        node.animations = node.animations || []

        // 移除任何现有的旋转动画
        node.animations = node.animations.filter(
          anim => !anim.targetProperty.includes("rotation.y")
        )

        // 添加新动画
        node.animations.push(rotationAnimation)

        // 使用beginDirectAnimation以获得更好的控制
        const animatable = this.scene!.beginDirectAnimation(
          node,
          [rotationAnimation],
          0,
          120,
          true // 循环播放
        )

        // 存储动画引用，以便后续管理
        if (animatable) {
          this.animationGroups.push({
            stop: () => {
              animatable.stop()
              // 恢复原始状态
              node.rotation = initialRotation
              if (hadQuaternion && originalQuaternion) {
                node.rotationQuaternion = originalQuaternion
              }
            },
            dispose: () => {
              if (node.animations) {
                node.animations = node.animations.filter(
                  anim => anim !== rotationAnimation
                )
              }
            },
          } as AnimationGroupLike)

          return true
        } else {
          // 恢复原始状态
          if (hadQuaternion && originalQuaternion) {
            node.rotationQuaternion = originalQuaternion
          }
          return false
        }
      }

      // 查找并应用动画的主函数
      const findAndAnimateNode = (modelId: string): boolean => {
        // 尝试精确匹配TransformNode
        const transformNode = this.scene!.getTransformNodeByName(modelId)
        if (transformNode) {
          return createAndApplyRotationAnimation(transformNode)
        }

        // 尝试精确匹配Mesh
        const mesh = this.scene!.getMeshByName(modelId)
        if (mesh) {
          return createAndApplyRotationAnimation(mesh)
        }

        // 尝试模糊匹配TransformNode
        const fuzzyTransformNodes = this.scene!.transformNodes.filter(
          t => t.name.includes(modelId) || modelId.includes(t.name)
        )

        if (fuzzyTransformNodes.length > 0) {
          let success = false
          fuzzyTransformNodes.forEach(node => {
            if (createAndApplyRotationAnimation(node)) {
              success = true
            }
          })
          return success
        }

        // 尝试模糊匹配Mesh
        const fuzzyMeshes = this.scene!.meshes.filter(
          m => m.name.includes(modelId) || modelId.includes(m.name)
        )

        if (fuzzyMeshes.length > 0) {
          let success = false
          fuzzyMeshes.forEach(mesh => {
            if (createAndApplyRotationAnimation(mesh)) {
              success = true
            }
          })
          return success
        }

        return false
      }

      let successCount = 0

      // 为每个模型ID应用动画
      modelIds.forEach(modelId => {
        if (findAndAnimateNode(modelId)) {
          successCount++
        }
      })

      return successCount > 0
    } catch (error) {
      return false
    }
  }

  /**
   * 为jiguang节点内的所有模型添加隐藏显示效果
   * @returns 操作是否成功
   */
  public addJiguangFlashing(): boolean {
    if (!this.scene) {
      return false
    }

    try {
      const jiguangNode = this.scene.getTransformNodeByName("jiguang")

      if (jiguangNode) {
        // 为jiguang节点及其子节点添加隐藏显示效果
        const addVisibilityToggleToNode = (node: BABYLON.Node) => {
          if (node instanceof BABYLON.AbstractMesh) {
            // 创建可见性切换动画
            const visibilityAnimation = new BABYLON.Animation(
              `jiguang_visibility_${node.name}`,
              "isVisible",
              10, // 10 FPS
              BABYLON.Animation.ANIMATIONTYPE_FLOAT,
              BABYLON.Animation.ANIMATIONLOOPMODE_CYCLE
            )

            const keys = []
            keys.push({ frame: 0, value: true }) // 显示
            keys.push({ frame: 5, value: true }) // 保持显示0.5秒
            keys.push({ frame: 6, value: false }) // 隐藏
            keys.push({ frame: 10, value: false }) // 保持隐藏0.4秒
            visibilityAnimation.setKeys(keys)

            node.animations = node.animations || []
            node.animations.push(visibilityAnimation)

            // 开始动画
            this.scene!.beginAnimation(node, 0, 10, true)
          }

          // 递归处理子节点
          if (node.getChildren) {
            node.getChildren().forEach(child => {
              addVisibilityToggleToNode(child)
            })
          }
        }

        addVisibilityToggleToNode(jiguangNode)
        return true
      } else {
        return false
      }
    } catch (error) {
      return false
    }
  }

  /**
   * 初始化所有默认动画和效果
   */
  public initializeDefaultEffects(): void {
    // 添加hongdeng灯光闪烁效果
    this.addHongdengLightFlashing()

    // 添加旋转动画到指定模型
    const rotatingModels = [
      "AUnwindingAxis",
      "BUnwindingAxis",
      "UpBRewindingAxis",
      "UpARewindingAxis",
      "DownARewindingAxis",
      "DownBRewindingAxis",
    ]
    this.addRotationAnimations(rotatingModels)

    // 添加jiguang闪烁效果
    this.addJiguangFlashing()
  }

  /**
   * 停止所有动画和效果
   */
  public stopAllEffects(): void {
    if (!this.scene) return

    // 停止所有运行中的动画
    this.scene.stopAllAnimations()

    // 停止动画组
    this.animationGroups.forEach(group => {
      group.stop()
      group.dispose()
    })
    this.animationGroups = []

    // 清理光源动画
    this.lightAnimations = []

    // 清理材质动画
    this.materialAnimations = []

    // 触发场景的onDisposeObservable，清理我们添加的interval
    this.scene.onDisposeObservable.notifyObservers(this.scene)
  }
}
