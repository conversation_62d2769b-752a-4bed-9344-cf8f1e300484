import store from "@/store"
import { debounce } from "@/utils"

export default function listenOperation(num) {
  let times = 1 // 计时起点
  const calcTime = 1000 * 60 * 1 // 计时计数间隔
  const endTime = num // logoutTime 2 / 3分钟计时一次 / 4 //未操作终点计时  每4分钟计数一次
  let opeTime = null
  opeTime && clearInterval(opeTime)
  startListen()
  document.body.addEventListener("click", clearOpeTime)
  document.body.addEventListener("mousemove", debounce(clearOpeTime, 100))
  document.body.addEventListener("keydown", clearOpeTime)

  function clearOpeTime() {
    times = 1
    clearInterval(opeTime)
    startListen()
  }
  let tipDialog = 1
  function startListen() {
    opeTime = setInterval(async () => {
      if (times >= endTime) {
        clearInterval(opeTime)
        document.body.removeEventListener("click", clearOpeTime)
        document.body.removeEventListener("mousemove", clearOpeTime)
        document.body.removeEventListener("keydown", clearOpeTime)
        if (tipDialog < 2) {
          store.commit("SET_OVER_TIME", true)
        }
        tipDialog++
      } else {
        times++
      }
    }, calcTime)
  }
}
