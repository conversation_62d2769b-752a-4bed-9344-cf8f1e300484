<template>
  <!-- 新增知识库记录弹窗 -->
  <hmx-dialog
    dialogWidth="50%"
    :dialogTitle="$t('knowledgeBase.addRecord')"
    customClass="hymson-dialog record-dialog"
    :isVisable="isShow"
    :isFooter="true"
    :disabled="isSubmitting"
    @closeDialog="closeFun"
    @save="saveDialog(formRef)"
    top="50px"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="add-user-ruleForm"
      status-icon
    >
      <el-form-item
        :label="$t('knowledgeBase.problemPhenomenon')"
        prop="phenomenonContent"
      >
        <el-autocomplete
          v-model="form.phenomenonContent"
          :fetch-suggestions="handleSearchPhenomenon"
          :trigger-on-focus="false"
          clearable
          style="width: 100%"
          :placeholder="$t('knowledgeBase.problemPhenomenonPlaceholder')"
        />
      </el-form-item>
      <el-form-item
        :label="$t('knowledgeBase.relatedFaultCause')"
        prop="reasonContent"
      >
        <el-autocomplete
          v-model="form.reasonContent"
          :fetch-suggestions="handleSearchReason"
          :trigger-on-focus="false"
          clearable
          style="width: 100%"
          :placeholder="$t('knowledgeBase.relatedFaultCausePlaceholder')"
        />
      </el-form-item>
      <el-form-item
        :label="$t('knowledgeBase.handlingMeasures')"
        prop="solutionContent"
      >
        <el-autocomplete
          v-model="form.solutionContent"
          :fetch-suggestions="handleSearchSolution"
          :trigger-on-focus="false"
          clearable
          style="width: 100%"
          :placeholder="$t('knowledgeBase.handlingMeasuresPlaceholder')"
        />
      </el-form-item>
    </el-form>
  </hmx-dialog>
</template>

<script setup name="RecordDialog">
import { reactive, ref, computed, toRefs, defineEmits } from "vue"
import HmxDialog from "@/components/hmx-dialog.vue"
import { useI18n } from "@xfe/locale"
import { getFuzzySearch } from "@/api/front/repairOrder.js"
import { postSingleKnowledgeBase } from "@/api/front/knowleage"
import { useMessage } from "@/hooks/web/useMessage"

const { createMessage } = useMessage()

const { t: $t } = useI18n()
const emit = defineEmits(["onSure", "onClose"])

const props = defineProps({
  // 当前所在知识库类型
  kbType: {
    type: Number,
    default: 1,
  },
  // 是否展示弹窗
  isShow: {
    type: Boolean,
    default: false,
  },
})

const currentType = computed(() => props.kbType)

const data = reactive({
  form: {
    phenomenonContent: "", //问题现象
    reasonContent: "", //关联故障原因
    solutionContent: "", //处理措施
    knowledgeCategory: 1, //知识库分类
  },
})

const formRef = ref()

const rules = {
  phenomenonContent: [
    {
      required: true,
      message: $t("knowledgeBase.problemPhenomenonPlaceholder"),
      trigger: "blur",
    },
  ],
  reasonContent: [
    {
      required: true,
      message: $t("knowledgeBase.relatedFaultCausePlaceholder"),
      trigger: "blur",
    },
  ],
  solutionContent: [
    {
      required: true,
      message: $t("knowledgeBase.handlingMeasuresPlaceholder"),
      trigger: "blur",
    },
  ],
}

// 模糊搜问题现象
const handleSearchPhenomenon = async (str, cb) => {
  let res = await getFuzzySearch({ knowledgeContent: 1, content: str })
  cb(
    res.map(item => ({
      value: item,
    }))
  )
}

// 模糊搜故障原因
const handleSearchReason = async (str, cb) => {
  let res = await getFuzzySearch({ knowledgeContent: 2, content: str })
  cb(
    res.map(item => ({
      value: item,
    }))
  )
}

// 模糊搜处理措施
const handleSearchSolution = async (str, cb) => {
  let res = await getFuzzySearch({ knowledgeContent: 3, content: str })
  cb(
    res.map(item => ({
      value: item,
    }))
  )
}

const isSubmitting = ref(false)

const saveDialog = async formEl => {
  if (!formEl || isSubmitting.value) return
  isSubmitting.value = true

  try {
    const valid = await formEl.validate()
    if (!valid) return

    data.form.knowledgeCategory = currentType.value
    await postSingleKnowledgeBase(data.form)
    createMessage($t("knowledgeBase.addSuccess"), "success")
    emit("onSure")
    closeFun()
  } catch (error) {
    if (error !== "cancel") {
      createMessage($t("knowledgeBase.addFailure"), "warning")
    }
  } finally {
    isSubmitting.value = false
  }
}

// 关闭弹框
const closeFun = () => {
  data.form = {
    phenomenonContent: "", //问题现象
    reasonContent: "", //关联故障原因
    solutionContent: "", //处理措施
    knowledgeCategory: 1, //知识库分类
  }
  emit("onClose")
}

const { form } = toRefs(data)
</script>

<style scoped lang="scss">
.form {
  width: 100%;
  height: 100%;
  .form-left {
    width: 100%;

    .reference-life {
      display: flex;

      .reference-life-left {
        width: 75%;
      }

      .reference-life-right {
        width: 25%;
      }
    }
  }

  .form-right {
    width: 48%;
    .three-d {
      display: flex;
      height: 100px;
      margin-bottom: 10px;
      .three-d-left {
        width: 65%;
      }
      .three-d-right {
        background-color: #bfa;
        width: 30%;
        display: flex;
        justify-content: center;
        align-items: center;
        .t-img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

::v-deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.three-d {
  display: flex;
  height: 100px;
  margin-bottom: 10px;
  .three-d-left {
    width: 65%;
  }
  .three-d-right {
    background-color: #bfa;
    width: 30%;
    display: flex;
    justify-content: center;
    align-items: center;
    .t-img {
      width: 100%;
      height: 100%;
    }
  }
}

::v-deep(.el-input.is-disabled .el-input__inner) {
  -webkit-text-fill-color: #fff !important;
}
::v-deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: rgb(29, 31, 34) !important;
}
::v-deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: rgb(29, 31, 34) !important;
}
</style>
