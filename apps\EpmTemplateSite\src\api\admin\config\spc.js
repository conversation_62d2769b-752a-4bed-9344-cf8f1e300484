import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix =
  config.base_url.homeServerPrefix + "/AnalyseWorkstation"

/**
 *  获取Spc参数
 */
export function getSpcParams() {
  return request.get({
    url: `${homeServerPrefix}/spc/parameter`,
  })
}

/**
 *  获取子组容量参数
 */
export function getSubgroupParams(data) {
  return request.get({
    url: `${homeServerPrefix}/spc/subgroupCapacity`,
    params: data,
  })
}

/**
 *  生成Xbar和R图
 */
export function getFigure(data) {
  return request.get({
    url: `${homeServerPrefix}/spc/Xbar-R`,
    params: data,
  })
}
