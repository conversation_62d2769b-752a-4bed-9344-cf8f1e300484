<template>
  <div class="log-tab">
    <div
      v-for="(item, index) of tabs"
      :class="{ active: isTab == index }"
      :key="item[customValueText]"
      class="footer-log-tab-item"
      @click="changeTab(item, index)"
    >
      <div class="icon">
        <img
          src="@/assets/images/box/icon-selected.svg"
          alt=""
          v-show="isTab == index"
        />
        <img
          src="@/assets/images/box/icon-unselected.svg"
          alt=""
          v-show="isTab !== index"
        />
      </div>
      <div class="content ml10" :class="{ 'active-font': isTab == index }">
        {{ $t(item.label) }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from "vue"

const props = defineProps({
  tabs: {
    type: Array,
    default: () => [],
  },
  // 自定义value和label的字段名
  customValueText: {
    type: String,
    default: "index",
  },
  customLabelText: {
    type: String,
    default: "label",
  },
})

const emits = defineEmits(["changeTab"])

let isTab = ref(0)

const changeTab = (item, index) => {
  if (isTab.value == index) return
  isTab.value = index
  emits("changeTab", item, index)
}
</script>
<style scoped lang="scss">
.footer-log-tab-item {
  position: relative;
  top: 1px;
  // z-index: 1;
  cursor: pointer;
  display: flex;
  justify-content: center;
  flex-grow: 0;
  flex-basis: auto;
  font-size: 16px;
  line-height: 32px;
  height: 32px;
  width: 150px;
  text-align: center;
  color: var(--tab-color);
  border-radius: 2px;
  border: 1px solid var(--tab-bottom-line-color);
  .icon {
    width: 12px;
    height: 12px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .content {
    margin-left: 10px;
  }
}
.log-tab {
  display: flex;
  & > div {
    &:nth-last-child(1) {
      border-right: 1px solid var(--tab-bottom-line-color);
    }
  }
}
.active {
  color: var(--tab-active-color);
  background: var(--tab-item-background);
}
</style>
