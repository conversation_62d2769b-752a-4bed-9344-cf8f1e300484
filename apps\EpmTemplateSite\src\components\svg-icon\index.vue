<template>
  <!-- 外面套一层 element图标 -->
  <el-icon :size="size" :color="color" :class="svgClass">
    <!--  :class="svgClass" :style="{ width: size + 'px', height: size + 'px', color: color }"  -->
    <svg aria-hidden="true">
      <use :xlink:href="`#icon-${name}`" />
    </svg>
  </el-icon>
</template>

<script>
export default {
  name: "svg-icon",
  props: {
    name: { type: String, required: true }, // svg 图标名称
    className: { type: String, default: "" }, // 指定的类样式
    size: { type: Number, default: 16 }, // 图标尺寸
    color: { type: String, default: "currentColor" }, // 图标颜色
  },
  computed: {
    // 计算属性
    svgClass() {
      if (this.className) {
        return "svg-icon" + this.className
      } else {
        return "svg-icon"
      }
    },
  },
}
</script>

<style scoped lang="scss">
/* .svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -2px;
  fill: currentColor;
  overflow: hidden;
} */
</style>
