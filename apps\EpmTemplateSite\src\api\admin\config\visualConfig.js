import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

//获取可视化配置列表
export function getVisualList() {
  return request.get({
    url: `${homeServerPrefix}/Visual`,
  })
}

// 新增可视化配置
export function addVisual(data) {
  return request.post({
    url: `${homeServerPrefix}/Visual`,
    data,
  })
}

// 删除可视化配置
export function deleteVisual(data) {
  return request.delete({
    url: `${homeServerPrefix}/Visual`,
    data,
  })
}

// 编辑可视化配置
export function updateVisual(data, id) {
  return request.put({
    url: `${homeServerPrefix}/Visual/${id}`,
    data,
  })
}

// 发布配置
export function publishConfig(id) {
  return request.put({
    url: `${homeServerPrefix}/Visual/publish/${id}`,
  })
}

// 获取已发布的配置
export function getPublish() {
  return request.get({
    url: `${homeServerPrefix}/Visual/publish`,
  })
}
