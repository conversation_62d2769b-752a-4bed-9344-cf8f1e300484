<template>
  <div class="xfe-layout">
    <div class="container">
      <hmxHeader></hmxHeader>
      <containerLayout>
        <div class="layout">
          <vertical-menu />
          <div class="layout-content">
            <div class="main-container">
              <Breadcrumb class="breadcrumb" />
              <div class="main-slot">
                <router-view></router-view>
              </div>
            </div>
          </div>
        </div>
      </containerLayout>
    </div>
  </div>
</template>

<script setup name="content">
import Breadcrumb from "@/components/bread-crumb/index.vue"
import hmxHeader from "@/components/hmx-header/index.vue"
import containerLayout from "./components/containerLayout.vue"
import VerticalMenu from "./components/menu/VerticalMenu.vue"
// import { RouterAlive } from "./routerAlive"
</script>

<style lang="scss" scoped>
.xfe-layout {
  width: 100vw;
  height: 100vh;

  display: flex;
  overflow: hidden;
  background: radial-gradient(
    closest-side at 50% 38%,
    rgba(32, 33, 47, 0.2) -300%,
    #141734 100%
  );
  opacity: 1;
  flex: 1;
  display: flex;
  flex-direction: column;
  .container {
    width: 100%;
    height: 100%;
  }
}
.layout {
  width: 100%;
  height: calc(100% - 10px);
  display: flex;
  padding: 20px;
  background: #00162c;
  .layout-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    .main-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      .breadcrumb {
        padding-left: 8px;
        height: 40px;
        line-height: 40px;
      }
      .main-slot {
        flex: 1;
        height: 100%;
        background-color: rgba(0, 75, 144, 0.3);
        border: 1px solid #0084fe;
        // padding: 14px;
        & > section {
          height: 100%;
        }
      }
    }
  }
}
</style>
