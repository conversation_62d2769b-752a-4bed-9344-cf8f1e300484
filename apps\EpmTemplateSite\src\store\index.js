import { createStore } from "vuex"
import createPersistedState from "vuex-persistedstate"

const modulesFiles = require.context("./modules", true, /\.js$/) // 遍历出modules下的所有文件
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, "$1")
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

export default createStore({
  state: {},
  mutations: {},
  actions: {},
  modules: {
    ...modules,
  },
  plugins: [
    createPersistedState({
      key: "vuex",
      storage: window.sessionStorage,
    }),
  ],
})
