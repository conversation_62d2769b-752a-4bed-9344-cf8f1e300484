import { request } from "@xfe/request"
import { config } from "@/config"
const homeServerPrefix = config.base_url.homeServerPrefix

/**
 *  导出交互配置表
 */
export function exportExcel() {
  return request.get({
    url: `${homeServerPrefix}/InspectionManager/Export`,
    header: {
      headers: { "Content-Type": "application/x-download" },
    },
    responseType: "blob",
  })
}

export function importExcel(data) {
  return request.post({
    url: `${homeServerPrefix}/InspectionManager/import`,
    headers: { "Content-Type": "multipart/form-data" },
    data,
  })
}
