<template>
  <!-- 递归树形菜单 -->
  <el-menu
    :router="isRouterMenu"
    :default-active="activeMenu"
    :collapse="isCollapse"
    :unique-opened="true"
    background-color="transparent"
    text-color="#0084FE"
    active-text-color="#fff"
  >
    <!-- 使用递归组件 -->
    <my-sub-menu
      v-for="item in menu"
      :key="item.path"
      :item="item"
    ></my-sub-menu>
  </el-menu>
</template>

<script setup name="MenuTree">
import { reactive, toRefs, watch } from "vue"
import { useRoute } from "vue-router"
import MySubMenu from "./sub-menu.vue"

const route = useRoute()

defineProps({
  menu: {
    type: Array,
    default: () => [],
    required: true,
  },
  isCollapse: {
    type: Boolean,
    default: false,
  },
  isRouterMenu: {
    type: Boolean,
    default: true,
  },
})

const data = reactive({
  isCollapse: false,
  activeMenu: "",
})

watch(
  () => route,
  route => {
    const { meta, path } = route
    if (meta.activeMenu) {
      data.activeMenu = meta.activeMenu
    } else {
      data.activeMenu = path
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

const { activeMenu } = toRefs(data)
</script>

<style lang="scss" scoped>
.el-menu {
  border-right: none !important;
  :deep(.el-sub-menu) {
    margin-bottom: 10px !important;
  }
}
</style>
