<template>
  <productStatisBase
    :tableColumn="tableColumn"
    :baseOption="baseOption"
    url="DigitalReport/Production"
    exportUrl="ProductData/teamTime/productInfo/export"
    :name="$t('digitalStatement.productionStatistics')"
  ></productStatisBase>
</template>
<script setup>
import productStatisBase from "../base/productStatisBase.vue"
import { ref, computed, watch } from "vue"
import { useStore } from "vuex"
import { useI18n } from "@xfe/locale"

const store = useStore()
let themeColor = computed(() => store.state.theme.themeColor)
const { t: $t } = useI18n()

let tableColumn = ref([
  {
    type: "index",
    label: "No.",
    align: "center",
    fixed: "left",
    width: "50",
    show: true,
  },
  {
    prop: "equipmentName",
    label: $t("digitalStatement.equipmentName"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "productModel",
    label: $t("digitalStatement.productModel"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "date",
    label: $t("digitalStatement.date"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
    slot: "date",
  },
  {
    prop: "teamTime",
    label: $t("digitalStatement.shift"),
    align: "center",
    showOverflowTooltip: true,
    show: true,
  },
  {
    prop: "yield",
    label: $t("digitalStatement.yield"),
    align: "center",
    show: true,
  },
])

let baseOption = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
      textStyle: {
        color: "#fff",
      },
    },
  },
  grid: {
    borderWidth: 0,
    top: "12%",
    left: "5%",
    right: "5%",
    bottom: "15%",
    textStyle: {
      color: "#fff",
    },
  },
  legend: {
    rigtt: "10%",
    top: "2%",
    textStyle: {
      color: "#fff",
    },
    data: [],
  },

  xAxis: {
    type: "category",
    axisLine: {
      lineStyle: {
        color: "#fff",
      },
    },
    axisLabel: {
      color: "#fff",
    },
    axisTick: {
      alignWithLabel: true,
    },
    data: [],
  },
  yAxis: {
    type: "value",
    name: $t("digitalStatement.yield"),
    splitLine: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: "#fff",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      interval: 0,
    },
    splitArea: {
      show: false,
    },
  },

  series: [
    {
      name: $t("digitalStatement.dayShift"),
      type: "bar",
      yAxisIndex: 0,
      barGap: "10%",
      label: {
        show: false,
        position: "inside",
      },
      color: "#EECB4E",
      data: [],
    },
    {
      name: $t("digitalStatement.nightShift"),
      type: "bar",
      yAxisIndex: 0,
      label: {
        show: false,
        position: "inside",
      },
      color: "#0084FE",
      data: [],
    },
  ],
}

const DARK_COLOR = "#fff"
const LIGHT_COLOR = "#606266"

watch(
  () => themeColor.value,
  v => {
    if (v === "light") {
      // 质量统计
      baseOption.legend.textStyle.color = LIGHT_COLOR
      baseOption.xAxis.axisLine.lineStyle.color = LIGHT_COLOR
      baseOption.yAxis.axisLabel.color = LIGHT_COLOR
      baseOption.series[0].label.color = LIGHT_COLOR
      baseOption.series[1].label.color = LIGHT_COLOR
    } else {
      // 质量统计
      baseOption.legend.textStyle.color = DARK_COLOR
      baseOption.xAxis.axisLine.lineStyle.color = DARK_COLOR
      baseOption.yAxis.axisLabel.color = DARK_COLOR
      baseOption.series[0].label.color = DARK_COLOR
      baseOption.series[1].label.color = DARK_COLOR
    }
  },
  { immediate: true }
)
</script>
