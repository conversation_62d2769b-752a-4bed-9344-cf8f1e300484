import { request } from "@xfe/request"
import { config } from "@/config"
const base = config.base_url.homeServerPrefix

export function getTableChart(url, params) {
  return request.get({
    url: `${base}/${url}`,
    params,
  })
}

export function getTeamTime() {
  return request.get({
    url: `${base}/TeamTime`,
  })
}

export function getProductngInfo(params) {
  return request.get({
    url: `${base}/ProductData/teamTime/productngInfo`,
    params,
  })
}

export function getTableLogs(url, params) {
  return request.get({
    url: `${base}/${url}`,
    params,
  })
}

export function exportLogs(url, params) {
  return request.get({
    url: `${base}/${url}`,
    headers: { "Content-Type": "application/x-download" },
    responseType: "blob",
    params,
  })
}

/**
 * 获取故障时长排行榜
 * @param {*} params
 * @returns
 */
export function getFaultDurationRanking(params) {
  return request.get({
    url: `${base}/FaultAnalysis/DurationRanking`,
    params,
  })
}

/**
 * 获取故障次数排行榜
 * @param {*} params
 * @returns
 */
export function getFaultNumberRanking(params) {
  return request.get({
    url: `${base}/FaultAnalysis/NumberRanking`,
    params,
  })
}

/**
 * 获取报警时长排行榜
 * @param {*} params
 * @returns
 */
export function getAlarmDurationRanking(params) {
  return request.get({
    url: `${base}/DigitalReport/DurationRanking`,
    params,
  })
}

/**
 * 获取报警次数排行榜
 * @param {*} params
 * @returns
 */
export function getAlarmNumberRanking(params) {
  return request.get({
    url: `${base}/DigitalReport/NumberRanking`,
    params,
  })
}

export function getFaultDetail(params) {
  return request.get({
    url: `${base}/FaultAnalysis/warn/details`,
    params,
  })
}

/**
 * 获取生产柱状图数据
 * @param {*} params
 * @returns
 */
export function getProductionBar(params) {
  return request.get({
    url: `${base}/DigitalReport/Production/Bar`,
    params,
  })
}
