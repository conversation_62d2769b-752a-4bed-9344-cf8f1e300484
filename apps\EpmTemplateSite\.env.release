# .env.basic 用于本地构建测试
NODE_ENV = "release"
VUE_APP_ENV_NAME = "release"

# 接口前缀
VUE_APP_BASEPATH = 'release'
# socket
VUE_APP_SCOKET = 'scoket_release'

# Whether to open mock
VUE_APP_USE_MOCK = true

# public path
VUE_APP_PUBLIC_PATH = /

# Cross-domain proxy, you can configure multiple
# Please note that no line breaks
VUE_APP_PROXY = [["/api","http://localhost:2000"]]
# Whether to enable https and http2 in the development environment
# it will greatly optimize the loading speed at the first startup
# 是否在开发环境开启 https 和 http2（开启后将大幅优化首次启动时加载速度）
VUE_APP_USE_HTTPS = true

# Delete console
VUE_APP_DROP_CONSOLE = false

# Basic interface address SPA
VUE_APP_GLOB_API_URL = "http://***********:40751/api"

# # Basic webscoket interface address SPA
VUE_APP_GLOB_APP_SCOKET = "//***********:40751"

# Basic interface address SPA by mock
VUE_APP_GLOB_APP_MOCK_URL = "http://localhost:6300/api"
VUE_APP_GLOB_APP_MOCK_SCOKET_URL = "http://localhost:6300"

# Basic server name
VUE_APP_GLOB_APP_SERVER_NAME = "/EpmTemplate"

# 3D server name
VUE_APP_GLOB_BBL_URL = "http://***********:40751/api"

# 3D iframe name
VUE_APP_GLOB_3D_URL = "http://***********:20000"
