import { App } from "vue"
import { initRequest } from "@xfe/request"
import useAuths from "@/core/useAuth"
import { config } from "@/config"
import { useMessage } from "@/hooks/web/useMessage"
import { useLocale } from "@xfe/locale"

const { getLocale } = useLocale()
// 为了解耦 `packages/*` 下面各模块，不再相互依赖
async function initPackages() {
  const _initRequest = async () => {
    //TODO 路由api前缀统一处理函数
    const { base_url, request_timeout, default_headers } = config

    const PATH_URL = base_url[process.env.VUE_APP_BASEPATH]
    const {
      getToken,
      logoutFun,
      clearStorage,
      getRefreshToken,
      refreshToken,
      getTenantId,
    } = useAuths()
    const RefreshToken = `${base_url.homeServerPrefix}/Account/login/refresh_token`
    const { createMessage } = useMessage()

    await initRequest(() => {
      return {
        apiUrl: PATH_URL,
        refreshToken: RefreshToken,
        timeOut: request_timeout,
        headers: default_headers,
        getTokenFunction: () => {
          return getToken()
        },
        getTenantId: () => {
          return getTenantId()
        },
        getRefreshTokenFunction: () => {
          return getRefreshToken()
        },
        refreshTokenFunction: () => {
          return Promise.resolve(refreshToken())
        },
        errorFunction: null,
        noticeFunction: null,
        errorModalFunction: null,
        timeoutFunction: () => {
          clearStorage()
          logoutFun()
        },
        unauthorizedFunction: () => {
          createMessage("非法访问，即将登出，请联系管理员!", "error")
          clearStorage()
          // logoutFun()
        },
        handleErrorFunction: (msg: string) => {
          createMessage(msg, "error")
        },
      }
    })
  }

  await Promise.all([_initRequest()])
}

export async function initApplication() {
  // ! Need to pay attention to the timing of execution
  // ! 需要注意调用时机
  await initPackages()
}
