 server {
      listen       1900;
      server_name  localhost;

      #charset koi8-r;

      #access_log  logs/host.access.log  main;
      location ~* /Api {
			add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
			if ($request_method = 'OPTIONS') {
				return 204;
                }
			proxy_pass http://***********:1900;
		}
		location ~* /NoticeHub {
			proxy_http_version 1.1;
			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header Connection "upgrade";
			proxy_set_header Host $http_host;
			add_header Access-Control-Allow-Origin *;
			proxy_pass http://***********:1900;
		}
    location / {
        add_header Access-Control-Allow-Origin * ;
        add_header Access-Control-Allow-Credentials true;
        add_header Access-Control-Allow-Methods 'GET,POST,OPTIONS';
        add_header Access-Control-Allow-Headers 'Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Requested-With';
        #proxy_pass http://gsx;
        root   D:\\hmx\\site\\HslSite\\base;
        index index.html;
    }
    location = /50x.html {
        root   html;
    }
  }

 upstream gsx {

    server 127.0.0.1:29000;

 }
