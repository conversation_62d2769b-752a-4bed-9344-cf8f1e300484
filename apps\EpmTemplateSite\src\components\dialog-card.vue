<template>
  <el-dialog
    @close="closeFun"
    @open="openFun"
    :title="dilogAddTitle"
    :close-on-click-modal="false"
    v-model="dialogVisble"
    :destroy-on-close="true"
    :append-to-body="appendToBody"
    :show-close="isClose"
    :width="dialogWidth"
    :class="customClass"
    :style="dialogStyle"
    :data-theme="themeColor"
  >
    <template #header>
      <!-- <div class="title-box">
        <img class="title-icon" src="@/assets/images/box/dialog/logInfo.svg" />
        <span class="title">{{ dilogAddTitle }}</span>
        <span class="triangle"></span>
      </div> -->
      <!-- <div class="triangle"></div> -->
      <div class="title-box">
        <span class="title-icon">
          <slot name="icon"></slot>
        </span>
        <span class="title" v-if="dilogAddTitle">{{ dilogAddTitle }}</span>
        <!-- 自定义title -->
        <span class="title" v-else><slot name="title"></slot></span>
      </div>
    </template>
    <slot></slot>
    <template #footer v-if="isFooter">
      <span class="dialog-footer">
        <el-button
          @click="closeFun"
          :size="clientWidth() ? 'large' : 'small'"
          v-if="isCancel"
          >{{ $t("common.cancelText") }}</el-button
        >
        <el-button
          type="primary"
          :disabled="disabled"
          :loading="loading"
          :size="clientWidth() ? 'large' : 'small'"
          @click="save"
          >{{ $t("common.okText") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup name="DialogCard">
import { defineEmits, defineProps, computed } from "vue"
import { clientWidth } from "@/utils/index"
import { useI18n } from "@xfe/locale"
import { useStore } from "vuex"

const { t: $t } = useI18n()
const emits = defineEmits(["save", "closeDialog", "openDialog"])

const props = defineProps({
  dilogAddTitle: {
    //  dialog的标题
    type: String,
    default: "",
  },
  isVisable: {
    // 弹窗是否显示
    type: Boolean,
    default: false,
  },
  isFooter: {
    //  是否显示弹窗底部
    type: Boolean,
    default: false,
  },
  isCancel: {
    // 是否显示取消
    type: Boolean,
    default: true,
  },
  cancelText: {
    // 取消的默认文本
    type: String,
    default: "取 消",
  },
  saveText: {
    // 确定的默认文本
    type: String,
    default: "确 定",
  },
  isClose: {
    // 是否显示关闭按钮  默认true
    type: Boolean,
    default: true,
  },
  dialogWidth: {
    // 弹窗的宽度
    type: [String, Number],
  },
  customClass: {
    // 弹框的自定义类名
    type: String,
    default: "hsx-dialog",
  },
  appendToBody: {
    // 是否挂载在body上  默认true
    type: Boolean,
    default: false,
  },
  dialogStyle: {
    // 弹窗的高度
    type: [Object],
    default: () => {},
  },
  disabled: {
    // 是否禁用按钮
    type: Boolean,
    default: false,
  },
  loading: {
    // 按钮是否等待
    type: Boolean,
    default: false,
  },
})

// const dialogVisble = computed(() => props.isVisable);
// 解决控制台报警告问题
const dialogVisble = computed({
  get() {
    // getter
    return props.isVisable
  },
  set(val) {
    // setter
    return val
  },
})

const save = () => {
  emits("save") // 保存
}

const closeFun = () => {
  emits("closeDialog") // 关闭弹窗
}

const openFun = () => {
  emits("openDialog") // 开启弹窗
}

const store = useStore()
let themeColor = computed(() => store.state.theme.themeColor)
</script>
<style scoped lang="scss">
.dialog-footer button:first-child {
  margin-right: 10px;
}
</style>
<style lang="scss">
.hsx-dialog.el-dialog {
  background-color: #0d142b;
  background: url("~@/assets/images/box/dialog/background.png") no-repeat; //var(--dialog-background);
  background-size: 100% 100%;
  background-position: center center;
  // padding-top: 20px;
  .el-dialog__header {
    display: flex;
    align-items: center;
    position: relative;
    top: -10px;
    // padding: 3px 1px;
  }
  .el-dialog__title,
  .el-dialog__body {
    color: #fff; //var(--title-color);
  }
  .el-dialog__body {
    padding-top: 10px;
    overflow: auto;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: #fff;
    font-size: 36px;
    position: relative;
    right: -10px;
    top: 30px;
  }
  .el-dialog__headerbtn:focus .el-dialog__close,
  .el-dialog__headerbtn:hover .el-dialog__close {
    color: #1890ff;
  }

  // .title-box{
  //   height: 40px;
  //   display: flex;
  //   align-items: center;
  //   padding-left: 20px;
  // border: ;
  // background-color: #074886;
  // border-left:1px solid rgb(24, 114, 255) ;
  // border-top: 1px solid rgb(24, 114, 255);
  .title-box {
    display: flex;
    .title-icon {
      // padding-bottom: 3px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .title {
      color: var(--title-color);
      font-size: 24px;
      margin-left: 5px;
      // margin-right: 30px;
    }
  }

  // }
  // .triangle {
  //       width: 0;
  //       height: 0;
  //       border-top: 42px solid transparent;
  //       border-right: 42px solid rgba(38, 39, 55, );
  //       transform: rotate(-90deg);
  //   }
}
</style>
