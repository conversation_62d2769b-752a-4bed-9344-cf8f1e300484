# .env.prod 用于线上构建，下面这些域名是真实域名
NODE_ENV = "production"
VUE_APP_ENV_NAME = "production"

# 接口前缀
VUE_APP_BASEPATH = 'pro'

# socket
VUE_APP_SCOKET = 'scoket_pro'

# Whether to open mock
VUE_APP_USE_MOCK = true

# public path
VUE_APP_PUBLIC_PATH = /

# Delete console
VUE_APP_DROP_CONSOLE = true

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VUE_APP_BUILD_COMPRESS = 'none'

# Whether to delete origin files when using compress, default false
VUE_APP_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# Whether to enable image compression
VUE_APP_USE_IMAGEMIN= true

# use pwa
VUE_APP_USE_PWA = false

# Is it compatible with older browsers
VUE_APP_LEGACY = false

# Basic interface address SPA
VUE_APP_GLOB_API_URL = "/api"

# Basic webscoket interface address SPA
VUE_APP_GLOB_APP_SCOKET = ""

# Basic interface address SPA by mock
VUE_APP_GLOB_APP_MOCK_URL = ""
VUE_APP_GLOB_APP_MOCK_SCOKET_URL = ""

# Basic server name
VUE_APP_GLOB_APP_SERVER_NAME = "/EpmTemplate"

# 3D iframe name
VUE_APP_GLOB_3D_URL = "http://localhost:20003"
