<template>
  <div class="s-table">
    <div class="head">
      <div v-for="head of tableHeader" :key="head.text" :style="head.style">
        {{ head.label }}
      </div>
    </div>

    <div class="infinite-list" v-infinite-scroll="load">
      <div
        :class="['list-item', item['bg'] ? 'bg-highlight' : '']"
        v-for="(item, i) in tableData"
        :key="i"
        @click="showDetail(item)"
      >
        <div
          v-for="head of tableHeader"
          :key="head.prop"
          :style="head.style"
          :class="['list-cell', head.class]"
          :title="head.prop === 'no.' ? '' : item[head.prop]"
        >
          <el-tooltip
            effect="dark"
            content="点击查看全部内容"
            placement="top-end"
            :show-arrow="false"
          >
            {{ head.prop === "no." ? i + 1 : item[head.prop] }}
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, watch } from "vue"
import { useI18n } from "@xfe/locale"

const props = defineProps({
  data: {
    type: Array,
    defaule: () => [],
  },
  dialogType: {
    type: String,
    defaule: "",
  },
})

// watch(
//   () => props.data,
//   val => {
//     val.data[0].bg = true
//     setTimeout(() => {
//       val.data.map(item => {
//         delete item.bg
//         return item
//       })
//     }, 2000)
//   }
// )
const { t: $t } = useI18n()
const emits = defineEmits(["showDetail"])
const showDetail = info => {
  emits("showDetail", { ...info, type: props.dialogType })
}
function load() {
  //滚动加载
}

const tableData = ref([])
const tableHeader = ref([])

watch(
  props.data,
  val => {
    const table = []
    val.forEach(item => {
      const obj = {
        colorFields: [],
        resultColor: "",
      }
      item.forEach(info => {
        obj[info.code] = info.value
        if (info.resultColor) {
          obj.resultColor = info.resultColor
          obj.colorFields.push(info.code)
        }
      })
      table.push(obj)
    })
    tableData.value = table

    if (val.length > 0 && tableHeader.value.length === 0) {
      const allColumns = []
      val[0].forEach(info => {
        if (info.isMain) {
          let obj = {
            prop: info.code,
            label: info.name,
            // align: "center",
            showOverflowTooltip: true,
            info: info,
            style: { flex: 1 },
            resultColor: "resultColor",
          }
          allColumns.push(obj)
        }
        // if (info.isMain) {
        //   obj.fixed = "left"
        // }
        // if (info.isMain && !info.resultColor) {
        //   obj.width = "300"
        // }
      })
      tableHeader.value = allColumns
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.s-table {
  .data-name {
    color: #4767ff;
  }
  .stations-box {
    background: #4767ff;
    .data-name {
      color: red;
    }
  }
  width: 100%;
  height: 100%;
  .head {
    margin: 5px 0;
    width: 100%;
    display: flex;
    color: #fff;
    font-size: 12px;
    height: 30px;
    line-height: 30px;
    font-weight: bold;
    background-color: rgba(0, 132, 254, 0.25);
    > div {
      text-align: center;
    }
  }
  .infinite-list {
    width: 100%;
    height: calc(100% - 45px);
    overflow: auto;
    .list-item {
      display: flex;
      color: #fff;
      font-size: 12px;
      height: 26px;
      line-height: 26px;
      margin-bottom: 8px;
      clip-path: polygon(0 0, 100% 0, 100% 22px, 97% 36px, 0 36px);
      // &:hover {
      //   background: var(--canvas-bg);
      //   opacity: 1;
      //   border: 1px solid rgba(25, 198, 255, 0.3);
      //   color: #fff;
      // }
      .list-cell {
        text-align: center;
        overflow: hidden; /*溢出的部分隐藏*/
        white-space: nowrap; /*文本不换行*/
        text-overflow: ellipsis;
        cursor: pointer;
      }
    }
    .bg-highlight {
      // background-color: var(--el-menu-hover-text-color);
    }
  }
}
::-webkit-scrollbar {
  display: none;
}
</style>
